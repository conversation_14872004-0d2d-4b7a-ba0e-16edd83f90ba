/**
 * TWL Cart Enhancements Hook
 * 
 * A simple hook that adds enterprise features to the existing cart
 * without breaking or replacing the original cart functionality.
 * 
 * Usage:
 * ```jsx
 * import { useCart } from '@/contexts/CartContext'
 * import { useCartEnhancements } from '@/lib/enterprise/minimal/useCartEnhancements'
 * 
 * function CartPage() {
 *   const cart = useCart() // Original cart
 *   const enhancements = useCartEnhancements(cart.items) // Optional enhancements
 *   
 *   return (
 *     <div>
 *       {enhancements.enhancedSummary.totalSavings > 0 && (
 *         <div>You save: ${enhancements.enhancedSummary.totalSavings}</div>
 *       )}
 *     </div>
 *   )
 * }
 * ```
 */

import { useState, useEffect, useCallback } from 'react'
import cartEnhancer from './CartEnhancer'

export function useCartEnhancements(cartItems = []) {
  const [enhancedItems, setEnhancedItems] = useState([])
  const [enhancedSummary, setEnhancedSummary] = useState({})
  const [recommendations, setRecommendations] = useState([])
  const [validationResults, setValidationResults] = useState({})
  const [isLoading, setIsLoading] = useState(false)
  const [isEnabled, setIsEnabled] = useState(true)

  // Enhance cart items
  const enhanceItems = useCallback(async () => {
    if (!cartItems.length || !isEnabled) {
      setEnhancedItems(cartItems)
      return
    }

    setIsLoading(true)
    
    try {
      const enhanced = await Promise.all(
        cartItems.map(item => cartEnhancer.enhanceProduct(item))
      )
      
      setEnhancedItems(enhanced)
    } catch (error) {
      console.warn('Failed to enhance cart items:', error)
      setEnhancedItems(cartItems) // Fallback to original
    } finally {
      setIsLoading(false)
    }
  }, [cartItems, isEnabled])

  // Enhance cart summary
  const enhanceSummary = useCallback(() => {
    if (!cartItems.length || !isEnabled) {
      setEnhancedSummary({})
      return
    }

    try {
      // Calculate basic summary
      const subtotal = enhancedItems.reduce((sum, item) => 
        sum + (item.price * (item.quantity || 1)), 0
      )
      
      const originalSummary = {
        subtotal: subtotal,
        tax: Math.round(subtotal * 0.16),
        shipping: 0,
        total: Math.round(subtotal * 1.16)
      }

      const enhanced = cartEnhancer.enhanceCartSummary(originalSummary, enhancedItems)
      setEnhancedSummary(enhanced)
    } catch (error) {
      console.warn('Failed to enhance summary:', error)
      setEnhancedSummary({})
    }
  }, [enhancedItems, isEnabled])

  // Get recommendations
  const loadRecommendations = useCallback(async () => {
    if (!cartItems.length || !isEnabled) {
      setRecommendations([])
      return
    }

    try {
      const recs = await cartEnhancer.getRecommendations(cartItems, 4)
      setRecommendations(recs)
    } catch (error) {
      console.warn('Failed to load recommendations:', error)
      setRecommendations([])
    }
  }, [cartItems, isEnabled])

  // Validate cart items
  const validateItems = useCallback(async () => {
    if (!cartItems.length || !isEnabled) {
      setValidationResults({})
      return
    }

    try {
      const results = await cartEnhancer.validateCartItems(cartItems)
      setValidationResults(results)
    } catch (error) {
      console.warn('Failed to validate cart items:', error)
      setValidationResults({})
    }
  }, [cartItems, isEnabled])

  // Run enhancements when cart items change
  useEffect(() => {
    enhanceItems()
  }, [enhanceItems])

  useEffect(() => {
    enhanceSummary()
  }, [enhanceSummary])

  useEffect(() => {
    loadRecommendations()
  }, [loadRecommendations])

  useEffect(() => {
    validateItems()
  }, [validateItems])

  // Enhanced add to cart function
  const enhancedAddToCart = useCallback(async (originalAddToCart, productId, size, quantity) => {
    try {
      // Call original add to cart function
      const result = await originalAddToCart(productId, size, quantity)
      
      // If successful and enhancements are enabled, try to enhance the product
      if (result && isEnabled) {
        try {
          // Find the product that was just added
          const addedProduct = { id: productId, size, quantity }
          const enhanced = await cartEnhancer.enhanceProduct(addedProduct)
          
          console.log(`✨ Enhanced product added to cart: ${enhanced.name || productId}`)
          
          return {
            ...result,
            enhanced: true,
            product: enhanced
          }
        } catch (error) {
          console.warn('Failed to enhance added product:', error)
        }
      }
      
      return result
    } catch (error) {
      console.error('Enhanced add to cart failed:', error)
      throw error
    }
  }, [isEnabled])

  // Get metrics
  const getMetrics = useCallback(() => {
    return cartEnhancer.getMetrics()
  }, [])

  // Toggle enhancements
  const toggleEnhancements = useCallback((enabled) => {
    setIsEnabled(enabled)
    cartEnhancer.setEnabled(enabled)
  }, [])

  return {
    // Enhanced data
    enhancedItems,
    enhancedSummary,
    recommendations,
    validationResults,
    
    // State
    isLoading,
    isEnabled,
    
    // Functions
    enhancedAddToCart,
    getMetrics,
    toggleEnhancements,
    
    // Utilities
    hasEnhancements: enhancedSummary._enhanced || false,
    hasSavings: (enhancedSummary.totalSavings || 0) > 0,
    hasRecommendations: recommendations.length > 0,
    hasValidationIssues: Object.values(validationResults).some(v => !v.isValid || v.hasStockIssue),
    
    // Quick access to enhanced data
    totalSavings: enhancedSummary.totalSavings || 0,
    limitedEditionCount: enhancedSummary.limitedEditionCount || 0,
    enhancedItemsCount: enhancedSummary.enhancedItemsCount || 0
  }
}

// Simple component wrapper for easy integration
export function CartEnhancementProvider({ children, cartItems }) {
  const enhancements = useCartEnhancements(cartItems)
  
  return (
    <div data-cart-enhancements={enhancements.isEnabled}>
      {children}
    </div>
  )
}

export default useCartEnhancements
