🧱 The White Laces – Component Library Spec Sheet
Glassmorphic | Minimalist Luxury | Streetwear Edge | Mobile-First
👟 Luxury Footwear Meets Gen Z Culture | E-commerce Platform 

1. 📦 Product Card (Glassmorphic)
🎯 Purpose:
Display product images with layered glassmorphic UI

🧩 Classes:
<div class="backdrop-blur-md bg-mist-gray border border-frosted-overlay rounded-xl p-4 shadow-glass">

🖼️ Image:
<img src="product.jpg" alt="Product" class="w-full h-auto rounded-lg mb-4" />

📄 Text Elements:
<h3 class="text-white font-bold">Nike x Off-White Dunk</h3>
<p class="text-sm text-cyber-blue">$180</p>

🎮 Button:
<button class="mt-3 w-full py-2 bg-neon-pulse text-white rounded-lg hover:bg-[#e01647] transition-all">
  Add to Cart
</button>

🌙 Dark Mode Support:
Yes — uses bg-mist-gray and text-white

2. 🔘 Buttons
Type,               Class,                                                                          State Variants,                         Use Case
Primary,            bg-neon-pulse text-white rounded-lg shadow-glass,                               Hover: hover:bg-[#e01647],              CTA buttons
Secondary,          bg-transparent border border-cyber-blue text-cyber-blue rounded-lg,             Hover: bg-cyber-blue/10,                View Collection
Ghost,              text-neon-pulse underline,                                                      Hover: opacity-80,                      Links
FAB,                fixed bottom-4 right-4 p-4 bg-gold-dust text-black rounded-full shadow-glass,   Hover: scale-105,                       Wishlist icon


3. 🗂️ Navigation Bar (Mobile Bottom Nav)
🧩 Structure:

<nav class="fixed bottom-0 left-0 right-0 flex justify-around items-center bg-mist-gray backdrop-blur-md border-t border-frosted-overlay text-white">
  <a href="/" class="flex flex-col items-center p-2 text-neon-pulse">
    <span>🏠</span>
    <span class="text-xs mt-1">Home</span>
  </a>
  <a href="/shop" class="flex flex-col items-center p-2">
    <span>🛍️</span>
    <span class="text-xs mt-1">Shop</span>
  </a>
  <a href="/search" class="flex flex-col items-center p-2">
    <span>🔍</span>
    <span class="text-xs mt-1">Search</span>
  </a>
  <a href="/community" class="flex flex-col items-center p-2">
    <span>👥</span>
    <span class="text-xs mt-1">Community</span>
  </a>
  <a href="/account" class="flex flex-col items-center p-2">
    <span>👤</span>
    <span class="text-xs mt-1">Account</span>
  </a>
</nav>


4. 🧾 Modals & Overlays
🎁 Example Modal (Quick View):

<div class="fixed inset-0 flex items-center justify-center z-50 bg-black/50 backdrop-blur-sm">
  <div class="bg-mist-gray border border-frosted-overlay rounded-xl p-6 w-11/12 max-w-md shadow-glass">
    <h2 class="text-xl font-bold text-white mb-4">Nike Air Max 97</h2>
    <img src="product.jpg" alt="Product" class="w-full h-auto rounded-lg mb-4" />
    <p class="text-cyber-blue mb-4">$220</p>
    <button class="w-full py-2 bg-neon-pulse text-white rounded-lg">Add to Cart</button>
  </div>
</div>


5. 🎁 Badges & Tags

Type,                    Class,                                                                     Use

Limited Edition,         bg-neon-pulse text-white px-2 py-1 text-xs rounded-full,                   New drops
VIP Exclusive,           bg-gold-dust text-black px-2 py-1 text-xs rounded-full,                    Loyalty users
On Sale,                 bg-velvet-red text-white px-2 py-1 text-xs rounded-full line-through,      Discounted items
New Arrival,             bg-cyber-blue text-black px-2 py-1 text-xs rounded-full,                   Fresh stock


6. 📤 Forms & Inputs

📝 Text Input:

<input type="text" placeholder="Email" class="w-full p-3 bg-mist-gray border border-frosted-overlay rounded-lg text-white placeholder:text-gray-400 focus:outline-none focus:border-neon-pulse" />

📋 Checkbox:
<label class="flex items-center space-x-2 cursor-pointer">
  <input type="checkbox" class="form-checkbox h-5 w-5 text-neon-pulse rounded border-frosted-overlay bg-mist-gray" />
  <span>I agree to the terms</span>
</label>

7. 🧭 Progress & Feedback
🔄 Loading Spinner:
<div class="animate-spin h-6 w-6 border-t-2 border-b-2 border-neon-pulse rounded-full"></div>


✅ Toast Notification:
<div class="fixed bottom-4 right-4 bg-mint-glow text-black px-4 py-2 rounded-lg shadow-glass backdrop-blur-sm animate-fade-in">
  Added to wishlist!
</div>


8. 📱 Responsive Layout Grid

🧩 Example Grid (2-column mobile, 3-column desktop):
<div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
  <!-- Product Cards Go Here -->
</div>


9. 🧪 Accessibility Ready
All components use semantic tags and are keyboard navigable:

aria-labels on icons
Proper contrast ratios (text-white, text-cyber-blue)
Focus states on interactive elements
Alt attributes on all images


10. 🧩 Utility Components

🎨 Gradient Banner:
<div class="bg-gradient-to-r from-gradient-start to-gradient-end text-white p-6 rounded-xl text-center">
  <h2 class="text-2xl font-bold">Limited Edition Drop</h2>
  <p>Available only until midnight.</p>
</div>


🧠 Empty State:
<div class="flex flex-col items-center justify-center py-10">
  <img src="empty-cart.svg" alt="Empty Cart" class="w-24 h-24 opacity-50" />
  <p class="text-gray-400 mt-4">Your cart is empty</p>
  <button class="mt-4 px-6 py-2 bg-neon-pulse text-white rounded-lg">Start Shopping</button>
</div>

