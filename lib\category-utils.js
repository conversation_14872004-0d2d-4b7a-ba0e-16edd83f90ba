/**
 * TWL Category Utilities
 * Handles category filtering and mapping for CYTTE product structure
 */

// CYTTE Category Mapping Configuration
export const CYTTE_CATEGORIES = {
  'sneakers': {
    id: 'sneakers',
    name: 'Sneakers',
    displayName: 'SNEAKERS',
    slug: 'sneakers',
    cytteFolder: '1. SNEAKERS',
    icon: '👟',
    description: 'Sneakers de lujo de las mejores marcas del mundo',
    keywords: ['sneakers', 'tennis', 'deportivos', 'casual', 'nike', 'adidas'],
    color: 'from-blue-500 to-blue-600'
  },
  'sandals': {
    id: 'sandals',
    name: 'Sandals', 
    displayName: 'SANDALIAS',
    slug: 'sandals',
    cytteFolder: '2. SANDALS',
    icon: '🩴',
    description: 'Sandalias premium para cada ocasión',
    keywords: ['sandalias', 'slides', 'chanclas', 'verano', 'playa'],
    color: 'from-orange-500 to-orange-600'
  },
  'formal': {
    id: 'formal',
    name: 'Formal',
    displayName: 'FORMAL',
    slug: 'formal',
    cytteFolder: '3. FORMAL',
    icon: '👔',
    description: 'Calzado formal de alta gama',
    keywords: ['formal', 'elegante', 'oficina', 'vestir', 'oxford', 'loafer'],
    color: 'from-gray-700 to-gray-800'
  },
  'casual': {
    id: 'casual',
    name: 'Casual',
    displayName: 'CASUAL',
    slug: 'casual',
    cytteFolder: '4. CASUAL',
    icon: '👞',
    description: 'Estilo casual con toque de lujo',
    keywords: ['casual', 'diario', 'cómodo', 'lifestyle', 'boots'],
    color: 'from-green-500 to-green-600'
  },
  'kids': {
    id: 'kids',
    name: 'Kids',
    displayName: 'NIÑOS',
    slug: 'kids',
    cytteFolder: '5. KIDS',
    icon: '👶',
    description: 'Calzado de lujo para los más pequeños',
    keywords: ['niños', 'kids', 'infantil', 'pequeños', 'children'],
    color: 'from-pink-500 to-pink-600'
  }
}

/**
 * Get category configuration by ID
 */
export const getCategoryById = (categoryId) => {
  return CYTTE_CATEGORIES[categoryId] || null
}

/**
 * Get category configuration by slug
 */
export const getCategoryBySlug = (slug) => {
  return Object.values(CYTTE_CATEGORIES).find(cat => cat.slug === slug) || null
}

/**
 * Get all categories as array
 */
export const getAllCategories = () => {
  return Object.values(CYTTE_CATEGORIES)
}

/**
 * Filter products by category
 */
export const filterProductsByCategory = (products, categoryId) => {
  if (!categoryId || categoryId === 'all') {
    return products
  }

  const category = getCategoryById(categoryId)
  if (!category) {
    console.warn(`Category not found: ${categoryId}`)
    return []
  }

  return products.filter(product => {
    // Primary matching: by product type/category
    const productCategory = product.type || product.category
    if (productCategory?.toLowerCase() === category.name.toLowerCase()) {
      return true
    }

    // Secondary matching: by CYTTE folder structure
    if (product.cytteFolder && product.cytteFolder.includes(category.cytteFolder)) {
      return true
    }

    // Tertiary matching: by keywords in product name/description
    const productText = `${product.name} ${product.description || ''}`.toLowerCase()
    return category.keywords.some(keyword => productText.includes(keyword))
  })
}

/**
 * Get category statistics from products
 */
export const getCategoryStats = (products) => {
  const stats = {}
  
  Object.keys(CYTTE_CATEGORIES).forEach(categoryId => {
    const categoryProducts = filterProductsByCategory(products, categoryId)
    const brands = [...new Set(categoryProducts.map(p => p.brand).filter(Boolean))]
    const prices = categoryProducts.map(p => p.price || 0).filter(p => p > 0)
    
    stats[categoryId] = {
      count: categoryProducts.length,
      brands: brands.length,
      brandList: brands,
      priceRange: {
        min: prices.length > 0 ? Math.min(...prices) : 0,
        max: prices.length > 0 ? Math.max(...prices) : 0,
        avg: prices.length > 0 ? Math.round(prices.reduce((a, b) => a + b, 0) / prices.length) : 0
      },
      products: categoryProducts
    }
  })
  
  return stats
}

/**
 * Search products within a category
 */
export const searchInCategory = (products, categoryId, searchTerm) => {
  const categoryProducts = filterProductsByCategory(products, categoryId)
  
  if (!searchTerm) {
    return categoryProducts
  }

  const search = searchTerm.toLowerCase()
  return categoryProducts.filter(product =>
    product.name?.toLowerCase().includes(search) ||
    product.brand?.toLowerCase().includes(search) ||
    product.description?.toLowerCase().includes(search) ||
    product.tags?.some(tag => tag.toLowerCase().includes(search))
  )
}

/**
 * Sort products within category
 */
export const sortCategoryProducts = (products, sortBy = 'newest') => {
  const sorted = [...products]
  
  switch (sortBy) {
    case 'price-low':
      return sorted.sort((a, b) => (a.price || 0) - (b.price || 0))
    case 'price-high':
      return sorted.sort((a, b) => (b.price || 0) - (a.price || 0))
    case 'name':
      return sorted.sort((a, b) => (a.name || '').localeCompare(b.name || ''))
    case 'brand':
      return sorted.sort((a, b) => (a.brand || '').localeCompare(b.brand || ''))
    case 'rating':
      return sorted.sort((a, b) => (b.rating || 0) - (a.rating || 0))
    case 'newest':
    default:
      return sorted.sort((a, b) => new Date(b.createdAt || 0) - new Date(a.createdAt || 0))
  }
}

/**
 * Get related categories based on product overlap
 */
export const getRelatedCategories = (categoryId, products) => {
  const currentCategory = getCategoryById(categoryId)
  if (!currentCategory) return []

  const currentProducts = filterProductsByCategory(products, categoryId)
  const currentBrands = new Set(currentProducts.map(p => p.brand))
  
  const related = []
  
  Object.keys(CYTTE_CATEGORIES).forEach(otherCategoryId => {
    if (otherCategoryId === categoryId) return
    
    const otherProducts = filterProductsByCategory(products, otherCategoryId)
    const otherBrands = new Set(otherProducts.map(p => p.brand))
    
    // Calculate brand overlap
    const commonBrands = [...currentBrands].filter(brand => otherBrands.has(brand))
    const overlapScore = commonBrands.length / Math.max(currentBrands.size, otherBrands.size)
    
    if (overlapScore > 0.1) { // At least 10% brand overlap
      related.push({
        category: CYTTE_CATEGORIES[otherCategoryId],
        overlapScore,
        commonBrands,
        productCount: otherProducts.length
      })
    }
  })
  
  return related.sort((a, b) => b.overlapScore - a.overlapScore)
}

/**
 * Generate category breadcrumbs
 */
export const getCategoryBreadcrumbs = (categoryId) => {
  const category = getCategoryById(categoryId)
  if (!category) return []

  return [
    { name: 'Inicio', href: '/' },
    { name: 'Tienda', href: '/shop' },
    { name: 'Categorías', href: '/categories' },
    { name: category.displayName, href: `/categories/${category.slug}`, active: true }
  ]
}

/**
 * Validate category slug
 */
export const isValidCategorySlug = (slug) => {
  return Object.values(CYTTE_CATEGORIES).some(cat => cat.slug === slug)
}

/**
 * Get category navigation items
 */
export const getCategoryNavItems = () => {
  return Object.values(CYTTE_CATEGORIES).map(category => ({
    id: category.id,
    name: category.displayName,
    href: `/categories/${category.slug}`,
    icon: category.icon,
    description: category.description
  }))
}
