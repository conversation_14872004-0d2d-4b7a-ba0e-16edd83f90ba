'use client'

import { motion } from 'framer-motion'

// Sneaker avatar options
const sneakerAvatars = {
  classic: {
    name: 'Classic',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" className="w-full h-full">
        <path
          d="M2 18h20l-2-6H4l-2 6z"
          fill="currentColor"
          className="text-lime-green"
        />
        <path
          d="M4 12h16v-2c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v2z"
          fill="currentColor"
          className="text-pure-black dark:text-pure-white"
        />
        <circle cx="7" cy="15" r="1" fill="currentColor" className="text-pure-white" />
        <circle cx="17" cy="15" r="1" fill="currentColor" className="text-pure-white" />
      </svg>
    )
  },
  luxury: {
    name: 'Luxury',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" className="w-full h-full">
        <path
          d="M2 18h20l-2-6H4l-2 6z"
          fill="currentColor"
          className="text-yellow-400"
        />
        <path
          d="M4 12h16v-2c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v2z"
          fill="currentColor"
          className="text-pure-black dark:text-pure-white"
        />
        <path
          d="M8 10h8l-1-2H9l-1 2z"
          fill="currentColor"
          className="text-yellow-400"
        />
        <circle cx="7" cy="15" r="1" fill="currentColor" className="text-pure-white" />
        <circle cx="17" cy="15" r="1" fill="currentColor" className="text-pure-white" />
      </svg>
    )
  },
  sport: {
    name: 'Sport',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" className="w-full h-full">
        <path
          d="M2 18h20l-2-6H4l-2 6z"
          fill="currentColor"
          className="text-blue-500"
        />
        <path
          d="M4 12h16v-2c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v2z"
          fill="currentColor"
          className="text-pure-white"
        />
        <path
          d="M6 10h12l-1-1H7l-1 1z"
          fill="currentColor"
          className="text-blue-500"
        />
        <circle cx="7" cy="15" r="1" fill="currentColor" className="text-pure-white" />
        <circle cx="17" cy="15" r="1" fill="currentColor" className="text-pure-white" />
        <path d="M10 14h4v1h-4z" fill="currentColor" className="text-blue-500" />
      </svg>
    )
  },
  street: {
    name: 'Street',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" className="w-full h-full">
        <path
          d="M2 18h20l-2-6H4l-2 6z"
          fill="currentColor"
          className="text-red-500"
        />
        <path
          d="M4 12h16v-2c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v2z"
          fill="currentColor"
          className="text-pure-black dark:text-pure-white"
        />
        <path
          d="M8 9h8v1H8z"
          fill="currentColor"
          className="text-red-500"
        />
        <circle cx="7" cy="15" r="1" fill="currentColor" className="text-pure-white" />
        <circle cx="17" cy="15" r="1" fill="currentColor" className="text-pure-white" />
        <path d="M9 14h6v1H9z" fill="currentColor" className="text-red-500" />
      </svg>
    )
  },
  minimal: {
    name: 'Minimal',
    icon: (
      <svg viewBox="0 0 24 24" fill="none" className="w-full h-full">
        <path
          d="M2 18h20l-2-6H4l-2 6z"
          fill="currentColor"
          className="text-gray-400"
        />
        <path
          d="M4 12h16v-2c0-1.1-.9-2-2-2H6c-1.1 0-2 .9-2 2v2z"
          fill="currentColor"
          className="text-pure-white dark:text-gray-300"
        />
        <circle cx="7" cy="15" r="1" fill="currentColor" className="text-pure-white" />
        <circle cx="17" cy="15" r="1" fill="currentColor" className="text-pure-white" />
      </svg>
    )
  }
}

export default function SneakerAvatar({ 
  type = 'classic', 
  size = 'md', 
  className = '',
  onClick,
  isSelected = false 
}) {
  const sizeClasses = {
    xs: 'w-4 h-4',
    sm: 'w-6 h-6', 
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
    xl: 'w-12 h-12'
  }

  const avatar = sneakerAvatars[type] || sneakerAvatars.classic

  return (
    <motion.div
      className={`
        ${sizeClasses[size]} 
        rounded-full 
        bg-light-gray dark:bg-neutral-700 
        border-2 
        ${isSelected ? 'border-lime-green' : 'border-transparent'} 
        flex items-center justify-center 
        cursor-pointer 
        transition-all duration-200 
        hover:border-lime-green 
        hover:scale-110
        ${className}
      `}
      onClick={onClick}
      whileHover={{ scale: 1.1 }}
      whileTap={{ scale: 0.95 }}
    >
      {avatar.icon}
    </motion.div>
  )
}

// Export avatar options for use in profile settings
export { sneakerAvatars }
