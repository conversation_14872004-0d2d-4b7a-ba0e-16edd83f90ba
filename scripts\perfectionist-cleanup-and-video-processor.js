#!/usr/bin/env node

/**
 * PERFECTIONIST CLEANUP & VIDEO PROCESSOR
 * 
 * ENTERPRISE-GRADE SOLUTION for TWL
 * 
 * Features:
 * - Removes old chaotic folder structure
 * - Processes all videos from CYTTE materials
 * - Converts videos to optimized web formats
 * - Maintains perfect directory organization
 * - Enterprise logging and verification
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

class PerfectionistCleanupProcessor {
  constructor() {
    this.cytteBasePath = path.join(process.cwd(), '--materials', 'shoes', '2. CYTTE');
    this.publicBasePath = path.join(process.cwd(), 'public', 'products');
    this.backupBasePath = path.join(process.cwd(), 'backup', 'cleanup-backup');
    
    this.stats = {
      oldFoldersRemoved: 0,
      videosFound: 0,
      videosConverted: 0,
      videosFailed: 0,
      startTime: Date.now()
    };
    
    this.logFile = null;
    this.oldFolders = [];
    this.videos = [];
  }

  async initialize() {
    console.log('🧹 PERFECTIONIST CLEANUP & VIDEO PROCESSOR');
    console.log('==========================================');
    console.log('🗂️  REMOVING OLD CHAOTIC FOLDER STRUCTURE');
    console.log('🎥 PROCESSING ALL CYTTE VIDEOS');
    console.log('🎯 MAINTAINING ENTERPRISE-GRADE ORGANIZATION');
    console.log('');
    
    // Initialize logging
    await this.initializeLogging();
    
    // Create backup
    await this.createBackup();
    
    this.log('✅ Perfectionist Cleanup Processor initialized');
  }

  async initializeLogging() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.logFile = path.join(process.cwd(), 'logs', `perfectionist-cleanup-${timestamp}.log`);
    
    await this.log('=== PERFECTIONIST CLEANUP SESSION STARTED ===');
    await this.log(`Timestamp: ${new Date().toISOString()}`);
    await this.log(`Public Path: ${this.publicBasePath}`);
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    console.log(message);
    
    if (this.logFile) {
      try {
        await fs.appendFile(this.logFile, logEntry);
      } catch (error) {
        console.error('❌ Logging error:', error.message);
      }
    }
  }

  async createBackup() {
    await this.log('📦 Creating backup before cleanup...');
    
    try {
      await fs.mkdir(this.backupBasePath, { recursive: true });
      await this.log('✅ Backup directory ready');
    } catch (error) {
      await this.log(`❌ Backup creation failed: ${error.message}`);
      throw error;
    }
  }

  async scanOldChaoticFolders() {
    await this.log('🔍 Scanning for old chaotic folders...');
    
    try {
      const items = await fs.readdir(this.publicBasePath, { withFileTypes: true });
      
      for (const item of items) {
        if (item.isDirectory()) {
          const folderName = item.name;
          
          // Check if it's NOT part of the new CYTTE structure
          if (!this.isNewCytteStructure(folderName)) {
            this.oldFolders.push({
              name: folderName,
              path: path.join(this.publicBasePath, folderName)
            });
          }
        }
      }
      
      this.stats.oldFoldersRemoved = this.oldFolders.length;
      await this.log(`📊 Found ${this.oldFolders.length} old chaotic folders to remove`);
      
    } catch (error) {
      await this.log(`❌ Error scanning old folders: ${error.message}`);
      throw error;
    }
  }

  isNewCytteStructure(folderName) {
    // New CYTTE structure starts with numbers and categories
    const cyttePatterns = [
      /^1\.\s*SNEAKERS/i,
      /^2\.\s*SANDALS/i,
      /^3\.\s*FORMAL/i,
      /^4\.\s*CASUAL/i,
      /^5\.\s*KIDS/i
    ];
    
    return cyttePatterns.some(pattern => pattern.test(folderName));
  }

  async removeOldChaoticFolders() {
    await this.log('🗑️  Removing old chaotic folders...');
    
    let removed = 0;
    let failed = 0;
    
    for (const folder of this.oldFolders) {
      try {
        // Create backup of this folder first
        const backupPath = path.join(this.backupBasePath, folder.name);
        await this.copyDirectory(folder.path, backupPath);
        
        // Remove the old folder
        await this.removeDirectory(folder.path);
        
        removed++;
        await this.log(`✅ Removed old folder: ${folder.name}`);
        
      } catch (error) {
        failed++;
        await this.log(`❌ Failed to remove: ${folder.name} - ${error.message}`);
      }
    }
    
    await this.log(`📊 Cleanup Results: ${removed} removed, ${failed} failed`);
    return { removed, failed };
  }

  async scanAllVideos() {
    await this.log('🎥 Scanning for all videos in CYTTE materials...');
    
    await this.scanVideosRecursive(this.cytteBasePath, []);
    
    this.stats.videosFound = this.videos.length;
    await this.log(`📊 Found ${this.stats.videosFound} videos to process`);
  }

  async scanVideosRecursive(dirPath, pathComponents) {
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item.name);
        
        if (item.isDirectory()) {
          await this.scanVideosRecursive(itemPath, [...pathComponents, item.name]);
        } else if (this.isVideoFile(item.name)) {
          this.videos.push({
            name: item.name,
            sourcePath: itemPath,
            pathComponents: [...pathComponents],
            targetPath: this.generateVideoTargetPath(pathComponents, item.name)
          });
        }
      }
      
    } catch (error) {
      // Directory might not be accessible
      if (error.code !== 'ENOENT') {
        await this.log(`⚠️  Error scanning ${dirPath}: ${error.message}`);
      }
    }
  }

  isVideoFile(fileName) {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    const ext = path.extname(fileName).toLowerCase();
    return videoExtensions.includes(ext);
  }

  generateVideoTargetPath(pathComponents, fileName) {
    // Convert to new CYTTE structure path
    const targetComponents = pathComponents.map(component => {
      // Apply same mapping as product reorganizer
      if (component.includes('SNEAKERS')) return '1. SNEAKERS';
      if (component.includes('SANDALS')) return '2. SANDALS';
      if (component.includes('FORMAL')) return '3. FORMAL';
      if (component.includes('CASUAL')) return '4. CASUAL';
      if (component.includes('KIDS')) return '5. KIDS';
      return component;
    });
    
    return path.join(this.publicBasePath, ...targetComponents, fileName);
  }

  async processAllVideos() {
    await this.log('🎬 Processing all videos...');
    
    let converted = 0;
    let failed = 0;
    
    for (const video of this.videos) {
      try {
        await this.processVideo(video);
        converted++;
        this.stats.videosConverted++;
        
        if (converted % 5 === 0) {
          await this.log(`📊 Progress: ${converted}/${this.videos.length} videos processed`);
        }
        
      } catch (error) {
        failed++;
        this.stats.videosFailed++;
        await this.log(`❌ Failed to process video: ${video.name} - ${error.message}`);
      }
    }
    
    await this.log(`📊 Video Processing Results: ${converted} converted, ${failed} failed`);
    return { converted, failed };
  }

  async processVideo(video) {
    // Create target directory
    await fs.mkdir(path.dirname(video.targetPath), { recursive: true });
    
    const ext = path.extname(video.name).toLowerCase();
    
    if (ext === '.mp4' || ext === '.webm') {
      // Already web-optimized, just copy
      await fs.copyFile(video.sourcePath, video.targetPath);
      await this.log(`📋 Copied optimized video: ${video.name}`);
    } else {
      // Convert to MP4 for web optimization
      const targetMp4Path = video.targetPath.replace(path.extname(video.targetPath), '.mp4');
      
      try {
        // Check if FFmpeg is available
        execSync('ffmpeg -version', { stdio: 'ignore' });
        
        // Convert video to web-optimized MP4
        const ffmpegCommand = `ffmpeg -i "${video.sourcePath}" -c:v libx264 -crf 23 -preset medium -c:a aac -b:a 128k -movflags +faststart "${targetMp4Path}"`;
        execSync(ffmpegCommand, { stdio: 'ignore' });
        
        await this.log(`🔄 Converted video: ${video.name} → ${path.basename(targetMp4Path)}`);
        
      } catch (ffmpegError) {
        // FFmpeg not available or conversion failed, copy original
        await fs.copyFile(video.sourcePath, video.targetPath);
        await this.log(`📋 FFmpeg unavailable, copied original: ${video.name}`);
      }
    }
  }

  async copyDirectory(source, target) {
    try {
      await fs.mkdir(target, { recursive: true });
      const items = await fs.readdir(source, { withFileTypes: true });
      
      for (const item of items) {
        const sourcePath = path.join(source, item.name);
        const targetPath = path.join(target, item.name);
        
        if (item.isDirectory()) {
          await this.copyDirectory(sourcePath, targetPath);
        } else {
          await fs.copyFile(sourcePath, targetPath);
        }
      }
    } catch (error) {
      throw new Error(`Failed to copy ${source} to ${target}: ${error.message}`);
    }
  }

  async removeDirectory(dirPath) {
    try {
      await fs.rm(dirPath, { recursive: true, force: true });
    } catch (error) {
      throw new Error(`Failed to remove ${dirPath}: ${error.message}`);
    }
  }

  async generateFinalReport() {
    const duration = Math.round((Date.now() - this.stats.startTime) / 1000);
    
    await this.log('\n🎉 PERFECTIONIST CLEANUP & VIDEO PROCESSING COMPLETE!');
    await this.log('====================================================');
    await this.log(`⏱️  Duration: ${Math.floor(duration / 60)}m ${duration % 60}s`);
    await this.log(`🗑️  Old folders removed: ${this.stats.oldFoldersRemoved}`);
    await this.log(`🎥 Videos found: ${this.stats.videosFound}`);
    await this.log(`🔄 Videos converted: ${this.stats.videosConverted}`);
    await this.log(`❌ Videos failed: ${this.stats.videosFailed}`);
    
    if (this.stats.videosFailed === 0 && this.stats.oldFoldersRemoved > 0) {
      await this.log('\n🎯 PERFECT SUCCESS! Enterprise-grade organization achieved!');
    }
    
    await this.log(`\n📄 Full log: ${this.logFile}`);
    await this.log(`📦 Backup: ${this.backupBasePath}`);
    
    return {
      duration,
      stats: this.stats
    };
  }
}

// Main execution
async function main() {
  const processor = new PerfectionistCleanupProcessor();
  
  try {
    await processor.initialize();
    
    // Scan and remove old chaotic folders
    await processor.scanOldChaoticFolders();
    await processor.removeOldChaoticFolders();
    
    // Scan and process all videos
    await processor.scanAllVideos();
    await processor.processAllVideos();
    
    // Generate final report
    const report = await processor.generateFinalReport();
    
    console.log('\n🎉 PERFECTIONIST CLEANUP COMPLETE!');
    console.log('Enterprise-grade organization achieved!');
    
    return report;
    
  } catch (error) {
    console.error('❌ Perfectionist cleanup failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = PerfectionistCleanupProcessor;
