'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'
import { STORAGE_KEYS } from '@/lib/constants'

export default function RecentlyViewed({ className = '', maxItems = 6 }) {
  const [recentlyViewed, setRecentlyViewed] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Load recently viewed products from localStorage
    const loadRecentlyViewed = () => {
      try {
        const stored = localStorage.getItem(STORAGE_KEYS.RECENTLY_VIEWED)
        if (stored) {
          const parsed = JSON.parse(stored)
          setRecentlyViewed(parsed.slice(0, maxItems))
        }
      } catch (error) {
        console.error('Error loading recently viewed:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadRecentlyViewed()
  }, [maxItems])

  const clearRecentlyViewed = () => {
    localStorage.removeItem(STORAGE_KEYS.RECENTLY_VIEWED)
    setRecentlyViewed([])
  }

  if (isLoading) {
    return (
      <div className={`space-y-4 ${className}`}>
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
            Vistos Recientemente
          </h2>
        </div>
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <div className="bg-warm-camel/20 rounded-lg h-32 mb-2"></div>
              <div className="bg-warm-camel/20 rounded h-4 mb-1"></div>
              <div className="bg-warm-camel/20 rounded h-3 w-2/3"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (recentlyViewed.length === 0) {
    return null
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`space-y-6 ${className}`}
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
          Vistos Recientemente
        </h2>
        <button
          onClick={clearRecentlyViewed}
          className="text-sm text-warm-camel hover:text-rich-gold transition-colors"
        >
          Limpiar historial
        </button>
      </div>

      {/* Products Grid */}
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {recentlyViewed.map((product, index) => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <TransitionLink href={`/product/${product.id}`}>
              <Card variant="default" className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
                <CardContent className="p-3">
                  
                  {/* Product Image */}
                  <div className="relative mb-3">
                    <div className="aspect-square bg-warm-camel/10 rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                      <span className="text-3xl">👟</span>
                    </div>
                    
                    {/* Quick View Badge */}
                    <div className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="w-6 h-6 bg-rich-gold rounded-full flex items-center justify-center">
                        <svg className="w-3 h-3 text-forest-emerald" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Product Info */}
                  <div className="space-y-1">
                    <h3 className="font-medium text-forest-emerald dark:text-light-cloud-gray text-sm line-clamp-2 group-hover:text-rich-gold transition-colors">
                      {product.name}
                    </h3>
                    
                    <p className="text-warm-camel text-xs">
                      {product.brand}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div>
                        <span className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm">
                          ${product.price?.toLocaleString()} MXN
                        </span>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <div className="text-xs text-warm-camel line-through">
                            ${product.originalPrice.toLocaleString()}
                          </div>
                        )}
                      </div>
                      
                      {product.rating && (
                        <div className="flex items-center gap-1">
                          <svg className="w-3 h-3 text-yellow-400 fill-current" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                          </svg>
                          <span className="text-xs text-warm-camel">
                            {product.rating}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TransitionLink>
          </motion.div>
        ))}
      </div>

      {/* View All Link */}
      {recentlyViewed.length >= maxItems && (
        <div className="text-center">
          <TransitionLink href="/shop">
            <AnimatedButton
              variant="secondary"
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              }
              iconPosition="right"
            >
              Ver Más Productos
            </AnimatedButton>
          </TransitionLink>
        </div>
      )}
    </motion.div>
  )
}

// Helper function to add product to recently viewed
export const addToRecentlyViewed = (product) => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.RECENTLY_VIEWED)
    let recentlyViewed = stored ? JSON.parse(stored) : []
    
    // Remove if already exists
    recentlyViewed = recentlyViewed.filter(item => item.id !== product.id)
    
    // Add to beginning
    recentlyViewed.unshift({
      id: product.id,
      name: product.name,
      brand: product.brand,
      price: product.price,
      originalPrice: product.originalPrice,
      rating: product.rating,
      image: product.images?.[0],
      viewedAt: new Date().toISOString()
    })
    
    // Keep only last 20 items
    recentlyViewed = recentlyViewed.slice(0, 20)
    
    localStorage.setItem(STORAGE_KEYS.RECENTLY_VIEWED, JSON.stringify(recentlyViewed))
  } catch (error) {
    console.error('Error saving to recently viewed:', error)
  }
}

// Helper function to get recently viewed products
export const getRecentlyViewed = () => {
  try {
    const stored = localStorage.getItem(STORAGE_KEYS.RECENTLY_VIEWED)
    return stored ? JSON.parse(stored) : []
  } catch (error) {
    console.error('Error getting recently viewed:', error)
    return []
  }
}
