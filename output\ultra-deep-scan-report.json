{"scanCompletedAt": "2025-06-15T16:17:55.191Z", "scanDuration": 1431, "statistics": {"foldersScanned": 656, "productsFound": 493, "imagesConverted": 9276, "jpgFilesRemoved": 2, "errors": 2}, "breakdown": {"byStyle": {"sneakers": 400, "sandals": 55, "formal": 2, "casual": 34, "kids": 2}, "byGender": {"MIXTE": 429, "MEN": 16, "WOMEN": 48}, "byBrand": {"1. NIKE Limited Edition": 222, "10. OFF WHITE": 2, "11. GIVENCHY": 7, "12. Maison MARGIELA": 4, "13. VALENTINO": 6, "14. PRADA": 8, "15. MIU MIU": 9, "16. BOTTEGA VENETA": 2, "17. BURBERRY": 4, "18. GOLDEN GOOSE": 5, "19. GAMA NORMAL": 4, "2. ADIDAS Limited Edition": 43, "3. HERMES": 2, "4. GUCCI": 25, "5. DIOR": 9, "6. LV": 14, "7. BALENCIAGA": 16, "8. CHANEL": 16, "9. LOUBOUTIN": 1, "Common Project": 1, "1. NIKE Collabs": 4, "10. MIU MIU": 3, "11. PRADA": 1, "12. HERMES": 1, "13. CROCS": 3, "14. BOTTEGA VENETA": 1, "15. BIRKENSTOCK": 2, "2. GUCCI": 5, "3. DIOR": 3, "4. LV": 11, "5. BALENCIAGA": 9, "6. CHANEL": 8, "7. MAISON MARGIELA": 1, "8. GIVENCHY": 1, "9. UGG": 2, "1. CHANEL": 1, "3. GUCCI": 1, "1. UGG": 27, "2. LV": 1, "3. MIU MIU": 2, "4. PRADA": 2, "5. BOTTEGA VENETA": 1, "6. GUCCI": 1, "7. Adidas": 1, "2. GOLDEN GOOSE": 1}}, "errors": [{"path": "C:\\2.MY_APP\\TWL\\V2\\--materials\\shoes\\2. CYTTE\\1. SNEAKERS\\15. MIU MIU\\2. WOMEN\\13453824583007 -- Tyre\\o_1ilka1gshvvr1c93qos1r0n1kd545.jpg", "error": "Image conversion failed: VipsJpeg: premature end of JPEG image", "timestamp": "2025-06-15T16:03:41.765Z"}, {"path": "C:\\2.MY_APP\\TWL\\V2\\--materials\\shoes\\2. CYTTE\\1. SNEAKERS\\8. CHANEL\\1. WOMEN\\13451055395007\\o_1i7aapb281klc1cq7vin11u816ta12.jpg", "error": "Image conversion failed: VipsJpeg: premature end of JPEG image", "timestamp": "2025-06-15T16:11:57.577Z"}], "nextSteps": ["Product database has been updated with all found products", "All images have been converted to WebP format", "Old JPG files have been removed from public folder", "Documentation has been updated", "Ready for production deployment"]}