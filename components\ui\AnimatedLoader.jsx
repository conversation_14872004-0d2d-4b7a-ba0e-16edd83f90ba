'use client'

import { motion } from 'framer-motion'

export default function AnimatedLoader({ 
  variant = 'default', 
  size = 'md',
  text = 'Cargando...',
  className = '' 
}) {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-10 h-10',
    lg: 'w-16 h-16',
    xl: 'w-24 h-24'
  }

  const variants = {
    // Elegant spinning loader with natural colors
    default: (
      <div className={`relative ${sizeClasses[size]} ${className}`}>
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-warm-camel/30"
          animate={{ rotate: 360 }}
          transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute inset-0 rounded-full border-2 border-transparent border-t-rich-gold"
          animate={{ rotate: 360 }}
          transition={{ duration: 1.5, repeat: Infinity, ease: "linear" }}
        />
        <motion.div
          className="absolute inset-2 rounded-full bg-gradient-natural opacity-20"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [0.2, 0.4, 0.2]
          }}
          transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
        />
      </div>
    ),

    // Morphing geometric shapes
    morph: (
      <div className={`relative ${sizeClasses[size]} ${className}`}>
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute inset-0 ${
              i === 0 ? 'bg-rich-gold/40' :
              i === 1 ? 'bg-warm-camel/40' :
              'bg-forest-emerald/40'
            }`}
            animate={{
              borderRadius: [
                "50%", "25%", "10%", "25%", "50%"
              ],
              rotate: [0, 90, 180, 270, 360],
              scale: [1, 0.8, 1.2, 0.9, 1]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              delay: i * 0.3,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    ),

    // Floating particles
    particles: (
      <div className={`relative ${sizeClasses[size]} ${className}`}>
        {[...Array(6)].map((_, i) => (
          <motion.div
            key={i}
            className={`absolute w-2 h-2 rounded-full ${
              i % 3 === 0 ? 'bg-rich-gold' :
              i % 3 === 1 ? 'bg-warm-camel' :
              'bg-forest-emerald'
            }`}
            style={{
              left: `${20 + (i * 10)}%`,
              top: `${20 + (i * 10)}%`
            }}
            animate={{
              y: [0, -20, 0],
              x: [0, Math.sin(i) * 10, 0],
              opacity: [0, 1, 0],
              scale: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    ),

    // Breathing pulse
    pulse: (
      <div className={`relative ${sizeClasses[size]} ${className}`}>
        <motion.div
          className="absolute inset-0 rounded-full bg-gradient-natural"
          animate={{
            scale: [1, 1.3, 1],
            opacity: [0.6, 0.2, 0.6]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute inset-2 rounded-full bg-rich-gold"
          animate={{
            scale: [1, 1.2, 1],
            opacity: [0.8, 0.4, 0.8]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 0.3,
            ease: "easeInOut"
          }}
        />
        <motion.div
          className="absolute inset-4 rounded-full bg-forest-emerald"
          animate={{
            scale: [1, 1.1, 1],
            opacity: [1, 0.6, 1]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            delay: 0.6,
            ease: "easeInOut"
          }}
        />
      </div>
    ),

    // Cascading bars
    bars: (
      <div className={`flex items-center gap-1 ${className}`}>
        {[...Array(5)].map((_, i) => (
          <motion.div
            key={i}
            className={`w-1 bg-gradient-to-t ${
              i % 3 === 0 ? 'from-rich-gold to-warm-camel' :
              i % 3 === 1 ? 'from-warm-camel to-forest-emerald' :
              'from-forest-emerald to-rich-gold'
            } rounded-full`}
            style={{ height: size === 'sm' ? '16px' : size === 'md' ? '24px' : '32px' }}
            animate={{
              scaleY: [0.3, 1, 0.3],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1.2,
              repeat: Infinity,
              delay: i * 0.1,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    ),

    // Elegant dots
    dots: (
      <div className={`flex items-center gap-2 ${className}`}>
        {[...Array(3)].map((_, i) => (
          <motion.div
            key={i}
            className={`w-3 h-3 rounded-full ${
              i === 0 ? 'bg-rich-gold' :
              i === 1 ? 'bg-warm-camel' :
              'bg-forest-emerald'
            }`}
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              delay: i * 0.2,
              ease: "easeInOut"
            }}
          />
        ))}
      </div>
    )
  }

  return (
    <div className="flex flex-col items-center gap-4">
      {variants[variant]}
      
      {text && (
        <motion.p
          className="text-sm text-forest-emerald dark:text-light-cloud-gray font-medium"
          animate={{
            opacity: [0.5, 1, 0.5]
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          {text}
        </motion.p>
      )}
    </div>
  )
}

// Full page loader component
export function FullPageLoader({ text = 'Cargando The White Laces...' }) {
  return (
    <motion.div
      className="fixed inset-0 bg-light-cloud-gray/80 dark:bg-deep-pine/80 backdrop-blur-sm flex items-center justify-center z-50"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      transition={{ duration: 0.3 }}
    >
      <motion.div
        className="glass-card p-8 rounded-2xl flex flex-col items-center gap-6"
        initial={{ scale: 0.8, y: 20 }}
        animate={{ scale: 1, y: 0 }}
        transition={{ duration: 0.5, type: "spring", stiffness: 200 }}
      >
        {/* Logo */}
        <motion.div
          className="w-16 h-16 bg-gradient-natural rounded-2xl flex items-center justify-center"
          animate={{
            rotate: [0, 360],
            scale: [1, 1.1, 1]
          }}
          transition={{
            rotate: { duration: 3, repeat: Infinity, ease: "linear" },
            scale: { duration: 2, repeat: Infinity, ease: "easeInOut" }
          }}
        >
          <span className="text-forest-emerald font-bold text-2xl">W</span>
        </motion.div>

        <AnimatedLoader variant="morph" size="lg" text={text} />
        
        {/* Progress bar */}
        <div className="w-48 h-1 bg-warm-camel/20 rounded-full overflow-hidden">
          <motion.div
            className="h-full bg-gradient-natural rounded-full"
            initial={{ width: "0%" }}
            animate={{ width: "100%" }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </div>
      </motion.div>
    </motion.div>
  )
}
