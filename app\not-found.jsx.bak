'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

export default function NotFound() {
  const suggestions = [
    { name: 'Inicio', href: '/', icon: '🏠' },
    { name: 'Tienda', href: '/shop', icon: '🛍️' },
    { name: 'UX Avanzada', href: '/ux-demo', icon: '✨' },
    { name: '<PERSON><PERSON>', href: '/brands', icon: '👟' },
    { name: '<PERSON><PERSON>ni<PERSON>', href: '/community', icon: '👥' },
    { name: 'Revista', href: '/magazine', icon: '📖' }
  ]

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine flex items-center justify-center px-4">
      <div className="max-w-2xl w-full text-center">
        
        {/* 404 Animation */}
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
          className="mb-8"
        >
          <motion.div
            className="text-9xl font-bold text-rich-gold mb-4"
            animate={{
              y: [0, -10, 0],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            404
          </motion.div>
          
          <motion.div
            className="text-6xl mb-6"
            animate={{
              rotate: [0, 10, -10, 0],
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            👟
          </motion.div>
        </motion.div>

        {/* Error Message */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            ¡Oops! Página no encontrada
          </h1>
          <p className="text-warm-camel text-lg mb-6">
            Parece que esta página se perdió en el camino. 
            No te preocupes, te ayudamos a encontrar lo que buscas.
          </p>
        </motion.div>

        {/* Search Suggestion */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6, duration: 0.6 }}
          className="mb-8"
        >
          <Card variant="glass">
            <CardContent className="p-6">
              <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                ¿Qué estabas buscando?
              </h2>
              <div className="flex gap-3">
                <input
                  type="text"
                  placeholder="Buscar productos, marcas..."
                  className="flex-1 px-4 py-3 rounded-lg border border-warm-camel/30 bg-white/10 backdrop-blur-sm text-forest-emerald dark:text-light-cloud-gray placeholder-warm-camel/60 focus:outline-none focus:ring-2 focus:ring-rich-gold/50"
                />
                <TransitionLink href="/shop">
                  <AnimatedButton
                    variant="primary"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    }
                  >
                    Buscar
                  </AnimatedButton>
                </TransitionLink>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Quick Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9, duration: 0.6 }}
          className="mb-8"
        >
          <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
            O explora estas secciones populares:
          </h3>
          
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {suggestions.map((suggestion, index) => (
              <motion.div
                key={suggestion.name}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1 + index * 0.1, duration: 0.6 }}
              >
                <TransitionLink href={suggestion.href}>
                  <Card variant="default" className="group hover:shadow-lg transition-all duration-300 cursor-pointer">
                    <CardContent className="p-4 text-center">
                      <div className="text-3xl mb-2 group-hover:scale-110 transition-transform duration-200">
                        {suggestion.icon}
                      </div>
                      <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray group-hover:text-rich-gold transition-colors">
                        {suggestion.name}
                      </h4>
                    </CardContent>
                  </Card>
                </TransitionLink>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Back to Home */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.5, duration: 0.6 }}
          className="flex flex-col sm:flex-row gap-4 justify-center"
        >
          <TransitionLink href="/">
            <AnimatedButton
              variant="primary"
              size="lg"
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                </svg>
              }
            >
              Volver al Inicio
            </AnimatedButton>
          </TransitionLink>
          
          <AnimatedButton
            variant="secondary"
            size="lg"
            onClick={() => window.history.back()}
            icon={
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
              </svg>
            }
          >
            Página Anterior
          </AnimatedButton>
        </motion.div>

        {/* Fun Facts */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 2, duration: 0.6 }}
          className="mt-12"
        >
          <Card variant="glass">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-3">
                💡 ¿Sabías que...?
              </h3>
              <p className="text-warm-camel text-sm">
                Los primeros Air Jordan se lanzaron en 1985 y revolucionaron para siempre 
                la industria del calzado deportivo. ¡Desde entonces, la cultura sneaker 
                no ha parado de crecer!
              </p>
            </CardContent>
          </Card>
        </motion.div>

        {/* Floating Sneaker Animation */}
        <motion.div
          className="fixed top-20 right-10 text-4xl opacity-20 pointer-events-none hidden lg:block"
          animate={{
            y: [0, -20, 0],
            rotate: [0, 5, -5, 0],
          }}
          transition={{
            duration: 4,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        >
          👟
        </motion.div>

        <motion.div
          className="fixed bottom-20 left-10 text-3xl opacity-20 pointer-events-none hidden lg:block"
          animate={{
            y: [0, 15, 0],
            rotate: [0, -3, 3, 0],
          }}
          transition={{
            duration: 3,
            repeat: Infinity,
            ease: "easeInOut",
            delay: 1
          }}
        >
          👠
        </motion.div>
      </div>
    </div>
  )
}
