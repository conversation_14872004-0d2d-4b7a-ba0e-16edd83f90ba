-- TWL E-commerce Database Schema - Row Level Security Policies
-- Version: 002
-- Description: Implement comprehensive RLS policies for data security

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE supplier_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE collection_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;

-- Helper function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(
        (auth.jwt() ->> 'role')::text = 'admin' OR
        (auth.jwt() ->> 'user_role')::text = 'admin' OR
        (auth.jwt() -> 'user_metadata' ->> 'role')::text = 'admin',
        false
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check if user is staff
CREATE OR REPLACE FUNCTION is_staff()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN COALESCE(
        is_admin() OR
        (auth.jwt() ->> 'role')::text = 'staff' OR
        (auth.jwt() ->> 'user_role')::text = 'staff' OR
        (auth.jwt() -> 'user_metadata' ->> 'role')::text = 'staff',
        false
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Users table policies
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Admins can view all users" ON users
    FOR SELECT USING (is_admin());

CREATE POLICY "Admins can update all users" ON users
    FOR UPDATE USING (is_admin());

-- Brands table policies (public read, admin write)
CREATE POLICY "Anyone can view active brands" ON brands
    FOR SELECT USING (true);

CREATE POLICY "Admins can manage brands" ON brands
    FOR ALL USING (is_admin());

-- Categories table policies (public read, admin write)
CREATE POLICY "Anyone can view active categories" ON categories
    FOR SELECT USING (active = true);

CREATE POLICY "Staff can view all categories" ON categories
    FOR SELECT USING (is_staff());

CREATE POLICY "Admins can manage categories" ON categories
    FOR ALL USING (is_admin());

-- Suppliers table policies (admin only - hidden from public)
CREATE POLICY "Only admins can access suppliers" ON suppliers
    FOR ALL USING (is_admin());

-- Products table policies
CREATE POLICY "Anyone can view active products" ON products
    FOR SELECT USING (status = 'active');

CREATE POLICY "Staff can view all products" ON products
    FOR SELECT USING (is_staff());

CREATE POLICY "Admins can manage products" ON products
    FOR ALL USING (is_admin());

-- Product variants table policies
CREATE POLICY "Anyone can view active variants" ON product_variants
    FOR SELECT USING (
        active = true AND 
        EXISTS (
            SELECT 1 FROM products 
            WHERE products.id = product_variants.product_id 
            AND products.status = 'active'
        )
    );

CREATE POLICY "Staff can view all variants" ON product_variants
    FOR SELECT USING (is_staff());

CREATE POLICY "Admins can manage variants" ON product_variants
    FOR ALL USING (is_admin());

-- Supplier products table policies (admin only)
CREATE POLICY "Only admins can access supplier products" ON supplier_products
    FOR ALL USING (is_admin());

-- Collections table policies
CREATE POLICY "Anyone can view active collections" ON collections
    FOR SELECT USING (active = true);

CREATE POLICY "Staff can view all collections" ON collections
    FOR SELECT USING (is_staff());

CREATE POLICY "Admins can manage collections" ON collections
    FOR ALL USING (is_admin());

-- Collection products table policies
CREATE POLICY "Anyone can view collection products" ON collection_products
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM collections 
            WHERE collections.id = collection_products.collection_id 
            AND collections.active = true
        )
    );

CREATE POLICY "Admins can manage collection products" ON collection_products
    FOR ALL USING (is_admin());

-- Carts table policies
CREATE POLICY "Users can view own cart" ON carts
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can update own cart" ON carts
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can insert own cart" ON carts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can delete own cart" ON carts
    FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Staff can view all carts" ON carts
    FOR SELECT USING (is_staff());

-- Wishlists table policies
CREATE POLICY "Users can view own wishlists" ON wishlists
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Anyone can view public wishlists" ON wishlists
    FOR SELECT USING (is_public = true);

CREATE POLICY "Users can manage own wishlists" ON wishlists
    FOR ALL USING (auth.uid() = user_id);

CREATE POLICY "Staff can view all wishlists" ON wishlists
    FOR SELECT USING (is_staff());

-- Wishlist items table policies
CREATE POLICY "Users can view own wishlist items" ON wishlist_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM wishlists 
            WHERE wishlists.id = wishlist_items.wishlist_id 
            AND (wishlists.user_id = auth.uid() OR wishlists.is_public = true)
        )
    );

CREATE POLICY "Users can manage own wishlist items" ON wishlist_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM wishlists 
            WHERE wishlists.id = wishlist_items.wishlist_id 
            AND wishlists.user_id = auth.uid()
        )
    );

CREATE POLICY "Staff can view all wishlist items" ON wishlist_items
    FOR SELECT USING (is_staff());

-- Orders table policies
CREATE POLICY "Users can view own orders" ON orders
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can create orders" ON orders
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Staff can view all orders" ON orders
    FOR SELECT USING (is_staff());

CREATE POLICY "Staff can update orders" ON orders
    FOR UPDATE USING (is_staff());

CREATE POLICY "Admins can manage orders" ON orders
    FOR ALL USING (is_admin());

-- Order items table policies
CREATE POLICY "Users can view own order items" ON order_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE orders.id = order_items.order_id 
            AND orders.user_id = auth.uid()
        )
    );

CREATE POLICY "Staff can view all order items" ON order_items
    FOR SELECT USING (is_staff());

CREATE POLICY "Staff can manage order items" ON order_items
    FOR ALL USING (is_staff());

-- Create function to automatically create user profile
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
    INSERT INTO public.users (id, email, email_verified)
    VALUES (
        NEW.id,
        NEW.email,
        NEW.email_confirmed_at IS NOT NULL
    );
    
    -- Create default wishlist
    INSERT INTO public.wishlists (user_id, name, is_default)
    VALUES (NEW.id, 'Mi Lista de Deseos', true);
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to create user profile on auth.users insert
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_new_user();

-- Create function to handle user updates
CREATE OR REPLACE FUNCTION handle_user_update()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE public.users
    SET 
        email = NEW.email,
        email_verified = NEW.email_confirmed_at IS NOT NULL,
        updated_at = NOW()
    WHERE id = NEW.id;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update user profile on auth.users update
CREATE TRIGGER on_auth_user_updated
    AFTER UPDATE ON auth.users
    FOR EACH ROW EXECUTE FUNCTION handle_user_update();

-- Create function to clean up expired carts
CREATE OR REPLACE FUNCTION cleanup_expired_carts()
RETURNS void AS $$
BEGIN
    DELETE FROM carts WHERE expires_at < NOW();
END;
$$ LANGUAGE plpgsql;

-- Create function to update product rating
CREATE OR REPLACE FUNCTION update_product_rating()
RETURNS TRIGGER AS $$
BEGIN
    UPDATE products
    SET 
        rating = (
            SELECT COALESCE(AVG(rating), 0)
            FROM reviews
            WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
        ),
        review_count = (
            SELECT COUNT(*)
            FROM reviews
            WHERE product_id = COALESCE(NEW.product_id, OLD.product_id)
        )
    WHERE id = COALESCE(NEW.product_id, OLD.product_id);
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL TABLES IN SCHEMA public TO anon, authenticated;
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION is_admin() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION is_staff() TO anon, authenticated;
GRANT EXECUTE ON FUNCTION cleanup_expired_carts() TO authenticated;

-- Create indexes for RLS performance
CREATE INDEX idx_users_auth_id ON users(id);
CREATE INDEX idx_carts_user_id ON carts(user_id);
CREATE INDEX idx_wishlists_user_id ON wishlists(user_id);
CREATE INDEX idx_orders_user_id ON orders(user_id);
