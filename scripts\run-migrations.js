#!/usr/bin/env node

/**
 * TWL Database Migration Runner
 * Runs SQL migrations against Supabase database
 */

const fs = require('fs').promises;
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Please set NEXT_PUBLIC_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY in .env.local');
  process.exit(1);
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

class MigrationRunner {
  constructor() {
    this.migrationsPath = path.join(process.cwd(), 'database', 'migrations');
    this.seedsPath = path.join(process.cwd(), 'database', 'seeds');
  }

  async ensureMigrationsTable() {
    console.log('📋 Ensuring migrations table exists...');
    
    const { error } = await supabase.rpc('exec_sql', {
      sql: `
        CREATE TABLE IF NOT EXISTS migrations (
          id SERIAL PRIMARY KEY,
          filename VARCHAR(255) NOT NULL UNIQUE,
          executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    });

    if (error) {
      console.error('❌ Error creating migrations table:', error);
      throw error;
    }

    console.log('✅ Migrations table ready');
  }

  async getExecutedMigrations() {
    const { data, error } = await supabase
      .from('migrations')
      .select('filename')
      .order('executed_at', { ascending: true });

    if (error) {
      console.error('❌ Error fetching executed migrations:', error);
      throw error;
    }

    return data.map(row => row.filename);
  }

  async getMigrationFiles() {
    try {
      const files = await fs.readdir(this.migrationsPath);
      return files
        .filter(file => file.endsWith('.sql'))
        .sort();
    } catch (error) {
      console.error('❌ Error reading migrations directory:', error);
      throw error;
    }
  }

  async getSeedFiles() {
    try {
      const files = await fs.readdir(this.seedsPath);
      return files
        .filter(file => file.endsWith('.sql'))
        .sort();
    } catch (error) {
      console.error('❌ Error reading seeds directory:', error);
      throw error;
    }
  }

  async executeSqlFile(filePath, filename) {
    console.log(`🔄 Executing ${filename}...`);
    
    try {
      const sql = await fs.readFile(filePath, 'utf8');
      
      // Split SQL into individual statements (basic splitting)
      const statements = sql
        .split(';')
        .map(stmt => stmt.trim())
        .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

      for (const statement of statements) {
        if (statement.trim()) {
          const { error } = await supabase.rpc('exec_sql', {
            sql: statement + ';'
          });

          if (error) {
            console.error(`❌ Error executing statement in ${filename}:`, error);
            console.error('Statement:', statement.substring(0, 200) + '...');
            throw error;
          }
        }
      }

      console.log(`✅ Successfully executed ${filename}`);
      return true;
    } catch (error) {
      console.error(`❌ Error executing ${filename}:`, error);
      throw error;
    }
  }

  async recordMigration(filename) {
    const { error } = await supabase
      .from('migrations')
      .insert({ filename });

    if (error) {
      console.error('❌ Error recording migration:', error);
      throw error;
    }
  }

  async runMigrations() {
    console.log('🚀 Starting database migrations...\n');

    try {
      await this.ensureMigrationsTable();
      
      const executedMigrations = await this.getExecutedMigrations();
      const migrationFiles = await this.getMigrationFiles();
      
      const pendingMigrations = migrationFiles.filter(
        file => !executedMigrations.includes(file)
      );

      if (pendingMigrations.length === 0) {
        console.log('✅ No pending migrations found. Database is up to date.');
        return;
      }

      console.log(`📦 Found ${pendingMigrations.length} pending migration(s):`);
      pendingMigrations.forEach(file => console.log(`   - ${file}`));
      console.log('');

      for (const filename of pendingMigrations) {
        const filePath = path.join(this.migrationsPath, filename);
        await this.executeSqlFile(filePath, filename);
        await this.recordMigration(filename);
      }

      console.log('\n🎉 All migrations completed successfully!');

    } catch (error) {
      console.error('\n❌ Migration failed:', error);
      process.exit(1);
    }
  }

  async runSeeds() {
    console.log('\n🌱 Starting database seeding...\n');

    try {
      const seedFiles = await this.getSeedFiles();
      
      if (seedFiles.length === 0) {
        console.log('✅ No seed files found.');
        return;
      }

      console.log(`📦 Found ${seedFiles.length} seed file(s):`);
      seedFiles.forEach(file => console.log(`   - ${file}`));
      console.log('');

      for (const filename of seedFiles) {
        const filePath = path.join(this.seedsPath, filename);
        await this.executeSqlFile(filePath, filename);
      }

      console.log('\n🎉 All seeds completed successfully!');

    } catch (error) {
      console.error('\n❌ Seeding failed:', error);
      process.exit(1);
    }
  }

  async run() {
    const args = process.argv.slice(2);
    const command = args[0] || 'migrate';

    switch (command) {
      case 'migrate':
        await this.runMigrations();
        break;
      
      case 'seed':
        await this.runSeeds();
        break;
      
      case 'reset':
        console.log('🔄 Running full database reset...');
        await this.runMigrations();
        await this.runSeeds();
        break;
      
      case 'status':
        await this.showStatus();
        break;
      
      default:
        console.log('Usage: node scripts/run-migrations.js [command]');
        console.log('Commands:');
        console.log('  migrate  - Run pending migrations (default)');
        console.log('  seed     - Run seed files');
        console.log('  reset    - Run migrations + seeds');
        console.log('  status   - Show migration status');
        process.exit(1);
    }
  }

  async showStatus() {
    console.log('📊 Database Migration Status\n');

    try {
      await this.ensureMigrationsTable();
      
      const executedMigrations = await this.getExecutedMigrations();
      const migrationFiles = await this.getMigrationFiles();
      
      console.log(`Total migration files: ${migrationFiles.length}`);
      console.log(`Executed migrations: ${executedMigrations.length}`);
      console.log(`Pending migrations: ${migrationFiles.length - executedMigrations.length}\n`);

      if (executedMigrations.length > 0) {
        console.log('✅ Executed migrations:');
        executedMigrations.forEach(file => console.log(`   - ${file}`));
        console.log('');
      }

      const pendingMigrations = migrationFiles.filter(
        file => !executedMigrations.includes(file)
      );

      if (pendingMigrations.length > 0) {
        console.log('⏳ Pending migrations:');
        pendingMigrations.forEach(file => console.log(`   - ${file}`));
      }

    } catch (error) {
      console.error('❌ Error checking status:', error);
      process.exit(1);
    }
  }
}

// Run the migration runner
const runner = new MigrationRunner();
runner.run().catch(error => {
  console.error('❌ Migration runner failed:', error);
  process.exit(1);
});
