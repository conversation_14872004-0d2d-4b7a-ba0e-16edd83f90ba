{"timestamp": "2025-06-16T02:37:21.025Z", "overall": "EXCELLENT", "categories": {"navigation": {"score": 90, "issues": [], "recommendations": ["Consider sticky navigation for better mobile UX"], "metrics": {"totalNavLinks": 0, "touchFriendlyNavLinks": 0, "touchFriendlyPercentage": 100}}, "touchInteractions": {"score": 84, "issues": [], "recommendations": ["Increase touch target sizes for better accessibility"], "metrics": {"totalInteractiveElements": 136, "touchFriendlyElements": 114, "tooSmallElements": 22, "touchFriendlyPercentage": 83.82352941176471}}, "performance": {"score": 85, "issues": [], "recommendations": ["Optimize First Contentful Paint (<2s)"], "metrics": {"loadTime": 0, "domContentLoaded": 0.09999990463256836, "firstPaint": 776, "firstContentfulPaint": 2976, "resourceCount": 29, "totalResourceSize": 0}}, "accessibility": {"score": 90, "issues": [], "recommendations": ["3 interactive elements could use better labeling"], "metrics": {"totalImages": 97, "imagesWithoutAlt": 0, "totalHeadings": 54, "interactiveElements": 114, "elementsWithoutLabels": 3}}, "responsiveDesign": {"score": 100, "issues": [], "recommendations": [], "metrics": {"viewportsTested": 5}}, "security": {"score": 100, "issues": [], "recommendations": [], "metrics": {"protocol": "http:", "insecureResources": 0, "totalForms": 1}}, "uxStandards": {"score": 95, "issues": [], "recommendations": ["Consider adding loading states for better UX"], "metrics": {"loadingElements": 0, "errorElements": 0, "emptyStateElements": 0, "feedbackElements": 0, "textElements": 250}}}, "recommendations": ["Consider sticky navigation for better mobile UX", "Increase touch target sizes for better accessibility", "Optimize First Contentful Paint (<2s)", "3 interactive elements could use better labeling", "Consider adding loading states for better UX"], "criticalIssues": [], "summary": {"totalCategories": 7, "averageScore": 92, "criticalIssues": 0, "recommendations": 5}}