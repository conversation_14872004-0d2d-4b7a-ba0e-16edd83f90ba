'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import SectionHeader from '@/components/ui/SectionHeader'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
import AuthModal from '@/components/ui/AuthModal'
import SneakerAvatar, { sneakerAvatars } from '@/components/ui/SneakerAvatar'

export default function AccountPage() {
  const { user, isAuthenticated, logout } = useAuth()
  const { getItemsCount } = useCart()
  const { getTotalItemsCount } = useWishlist()
  const [activeTab, setActiveTab] = useState('profile')
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)

  // If not authenticated, show login prompt
  if (!isAuthenticated) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-dark-gray pt-36">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="py-16">
            <SectionHeader 
              title="MI CUENTA" 
              subtitle="Inicia sesión para acceder a tu cuenta"
              align="center"
              size="lg"
            />
          </div>

          <div className="text-center py-16">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="max-w-md mx-auto"
            >
              <div className="text-8xl mb-6">👤</div>
              <h3 className="text-2xl font-godber font-bold text-pure-black dark:text-pure-white mb-4 tracking-godber-sm">
                Accede a tu cuenta
              </h3>
              <p className="text-text-gray dark:text-neutral-400 mb-8 font-poppins">
                Inicia sesión para ver tu perfil, pedidos, wishlist y más.
              </p>
              
              <div className="space-y-4">
                <AnimatedButton
                  variant="primary"
                  size="lg"
                  className="w-full"
                  onClick={() => setIsAuthModalOpen(true)}
                >
                  Iniciar Sesión
                </AnimatedButton>

                <AnimatedButton
                  variant="outline"
                  size="lg"
                  className="w-full"
                  onClick={() => setIsAuthModalOpen(true)}
                >
                  Crear Cuenta
                </AnimatedButton>
              </div>
            </motion.div>
          </div>
        </div>
      </div>
    )
  }

  const tabs = [
    { id: 'profile', name: 'Perfil', icon: '👤' },
    { id: 'orders', name: 'Pedidos', icon: '📦' },
    { id: 'wishlist', name: 'Wishlist', icon: '💖' },
    { id: 'settings', name: 'Configuración', icon: '⚙️' }
  ]

  const recentOrders = [
    { id: 1, date: '2024-01-15', total: 3490, status: 'Entregado', items: 2 },
    { id: 2, date: '2024-01-10', total: 5200, status: 'En tránsito', items: 1 },
    { id: 3, date: '2024-01-05', total: 2800, status: 'Procesando', items: 3 }
  ]

  return (
    <div className="min-h-screen bg-pure-white dark:bg-dark-gray pt-36">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Page Header */}
        <div className="py-16">
          <SectionHeader 
            title="MI CUENTA" 
            subtitle={`Bienvenido de vuelta, ${user?.firstName || 'Usuario'}`}
            align="center"
            size="lg"
          />
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8 mb-16">
          {/* Sidebar Navigation */}
          <div className="lg:col-span-1">
            <Card variant="default" className="sticky top-24">
              <CardContent className="p-6">
                {/* User Info */}
                <div className="text-center mb-6 pb-6 border-b border-gray-200 dark:border-neutral-700">
                  <div className="mx-auto mb-3">
                    <SneakerAvatar
                      type={user?.avatar || 'classic'}
                      size="xl"
                      className="border-2 border-lime-green"
                    />
                  </div>
                  <h3 className="font-godber font-bold text-pure-black dark:text-pure-white tracking-godber-sm">
                    {user?.firstName} {user?.lastName}
                  </h3>
                  <p className="text-sm text-text-gray dark:text-neutral-400 font-poppins">
                    {user?.email}
                  </p>
                </div>

                {/* Navigation Tabs */}
                <nav className="space-y-2">
                  {tabs.map((tab) => (
                    <button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center gap-3 px-4 py-3 rounded-lg text-left transition-all duration-200 ${
                        activeTab === tab.id
                          ? 'bg-primary text-pure-black font-semibold'
                          : 'text-text-gray dark:text-neutral-400 hover:bg-gray-100 dark:hover:bg-neutral-700'
                      }`}
                    >
                      <span className="text-lg">{tab.icon}</span>
                      <span className="font-poppins">{tab.name}</span>
                    </button>
                  ))}
                </nav>

                {/* Logout Button */}
                <div className="mt-6 pt-6 border-t border-gray-200 dark:border-neutral-700">
                  <AnimatedButton
                    variant="outline"
                    size="sm"
                    className="w-full text-red-500 border-red-500 hover:bg-red-50"
                    onClick={logout}
                  >
                    Cerrar Sesión
                  </AnimatedButton>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* Profile Tab */}
            {activeTab === 'profile' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                <Card variant="default">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-godber font-bold text-pure-black dark:text-pure-white mb-6">
                      Información Personal
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label className="block text-sm font-medium text-text-gray dark:text-neutral-400 mb-2">
                          Nombre
                        </label>
                        <input
                          type="text"
                          value={user?.firstName || ''}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-neutral-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-neutral-800 text-pure-black dark:text-pure-white"
                          readOnly
                        />
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-text-gray dark:text-neutral-400 mb-2">
                          Apellido
                        </label>
                        <input
                          type="text"
                          value={user?.lastName || ''}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-neutral-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-neutral-800 text-pure-black dark:text-pure-white"
                          readOnly
                        />
                      </div>
                      
                      <div className="md:col-span-2">
                        <label className="block text-sm font-medium text-text-gray dark:text-neutral-400 mb-2">
                          Email
                        </label>
                        <input
                          type="email"
                          value={user?.email || ''}
                          className="w-full px-4 py-2 border border-gray-300 dark:border-neutral-600 rounded-lg focus:outline-none focus:ring-2 focus:ring-primary bg-white dark:bg-neutral-800 text-pure-black dark:text-pure-white"
                          readOnly
                        />
                      </div>
                    </div>

                    <div className="mt-6">
                      <AnimatedButton variant="primary" size="md">
                        Editar Perfil
                      </AnimatedButton>
                    </div>
                  </CardContent>
                </Card>

                {/* Avatar Selection */}
                <Card variant="default">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-godber font-bold text-pure-black dark:text-pure-white mb-6">
                      Avatar de Perfil
                    </h3>

                    <div className="flex items-center gap-6 mb-6">
                      <div className="text-center">
                        <SneakerAvatar
                          type={user?.avatar || 'classic'}
                          size="xl"
                          className="border-2 border-lime-green mb-2"
                        />
                        <p className="text-sm text-text-gray dark:text-neutral-400">
                          Avatar Actual
                        </p>
                      </div>

                      <div className="flex-1">
                        <p className="text-text-gray dark:text-neutral-400 mb-4">
                          Elige tu avatar de sneaker favorito para personalizar tu perfil
                        </p>

                        <div className="grid grid-cols-5 gap-3">
                          {Object.entries(sneakerAvatars).map(([type, avatar]) => (
                            <div key={type} className="text-center">
                              <SneakerAvatar
                                type={type}
                                size="lg"
                                isSelected={user?.avatar === type}
                                onClick={() => {
                                  // TODO: Update user avatar
                                  console.log('Selected avatar:', type)
                                }}
                                className="mb-2"
                              />
                              <p className="text-xs text-text-gray dark:text-neutral-400">
                                {avatar.name}
                              </p>
                            </div>
                          ))}
                        </div>
                      </div>
                    </div>

                    <div className="mt-6">
                      <AnimatedButton variant="primary" size="md">
                        Guardar Avatar
                      </AnimatedButton>
                    </div>
                  </CardContent>
                </Card>

                {/* Quick Stats */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <Card variant="default">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl mb-2">📦</div>
                      <div className="text-2xl font-bold text-pure-black dark:text-pure-white">
                        {recentOrders.length}
                      </div>
                      <div className="text-sm text-text-gray dark:text-neutral-400">
                        Pedidos Totales
                      </div>
                    </CardContent>
                  </Card>

                  <Card variant="default">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl mb-2">💖</div>
                      <div className="text-2xl font-bold text-pure-black dark:text-pure-white">
                        {getTotalItemsCount()}
                      </div>
                      <div className="text-sm text-text-gray dark:text-neutral-400">
                        En Wishlist
                      </div>
                    </CardContent>
                  </Card>

                  <Card variant="default">
                    <CardContent className="p-6 text-center">
                      <div className="text-3xl mb-2">🛒</div>
                      <div className="text-2xl font-bold text-pure-black dark:text-pure-white">
                        {getItemsCount()}
                      </div>
                      <div className="text-sm text-text-gray dark:text-neutral-400">
                        En Carrito
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </motion.div>
            )}

            {/* Orders Tab */}
            {activeTab === 'orders' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card variant="default">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-godber font-bold text-pure-black dark:text-pure-white mb-6">
                      Mis Pedidos
                    </h3>
                    
                    <div className="space-y-4">
                      {recentOrders.map((order) => (
                        <div
                          key={order.id}
                          className="flex items-center justify-between p-4 border border-gray-200 dark:border-neutral-700 rounded-lg hover:shadow-md transition-shadow"
                        >
                          <div>
                            <div className="font-semibold text-pure-black dark:text-pure-white">
                              Pedido #{order.id}
                            </div>
                            <div className="text-sm text-text-gray dark:text-neutral-400">
                              {order.date} • {order.items} productos
                            </div>
                          </div>
                          
                          <div className="text-right">
                            <div className="font-bold text-pure-black dark:text-pure-white">
                              ${order.total.toLocaleString()} MXN
                            </div>
                            <Badge 
                              variant={order.status === 'Entregado' ? 'success' : order.status === 'En tránsito' ? 'warning' : 'default'}
                              size="sm"
                            >
                              {order.status}
                            </Badge>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="mt-6">
                      <Link href="/orders">
                        <AnimatedButton variant="outline" size="md">
                          Ver Todos los Pedidos
                        </AnimatedButton>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Wishlist Tab */}
            {activeTab === 'wishlist' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card variant="default">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-godber font-bold text-pure-black dark:text-pure-white mb-6">
                      Mi Wishlist
                    </h3>
                    
                    <div className="text-center py-8">
                      <div className="text-6xl mb-4">💖</div>
                      <p className="text-text-gray dark:text-neutral-400 mb-6">
                        Tienes {getTotalItemsCount()} productos en tu wishlist
                      </p>
                      <Link href="/wishlist">
                        <AnimatedButton variant="primary" size="md">
                          Ver Mi Wishlist
                        </AnimatedButton>
                      </Link>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {/* Settings Tab */}
            {activeTab === 'settings' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card variant="default">
                  <CardContent className="p-6">
                    <h3 className="text-xl font-godber font-bold text-pure-black dark:text-pure-white mb-6">
                      Configuración
                    </h3>
                    
                    <div className="space-y-6">
                      <div>
                        <h4 className="font-semibold text-pure-black dark:text-pure-white mb-4">
                          Preferencias de Notificación
                        </h4>
                        <div className="space-y-3">
                          <label className="flex items-center gap-3">
                            <input type="checkbox" className="rounded" defaultChecked />
                            <span className="text-text-gray dark:text-neutral-400">
                              Notificaciones de nuevos productos
                            </span>
                          </label>
                          <label className="flex items-center gap-3">
                            <input type="checkbox" className="rounded" defaultChecked />
                            <span className="text-text-gray dark:text-neutral-400">
                              Ofertas y descuentos especiales
                            </span>
                          </label>
                          <label className="flex items-center gap-3">
                            <input type="checkbox" className="rounded" />
                            <span className="text-text-gray dark:text-neutral-400">
                              Newsletter semanal
                            </span>
                          </label>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold text-pure-black dark:text-pure-white mb-4">
                          Privacidad
                        </h4>
                        <div className="space-y-3">
                          <label className="flex items-center gap-3">
                            <input type="checkbox" className="rounded" defaultChecked />
                            <span className="text-text-gray dark:text-neutral-400">
                              Perfil público
                            </span>
                          </label>
                          <label className="flex items-center gap-3">
                            <input type="checkbox" className="rounded" />
                            <span className="text-text-gray dark:text-neutral-400">
                              Compartir actividad en redes sociales
                            </span>
                          </label>
                        </div>
                      </div>

                      <div className="pt-6">
                        <AnimatedButton variant="primary" size="md">
                          Guardar Configuración
                        </AnimatedButton>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </div>

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode="login"
      />
    </div>
  )
}
