<!DOCTYPE html>
<html>
<head>
    <title>TWL Mobile Access</title>
    <style>
        body { 
            font-family: Arial, sans-serif; 
            text-align: center; 
            padding: 20px;
            background: #f0f0f0;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            max-width: 400px;
            margin: 0 auto;
        }
        .qr-code {
            margin: 20px 0;
        }
        .url {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            font-family: monospace;
            font-size: 16px;
            margin: 15px 0;
            word-break: break-all;
        }
        .instructions {
            text-align: left;
            margin: 20px 0;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background: #e3f2fd;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 TWL Mobile Testing</h1>
        
        <div class="qr-code">
            <img src="https://api.qrserver.com/v1/create-qr-code/?size=200x200&data=http://192.168.1.59:3000" alt="QR Code for TWL Mobile Access">
        </div>
        
        <div class="url">
            http://192.168.1.59:3000
        </div>
        
        <div class="instructions">
            <h3>📱 How to Test:</h3>
            <div class="step">
                <strong>1.</strong> Make sure your mobile is on the same WiFi network
            </div>
            <div class="step">
                <strong>2.</strong> Scan the QR code above OR manually type the URL
            </div>
            <div class="step">
                <strong>3.</strong> Test touch interactions on product cards
            </div>
            <div class="step">
                <strong>4.</strong> Check haptic feedback and mobile navigation
            </div>
        </div>
        
        <h3>🧪 Mobile Features to Test:</h3>
        <ul style="text-align: left;">
            <li>Touch product cards to see second images</li>
            <li>Feel haptic vibration on touch</li>
            <li>Test larger touch targets</li>
            <li>Try bottom navigation</li>
            <li>Test category filtering</li>
            <li>Check responsive design</li>
        </ul>
    </div>
</body>
</html>
