#!/usr/bin/env node

/**
 * 🚀 MO<PERSON>LE VALIDATION TEST - QUICK MOBILE OPTIMIZATION VERIFICATION
 * 
 * Simple validation test to verify mobile optimizations are working
 * without complex interactions that might fail
 */

const puppeteer = require('puppeteer')
const fs = require('fs').promises
const path = require('path')

class MobileValidationTester {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      overall: 'PENDING',
      tests: {},
      summary: {}
    }
  }

  async runTests() {
    console.log('🚀 MOBILE VALIDATION TEST SUITE')
    console.log('===============================')
    console.log('🎯 Validating mobile-first optimizations...')
    console.log('')

    try {
      // Launch browser with mobile emulation
      const browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage'
        ]
      })

      const page = await browser.newPage()

      // Emulate mobile device (iPhone 13 Pro)
      await page.emulate({
        name: 'iPhone 13 Pro',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        viewport: {
          width: 390,
          height: 844,
          deviceScaleFactor: 3,
          isMobile: true,
          hasTouch: true,
          isLandscape: false
        }
      })

      // Test suite
      await this.testPageLoad(page)
      await this.testMobileOptimizations(page)
      await this.testResponsiveDesign(page)
      await this.testTouchOptimizations(page)
      await this.testPerformanceBasics(page)

      // Generate final results
      this.calculateResults()
      
      // Save results
      await this.saveResults()
      
      // Generate report
      this.generateReport()

      await browser.close()

      return this.results

    } catch (error) {
      console.error('❌ Mobile validation test failed:', error)
      throw error
    }
  }

  async testPageLoad(page) {
    console.log('📱 Testing page load...')
    
    const startTime = Date.now()
    
    try {
      await page.goto('http://localhost:3000', {
        waitUntil: 'networkidle0',
        timeout: 30000
      })

      const loadTime = Date.now() - startTime

      this.results.tests.pageLoad = {
        success: true,
        loadTime: loadTime,
        score: loadTime < 5000 ? 100 : loadTime < 10000 ? 75 : 50
      }

      console.log(`   ✅ Page loaded successfully in ${loadTime}ms`)
    } catch (error) {
      this.results.tests.pageLoad = {
        success: false,
        error: error.message,
        score: 0
      }
      console.log(`   ❌ Page load failed: ${error.message}`)
    }
  }

  async testMobileOptimizations(page) {
    console.log('📱 Testing mobile optimizations...')
    
    try {
      // Check if mobile-specific CSS is applied
      const mobileOptimizations = await page.evaluate(() => {
        const html = document.documentElement
        const body = document.body
        
        // Check for mobile-first styles
        const styles = window.getComputedStyle(html)
        const bodyStyles = window.getComputedStyle(body)
        
        return {
          touchAction: styles.touchAction || 'auto',
          textSizeAdjust: styles.webkitTextSizeAdjust || styles.textSizeAdjust || 'auto',
          overflowX: bodyStyles.overflowX || 'visible',
          scrollBehavior: styles.scrollBehavior || 'auto'
        }
      })

      const optimizationsWorking = 
        mobileOptimizations.touchAction.includes('manipulation') ||
        mobileOptimizations.textSizeAdjust === '100%' ||
        mobileOptimizations.overflowX === 'hidden' ||
        mobileOptimizations.scrollBehavior === 'smooth'

      this.results.tests.mobileOptimizations = {
        success: optimizationsWorking,
        details: mobileOptimizations,
        score: optimizationsWorking ? 100 : 50
      }

      console.log(`   ✅ Mobile optimizations: ${optimizationsWorking ? 'Active' : 'Partial'}`)
    } catch (error) {
      this.results.tests.mobileOptimizations = {
        success: false,
        error: error.message,
        score: 0
      }
      console.log(`   ❌ Mobile optimizations test failed: ${error.message}`)
    }
  }

  async testResponsiveDesign(page) {
    console.log('📱 Testing responsive design...')
    
    try {
      // Test different viewport sizes
      const viewports = [
        { width: 390, height: 844, name: 'iPhone 13 Pro' },
        { width: 375, height: 667, name: 'iPhone SE' },
        { width: 414, height: 896, name: 'iPhone 11 Pro Max' }
      ]

      let responsiveScore = 0
      const results = []

      for (const viewport of viewports) {
        await page.setViewport(viewport)
        await new Promise(resolve => setTimeout(resolve, 500))

        const layoutInfo = await page.evaluate(() => {
          return {
            width: window.innerWidth,
            height: window.innerHeight,
            hasHorizontalScroll: document.body.scrollWidth > window.innerWidth,
            hasVerticalScroll: document.body.scrollHeight > window.innerHeight
          }
        })

        const isResponsive = !layoutInfo.hasHorizontalScroll && layoutInfo.width === viewport.width

        if (isResponsive) responsiveScore += 33.33

        results.push({
          viewport: viewport.name,
          responsive: isResponsive,
          details: layoutInfo
        })
      }

      this.results.tests.responsiveDesign = {
        success: responsiveScore > 66,
        score: Math.round(responsiveScore),
        details: results
      }

      console.log(`   ✅ Responsive design score: ${Math.round(responsiveScore)}%`)
    } catch (error) {
      this.results.tests.responsiveDesign = {
        success: false,
        error: error.message,
        score: 0
      }
      console.log(`   ❌ Responsive design test failed: ${error.message}`)
    }
  }

  async testTouchOptimizations(page) {
    console.log('👆 Testing touch optimizations...')
    
    try {
      // Reset to mobile viewport
      await page.setViewport({ width: 390, height: 844 })

      const touchOptimizations = await page.evaluate(() => {
        // Check for touch-friendly elements
        const buttons = document.querySelectorAll('button, [role="button"], a')
        let touchFriendlyCount = 0

        buttons.forEach(button => {
          const rect = button.getBoundingClientRect()
          const styles = window.getComputedStyle(button)
          
          // Check if element meets touch target guidelines (44px minimum)
          if (rect.width >= 44 && rect.height >= 44) {
            touchFriendlyCount++
          }
        })

        return {
          totalButtons: buttons.length,
          touchFriendlyButtons: touchFriendlyCount,
          touchFriendlyPercentage: buttons.length > 0 ? (touchFriendlyCount / buttons.length) * 100 : 100
        }
      })

      const score = Math.round(touchOptimizations.touchFriendlyPercentage)

      this.results.tests.touchOptimizations = {
        success: score > 70,
        score: score,
        details: touchOptimizations
      }

      console.log(`   ✅ Touch-friendly elements: ${score}%`)
    } catch (error) {
      this.results.tests.touchOptimizations = {
        success: false,
        error: error.message,
        score: 0
      }
      console.log(`   ❌ Touch optimizations test failed: ${error.message}`)
    }
  }

  async testPerformanceBasics(page) {
    console.log('⚡ Testing performance basics...')
    
    try {
      const performanceMetrics = await page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0]
        const paint = performance.getEntriesByType('paint')
        
        return {
          domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
          loadComplete: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
          firstPaint: paint.find(entry => entry.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
          resourceCount: performance.getEntriesByType('resource').length
        }
      })

      // Calculate performance score
      let score = 100
      if (performanceMetrics.firstContentfulPaint > 3000) score -= 30
      else if (performanceMetrics.firstContentfulPaint > 2000) score -= 15
      
      if (performanceMetrics.resourceCount > 100) score -= 20
      else if (performanceMetrics.resourceCount > 50) score -= 10

      this.results.tests.performanceBasics = {
        success: score > 60,
        score: Math.max(0, score),
        details: performanceMetrics
      }

      console.log(`   ✅ Performance score: ${Math.max(0, score)}%`)
    } catch (error) {
      this.results.tests.performanceBasics = {
        success: false,
        error: error.message,
        score: 0
      }
      console.log(`   ❌ Performance test failed: ${error.message}`)
    }
  }

  calculateResults() {
    const tests = Object.values(this.results.tests)
    const successfulTests = tests.filter(test => test.success).length
    const totalTests = tests.length
    const averageScore = tests.reduce((sum, test) => sum + (test.score || 0), 0) / totalTests

    this.results.summary = {
      totalTests,
      successfulTests,
      successRate: Math.round((successfulTests / totalTests) * 100),
      averageScore: Math.round(averageScore)
    }

    if (this.results.summary.averageScore >= 90) this.results.overall = 'EXCELLENT'
    else if (this.results.summary.averageScore >= 80) this.results.overall = 'GOOD'
    else if (this.results.summary.averageScore >= 70) this.results.overall = 'FAIR'
    else this.results.overall = 'NEEDS_IMPROVEMENT'
  }

  async saveResults() {
    const reportsDir = path.join(process.cwd(), 'reports')
    
    try {
      await fs.mkdir(reportsDir, { recursive: true })
    } catch (error) {
      // Directory already exists
    }
    
    const resultsPath = path.join(reportsDir, 'mobile-validation-results.json')
    await fs.writeFile(resultsPath, JSON.stringify(this.results, null, 2))
    
    console.log(`📊 Results saved to: ${resultsPath}`)
  }

  generateReport() {
    console.log('')
    console.log('📊 MOBILE VALIDATION TEST RESULTS')
    console.log('=================================')
    console.log(`🎯 Overall Status: ${this.results.overall}`)
    console.log(`📈 Average Score: ${this.results.summary.averageScore}/100`)
    console.log(`✅ Success Rate: ${this.results.summary.successRate}% (${this.results.summary.successfulTests}/${this.results.summary.totalTests})`)
    console.log('')
    
    Object.entries(this.results.tests).forEach(([testName, result]) => {
      const status = result.success ? '✅' : '❌'
      const score = result.score !== undefined ? ` (${result.score}/100)` : ''
      console.log(`   ${status} ${testName}${score}`)
    })
    
    console.log('')
    console.log('🎉 MOBILE VALIDATION COMPLETE!')
    
    if (this.results.summary.averageScore >= 80) {
      console.log('🏆 Mobile optimizations are working excellently!')
    } else if (this.results.summary.averageScore >= 70) {
      console.log('👍 Mobile optimizations are working well!')
    } else {
      console.log('⚠️  Mobile optimizations need improvement.')
    }
  }
}

// Main execution
async function main() {
  const tester = new MobileValidationTester()
  
  try {
    const results = await tester.runTests()
    
    if (results.summary.averageScore < 70) {
      console.log('\n⚠️  Mobile validation below target.')
      process.exit(1)
    }
    
    console.log('\n🎉 Mobile validation successful!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Mobile validation failed:', error)
    process.exit(1)
  }
}

// Run the test
if (require.main === module) {
  main()
}

module.exports = MobileValidationTester
