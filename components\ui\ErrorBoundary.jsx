'use client'

import React from 'react'
import { motion } from 'framer-motion'

class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props)
    this.state = { hasError: false, error: null, errorInfo: null }
  }

  static getDerivedStateFromError(error) {
    // Update state so the next render will show the fallback UI
    return { hasError: true }
  }

  componentDidCatch(error, errorInfo) {
    // Log the error to an error reporting service
    console.error('ErrorBoundary caught an error:', error, errorInfo)
    
    this.setState({
      error: error,
      errorInfo: errorInfo
    })

    // You can also log the error to an error reporting service here
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'exception', {
        description: error.toString(),
        fatal: false
      })
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: null, errorInfo: null })
  }

  render() {
    if (this.state.hasError) {
      // Custom fallback UI
      return this.props.fallback || (
        <ErrorFallback 
          error={this.state.error}
          onRetry={this.handleRetry}
          variant={this.props.variant}
        />
      )
    }

    return this.props.children
  }
}

// Default Error Fallback Component
function ErrorFallback({ error, onRetry, variant = 'default' }) {
  const variants = {
    default: {
      container: 'min-h-[400px] bg-pure-white dark:bg-dark-gray',
      icon: '⚠️',
      title: 'Algo salió mal',
      description: 'Ha ocurrido un error inesperado. Por favor, inténtalo de nuevo.'
    },
    card: {
      container: 'min-h-[200px] bg-light-gray dark:bg-neutral-700 rounded-lg',
      icon: '😕',
      title: 'Error al cargar',
      description: 'No pudimos cargar este contenido.'
    },
    inline: {
      container: 'py-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800',
      icon: '⚠️',
      title: 'Error',
      description: 'Algo salió mal con este componente.'
    },
    page: {
      container: 'min-h-screen bg-pure-white dark:bg-dark-gray',
      icon: '🔧',
      title: 'Error de página',
      description: 'La página encontró un problema. Nuestro equipo ha sido notificado.'
    }
  }

  const config = variants[variant] || variants.default

  return (
    <div className={`flex items-center justify-center p-8 ${config.container}`}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center max-w-md mx-auto"
      >
        <motion.div
          initial={{ scale: 0 }}
          animate={{ scale: 1 }}
          transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
          className="text-6xl mb-4"
        >
          {config.icon}
        </motion.div>
        
        <h2 className="text-xl font-godber font-bold text-pure-black dark:text-pure-white mb-2 tracking-godber-sm">
          {config.title}
        </h2>
        
        <p className="text-text-gray dark:text-neutral-400 font-poppins mb-6">
          {config.description}
        </p>

        {process.env.NODE_ENV === 'development' && error && (
          <details className="mb-6 text-left">
            <summary className="cursor-pointer text-sm text-red-600 dark:text-red-400 mb-2">
              Detalles del error (desarrollo)
            </summary>
            <pre className="text-xs bg-red-100 dark:bg-red-900/30 p-3 rounded overflow-auto text-red-800 dark:text-red-300">
              {error.toString()}
            </pre>
          </details>
        )}

        <div className="flex flex-col sm:flex-row gap-3 justify-center">
          <button
            onClick={onRetry}
            className="px-6 py-3 bg-pure-white border-2 border-primary text-pure-black font-poppins font-semibold rounded-xl hover:bg-primary hover:text-pure-black transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg"
          >
            Intentar de nuevo
          </button>
          
          <button
            onClick={() => window.location.reload()}
            className="px-6 py-3 bg-pure-white border-2 border-orange-500 text-pure-black font-poppins font-semibold rounded-xl hover:bg-orange-500 hover:text-pure-black transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-lg"
          >
            Recargar página
          </button>
        </div>

        <div className="mt-6 text-xs text-text-gray dark:text-neutral-400">
          <p>Si el problema persiste, contacta a soporte:</p>
          <a 
            href="mailto:<EMAIL>" 
            className="text-primary hover:underline"
          >
            <EMAIL>
          </a>
        </div>
      </motion.div>
    </div>
  )
}

// Specialized Error Boundaries for different use cases

// Product Error Boundary
export function ProductErrorBoundary({ children }) {
  return (
    <ErrorBoundary 
      variant="card"
      fallback={
        <div className="min-h-[300px] bg-light-gray dark:bg-neutral-700 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl mb-2">📦</div>
            <p className="text-text-gray dark:text-neutral-400 font-poppins text-sm">
              Error al cargar producto
            </p>
          </div>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  )
}

// Cart Error Boundary
export function CartErrorBoundary({ children }) {
  return (
    <ErrorBoundary 
      variant="inline"
      fallback={
        <div className="p-4 bg-red-50 dark:bg-red-900/20 rounded-lg border border-red-200 dark:border-red-800 text-center">
          <div className="text-2xl mb-2">🛒</div>
          <p className="text-red-700 dark:text-red-300 font-poppins text-sm">
            Error en el carrito de compras
          </p>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  )
}

// Page Error Boundary
export function PageErrorBoundary({ children }) {
  return (
    <ErrorBoundary variant="page">
      {children}
    </ErrorBoundary>
  )
}

// Component Error Boundary (for individual components)
export function ComponentErrorBoundary({ children, componentName = 'componente' }) {
  return (
    <ErrorBoundary 
      variant="inline"
      fallback={
        <div className="p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded border border-yellow-200 dark:border-yellow-800 text-center">
          <p className="text-yellow-700 dark:text-yellow-300 font-poppins text-xs">
            Error en {componentName}
          </p>
        </div>
      }
    >
      {children}
    </ErrorBoundary>
  )
}

export default ErrorBoundary
