/**
 * 🚀 MOBILE PERFORMANCE OPTIMIZATION - THE MAESTRO'S SPEED ENGINE
 * 
 * Advanced performance optimization utilities for mobile-first experiences
 * Built for 60fps interactions and lightning-fast load times
 * 
 * Features:
 * - Intelligent resource loading
 * - Frame rate optimization
 * - Memory management
 * - Battery-aware optimizations
 * - Network-aware loading
 * - Intersection observer utilities
 * - Virtual scrolling helpers
 */

export interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  batteryLevel?: number
  networkType?: string
  loadTime: number
  renderTime: number
}

export interface OptimizationConfig {
  targetFPS: number
  memoryThreshold: number
  batteryThreshold: number
  enableLazyLoading: boolean
  enableVirtualScrolling: boolean
  enableImageOptimization: boolean
  enablePreloading: boolean
}

// Default optimization configuration
export const DEFAULT_OPTIMIZATION_CONFIG: OptimizationConfig = {
  targetFPS: 60,
  memoryThreshold: 50, // MB
  batteryThreshold: 0.2, // 20%
  enableLazyLoading: true,
  enableVirtualScrolling: true,
  enableImageOptimization: true,
  enablePreloading: true
}

/**
 * Frame Rate Monitor for smooth animations
 */
export class FrameRateMonitor {
  private frameCount = 0
  private lastTime = performance.now()
  private fps = 60
  private callbacks: ((fps: number) => void)[] = []
  private isRunning = false
  private animationId?: number

  start() {
    if (this.isRunning) return
    
    this.isRunning = true
    this.frameCount = 0
    this.lastTime = performance.now()
    this.tick()
  }

  stop() {
    this.isRunning = false
    if (this.animationId) {
      cancelAnimationFrame(this.animationId)
    }
  }

  onFPSChange(callback: (fps: number) => void) {
    this.callbacks.push(callback)
    return () => {
      const index = this.callbacks.indexOf(callback)
      if (index > -1) {
        this.callbacks.splice(index, 1)
      }
    }
  }

  getCurrentFPS(): number {
    return this.fps
  }

  private tick = () => {
    if (!this.isRunning) return

    const currentTime = performance.now()
    this.frameCount++

    // Calculate FPS every second
    if (currentTime - this.lastTime >= 1000) {
      this.fps = Math.round((this.frameCount * 1000) / (currentTime - this.lastTime))
      this.frameCount = 0
      this.lastTime = currentTime

      // Notify callbacks
      this.callbacks.forEach(callback => callback(this.fps))
    }

    this.animationId = requestAnimationFrame(this.tick)
  }
}

/**
 * Memory Monitor for efficient resource management
 */
export class MemoryMonitor {
  static getMemoryUsage(): number {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return Math.round(memory.usedJSHeapSize / 1024 / 1024) // MB
    }
    return 0
  }

  static isMemoryPressure(threshold: number = 50): boolean {
    return MemoryMonitor.getMemoryUsage() > threshold
  }

  static cleanup() {
    // Force garbage collection if available (Chrome DevTools)
    if ('gc' in window) {
      (window as any).gc()
    }
  }
}

/**
 * Battery-aware optimizations
 */
export class BatteryOptimizer {
  private static batteryAPI: any = null

  static async initialize(): Promise<void> {
    try {
      if ('getBattery' in navigator) {
        BatteryOptimizer.batteryAPI = await (navigator as any).getBattery()
      }
    } catch (error) {
      console.debug('Battery API not supported:', error)
    }
  }

  static getBatteryLevel(): number | null {
    return BatteryOptimizer.batteryAPI?.level || null
  }

  static isCharging(): boolean | null {
    return BatteryOptimizer.batteryAPI?.charging || null
  }

  static isLowBattery(threshold: number = 0.2): boolean {
    const level = BatteryOptimizer.getBatteryLevel()
    return level !== null && level < threshold
  }

  static shouldReduceAnimations(): boolean {
    return BatteryOptimizer.isLowBattery() && !BatteryOptimizer.isCharging()
  }
}

/**
 * Network-aware loading optimizations
 */
export class NetworkOptimizer {
  static getConnectionType(): string {
    const connection = (navigator as any).connection || 
                     (navigator as any).mozConnection || 
                     (navigator as any).webkitConnection
    
    return connection?.effectiveType || 'unknown'
  }

  static isSlowConnection(): boolean {
    const connectionType = NetworkOptimizer.getConnectionType()
    return ['slow-2g', '2g'].includes(connectionType)
  }

  static isFastConnection(): boolean {
    const connectionType = NetworkOptimizer.getConnectionType()
    return ['4g'].includes(connectionType)
  }

  static shouldPreloadImages(): boolean {
    return NetworkOptimizer.isFastConnection() && !BatteryOptimizer.isLowBattery()
  }

  static getOptimalImageQuality(): 'low' | 'medium' | 'high' {
    if (NetworkOptimizer.isSlowConnection() || BatteryOptimizer.isLowBattery()) {
      return 'low'
    }
    if (NetworkOptimizer.isFastConnection()) {
      return 'high'
    }
    return 'medium'
  }
}

/**
 * Intelligent Image Loader with optimization
 */
export class IntelligentImageLoader {
  private static cache = new Map<string, HTMLImageElement>()
  private static loadingQueue: string[] = []
  private static maxConcurrent = 3

  static async loadImage(
    src: string, 
    options: {
      priority?: 'high' | 'medium' | 'low'
      quality?: 'low' | 'medium' | 'high'
      lazy?: boolean
    } = {}
  ): Promise<HTMLImageElement> {
    const { priority = 'medium', quality = 'medium', lazy = true } = options

    // Check cache first
    if (IntelligentImageLoader.cache.has(src)) {
      return IntelligentImageLoader.cache.get(src)!
    }

    // Optimize source based on network conditions
    const optimizedSrc = IntelligentImageLoader.optimizeImageSrc(src, quality)

    return new Promise((resolve, reject) => {
      const img = new Image()
      
      img.onload = () => {
        IntelligentImageLoader.cache.set(src, img)
        resolve(img)
      }
      
      img.onerror = reject

      // Set loading attribute for lazy loading
      if (lazy && 'loading' in img) {
        img.loading = 'lazy'
      }

      // Set decoding for better performance
      if ('decoding' in img) {
        img.decoding = 'async'
      }

      img.src = optimizedSrc
    })
  }

  private static optimizeImageSrc(src: string, quality: 'low' | 'medium' | 'high'): string {
    // Add quality parameters based on network conditions
    const url = new URL(src, window.location.origin)
    
    switch (quality) {
      case 'low':
        url.searchParams.set('q', '60')
        url.searchParams.set('w', '400')
        break
      case 'medium':
        url.searchParams.set('q', '80')
        url.searchParams.set('w', '800')
        break
      case 'high':
        url.searchParams.set('q', '95')
        url.searchParams.set('w', '1200')
        break
    }

    return url.toString()
  }

  static preloadImages(srcs: string[], priority: 'high' | 'medium' | 'low' = 'low') {
    if (!NetworkOptimizer.shouldPreloadImages()) return

    srcs.forEach(src => {
      IntelligentImageLoader.loadImage(src, { priority, lazy: false })
    })
  }

  static clearCache() {
    IntelligentImageLoader.cache.clear()
  }
}

/**
 * Intersection Observer utilities for lazy loading
 */
export class LazyLoadManager {
  private static observers = new Map<string, IntersectionObserver>()

  static observe(
    element: Element,
    callback: (entry: IntersectionObserverEntry) => void,
    options: IntersectionObserverInit = {}
  ): () => void {
    const defaultOptions: IntersectionObserverInit = {
      rootMargin: '50px',
      threshold: 0.1,
      ...options
    }

    const key = JSON.stringify(defaultOptions)
    
    if (!LazyLoadManager.observers.has(key)) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            const callback = (entry.target as any).__lazyCallback
            if (callback) {
              callback(entry)
              observer.unobserve(entry.target)
            }
          }
        })
      }, defaultOptions)
      
      LazyLoadManager.observers.set(key, observer)
    }

    const observer = LazyLoadManager.observers.get(key)!
    ;(element as any).__lazyCallback = callback
    observer.observe(element)

    return () => {
      observer.unobserve(element)
      delete (element as any).__lazyCallback
    }
  }

  static cleanup() {
    LazyLoadManager.observers.forEach(observer => observer.disconnect())
    LazyLoadManager.observers.clear()
  }
}

/**
 * Virtual Scrolling utilities for large lists
 */
export class VirtualScrollManager {
  static calculateVisibleItems(
    containerHeight: number,
    itemHeight: number,
    scrollTop: number,
    overscan: number = 5
  ): { startIndex: number; endIndex: number; totalItems: number } {
    const visibleItemCount = Math.ceil(containerHeight / itemHeight)
    const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan)
    const endIndex = Math.min(
      startIndex + visibleItemCount + overscan * 2,
      Number.MAX_SAFE_INTEGER
    )

    return {
      startIndex,
      endIndex,
      totalItems: visibleItemCount + overscan * 2
    }
  }

  static getItemOffset(index: number, itemHeight: number): number {
    return index * itemHeight
  }
}

/**
 * Performance monitoring and optimization coordinator
 */
export class PerformanceOptimizer {
  private frameMonitor = new FrameRateMonitor()
  private config: OptimizationConfig
  private isOptimizing = false

  constructor(config: Partial<OptimizationConfig> = {}) {
    this.config = { ...DEFAULT_OPTIMIZATION_CONFIG, ...config }
  }

  async initialize(): Promise<void> {
    await BatteryOptimizer.initialize()
    
    this.frameMonitor.onFPSChange((fps) => {
      if (fps < this.config.targetFPS * 0.8) {
        this.optimizeForPerformance()
      }
    })

    this.frameMonitor.start()
  }

  private optimizeForPerformance(): void {
    if (this.isOptimizing) return
    
    this.isOptimizing = true

    // Reduce animations if performance is poor
    if (BatteryOptimizer.shouldReduceAnimations()) {
      document.documentElement.style.setProperty('--animation-duration', '0.1s')
    }

    // Clear image cache if memory pressure
    if (MemoryMonitor.isMemoryPressure(this.config.memoryThreshold)) {
      IntelligentImageLoader.clearCache()
      MemoryMonitor.cleanup()
    }

    // Reduce image quality on slow connections
    if (NetworkOptimizer.isSlowConnection()) {
      document.documentElement.style.setProperty('--image-quality', 'low')
    }

    setTimeout(() => {
      this.isOptimizing = false
    }, 1000)
  }

  getMetrics(): PerformanceMetrics {
    return {
      fps: this.frameMonitor.getCurrentFPS(),
      memoryUsage: MemoryMonitor.getMemoryUsage(),
      batteryLevel: BatteryOptimizer.getBatteryLevel(),
      networkType: NetworkOptimizer.getConnectionType(),
      loadTime: performance.timing.loadEventEnd - performance.timing.navigationStart,
      renderTime: performance.timing.domContentLoadedEventEnd - performance.timing.domLoading
    }
  }

  cleanup(): void {
    this.frameMonitor.stop()
    LazyLoadManager.cleanup()
    IntelligentImageLoader.clearCache()
  }
}

/**
 * Debounced scroll handler for better performance
 */
export function createOptimizedScrollHandler(
  callback: (scrollTop: number, scrollLeft: number) => void,
  delay: number = 16 // ~60fps
): (event: Event) => void {
  let timeoutId: NodeJS.Timeout
  let lastScrollTop = 0
  let lastScrollLeft = 0

  return (event: Event) => {
    const target = event.target as Element
    const scrollTop = target.scrollTop
    const scrollLeft = target.scrollLeft

    // Only process if scroll position actually changed
    if (scrollTop === lastScrollTop && scrollLeft === lastScrollLeft) {
      return
    }

    lastScrollTop = scrollTop
    lastScrollLeft = scrollLeft

    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      callback(scrollTop, scrollLeft)
    }, delay)
  }
}

/**
 * Throttled resize handler
 */
export function createOptimizedResizeHandler(
  callback: (width: number, height: number) => void,
  delay: number = 100
): () => void {
  let timeoutId: NodeJS.Timeout
  let lastWidth = window.innerWidth
  let lastHeight = window.innerHeight

  return () => {
    const width = window.innerWidth
    const height = window.innerHeight

    // Only process if dimensions actually changed
    if (width === lastWidth && height === lastHeight) {
      return
    }

    lastWidth = width
    lastHeight = height

    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => {
      callback(width, height)
    }, delay)
  }
}
