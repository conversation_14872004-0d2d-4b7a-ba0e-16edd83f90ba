// Product Page Functionality Audit
const fs = require('fs').promises
const path = require('path')

console.log('🔍 PRODUCT PAGE FUNCTIONALITY AUDIT')
console.log('===================================')

async function auditProductPage() {
  try {
    console.log('📋 Testing Real Product Loader...')
    
    // Test the real product loader directly
    const { loadRealProduct } = await import('../lib/real-products-loader.js')
    
    // Test cases
    const testCases = [
      'sneakers-nike-mixte-air-force-bd7700-222',
      'sneakers-nike-mixte-air-force-ao4606-001',
      'invalid-product-id'
    ]
    
    const results = []
    
    for (const testId of testCases) {
      console.log(`\n🧪 Testing product ID: ${testId}`)
      
      try {
        const startTime = Date.now()
        const product = await loadRealProduct(testId)
        const loadTime = Date.now() - startTime
        
        if (product) {
          console.log(`✅ Product loaded successfully in ${loadTime}ms`)
          console.log(`   Name: ${product.name}`)
          console.log(`   Images: ${product.images?.length || 0}`)
          console.log(`   Videos: ${product.videos?.length || 0}`)
          console.log(`   Models: ${product.models?.length || 0}`)
          
          results.push({
            id: testId,
            success: true,
            loadTime,
            product: {
              name: product.name,
              imageCount: product.images?.length || 0,
              videoCount: product.videos?.length || 0,
              modelCount: product.models?.length || 0,
              hasPrice: !!product.price,
              hasSizes: !!product.sizes,
              hasDescription: !!product.description
            }
          })
        } else {
          console.log(`❌ Product not found`)
          results.push({
            id: testId,
            success: false,
            error: 'Product not found'
          })
        }
      } catch (error) {
        console.log(`❌ Error loading product: ${error.message}`)
        results.push({
          id: testId,
          success: false,
          error: error.message
        })
      }
    }
    
    // Test file system integrity
    console.log('\n📁 Testing File System Integrity...')
    await testFileSystemIntegrity()
    
    // Test image loading
    console.log('\n🖼️ Testing Image Loading...')
    await testImageLoading()
    
    // Generate audit report
    console.log('\n📊 PRODUCT PAGE AUDIT RESULTS')
    console.log('=============================')
    
    const successfulTests = results.filter(r => r.success)
    const failedTests = results.filter(r => !r.success)
    
    console.log(`✅ Successful tests: ${successfulTests.length}/${results.length}`)
    console.log(`❌ Failed tests: ${failedTests.length}/${results.length}`)
    
    if (successfulTests.length > 0) {
      const avgLoadTime = successfulTests.reduce((sum, r) => sum + r.loadTime, 0) / successfulTests.length
      console.log(`⚡ Average load time: ${Math.round(avgLoadTime)}ms`)
      
      console.log('\n📋 Product Data Validation:')
      successfulTests.forEach(result => {
        if (result.product) {
          console.log(`   ${result.id}:`)
          console.log(`     - Images: ${result.product.imageCount}`)
          console.log(`     - Videos: ${result.product.videoCount}`)
          console.log(`     - Models: ${result.product.modelCount}`)
          console.log(`     - Has Price: ${result.product.hasPrice}`)
          console.log(`     - Has Sizes: ${result.product.hasSizes}`)
          console.log(`     - Has Description: ${result.product.hasDescription}`)
        }
      })
    }
    
    if (failedTests.length > 0) {
      console.log('\n❌ Failed Tests:')
      failedTests.forEach(result => {
        console.log(`   ${result.id}: ${result.error}`)
      })
    }
    
    // Performance benchmarks
    console.log('\n🎯 PERFORMANCE BENCHMARKS:')
    console.log('   ✅ Load time target: <2000ms')
    console.log('   ✅ Image count target: >5 per product')
    console.log('   ✅ Model variants target: ≥1 per product')
    console.log('   ✅ Data completeness target: 100%')
    
  } catch (error) {
    console.error('❌ Product page audit failed:', error.message)
  }
}

async function testFileSystemIntegrity() {
  try {
    const productsDir = path.join(process.cwd(), 'public', 'products')
    
    // Check if products directory exists
    const stats = await fs.stat(productsDir)
    console.log(`✅ Products directory exists: ${productsDir}`)
    
    // Count total files
    let totalFiles = 0
    let imageFiles = 0
    let videoFiles = 0
    
    await countFiles(productsDir, (file) => {
      totalFiles++
      const ext = path.extname(file).toLowerCase()
      if (['.webp', '.jpg', '.jpeg', '.png'].includes(ext)) {
        imageFiles++
      } else if (['.mp4', '.mov', '.avi', '.mkv'].includes(ext)) {
        videoFiles++
      }
    })
    
    console.log(`📊 File system stats:`)
    console.log(`   Total files: ${totalFiles}`)
    console.log(`   Image files: ${imageFiles}`)
    console.log(`   Video files: ${videoFiles}`)
    
  } catch (error) {
    console.log(`❌ File system integrity check failed: ${error.message}`)
  }
}

async function countFiles(dirPath, callback) {
  try {
    const items = await fs.readdir(dirPath, { withFileTypes: true })
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item.name)
      
      if (item.isDirectory()) {
        await countFiles(fullPath, callback)
      } else {
        callback(fullPath)
      }
    }
  } catch (error) {
    // Skip directories that can't be read
  }
}

async function testImageLoading() {
  try {
    // Test if specific product images exist
    const testImagePath = path.join(
      process.cwd(), 
      'public', 
      'products', 
      '1. SNEAKERS',
      '1. NIKE Limited Edition',
      '1. AIR FORCE',
      '1. MIXTE',
      '1. GUCCI',
      'BD7700-222 -- Gucci',
      'o_1hfi0lgi514331ru41hu4km31qsp47.webp'
    )
    
    const stats = await fs.stat(testImagePath)
    console.log(`✅ Test image exists: ${Math.round(stats.size / 1024)}KB`)
    
    // Test video file
    const testVideoPath = path.join(
      process.cwd(), 
      'public', 
      'products', 
      '1. SNEAKERS',
      '1. NIKE Limited Edition',
      '1. AIR FORCE',
      '1. MIXTE',
      '1. GUCCI',
      'BD7700-222 -- Gucci',
      'Video-nike-gucci-1.mp4'
    )
    
    const videoStats = await fs.stat(testVideoPath)
    console.log(`✅ Test video exists: ${Math.round(videoStats.size / (1024 * 1024) * 100) / 100}MB`)
    
  } catch (error) {
    console.log(`❌ Image loading test failed: ${error.message}`)
  }
}

// Run the audit
auditProductPage()
