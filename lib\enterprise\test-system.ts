/**
 * TWL Enterprise System Test
 * Simple test to verify the system works correctly
 */

import { initializeTWLSystem, shutdownTWLSystem } from './TWLEnterpriseSystem'

async function testEnterpriseSystem() {
  console.log('🧪 Testing TWL Enterprise System...')
  
  try {
    // Initialize system
    console.log('1. Initializing system...')
    const system = await initializeTWLSystem({
      productsBasePath: 'public/products',
      enableCache: true,
      enableAutoScan: false, // Disable auto-scan for testing
      environment: 'development',
      logLevel: 'info'
    })
    
    // Check system health
    console.log('2. Checking system health...')
    const health = system.getHealth()
    console.log('✅ System Status:', health.status)
    console.log('✅ System Ready:', system.isReady())
    
    // Test product loading (this will work even if no real products exist)
    console.log('3. Testing product loading...')
    const testProduct = await system.getProduct('test-product-id')
    console.log('✅ Product loading test:', testProduct ? 'Found' : 'Not found (expected)')
    
    // Test search functionality
    console.log('4. Testing search functionality...')
    const searchResults = await system.searchProducts('test', {}, 1, 5)
    console.log('✅ Search test:', searchResults ? 'Working' : 'Failed')
    
    // Test get all products
    console.log('5. Testing get all products...')
    const allProducts = await system.getAllProducts()
    console.log('✅ Get all products:', allProducts.length, 'products found')
    
    // Test system metrics
    console.log('6. Testing system metrics...')
    const metrics = system.getMetrics()
    console.log('✅ Metrics available:', !!metrics)
    console.log('   - Uptime:', Math.round(metrics.system.uptime / 1000), 'seconds')
    console.log('   - Environment:', metrics.system.environment)
    console.log('   - Products:', metrics.products.total)
    
    // Test API access
    console.log('7. Testing API access...')
    const api = system.getAPI()
    console.log('✅ API available:', !!api)
    
    console.log('8. Shutting down system...')
    await shutdownTWLSystem()
    console.log('✅ System shutdown complete')
    
    console.log('\n🎉 All tests passed! Enterprise system is working correctly.')
    
  } catch (error) {
    console.error('❌ Test failed:', error)
    process.exit(1)
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  testEnterpriseSystem()
}

export default testEnterpriseSystem
