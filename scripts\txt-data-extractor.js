#!/usr/bin/env node

/**
 * ULTIMATE .TXT DATA EXTRACTOR
 * 
 * Extracts precious product information from CYTTE .txt files:
 * - Supplier pricing
 * - Detailed descriptions
 * - Available sizes
 * - Article numbers
 * - Quality levels
 * - Keywords
 * 
 * Integrates this data into our enterprise database
 */

const fs = require('fs').promises;
const path = require('path');

class TxtDataExtractor {
  constructor() {
    this.materialsPath = path.join('C:', '2.MY_APP', 'TWL', 'V2', '--materials', 'shoes', '2. CYTTE');
    this.catalogPath = path.join(process.cwd(), 'database', 'product-catalog.json');
    this.extractedData = {};
    this.totalFiles = 0;
    this.processedFiles = 0;
    this.errors = [];
  }

  async extractAllTxtData() {
    console.log('🔍 STARTING ULTIMATE .TXT DATA EXTRACTION');
    console.log('==========================================');
    console.log('💎 Extracting precious product information from CYTTE .txt files');
    console.log('');

    try {
      // Find all .txt files
      const txtFiles = await this.findAllTxtFiles();
      this.totalFiles = txtFiles.length;
      
      console.log(`📄 Found ${this.totalFiles} .txt files to process`);
      console.log('');

      // Process each .txt file
      for (const txtFile of txtFiles) {
        await this.processTxtFile(txtFile);
      }

      // Load existing catalog
      const catalog = await this.loadCatalog();
      
      // Integrate extracted data
      await this.integrateDataIntoCatalog(catalog);
      
      // Save updated catalog
      await this.saveCatalog(catalog);
      
      // Generate report
      this.generateReport();
      
      return this.extractedData;
      
    } catch (error) {
      console.error('❌ Extraction failed:', error);
      throw error;
    }
  }

  async findAllTxtFiles() {
    const txtFiles = [];
    
    async function scanDirectory(dirPath) {
      try {
        const items = await fs.readdir(dirPath, { withFileTypes: true });
        
        for (const item of items) {
          const fullPath = path.join(dirPath, item.name);
          
          if (item.isDirectory()) {
            await scanDirectory(fullPath);
          } else if (item.isFile() && item.name.endsWith('.txt') && item.name !== 'Texto INTRO.txt') {
            txtFiles.push(fullPath);
          }
        }
      } catch (error) {
        // Skip directories we can't access
      }
    }
    
    await scanDirectory(this.materialsPath);
    return txtFiles;
  }

  async processTxtFile(txtFilePath) {
    try {
      this.processedFiles++;
      
      // Extract SKU from file path
      const sku = this.extractSkuFromPath(txtFilePath);
      if (!sku) {
        console.log(`⚠️  Could not extract SKU from: ${txtFilePath}`);
        return;
      }
      
      // Read file content
      const content = await fs.readFile(txtFilePath, 'utf8');
      
      // Parse content
      const extractedInfo = this.parseFileContent(content, txtFilePath);
      
      if (extractedInfo) {
        this.extractedData[sku] = extractedInfo;
        
        if (this.processedFiles % 50 === 0) {
          console.log(`📄 Processed ${this.processedFiles}/${this.totalFiles} files...`);
        }
      }
      
    } catch (error) {
      this.errors.push(`Error processing ${txtFilePath}: ${error.message}`);
    }
  }

  extractSkuFromPath(txtFilePath) {
    // Extract SKU from path patterns like:
    // .../BD7700-222 -- Gucci/Description.txt
    // .../JGD212-EJD -- GUCCI/Description.txt
    
    const pathParts = txtFilePath.split(path.sep);
    
    // Look for folder with SKU pattern
    for (let i = pathParts.length - 1; i >= 0; i--) {
      const part = pathParts[i];
      
      // Match patterns like "BD7700-222 -- Gucci" or "JGD212-EJD -- GUCCI"
      const skuMatch = part.match(/^([A-Z0-9\-]+)\s*--\s*/);
      if (skuMatch) {
        return skuMatch[1];
      }
      
      // Match patterns like "FC1688-133"
      const simpleSkuMatch = part.match(/^([A-Z0-9\-]+)$/);
      if (simpleSkuMatch && simpleSkuMatch[1].length >= 6) {
        return simpleSkuMatch[1];
      }
    }
    
    return null;
  }

  parseFileContent(content, filePath) {
    const lines = content.split('\n').map(line => line.trim()).filter(line => line);
    
    if (lines.length === 0) return null;
    
    const extractedInfo = {
      supplierCostRMB: null,
      supplierCostUSD: null,
      detailedDescription: '',
      availableSizes: [],
      articleNumber: null,
      internalId: null,
      qualityLevel: null,
      keywords: [],
      rawContent: content,
      filePath: filePath
    };
    
    for (const line of lines) {
      // Extract supplier pricing: 💰250 -- 35$ (250 RMB = 35 USD)
      const priceMatch = line.match(/💰(\d+)\s*--\s*(\d+)\$/);
      if (priceMatch) {
        extractedInfo.supplierCostRMB = parseInt(priceMatch[1]); // Cost in Chinese Yuan
        extractedInfo.supplierCostUSD = parseInt(priceMatch[2]); // Cost in USD
        continue;
      }
      
      // Extract sizes: Tamaño: 36 36,5 37,5 38...
      const sizeMatch = line.match(/Tamaño:\s*(.+)/i);
      if (sizeMatch) {
        const sizes = sizeMatch[1].split(/\s+/).map(size => {
          const numSize = parseFloat(size.replace(',', '.'));
          return isNaN(numSize) ? null : numSize;
        }).filter(size => size !== null);
        extractedInfo.availableSizes = sizes;
        continue;
      }
      
      // Extract article number: Número de artículo: BD7700-222 #13852898443005
      const articleMatch = line.match(/Número de artículo:\s*([A-Z0-9\-]+)/i);
      if (articleMatch) {
        extractedInfo.articleNumber = articleMatch[1];
        continue;
      }
      
      // Extract internal ID: ID:JGD212-EJD
      const idMatch = line.match(/ID:\s*([A-Z0-9\-]+)/i);
      if (idMatch) {
        extractedInfo.internalId = idMatch[1];
        continue;
      }
      
      // Extract quality level: Nivel corporativo ✅
      if (line.includes('Nivel corporativo') || line.includes('✅')) {
        extractedInfo.qualityLevel = 'Corporate Level';
        continue;
      }
      
      // Extract keywords (simple brand mentions)
      const keywordMatches = line.toLowerCase().match(/\b(nike|gucci|lv|dior|chanel|balenciaga|adidas|jordan|air force|supreme|off white)\b/g);
      if (keywordMatches) {
        extractedInfo.keywords.push(...keywordMatches);
      }
      
      // Collect description lines (skip price and size lines)
      if (!line.includes('💰') && 
          !line.includes('Tamaño:') && 
          !line.includes('Número de artículo:') && 
          !line.includes('ID:') &&
          line.length > 10) {
        extractedInfo.detailedDescription += line + ' ';
      }
    }
    
    // Clean up description
    extractedInfo.detailedDescription = extractedInfo.detailedDescription.trim();
    
    // Remove duplicate keywords
    extractedInfo.keywords = [...new Set(extractedInfo.keywords)];
    
    return extractedInfo;
  }

  async loadCatalog() {
    try {
      const catalogData = await fs.readFile(this.catalogPath, 'utf8');
      return JSON.parse(catalogData);
    } catch (error) {
      console.error('❌ Could not load catalog:', error);
      throw error;
    }
  }

  async integrateDataIntoCatalog(catalog) {
    console.log('🔄 Integrating extracted data into catalog...');
    
    let integratedCount = 0;
    let notFoundCount = 0;
    
    for (const [extractedSku, txtData] of Object.entries(this.extractedData)) {
      // Find matching product in catalog
      let foundProduct = null;
      
      for (const [catalogId, product] of Object.entries(catalog.products)) {
        // Match by SKU or similar identifier
        if (product.sku === extractedSku || 
            product.sku.includes(extractedSku) || 
            extractedSku.includes(product.sku) ||
            product.id === extractedSku) {
          foundProduct = product;
          break;
        }
      }
      
      if (foundProduct) {
        // Integrate the precious supplier data (BACKEND ONLY - NOT VISIBLE TO CUSTOMERS)
        foundProduct.supplierInfo = {
          costRMB: txtData.supplierCostRMB,
          costUSD: txtData.supplierCostUSD,
          currency: 'USD',
          exchangeRate: txtData.supplierCostRMB && txtData.supplierCostUSD ?
            (txtData.supplierCostRMB / txtData.supplierCostUSD).toFixed(2) : null,
          lastUpdated: new Date().toISOString(),
          confidential: true // Mark as backend-only data
        };
        
        foundProduct.detailedDescription = txtData.detailedDescription || foundProduct.description;
        
        // Update sizes with actual available sizes
        if (txtData.availableSizes.length > 0) {
          foundProduct.sizing.availableSizes = txtData.availableSizes;
          foundProduct.sizing.estimated = false;
        }
        
        foundProduct.articleNumber = txtData.articleNumber;
        foundProduct.internalId = txtData.internalId;
        foundProduct.qualityLevel = txtData.qualityLevel;
        
        // Add extracted keywords to search keywords
        if (txtData.keywords.length > 0) {
          foundProduct.searchKeywords = [...new Set([
            ...foundProduct.searchKeywords,
            ...txtData.keywords
          ])];
        }
        
        // Calculate suggested retail pricing (for internal use)
        if (txtData.supplierCostUSD) {
          const transportCost = 35; // China to Mexico transport cost
          const totalCost = txtData.supplierCostUSD + transportCost;

          foundProduct.pricingStrategy = {
            supplierCost: txtData.supplierCostUSD,
            transportCost: transportCost,
            totalCost: totalCost,
            suggestedRetail: Math.round(totalCost * 2.5), // 150% markup on total cost
            premiumRetail: Math.round(totalCost * 3.0), // 200% markup on total cost
            luxuryRetail: Math.round(totalCost * 4.0), // 300% markup on total cost
            currency: 'USD',
            markupStrategy: 'luxury_positioning_with_transport',
            profitMargin: {
              suggested: Math.round(((Math.round(totalCost * 2.5) - totalCost) / totalCost) * 100),
              premium: Math.round(((Math.round(totalCost * 3.0) - totalCost) / totalCost) * 100),
              luxury: Math.round(((Math.round(totalCost * 4.0) - totalCost) / totalCost) * 100)
            },
            lastCalculated: new Date().toISOString()
          };
        }

        // Add source information
        foundProduct.dataSource = {
          txtFile: txtData.filePath,
          extractedAt: new Date().toISOString()
        };
        
        integratedCount++;
      } else {
        notFoundCount++;
        console.log(`⚠️  No matching product found for SKU: ${extractedSku}`);
      }
    }
    
    console.log(`✅ Integrated data for ${integratedCount} products`);
    console.log(`⚠️  Could not match ${notFoundCount} extracted records`);
  }

  async saveCatalog(catalog) {
    // Update metadata
    catalog.metadata.lastUpdated = new Date().toISOString();
    catalog.metadata.txtDataExtracted = true;
    catalog.metadata.txtFilesProcessed = this.processedFiles;
    catalog.metadata.txtDataIntegrated = Object.keys(this.extractedData).length;
    
    await fs.writeFile(this.catalogPath, JSON.stringify(catalog, null, 2));
    console.log('💾 Updated catalog saved successfully');
  }

  generateReport() {
    console.log('');
    console.log('📊 ULTIMATE .TXT EXTRACTION REPORT');
    console.log('==================================');
    console.log(`📄 Total .txt files found: ${this.totalFiles}`);
    console.log(`✅ Files processed: ${this.processedFiles}`);
    console.log(`💎 Data records extracted: ${Object.keys(this.extractedData).length}`);
    console.log(`❌ Errors encountered: ${this.errors.length}`);
    
    if (this.errors.length > 0) {
      console.log('');
      console.log('⚠️  ERRORS:');
      this.errors.slice(0, 10).forEach(error => {
        console.log(`   ${error}`);
      });
      if (this.errors.length > 10) {
        console.log(`   ... and ${this.errors.length - 10} more errors`);
      }
    }
    
    // Sample extracted data
    const sampleKeys = Object.keys(this.extractedData).slice(0, 3);
    if (sampleKeys.length > 0) {
      console.log('');
      console.log('💎 SAMPLE EXTRACTED DATA:');
      sampleKeys.forEach(sku => {
        const data = this.extractedData[sku];
        console.log(`   ${sku}:`);
        console.log(`     💰 Supplier Cost: ¥${data.supplierCostRMB} = $${data.supplierCostUSD} USD`);
        console.log(`     🚚 + Transport: $35 USD (China → Mexico)`);
        console.log(`     💵 Total Cost: $${data.supplierCostUSD + 35} USD`);
        console.log(`     👟 Sizes: ${data.availableSizes.slice(0, 5).join(', ')}${data.availableSizes.length > 5 ? '...' : ''}`);
        console.log(`     📝 Description: ${data.detailedDescription.substring(0, 60)}...`);
      });
    }
    
    console.log('');
    console.log('🎉 .TXT DATA EXTRACTION COMPLETE!');
    console.log('Enterprise-grade product intelligence successfully integrated!');
  }
}

// Main execution
async function main() {
  const extractor = new TxtDataExtractor();
  
  try {
    const results = await extractor.extractAllTxtData();
    
    console.log('\n🎉 PERFECTIONIST EXTRACTION COMPLETE!');
    console.log('All precious .txt data successfully integrated into TWL database!');
    
    return results;
    
  } catch (error) {
    console.error('❌ Extraction failed:', error);
    process.exit(1);
  }
}

// Run the extractor
if (require.main === module) {
  main();
}

module.exports = TxtDataExtractor;
