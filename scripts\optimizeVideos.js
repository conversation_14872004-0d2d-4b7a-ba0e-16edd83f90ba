#!/usr/bin/env node

// 🎬 TWL ENTERPRISE VIDEO OPTIMIZATION SCRIPT
// 🎯 Optimize 573 videos across all product directories while maintaining quality

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 🎯 ENTERPRISE OPTIMIZATION SETTINGS
const OPTIMIZATION_CONFIG = {
  // Target file size reduction: 60-80%
  targetReduction: 0.7,
  
  // Video quality settings
  quality: {
    crf: 28, // Constant Rate Factor (18-28 for good quality)
    preset: 'medium', // Encoding speed vs compression efficiency
    maxBitrate: '2M', // Maximum bitrate
    bufsize: '4M' // Buffer size
  },
  
  // Output settings
  outputFormat: 'mp4',
  videoCodec: 'libx264',
  audioCodec: 'aac',
  
  // File naming
  optimizedSuffix: '_optimized',
  backupSuffix: '_original'
}

// 📊 OPTIMIZATION STATISTICS
let stats = {
  totalVideos: 0,
  optimizedVideos: 0,
  totalSizeBefore: 0,
  totalSizeAfter: 0,
  errors: [],
  skipped: []
}

// 🔍 FIND ALL VIDEO FILES IN PRODUCTS DIRECTORY
const findAllVideos = (dir) => {
  const videos = []
  
  try {
    const items = fs.readdirSync(dir, { withFileTypes: true })
    
    for (const item of items) {
      const fullPath = path.join(dir, item.name)
      
      if (item.isDirectory()) {
        // Recursively search subdirectories
        videos.push(...findAllVideos(fullPath))
      } else if (item.isFile() && isVideoFile(item.name)) {
        videos.push(fullPath)
      }
    }
  } catch (error) {
    console.error(`❌ Error reading directory ${dir}:`, error.message)
  }
  
  return videos
}

// 🎬 CHECK IF FILE IS A VIDEO
const isVideoFile = (filename) => {
  const videoExtensions = ['.mp4', '.avi', '.mov', '.mkv', '.webm', '.flv']
  const ext = path.extname(filename).toLowerCase()
  return videoExtensions.includes(ext)
}

// 📏 GET FILE SIZE IN BYTES
const getFileSize = (filePath) => {
  try {
    const stats = fs.statSync(filePath)
    return stats.size
  } catch (error) {
    return 0
  }
}

// 🎯 OPTIMIZE SINGLE VIDEO FILE
const optimizeVideo = async (inputPath) => {
  const dir = path.dirname(inputPath)
  const filename = path.basename(inputPath, path.extname(inputPath))
  const ext = path.extname(inputPath)
  
  const outputPath = path.join(dir, `${filename}${OPTIMIZATION_CONFIG.optimizedSuffix}${ext}`)
  const backupPath = path.join(dir, `${filename}${OPTIMIZATION_CONFIG.backupSuffix}${ext}`)
  
  console.log(`🎬 Optimizing: ${path.relative(process.cwd(), inputPath)}`)
  
  try {
    // Get original file size
    const originalSize = getFileSize(inputPath)
    stats.totalSizeBefore += originalSize
    
    // Skip if already optimized
    if (filename.includes(OPTIMIZATION_CONFIG.optimizedSuffix)) {
      console.log(`⏭️  Skipping already optimized file`)
      stats.skipped.push(inputPath)
      return
    }
    
    // Check if FFmpeg is available
    try {
      execSync('ffmpeg -version', { stdio: 'ignore' })
    } catch (error) {
      throw new Error('FFmpeg not found. Please install FFmpeg to use video optimization.')
    }
    
    // Build FFmpeg command for enterprise-grade optimization
    const ffmpegCommand = [
      'ffmpeg',
      '-i', `"${inputPath}"`,
      '-c:v', OPTIMIZATION_CONFIG.videoCodec,
      '-crf', OPTIMIZATION_CONFIG.quality.crf,
      '-preset', OPTIMIZATION_CONFIG.quality.preset,
      '-maxrate', OPTIMIZATION_CONFIG.quality.maxBitrate,
      '-bufsize', OPTIMIZATION_CONFIG.quality.bufsize,
      '-c:a', OPTIMIZATION_CONFIG.audioCodec,
      '-b:a', '128k',
      '-movflags', '+faststart', // Optimize for web streaming
      '-pix_fmt', 'yuv420p', // Ensure compatibility
      '-y', // Overwrite output file
      `"${outputPath}"`
    ].join(' ')
    
    console.log(`🔧 Running optimization...`)
    
    // Execute FFmpeg command
    execSync(ffmpegCommand, { stdio: 'pipe' })
    
    // Check if optimization was successful
    if (!fs.existsSync(outputPath)) {
      throw new Error('Optimization failed - output file not created')
    }
    
    const optimizedSize = getFileSize(outputPath)
    stats.totalSizeAfter += optimizedSize
    
    // Calculate size reduction
    const reduction = ((originalSize - optimizedSize) / originalSize) * 100
    
    console.log(`✅ Optimized successfully:`)
    console.log(`   Original: ${formatFileSize(originalSize)}`)
    console.log(`   Optimized: ${formatFileSize(optimizedSize)}`)
    console.log(`   Reduction: ${reduction.toFixed(1)}%`)
    
    // If optimization achieved good results, replace original
    if (reduction > 10) { // Only replace if we saved at least 10%
      // Create backup of original
      fs.renameSync(inputPath, backupPath)
      
      // Replace original with optimized version
      fs.renameSync(outputPath, inputPath)
      
      console.log(`🔄 Replaced original file (backup created)`)
      stats.optimizedVideos++
    } else {
      // Remove optimized version if savings are minimal
      fs.unlinkSync(outputPath)
      console.log(`⚠️  Minimal savings, keeping original`)
      stats.skipped.push(inputPath)
    }
    
  } catch (error) {
    console.error(`❌ Error optimizing ${inputPath}:`, error.message)
    stats.errors.push({ file: inputPath, error: error.message })
    
    // Clean up failed optimization
    if (fs.existsSync(outputPath)) {
      fs.unlinkSync(outputPath)
    }
  }
}

// 📊 FORMAT FILE SIZE FOR DISPLAY
const formatFileSize = (bytes) => {
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  if (bytes === 0) return '0 Bytes'
  const i = Math.floor(Math.log(bytes) / Math.log(1024))
  return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i]
}

// 🧹 CLEANUP OLD BACKUP FILES (OPTIONAL)
const cleanupBackups = (productsDir) => {
  console.log('\n🧹 Cleaning up old backup files...')
  
  const backupFiles = findAllVideos(productsDir).filter(file => 
    file.includes(OPTIMIZATION_CONFIG.backupSuffix)
  )
  
  let cleanedCount = 0
  for (const backupFile of backupFiles) {
    try {
      fs.unlinkSync(backupFile)
      cleanedCount++
    } catch (error) {
      console.error(`❌ Error removing backup ${backupFile}:`, error.message)
    }
  }
  
  console.log(`✅ Cleaned up ${cleanedCount} backup files`)
}

// 📈 PRINT OPTIMIZATION SUMMARY
const printSummary = () => {
  console.log('\n' + '='.repeat(60))
  console.log('🎬 TWL VIDEO OPTIMIZATION SUMMARY')
  console.log('='.repeat(60))
  console.log(`📊 Total videos found: ${stats.totalVideos}`)
  console.log(`✅ Successfully optimized: ${stats.optimizedVideos}`)
  console.log(`⏭️  Skipped: ${stats.skipped.length}`)
  console.log(`❌ Errors: ${stats.errors.length}`)
  console.log('')
  console.log(`💾 Total size before: ${formatFileSize(stats.totalSizeBefore)}`)
  console.log(`💾 Total size after: ${formatFileSize(stats.totalSizeAfter)}`)
  
  if (stats.totalSizeBefore > 0) {
    const totalReduction = ((stats.totalSizeBefore - stats.totalSizeAfter) / stats.totalSizeBefore) * 100
    const spaceSaved = stats.totalSizeBefore - stats.totalSizeAfter
    console.log(`📉 Total reduction: ${totalReduction.toFixed(1)}%`)
    console.log(`💰 Space saved: ${formatFileSize(spaceSaved)}`)
  }
  
  if (stats.errors.length > 0) {
    console.log('\n❌ ERRORS:')
    stats.errors.forEach(error => {
      console.log(`   ${error.file}: ${error.error}`)
    })
  }
  
  console.log('\n🎉 Video optimization complete!')
}

// 🚀 MAIN EXECUTION FUNCTION
const main = async () => {
  console.log('🎬 TWL ENTERPRISE VIDEO OPTIMIZATION SCRIPT')
  console.log('🎯 Optimizing videos for web performance...\n')
  
  const productsDir = path.join(process.cwd(), 'public', 'products')
  
  // Check if products directory exists
  if (!fs.existsSync(productsDir)) {
    console.error('❌ Products directory not found:', productsDir)
    process.exit(1)
  }
  
  // Find all video files
  console.log('🔍 Scanning for video files...')
  const videoFiles = findAllVideos(productsDir)
  stats.totalVideos = videoFiles.length
  
  console.log(`📊 Found ${videoFiles.length} video files`)
  
  if (videoFiles.length === 0) {
    console.log('ℹ️  No video files found to optimize')
    return
  }
  
  // Process each video file
  for (let i = 0; i < videoFiles.length; i++) {
    const videoFile = videoFiles[i]
    console.log(`\n[${i + 1}/${videoFiles.length}]`)
    await optimizeVideo(videoFile)
  }
  
  // Print summary
  printSummary()
  
  // Ask about cleanup (in a real implementation, you might want to prompt the user)
  // For now, we'll skip automatic cleanup to be safe
  console.log('\n💡 To clean up backup files later, run: node scripts/cleanupBackups.js')
}

// 🎯 COMMAND LINE INTERFACE
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 Fatal error:', error)
    process.exit(1)
  })
}

export { optimizeVideo, findAllVideos, cleanupBackups }
