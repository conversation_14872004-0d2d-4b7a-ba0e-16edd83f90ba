'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'

export default function ARTryOn({ product, onClose }) {
  const [isARSupported, setIsARSupported] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [cameraStream, setCameraStream] = useState(null)
  const [isRecording, setIsRecording] = useState(false)
  const [capturedImage, setCapturedImage] = useState(null)
  const videoRef = useRef(null)
  const canvasRef = useRef(null)

  useEffect(() => {
    checkARSupport()
    return () => {
      if (cameraStream) {
        cameraStream.getTracks().forEach(track => track.stop())
      }
    }
  }, [cameraStream])

  const checkARSupport = async () => {
    setIsLoading(true)
    
    // Check for WebXR support
    if ('xr' in navigator) {
      try {
        const isSupported = await navigator.xr.isSessionSupported('immersive-ar')
        setIsARSupported(isSupported)
      } catch (error) {
        console.log('WebXR not supported:', error)
        setIsARSupported(false)
      }
    } else {
      // Fallback to camera-based AR simulation
      setIsARSupported(true)
    }
    
    setIsLoading(false)
  }

  const startCamera = async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: 'user', // Front camera for foot AR
          width: { ideal: 1280 },
          height: { ideal: 720 }
        }
      })
      
      setCameraStream(stream)
      if (videoRef.current) {
        videoRef.current.srcObject = stream
      }
      setIsRecording(true)
    } catch (error) {
      console.error('Error accessing camera:', error)
      alert('No se pudo acceder a la cámara. Por favor, permite el acceso para usar la función AR.')
    }
  }

  const stopCamera = () => {
    if (cameraStream) {
      cameraStream.getTracks().forEach(track => track.stop())
      setCameraStream(null)
    }
    setIsRecording(false)
  }

  const capturePhoto = () => {
    if (videoRef.current && canvasRef.current) {
      const canvas = canvasRef.current
      const video = videoRef.current
      const context = canvas.getContext('2d')
      
      canvas.width = video.videoWidth
      canvas.height = video.videoHeight
      
      // Draw video frame to canvas
      context.drawImage(video, 0, 0)
      
      // Add AR overlay (simulated shoe placement)
      drawAROverlay(context, canvas.width, canvas.height)
      
      // Convert to image
      const imageData = canvas.toDataURL('image/png')
      setCapturedImage(imageData)
    }
  }

  const drawAROverlay = (context, width, height) => {
    // Simulate AR shoe overlay
    context.save()
    
    // Draw shoe outline (simplified)
    const centerX = width / 2
    const centerY = height * 0.7
    const shoeWidth = width * 0.3
    const shoeHeight = height * 0.15
    
    // Shoe shadow
    context.fillStyle = 'rgba(0, 0, 0, 0.3)'
    context.fillRect(centerX - shoeWidth/2, centerY + shoeHeight/2, shoeWidth, 10)
    
    // Shoe body
    context.fillStyle = product.name.toLowerCase().includes('white') ? '#f8f9fa' : '#2d3748'
    context.strokeStyle = '#4a5568'
    context.lineWidth = 2
    
    // Draw simplified shoe shape
    context.beginPath()
    context.ellipse(centerX, centerY, shoeWidth/2, shoeHeight/2, 0, 0, 2 * Math.PI)
    context.fill()
    context.stroke()
    
    // Add product label
    context.fillStyle = '#FFD166'
    context.font = 'bold 16px Arial'
    context.textAlign = 'center'
    context.fillText(product.name, centerX, centerY - shoeHeight/2 - 20)
    
    // Add size indicator
    context.fillStyle = '#2E4B3A'
    context.font = '14px Arial'
    context.fillText('Talla: 9 US', centerX, centerY - shoeHeight/2 - 5)
    
    context.restore()
  }

  const shareARPhoto = async () => {
    if (capturedImage && navigator.share) {
      try {
        // Convert data URL to blob
        const response = await fetch(capturedImage)
        const blob = await response.blob()
        const file = new File([blob], 'ar-try-on.png', { type: 'image/png' })
        
        await navigator.share({
          title: `Probándome ${product.name}`,
          text: `¡Mira cómo me quedan estos ${product.name} de ${product.brand}! 👟✨`,
          files: [file]
        })
      } catch (error) {
        console.error('Error sharing:', error)
        // Fallback to download
        downloadARPhoto()
      }
    } else {
      downloadARPhoto()
    }
  }

  const downloadARPhoto = () => {
    if (capturedImage) {
      const link = document.createElement('a')
      link.download = `ar-try-on-${product.name.replace(/\s+/g, '-').toLowerCase()}.png`
      link.href = capturedImage
      link.click()
    }
  }

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <Card variant="glass" className="max-w-md w-full" onClick={(e) => e.stopPropagation()}>
          <CardContent className="p-8 text-center">
            <div className="text-6xl mb-4">🔄</div>
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Verificando compatibilidad AR
            </h3>
            <p className="text-warm-camel">
              Comprobando si tu dispositivo soporta realidad aumentada...
            </p>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  if (!isARSupported) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <Card variant="glass" className="max-w-md w-full" onClick={(e) => e.stopPropagation()}>
          <CardContent className="p-8 text-center">
            <div className="text-6xl mb-4">📱</div>
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
              AR No Disponible
            </h3>
            <p className="text-warm-camel mb-6">
              Tu dispositivo no soporta realidad aumentada o no tiene cámara disponible. 
              Intenta usar un dispositivo móvil más reciente.
            </p>
            <AnimatedButton variant="primary" onClick={onClose}>
              Entendido
            </AnimatedButton>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <Card variant="glass" className="max-w-4xl w-full max-h-[90vh] overflow-hidden" onClick={(e) => e.stopPropagation()}>
        <CardContent className="p-6">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-semibold text-white mb-2">
                Pruébate con AR
              </h2>
              <p className="text-gray-300">
                {product.brand} {product.name}
              </p>
            </div>
            <AnimatedButton
              variant="ghost"
              onClick={onClose}
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              }
            />
          </div>

          {!isRecording ? (
            /* Start AR Experience */
            <div className="text-center py-12">
              <motion.div
                className="text-8xl mb-6"
                animate={{
                  scale: [1, 1.1, 1],
                  rotate: [0, 5, -5, 0]
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity
                }}
              >
                👟
              </motion.div>
              
              <h3 className="text-xl font-semibold text-white mb-4">
                ¡Pruébate estos zapatos virtualmente!
              </h3>
              
              <p className="text-gray-300 mb-8 max-w-md mx-auto">
                Usa tu cámara para ver cómo te quedan estos zapatos. 
                Coloca tu pie frente a la cámara para la mejor experiencia.
              </p>

              <div className="space-y-4">
                <AnimatedButton
                  variant="primary"
                  size="lg"
                  onClick={startCamera}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  }
                >
                  Iniciar Experiencia AR
                </AnimatedButton>

                <div className="text-xs text-gray-400 space-y-1">
                  <p>• Asegúrate de tener buena iluminación</p>
                  <p>• Coloca tu pie completamente en el encuadre</p>
                  <p>• Mantén el dispositivo estable</p>
                </div>
              </div>
            </div>
          ) : (
            /* AR Camera View */
            <div className="space-y-4">
              
              {/* Camera Feed */}
              <div className="relative bg-black rounded-lg overflow-hidden">
                <video
                  ref={videoRef}
                  autoPlay
                  playsInline
                  muted
                  className="w-full h-96 object-cover"
                />
                
                {/* AR Overlay Instructions */}
                <div className="absolute top-4 left-4 right-4">
                  <div className="bg-black/50 backdrop-blur-sm rounded-lg p-3">
                    <p className="text-white text-sm text-center">
                      📍 Coloca tu pie en el centro de la pantalla
                    </p>
                  </div>
                </div>

                {/* AR Crosshair */}
                <div className="absolute inset-0 flex items-center justify-center pointer-events-none">
                  <div className="relative">
                    <div className="w-32 h-32 border-2 border-rich-gold rounded-full opacity-50"></div>
                    <div className="absolute inset-0 flex items-center justify-center">
                      <div className="w-2 h-2 bg-rich-gold rounded-full"></div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Controls */}
              <div className="flex justify-center gap-4">
                <AnimatedButton
                  variant="primary"
                  onClick={capturePhoto}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                    </svg>
                  }
                >
                  Capturar Foto
                </AnimatedButton>

                <AnimatedButton
                  variant="secondary"
                  onClick={stopCamera}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  }
                >
                  Detener
                </AnimatedButton>
              </div>
            </div>
          )}

          {/* Captured Photo */}
          <AnimatePresence>
            {capturedImage && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className="mt-6"
              >
                <Card variant="default">
                  <CardContent className="p-4">
                    <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                      ¡Foto AR Capturada! 📸
                    </h3>
                    
                    <div className="flex flex-col md:flex-row gap-4">
                      <div className="flex-1">
                        <img
                          src={capturedImage}
                          alt="AR Try-on"
                          className="w-full rounded-lg shadow-lg"
                        />
                      </div>
                      
                      <div className="flex-1 space-y-4">
                        <div>
                          <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                            ¿Te gusta cómo te quedan?
                          </h4>
                          <p className="text-warm-camel text-sm">
                            Comparte tu foto AR en redes sociales o guárdala para comparar con otros modelos.
                          </p>
                        </div>

                        <div className="space-y-2">
                          <AnimatedButton
                            variant="primary"
                            className="w-full"
                            onClick={shareARPhoto}
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                              </svg>
                            }
                          >
                            Compartir
                          </AnimatedButton>

                          <AnimatedButton
                            variant="secondary"
                            className="w-full"
                            onClick={downloadARPhoto}
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            }
                          >
                            Descargar
                          </AnimatedButton>

                          <AnimatedButton
                            variant="ghost"
                            className="w-full"
                            onClick={() => setCapturedImage(null)}
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                            }
                          >
                            Tomar Otra
                          </AnimatedButton>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Hidden canvas for photo capture */}
          <canvas ref={canvasRef} className="hidden" />
        </CardContent>
      </Card>
    </motion.div>
  )
}
