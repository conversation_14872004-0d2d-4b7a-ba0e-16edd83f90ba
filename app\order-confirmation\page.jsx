'use client'

import { useState, useEffect } from 'react'
import { useSearchParams, useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import AnimatedLoader from '@/components/ui/AnimatedLoader'
import { 
  CheckCircleIcon, 
  TruckIcon, 
  EnvelopeIcon, 
  ShoppingBagIcon,
  CalendarIcon,
  MapPinIcon,
  CreditCardIcon
} from '@heroicons/react/24/outline'

export default function OrderConfirmationPage() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const orderNumber = searchParams.get('order')
  
  const [order, setOrder] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    if (orderNumber) {
      fetchOrder(orderNumber)
    } else {
      setError('No order number provided')
      setLoading(false)
    }
  }, [orderNumber])

  const fetchOrder = async (orderNum) => {
    try {
      setLoading(true)
      
      // For now, simulate order data since we don't have user authentication
      // In production, this would fetch from /api/orders/[id] with proper auth
      const mockOrder = {
        id: `order_${Date.now()}`,
        orderNumber: orderNum,
        status: 'confirmed',
        paymentStatus: 'paid',
        createdAt: new Date().toISOString(),
        estimatedDelivery: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        totalAmount: 2500,
        currency: 'MXN',
        shippingAddress: {
          firstName: 'Juan',
          lastName: 'Pérez',
          street: 'Av. Reforma 123',
          city: 'Ciudad de México',
          state: 'CDMX',
          zipCode: '06600',
          country: 'México'
        },
        paymentMethod: {
          type: 'card',
          last4: '4242'
        },
        items: [
          {
            id: 1,
            productName: 'Nike Air Force 1',
            size: '42',
            quantity: 1,
            unitPrice: 2500,
            totalPrice: 2500,
            product: {
              images: ['/placeholder-shoe.jpg'],
              brand: { name: 'Nike' }
            }
          }
        ]
      }
      
      setOrder(mockOrder)
    } catch (error) {
      console.error('Error fetching order:', error)
      setError('Error loading order details')
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine flex items-center justify-center">
        <AnimatedLoader />
      </div>
    )
  }

  if (error || !order) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
        <div className="max-w-2xl mx-auto px-4 sm:px-6 lg:px-8 py-12 text-center">
          <div className="text-red-500 mb-4">
            <ExclamationTriangleIcon className="h-16 w-16 mx-auto" />
          </div>
          <h1 className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Error al cargar el pedido
          </h1>
          <p className="text-warm-camel mb-6">
            {error || 'No se pudo encontrar la información del pedido'}
          </p>
          <Button onClick={() => router.push('/')}>
            Volver al inicio
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        
        {/* Success Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <div className="inline-flex items-center justify-center w-20 h-20 bg-green-500 rounded-full mb-6">
            <CheckCircleIcon className="h-12 w-12 text-white" />
          </div>
          
          <h1 className="text-3xl lg:text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            ¡Pedido Confirmado!
          </h1>
          
          <p className="text-lg text-warm-camel mb-2">
            Gracias por tu compra en The White Laces
          </p>
          
          <p className="text-warm-camel">
            Número de pedido: <span className="font-mono font-semibold text-lime-500">{order.orderNumber}</span>
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Order Details */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Order Status */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.1 }}
            >
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Estado del Pedido
                  </h2>
                  
                  <div className="flex items-center gap-4 mb-4">
                    <div className="flex items-center justify-center w-12 h-12 bg-green-500 rounded-full">
                      <CheckCircleIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                        Pedido Confirmado
                      </p>
                      <p className="text-sm text-warm-camel">
                        {new Date(order.createdAt).toLocaleDateString('es-MX', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric',
                          hour: '2-digit',
                          minute: '2-digit'
                        })}
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4 mb-4">
                    <div className="flex items-center justify-center w-12 h-12 bg-warm-camel/20 rounded-full">
                      <TruckIcon className="h-6 w-6 text-warm-camel" />
                    </div>
                    <div>
                      <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                        En Preparación
                      </p>
                      <p className="text-sm text-warm-camel">
                        Estimado: 1-2 días hábiles
                      </p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-4">
                    <div className="flex items-center justify-center w-12 h-12 bg-warm-camel/10 rounded-full">
                      <CalendarIcon className="h-6 w-6 text-warm-camel/50" />
                    </div>
                    <div>
                      <p className="font-medium text-warm-camel/50">
                        Entrega Estimada
                      </p>
                      <p className="text-sm text-warm-camel/50">
                        {new Date(order.estimatedDelivery).toLocaleDateString('es-MX', {
                          year: 'numeric',
                          month: 'long',
                          day: 'numeric'
                        })}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Order Items */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Productos Pedidos
                  </h2>
                  
                  <div className="space-y-4">
                    {order.items.map((item, index) => (
                      <div key={index} className="flex items-center gap-4 p-4 bg-warm-camel/5 rounded-lg">
                        <div className="w-16 h-16 bg-gradient-to-br from-warm-camel/20 to-lime-500/20 rounded-lg flex items-center justify-center">
                          {item.product?.images?.[0] ? (
                            <img 
                              src={item.product.images[0]} 
                              alt={item.productName}
                              className="w-full h-full object-cover rounded-lg"
                            />
                          ) : (
                            <ShoppingBagIcon className="h-8 w-8 text-warm-camel" />
                          )}
                        </div>
                        
                        <div className="flex-1">
                          <h3 className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                            {item.productName}
                          </h3>
                          <p className="text-sm text-warm-camel">
                            {item.product?.brand?.name} • Talla {item.size} • Cantidad: {item.quantity}
                          </p>
                        </div>
                        
                        <div className="text-right">
                          <p className="font-semibold text-lime-500">
                            ${item.totalPrice.toLocaleString()} MXN
                          </p>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Shipping Address */}
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
            >
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4 flex items-center gap-2">
                    <MapPinIcon className="h-5 w-5" />
                    Dirección de Envío
                  </h2>
                  
                  <div className="text-warm-camel">
                    <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                      {order.shippingAddress.firstName} {order.shippingAddress.lastName}
                    </p>
                    <p>{order.shippingAddress.street}</p>
                    <p>
                      {order.shippingAddress.city}, {order.shippingAddress.state} {order.shippingAddress.zipCode}
                    </p>
                    <p>{order.shippingAddress.country}</p>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            
            {/* Order Summary */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.4 }}
            >
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Resumen del Pedido
                  </h2>
                  
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between">
                      <span className="text-warm-camel">Total:</span>
                      <span className="font-semibold text-lime-500">
                        ${order.totalAmount.toLocaleString()} {order.currency}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-warm-camel">Método de pago:</span>
                      <span className="text-forest-emerald dark:text-light-cloud-gray flex items-center gap-1">
                        <CreditCardIcon className="h-4 w-4" />
                        **** {order.paymentMethod.last4}
                      </span>
                    </div>
                    
                    <div className="flex justify-between">
                      <span className="text-warm-camel">Estado del pago:</span>
                      <span className="text-green-500 font-medium">
                        ✓ Pagado
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Next Steps */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.5 }}
            >
              <Card>
                <CardContent className="p-6">
                  <h2 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    ¿Qué sigue?
                  </h2>
                  
                  <div className="space-y-4 text-sm">
                    <div className="flex items-start gap-3">
                      <EnvelopeIcon className="h-5 w-5 text-lime-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                          Confirmación por email
                        </p>
                        <p className="text-warm-camel">
                          Te enviaremos los detalles del pedido a tu correo
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <TruckIcon className="h-5 w-5 text-lime-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                          Preparación del envío
                        </p>
                        <p className="text-warm-camel">
                          Procesaremos tu pedido en 1-2 días hábiles
                        </p>
                      </div>
                    </div>
                    
                    <div className="flex items-start gap-3">
                      <CheckCircleIcon className="h-5 w-5 text-lime-500 mt-0.5" />
                      <div>
                        <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                          Seguimiento del envío
                        </p>
                        <p className="text-warm-camel">
                          Recibirás el número de rastreo cuando se envíe
                        </p>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Actions */}
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.6 }}
              className="space-y-3"
            >
              <Button 
                onClick={() => router.push('/search')}
                className="w-full bg-lime-500 hover:bg-lime-600 text-black"
              >
                Seguir Comprando
              </Button>
              
              <Button 
                onClick={() => router.push('/account/orders')}
                variant="outline"
                className="w-full"
              >
                Ver Mis Pedidos
              </Button>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
