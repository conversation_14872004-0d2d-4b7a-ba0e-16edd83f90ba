'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

const limitedEditions = [
  {
    id: 1,
    name: 'Nike x Travis Scott Air Jordan 1 Low',
    brand: 'Nike x Travis Scott',
    price: 45000,
    originalPrice: 3500,
    releaseDate: '2024-02-15',
    stock: 12,
    totalUnits: 500,
    status: 'available',
    rarity: 'ultra-rare',
    description: 'Colaboración exclusiva con el artista <PERSON>',
    features: ['Suede premium', 'Swoosh invertido', 'Cactus Jack branding'],
    image: '🔥'
  },
  {
    id: 2,
    name: 'Gucci x Adidas Gazelle',
    brand: 'Gucci x Adidas',
    price: 89000,
    originalPrice: 12000,
    releaseDate: '2024-01-20',
    stock: 3,
    totalUnits: 200,
    status: 'almost-sold-out',
    rarity: 'legendary',
    description: 'Fusión única entre lujo italiano y heritage deportivo',
    features: ['Piel italiana', 'Monograma Gucci', 'Edición numerada'],
    image: '👑'
  },
  {
    id: 3,
    name: 'Off-White x Nike Air Force 1 "MCA"',
    brand: 'Off-White x Nike',
    price: 67000,
    originalPrice: 4200,
    releaseDate: '2024-03-01',
    stock: 0,
    totalUnits: 300,
    status: 'sold-out',
    rarity: 'legendary',
    description: 'Edición especial del Museum of Contemporary Art',
    features: ['Colorway exclusivo', 'Zip-tie azul', 'Texto "AIR"'],
    image: '🎨'
  },
  {
    id: 4,
    name: 'Dior x Air Jordan 1 High',
    brand: 'Dior x Jordan',
    price: 125000,
    originalPrice: 8500,
    releaseDate: '2024-02-28',
    stock: 1,
    totalUnits: 100,
    status: 'last-chance',
    rarity: 'mythical',
    description: 'La colaboración más exclusiva entre Dior y Jordan',
    features: ['Piel Dior', 'Swoosh metálico', 'Caja de lujo'],
    image: '💎'
  },
  {
    id: 5,
    name: 'Yeezy Boost 350 V2 "Zebra" Restock',
    brand: 'Adidas Yeezy',
    price: 8500,
    originalPrice: 4500,
    releaseDate: '2024-03-15',
    stock: 45,
    totalUnits: 1000,
    status: 'available',
    rarity: 'rare',
    description: 'Restock del icónico colorway Zebra',
    features: ['Primeknit', 'Boost technology', 'Patrón zebra'],
    image: '🦓'
  },
  {
    id: 6,
    name: 'Fragment x Travis Scott x Air Jordan 1 Low',
    brand: 'Fragment x Travis Scott x Jordan',
    price: 78000,
    originalPrice: 4800,
    releaseDate: '2024-04-01',
    stock: 8,
    totalUnits: 250,
    status: 'coming-soon',
    rarity: 'legendary',
    description: 'Triple colaboración histórica',
    features: ['Fragment lightning', 'Cactus Jack details', 'Premium materials'],
    image: '⚡'
  }
]

const getRarityColor = (rarity) => {
  switch (rarity) {
    case 'rare': return 'from-blue-500 to-blue-600'
    case 'ultra-rare': return 'from-purple-500 to-purple-600'
    case 'legendary': return 'from-orange-500 to-red-600'
    case 'mythical': return 'from-yellow-400 to-orange-500'
    default: return 'from-gray-500 to-gray-600'
  }
}

const getStatusBadge = (status, stock) => {
  switch (status) {
    case 'available':
      return <Badge variant="success" size="sm">Disponible</Badge>
    case 'almost-sold-out':
      return <Badge variant="warning" size="sm">Últimas unidades</Badge>
    case 'sold-out':
      return <Badge variant="danger" size="sm">Agotado</Badge>
    case 'last-chance':
      return <Badge variant="danger" size="sm">Última oportunidad</Badge>
    case 'coming-soon':
      return <Badge variant="info" size="sm">Próximamente</Badge>
    default:
      return <Badge variant="default" size="sm">Disponible</Badge>
  }
}

export default function LimitedEditionsPage() {
  const availableItems = limitedEditions.filter(item => item.status === 'available' || item.status === 'almost-sold-out' || item.status === 'last-chance')
  const soldOutItems = limitedEditions.filter(item => item.status === 'sold-out')
  const comingSoonItems = limitedEditions.filter(item => item.status === 'coming-soon')

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Ediciones Limitadas
          </h1>
          <p className="text-warm-camel text-lg max-w-3xl mx-auto">
            Las colaboraciones más exclusivas y codiciadas del mundo del sneaker. 
            Piezas únicas que definen la cultura urbana.
          </p>
        </motion.div>

        {/* Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12"
        >
          <Card variant="glass">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                {limitedEditions.length}
              </div>
              <div className="text-sm text-warm-camel">Ediciones Totales</div>
            </CardContent>
          </Card>
          
          <Card variant="glass">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                {availableItems.length}
              </div>
              <div className="text-sm text-warm-camel">Disponibles</div>
            </CardContent>
          </Card>
          
          <Card variant="glass">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                {soldOutItems.length}
              </div>
              <div className="text-sm text-warm-camel">Agotados</div>
            </CardContent>
          </Card>
          
          <Card variant="glass">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                {comingSoonItems.length}
              </div>
              <div className="text-sm text-warm-camel">Próximamente</div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Available Items */}
        {availableItems.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
            className="mb-16"
          >
            <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8 flex items-center gap-2">
              <span>🔥</span>
              Disponibles Ahora
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {availableItems.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                >
                  <Card variant="glass" className="group hover:shadow-lg transition-all duration-300">
                    <CardContent className="p-6">
                      
                      {/* Header */}
                      <div className="flex items-start justify-between mb-4">
                        <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${getRarityColor(item.rarity)} flex items-center justify-center text-2xl`}>
                          {item.image}
                        </div>
                        {getStatusBadge(item.status, item.stock)}
                      </div>

                      {/* Product Info */}
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2 line-clamp-2">
                        {item.name}
                      </h3>
                      
                      <p className="text-warm-camel text-sm mb-2">{item.brand}</p>
                      
                      <p className="text-warm-camel text-xs mb-4 line-clamp-2">
                        {item.description}
                      </p>

                      {/* Price */}
                      <div className="mb-4">
                        <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                          ${item.price.toLocaleString()} MXN
                        </div>
                        <div className="text-sm text-warm-camel line-through">
                          Retail: ${item.originalPrice.toLocaleString()} MXN
                        </div>
                      </div>

                      {/* Stock Info */}
                      <div className="mb-4">
                        <div className="flex justify-between text-sm text-warm-camel mb-1">
                          <span>Stock disponible</span>
                          <span>{item.stock} / {item.totalUnits}</span>
                        </div>
                        <div className="w-full bg-warm-camel/20 rounded-full h-2">
                          <div 
                            className="bg-rich-gold h-2 rounded-full transition-all duration-300"
                            style={{ width: `${(item.stock / item.totalUnits) * 100}%` }}
                          />
                        </div>
                      </div>

                      {/* Features */}
                      <div className="mb-6">
                        <div className="flex flex-wrap gap-1">
                          {item.features.slice(0, 2).map((feature, i) => (
                            <Badge key={i} variant="outline" size="sm">
                              {feature}
                            </Badge>
                          ))}
                          {item.features.length > 2 && (
                            <Badge variant="outline" size="sm">
                              +{item.features.length - 2} más
                            </Badge>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="space-y-2">
                        <TransitionLink href={`/product/${item.id}`}>
                          <AnimatedButton
                            variant="primary"
                            className="w-full"
                            disabled={item.stock === 0}
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                              </svg>
                            }
                          >
                            {item.stock === 0 ? 'Agotado' : 'Comprar Ahora'}
                          </AnimatedButton>
                        </TransitionLink>
                        
                        <AnimatedButton
                          variant="secondary"
                          size="sm"
                          className="w-full"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          }
                        >
                          Ver Detalles
                        </AnimatedButton>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Coming Soon */}
        {comingSoonItems.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="mb-16"
          >
            <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8 flex items-center gap-2">
              <span>⏰</span>
              Próximamente
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
              {comingSoonItems.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.9 + index * 0.1 }}
                >
                  <Card variant="default" className="opacity-75">
                    <CardContent className="p-6">
                      <div className="flex items-start justify-between mb-4">
                        <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${getRarityColor(item.rarity)} flex items-center justify-center text-2xl`}>
                          {item.image}
                        </div>
                        {getStatusBadge(item.status)}
                      </div>

                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                        {item.name}
                      </h3>
                      
                      <p className="text-warm-camel text-sm mb-4">{item.brand}</p>
                      
                      <div className="text-center py-4">
                        <div className="text-warm-camel text-sm mb-2">Fecha de lanzamiento</div>
                        <div className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray">
                          {new Date(item.releaseDate).toLocaleDateString('es-MX', {
                            year: 'numeric',
                            month: 'long',
                            day: 'numeric'
                          })}
                        </div>
                      </div>

                      <AnimatedButton
                        variant="outline"
                        className="w-full"
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 00-15 0v5h5l-5 5-5-5h5V7a9.5 9.5 0 0119 0v10z" />
                          </svg>
                        }
                      >
                        Notificarme
                      </AnimatedButton>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </div>
          </motion.div>
        )}

        {/* Newsletter Signup */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="text-center"
        >
          <Card variant="glass">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
                No te pierdas ningún drop
              </h2>
              <p className="text-warm-camel mb-8 max-w-2xl mx-auto">
                Suscríbete a nuestras notificaciones y sé el primero en saber sobre 
                nuevas ediciones limitadas, restocks y colaboraciones exclusivas.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center max-w-md mx-auto">
                <input
                  type="email"
                  placeholder="<EMAIL>"
                  className="flex-1 px-4 py-3 rounded-lg border border-warm-camel/30 bg-white/10 backdrop-blur-sm text-forest-emerald dark:text-light-cloud-gray placeholder-warm-camel/60 focus:outline-none focus:ring-2 focus:ring-rich-gold/50"
                />
                <AnimatedButton
                  variant="primary"
                  icon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5-5-5h5v-5a7.5 7.5 0 00-15 0v5h5l-5 5-5-5h5V7a9.5 9.5 0 0119 0v10z" />
                    </svg>
                  }
                >
                  Suscribirse
                </AnimatedButton>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
