/**
 * CYTTE DATABASE EXPANDER HELPER FUNCTIONS
 * Shared utilities for product creation and mapping
 */

// Style mapping
function mapStyle(styleName) {
  const styleMap = {
    '1. SNEAKERS': { id: 'sneakers', display: 'Sneakers' },
    '2. SANDALS': { id: 'sandals', display: 'Sandalias' },
    '3. FORMAL': { id: 'formal', display: 'Formal' },
    '4. CASUAL': { id: 'casual', display: 'Casual' },
    '5. KIDS': { id: 'kids', display: 'Niños' }
  }
  
  return styleMap[styleName] || { 
    id: styleName.toLowerCase().replace(/\s+/g, '-'), 
    display: styleName 
  }
}

// Brand mapping
function mapBrand(brandName) {
  const brandMap = {
    '1. NIKE Limited Edition': { id: 'nike', name: 'Nike', type: 'streetwear' },
    '2. ADIDAS Limited Edition': { id: 'adidas', name: 'Adidas', type: 'streetwear' },
    '3. HERMES': { id: 'hermes', name: 'Herm<PERSON>', type: 'luxury' },
    '4. GUCCI': { id: 'gucci', name: '<PERSON><PERSON>', type: 'luxury' },
    '5. DIOR': { id: 'dior', name: 'Dior', type: 'luxury' },
    '6. LV': { id: 'lv', name: 'Louis Vuitton', type: 'luxury' },
    '7. BALENCIAGA': { id: 'balenciaga', name: 'Balenciaga', type: 'luxury' },
    '8. CHANEL': { id: 'chanel', name: 'Chanel', type: 'luxury' },
    '9. LOUBOUTIN': { id: 'louboutin', name: 'Christian Louboutin', type: 'luxury' },
    '10. OFF WHITE': { id: 'off-white', name: 'Off-White', type: 'streetwear' },
    '11. GIVENCHY': { id: 'givenchy', name: 'Givenchy', type: 'luxury' },
    '12. Maison MARGIELA': { id: 'maison-margiela', name: 'Maison Margiela', type: 'luxury' },
    '13. VALENTINO': { id: 'valentino', name: 'Valentino', type: 'luxury' },
    '14. PRADA': { id: 'prada', name: 'Prada', type: 'luxury' },
    '15. MIU MIU': { id: 'miu-miu', name: 'Miu Miu', type: 'luxury' },
    '16. BOTTEGA VENETA': { id: 'bottega-veneta', name: 'Bottega Veneta', type: 'luxury' },
    '17. BURBERRY': { id: 'burberry', name: 'Burberry', type: 'luxury' },
    '18. GOLDEN GOOSE': { id: 'golden-goose', name: 'Golden Goose', type: 'luxury' },
    '19. GAMA NORMAL': { id: 'gama-normal', name: 'Gama Normal', type: 'casual' },
    'Common Project': { id: 'common-project', name: 'Common Projects', type: 'minimalist' },
    '1. NIKE Collabs': { id: 'nike-collabs', name: 'Nike Collaborations', type: 'streetwear' },
    '9. UGG': { id: 'ugg', name: 'UGG', type: 'casual' },
    '13. CROCS': { id: 'crocs', name: 'Crocs', type: 'casual' },
    '15. BIRKENSTOCK': { id: 'birkenstock', name: 'Birkenstock', type: 'casual' }
  }
  
  return brandMap[brandName] || { 
    id: brandName.toLowerCase().replace(/\s+/g, '-'), 
    name: brandName, 
    type: 'unknown' 
  }
}

// Gender mapping
function mapGender(genderName) {
  const genderMap = {
    'MIXTE': { id: 'mixte', display: 'Unisex', path: 'mixte' },
    'WOMEN': { id: 'women', display: 'Mujer', path: 'women' },
    'MEN': { id: 'men', display: 'Hombre', path: 'men' },
    '1. MIXTE': { id: 'mixte', display: 'Unisex', path: 'mixte' },
    '2. WOMEN': { id: 'women', display: 'Mujer', path: 'women' },
    '3. MEN': { id: 'men', display: 'Hombre', path: 'men' }
  }
  
  return genderMap[genderName] || { id: 'mixte', display: 'Unisex', path: 'mixte' }
}

// Model family mapping
function mapModelFamily(modelName) {
  const modelMap = {
    'jordan': { id: 'jordan', display: 'Jordan' },
    'air-force': { id: 'air-force', display: 'Air Force' },
    'dunk': { id: 'dunk', display: 'Dunk' },
    'blazer': { id: 'blazer', display: 'Blazer' },
    'cortez': { id: 'cortez', display: 'Cortez' },
    'air-max': { id: 'air-max', display: 'Air Max' },
    'ace': { id: 'ace', display: 'Ace' },
    'screener': { id: 'screener', display: 'Screener' },
    'rython': { id: 'rython', display: 'Rython' },
    'horsebit': { id: 'horsebit', display: 'Horsebit' },
    'tennis': { id: 'tennis', display: 'Tennis' },
    'standard': { id: 'standard', display: 'Standard' }
  }
  
  return modelMap[modelName] || { 
    id: modelName.toLowerCase().replace(/\s+/g, '-'), 
    display: modelName 
  }
}

// Product type mapping
function getProductType(style) {
  const typeMap = {
    'sneakers': 'sneaker',
    'sandals': 'sandal',
    'formal': 'formal',
    'casual': 'casual',
    'kids': 'kids'
  }
  return typeMap[style] || 'shoe'
}

// Product subtype mapping
function getProductSubType(model) {
  const subTypeMap = {
    'Jordan': 'high-top',
    'Air Force': 'low-top',
    'Dunk': 'low-top',
    'Blazer': 'high-top',
    'Ace': 'low-top',
    'Horsebit': 'loafer',
    'Tennis': 'low-top'
  }
  
  if (model.toLowerCase().includes('high')) return 'high-top'
  if (model.toLowerCase().includes('low')) return 'low-top'
  if (model.toLowerCase().includes('slide')) return 'slide'
  if (model.toLowerCase().includes('loafer')) return 'loafer'
  
  return subTypeMap[model] || 'sneaker'
}

// Price generation
function generatePrice(brandType, isCollaboration) {
  const basePrices = {
    'luxury': [15000, 45000],
    'streetwear': [3000, 25000],
    'casual': [1500, 8000],
    'athletic': [2000, 12000],
    'unknown': [2000, 10000]
  }
  
  const [min, max] = basePrices[brandType] || [2000, 10000]
  let price = Math.floor(Math.random() * (max - min) + min)
  
  if (isCollaboration) {
    price = Math.floor(price * 1.5) // Collaborations cost more
  }
  
  // Round to nearest 100
  return Math.round(price / 100) * 100
}

// Size generation
function generateSizes(gender) {
  const sizeMaps = {
    'women': ['5', '5.5', '6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10'],
    'men': ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12', '13'],
    'mixte': ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12']
  }
  
  return sizeMaps[gender] || sizeMaps['mixte']
}

// Material generation
function generateMaterials(brandType) {
  const materialSets = {
    'luxury': ['Cuero Italiano Premium', 'Gamuza', 'Seda', 'Detalles Dorados'],
    'streetwear': ['Cuero Premium', 'Canvas', 'Mesh Transpirable', 'Suela de Goma'],
    'casual': ['Cuero', 'Textil', 'Suela Cómoda'],
    'athletic': ['Mesh Deportivo', 'Cuero Sintético', 'Suela Air', 'Tecnología Avanzada'],
    'unknown': ['Materiales de Calidad']
  }
  
  return materialSets[brandType] || ['Materiales de Calidad']
}

// Release date generation
function generateReleaseDate() {
  const start = new Date('2023-01-01')
  const end = new Date('2024-12-31')
  const randomTime = start.getTime() + Math.random() * (end.getTime() - start.getTime())
  return new Date(randomTime).toISOString().split('T')[0]
}

// Tag generation
function generateTags(brand, model, collaborator, style) {
  const tags = [
    brand.toLowerCase().replace(/\s+/g, '-'),
    model.toLowerCase().replace(/\s+/g, '-'),
    style
  ]
  
  if (collaborator) {
    tags.push(collaborator.toLowerCase().replace(/\s+/g, '-'))
    tags.push('collaboration')
  }
  
  return tags
}

// Keyword generation
function generateKeywords(brand, model, reference) {
  return [
    brand.toLowerCase(),
    model.toLowerCase(),
    reference.toLowerCase(),
    'zapatos',
    'calzado',
    'moda'
  ]
}

// Search terms generation
function generateSearchTerms(brand, model, collaborator) {
  const terms = [
    `${brand} ${model}`,
    brand,
    model
  ]
  
  if (collaborator) {
    terms.push(`${collaborator} ${brand}`)
    terms.push(collaborator)
  }
  
  return terms
}

// Product name generation
function generateProductName(brand, model, reference, collaborator) {
  if (collaborator && collaborator !== brand) {
    return `${collaborator} x ${brand} ${model} "${reference}"`
  }
  return `${brand} ${model} "${reference}"`
}

// Product description generation
function generateProductDescription(brand, model, reference, isCollaboration) {
  if (isCollaboration) {
    return `Colaboración exclusiva ${brand} ${model}. Edición limitada con detalles únicos.`
  }
  return `${brand} ${model} de alta calidad. Diseño elegante y materiales premium.`
}

module.exports = {
  mapStyle,
  mapBrand,
  mapGender,
  mapModelFamily,
  getProductType,
  getProductSubType,
  generatePrice,
  generateSizes,
  generateMaterials,
  generateReleaseDate,
  generateTags,
  generateKeywords,
  generateSearchTerms,
  generateProductName,
  generateProductDescription
}
