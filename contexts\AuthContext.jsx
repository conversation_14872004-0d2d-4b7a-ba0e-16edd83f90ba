'use client'

import { createContext, useContext, useReducer, useEffect } from 'react'

// Auth Context
const AuthContext = createContext()

// Auth Actions
const AUTH_ACTIONS = {
  LOGIN: 'LOGIN',
  LOGOUT: 'LOGOUT',
  REGISTER: 'REGISTER',
  UPDATE_PROFILE: 'UPDATE_PROFILE',
  LOAD_USER: 'LOAD_USER',
  SET_LOADING: 'SET_LOADING',
  SET_ERROR: 'SET_ERROR'
}

// Auth Reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return {
        ...state,
        loading: action.payload
      }
    
    case AUTH_ACTIONS.SET_ERROR:
      return {
        ...state,
        error: action.payload,
        loading: false
      }
    
    case AUTH_ACTIONS.LOGIN:
    case AUTH_ACTIONS.REGISTER:
      return {
        ...state,
        user: action.payload.user,
        token: action.payload.token,
        isAuthenticated: true,
        loading: false,
        error: null
      }
    
    case AUTH_ACTIONS.LOGOUT:
      return {
        ...state,
        user: null,
        token: null,
        isAuthenticated: false,
        loading: false,
        error: null
      }
    
    case AUTH_ACTIONS.UPDATE_PROFILE:
      return {
        ...state,
        user: { ...state.user, ...action.payload },
        loading: false,
        error: null
      }
    
    case AUTH_ACTIONS.LOAD_USER:
      return {
        ...state,
        user: action.payload,
        isAuthenticated: true,
        loading: false
      }
    
    default:
      return state
  }
}

// Initial Auth State
const initialAuthState = {
  user: null,
  token: null,
  isAuthenticated: false,
  loading: false,
  error: null
}

// Mock user data for demo
const mockUsers = [
  {
    id: '1',
    email: '<EMAIL>',
    password: 'demo123',
    firstName: 'María',
    lastName: 'González',
    phone: '+52 55 1234 5678',
    dateOfBirth: '1995-06-15',
    gender: 'female',
    preferences: {
      newsletter: true,
      smsNotifications: false,
      favoriteCategories: ['luxury', 'sneakers'],
      preferredBrands: ['gucci', 'dior', 'nike']
    },
    addresses: [
      {
        id: '1',
        type: 'home',
        firstName: 'María',
        lastName: 'González',
        street: 'Av. Reforma 123',
        city: 'Ciudad de México',
        state: 'CDMX',
        zipCode: '06600',
        country: 'México',
        phone: '+52 55 1234 5678',
        isDefault: true
      }
    ],
    paymentMethods: [
      {
        id: '1',
        type: 'card',
        brand: 'visa',
        last4: '4242',
        expiryMonth: 12,
        expiryYear: 2027,
        isDefault: true
      }
    ],
    createdAt: '2024-01-15T10:00:00Z',
    lastLogin: new Date().toISOString()
  }
]

// Auth Provider Component
export function AuthProvider({ children }) {
  const [state, dispatch] = useReducer(authReducer, initialAuthState)
  
  // Load user from localStorage on mount
  useEffect(() => {
    const token = localStorage.getItem('twl-token')
    const userData = localStorage.getItem('twl-user')
    
    if (token && userData) {
      try {
        const user = JSON.parse(userData)
        dispatch({
          type: AUTH_ACTIONS.LOAD_USER,
          payload: user
        })
      } catch (error) {
        console.error('Error loading user from localStorage:', error)
        localStorage.removeItem('twl-token')
        localStorage.removeItem('twl-user')
      }
    }
  }, [])
  
  // Save auth data to localStorage
  const saveAuthData = (user, token) => {
    localStorage.setItem('twl-token', token)
    localStorage.setItem('twl-user', JSON.stringify(user))
  }
  
  // Clear auth data from localStorage
  const clearAuthData = () => {
    localStorage.removeItem('twl-token')
    localStorage.removeItem('twl-user')
  }
  
  // Login function
  const login = async (email, password) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const user = mockUsers.find(u => u.email === email && u.password === password)
      
      if (!user) {
        throw new Error('Credenciales inválidas')
      }
      
      const token = `mock-token-${user.id}-${Date.now()}`
      const userWithoutPassword = { ...user }
      delete userWithoutPassword.password
      
      saveAuthData(userWithoutPassword, token)
      
      dispatch({
        type: AUTH_ACTIONS.LOGIN,
        payload: { user: userWithoutPassword, token }
      })
      
      return { success: true }
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.SET_ERROR,
        payload: error.message
      })
      return { success: false, error: error.message }
    }
  }
  
  // Register function
  const register = async (userData) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      // Check if user already exists
      const existingUser = mockUsers.find(u => u.email === userData.email)
      if (existingUser) {
        throw new Error('El email ya está registrado')
      }
      
      const newUser = {
        id: Date.now().toString(),
        ...userData,
        preferences: {
          newsletter: true,
          smsNotifications: false,
          favoriteCategories: [],
          preferredBrands: []
        },
        addresses: [],
        paymentMethods: [],
        createdAt: new Date().toISOString(),
        lastLogin: new Date().toISOString()
      }
      
      const userWithoutPassword = { ...newUser }
      delete userWithoutPassword.password
      
      const token = `mock-token-${newUser.id}-${Date.now()}`
      
      // Add to mock users (in real app, this would be API call)
      mockUsers.push(newUser)
      
      saveAuthData(userWithoutPassword, token)
      
      dispatch({
        type: AUTH_ACTIONS.REGISTER,
        payload: { user: userWithoutPassword, token }
      })
      
      return { success: true }
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.SET_ERROR,
        payload: error.message
      })
      return { success: false, error: error.message }
    }
  }
  
  // Logout function
  const logout = () => {
    clearAuthData()
    dispatch({ type: AUTH_ACTIONS.LOGOUT })
  }
  
  // Update profile function
  const updateProfile = async (updates) => {
    dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true })
    
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const updatedUser = { ...state.user, ...updates }
      saveAuthData(updatedUser, state.token)
      
      dispatch({
        type: AUTH_ACTIONS.UPDATE_PROFILE,
        payload: updates
      })
      
      return { success: true }
    } catch (error) {
      dispatch({
        type: AUTH_ACTIONS.SET_ERROR,
        payload: error.message
      })
      return { success: false, error: error.message }
    }
  }
  
  // Clear error function
  const clearError = () => {
    dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: null })
  }
  
  const value = {
    // State
    user: state.user,
    token: state.token,
    isAuthenticated: state.isAuthenticated,
    loading: state.loading,
    error: state.error,
    
    // Actions
    login,
    register,
    logout,
    updateProfile,
    clearError
  }
  
  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}

// Custom hook to use auth context
export function useAuth() {
  const context = useContext(AuthContext)
  
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  
  return context
}
