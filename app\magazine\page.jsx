'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

const featuredArticle = {
  id: 1,
  title: 'El Resurgimiento de los Air Jordan 1: Por Qué Siguen Siendo Relevantes en 2024',
  excerpt: 'Exploramos cómo los icónicos Air Jordan 1 han mantenido su relevancia cultural y comercial después de casi 40 años.',
  author: '<PERSON>',
  date: '2024-01-15',
  readTime: '8 min',
  category: 'Historia',
  image: '🏀',
  featured: true
}

const articles = [
  {
    id: 2,
    title: 'Guía Completa: Cómo Cuidar Tus Sneakers de Lujo',
    excerpt: 'Tips profesionales para mantener tus inversiones en calzado como nuevas.',
    author: '<PERSON>',
    date: '2024-01-12',
    readTime: '6 min',
    category: 'Cuidado',
    image: '✨'
  },
  {
    id: 3,
    title: 'Las Colaboraciones Más Esperadas de 2024',
    excerpt: 'Un vistazo a las colaboraciones que definirán el año en el mundo del sneaker.',
    author: 'Ana Rodríguez',
    date: '2024-01-10',
    readTime: '5 min',
    category: 'Tendencias',
    image: '🤝'
  },
  {
    id: 4,
    title: 'Streetwear Mexicano: La Nueva Ola Creativa',
    excerpt: 'Cómo los diseñadores mexicanos están redefiniendo el streetwear global.',
    author: 'Luis Herrera',
    date: '2024-01-08',
    readTime: '7 min',
    category: 'Cultura',
    image: '🇲🇽'
  },
  {
    id: 5,
    title: 'Inversión en Sneakers: ¿Moda o Negocio?',
    excerpt: 'Analizamos el mercado de reventa y las mejores estrategias de inversión.',
    author: 'Roberto Silva',
    date: '2024-01-05',
    readTime: '9 min',
    category: 'Inversión',
    image: '📈'
  },
  {
    id: 6,
    title: 'Sostenibilidad en la Industria del Calzado',
    excerpt: 'Las marcas que están liderando el cambio hacia un futuro más verde.',
    author: 'Elena Martínez',
    date: '2024-01-03',
    readTime: '6 min',
    category: 'Sostenibilidad',
    image: '🌱'
  }
]

const categories = [
  { name: 'Historia', count: 12, color: 'from-blue-500 to-blue-600' },
  { name: 'Tendencias', count: 18, color: 'from-purple-500 to-purple-600' },
  { name: 'Cuidado', count: 8, color: 'from-green-500 to-green-600' },
  { name: 'Cultura', count: 15, color: 'from-orange-500 to-orange-600' },
  { name: 'Inversión', count: 6, color: 'from-red-500 to-red-600' },
  { name: 'Sostenibilidad', count: 4, color: 'from-teal-500 to-teal-600' }
]

const getCategoryColor = (category) => {
  const categoryObj = categories.find(cat => cat.name === category)
  return categoryObj ? categoryObj.color : 'from-gray-500 to-gray-600'
}

export default function MagazinePage() {
  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            TWL Magazine
          </h1>
          <p className="text-warm-camel text-lg max-w-3xl mx-auto">
            Historias, tendencias y cultura del mundo del sneaker. 
            Contenido exclusivo para los verdaderos apasionados del calzado.
          </p>
        </motion.div>

        {/* Featured Article */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-16"
        >
          <Card variant="glass" className="overflow-hidden">
            <CardContent className="p-0">
              <div className="md:flex">
                <div className="md:w-1/3 bg-gradient-to-br from-rich-gold to-warm-camel flex items-center justify-center p-12">
                  <div className="text-8xl">{featuredArticle.image}</div>
                </div>
                
                <div className="md:w-2/3 p-8">
                  <div className="flex items-center gap-3 mb-4">
                    <Badge variant="primary" size="sm">Destacado</Badge>
                    <Badge 
                      variant="outline" 
                      size="sm"
                      className={`bg-gradient-to-r ${getCategoryColor(featuredArticle.category)} text-white border-0`}
                    >
                      {featuredArticle.category}
                    </Badge>
                  </div>
                  
                  <h2 className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-4 line-clamp-2">
                    {featuredArticle.title}
                  </h2>
                  
                  <p className="text-warm-camel mb-6 line-clamp-3">
                    {featuredArticle.excerpt}
                  </p>
                  
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4 text-sm text-warm-camel">
                      <span>Por {featuredArticle.author}</span>
                      <span>•</span>
                      <span>{new Date(featuredArticle.date).toLocaleDateString('es-MX')}</span>
                      <span>•</span>
                      <span>{featuredArticle.readTime} lectura</span>
                    </div>
                    
                    <TransitionLink href={`/magazine/${featuredArticle.id}`}>
                      <AnimatedButton
                        variant="primary"
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                          </svg>
                        }
                        iconPosition="right"
                      >
                        Leer Artículo
                      </AnimatedButton>
                    </TransitionLink>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Articles Grid */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.4 }}
              className="mb-8"
            >
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8">
                Últimos Artículos
              </h2>
              
              <div className="space-y-6">
                {articles.map((article, index) => (
                  <motion.div
                    key={article.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                  >
                    <Card variant="default" className="group hover:shadow-lg transition-all duration-300">
                      <CardContent className="p-6">
                        <div className="flex gap-6">
                          <div className={`w-20 h-20 rounded-lg bg-gradient-to-r ${getCategoryColor(article.category)} flex items-center justify-center text-3xl flex-shrink-0`}>
                            {article.image}
                          </div>
                          
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge 
                                variant="outline" 
                                size="sm"
                                className={`bg-gradient-to-r ${getCategoryColor(article.category)} text-white border-0`}
                              >
                                {article.category}
                              </Badge>
                            </div>
                            
                            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2 group-hover:text-rich-gold transition-colors line-clamp-2">
                              {article.title}
                            </h3>
                            
                            <p className="text-warm-camel mb-4 line-clamp-2">
                              {article.excerpt}
                            </p>
                            
                            <div className="flex items-center justify-between">
                              <div className="flex items-center gap-3 text-sm text-warm-camel">
                                <span>{article.author}</span>
                                <span>•</span>
                                <span>{new Date(article.date).toLocaleDateString('es-MX')}</span>
                                <span>•</span>
                                <span>{article.readTime}</span>
                              </div>
                              
                              <TransitionLink href={`/magazine/${article.id}`}>
                                <AnimatedButton
                                  variant="ghost"
                                  size="sm"
                                  icon={
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                                    </svg>
                                  }
                                >
                                  Leer
                                </AnimatedButton>
                              </TransitionLink>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
              
              <div className="text-center mt-8">
                <AnimatedButton
                  variant="secondary"
                  icon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  }
                >
                  Cargar Más Artículos
                </AnimatedButton>
              </div>
            </motion.div>
          </div>

          {/* Sidebar */}
          <div className="space-y-8">
            
            {/* Categories */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.8 }}
            >
              <Card variant="glass">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Categorías
                  </h3>
                  
                  <div className="space-y-3">
                    {categories.map((category, index) => (
                      <motion.button
                        key={category.name}
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: 0.9 + index * 0.1 }}
                        className="w-full flex items-center justify-between p-3 rounded-lg hover:bg-warm-camel/10 transition-colors group"
                        whileHover={{ x: 4 }}
                      >
                        <span className="text-forest-emerald dark:text-light-cloud-gray group-hover:text-rich-gold transition-colors">
                          {category.name}
                        </span>
                        <Badge variant="outline" size="sm">
                          {category.count}
                        </Badge>
                      </motion.button>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Newsletter */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.2 }}
            >
              <Card variant="glass">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Newsletter TWL
                  </h3>
                  
                  <p className="text-warm-camel text-sm mb-4">
                    Recibe los mejores artículos y contenido exclusivo directamente en tu email.
                  </p>
                  
                  <div className="space-y-3">
                    <input
                      type="email"
                      placeholder="<EMAIL>"
                      className="w-full px-4 py-3 rounded-lg border border-warm-camel/30 bg-white/10 backdrop-blur-sm text-forest-emerald dark:text-light-cloud-gray placeholder-warm-camel/60 focus:outline-none focus:ring-2 focus:ring-rich-gold/50"
                    />
                    <AnimatedButton
                      variant="primary"
                      className="w-full"
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                        </svg>
                      }
                    >
                      Suscribirse
                    </AnimatedButton>
                  </div>
                </CardContent>
              </Card>
            </motion.div>

            {/* Popular Articles */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 1.4 }}
            >
              <Card variant="glass">
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Más Populares
                  </h3>
                  
                  <div className="space-y-4">
                    {articles.slice(0, 3).map((article, index) => (
                      <motion.div
                        key={article.id}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 1.5 + index * 0.1 }}
                        className="flex gap-3 group cursor-pointer"
                      >
                        <div className="w-8 h-8 bg-rich-gold text-forest-emerald rounded-full flex items-center justify-center text-sm font-bold flex-shrink-0">
                          {index + 1}
                        </div>
                        
                        <div className="flex-1">
                          <h4 className="text-forest-emerald dark:text-light-cloud-gray text-sm font-medium group-hover:text-rich-gold transition-colors line-clamp-2">
                            {article.title}
                          </h4>
                          <p className="text-warm-camel text-xs mt-1">
                            {article.readTime} • {new Date(article.date).toLocaleDateString('es-MX')}
                          </p>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
