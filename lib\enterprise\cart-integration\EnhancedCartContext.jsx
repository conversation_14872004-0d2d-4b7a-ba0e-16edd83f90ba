'use client'

/**
 * Enhanced Cart Context with Enterprise Integration
 * Extends existing cart functionality with enterprise product system features
 */

import { createContext, useContext, useReducer, useEffect, useState, useCallback } from 'react'
import productAdapter from './ProductAdapter'

// Enhanced Cart Context
const EnhancedCartContext = createContext()

// Cart Actions (extended from original)
const CART_ACTIONS = {
  ADD_ITEM: 'ADD_ITEM',
  REMOVE_ITEM: 'REMOVE_ITEM',
  UPDATE_QUANTITY: 'UPDATE_QUANTITY',
  CLEAR_CART: 'CLEAR_CART',
  LOAD_CART: 'LOAD_CART',
  APPLY_DISCOUNT: 'APPLY_DISCOUNT',
  REMOVE_DISCOUNT: 'REMOVE_DISCOUNT',
  // New enterprise actions
  VALIDATE_ITEMS: 'VALIDATE_ITEMS',
  UPDATE_ITEM_VALIDATION: 'UPDATE_ITEM_VALIDATION',
  SET_RECOMMENDATIONS: 'SET_RECOMMENDATIONS',
  UPDATE_ITEM_DETAILS: 'UPDATE_ITEM_DETAILS'
}

// Initial state
const initialCartState = {
  items: [],
  discount: null,
  validationResults: {},
  recommendations: [],
  isValidating: false,
  lastValidated: null,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

// Enhanced Cart Reducer
const enhancedCartReducer = (state, action) => {
  console.log('🛒 Enhanced cartReducer called with action:', action.type)

  switch (action.type) {
    case CART_ACTIONS.ADD_ITEM: {
      const { productId, size, quantity, product } = action.payload
      const existingItemIndex = state.items.findIndex(
        item => item.productId === productId && item.size === size
      )

      if (existingItemIndex >= 0) {
        // Update existing item
        const updatedItems = [...state.items]
        updatedItems[existingItemIndex].quantity += quantity
        updatedItems[existingItemIndex].updatedAt = new Date().toISOString()

        return {
          ...state,
          items: updatedItems,
          updatedAt: new Date().toISOString()
        }
      } else {
        // Add new item with enhanced data
        const newItem = {
          id: `${productId}-${size}`,
          productId,
          name: product?.name || 'Unknown Product',
          brand: product?.brand || product?.brandInfo?.name || 'Unknown Brand',
          image: product?.image || product?.images?.[0] || '/placeholder-shoe.jpg',
          size,
          quantity,
          price: product?.price || 0,
          originalPrice: product?.originalPrice,
          discountPercent: product?.discountPercent,
          isLimitedEdition: product?.isLimitedEdition,
          stockLevel: product?.stockLevel,
          inStock: product?.inStock,
          rating: product?.rating,
          reviewCount: product?.reviewCount,
          category: product?.category,
          brandInfo: product?.brandInfo,
          addedAt: new Date().toISOString(),
          updatedAt: new Date().toISOString(),
          _source: product?._source || 'legacy'
        }

        return {
          ...state,
          items: [...state.items, newItem],
          updatedAt: new Date().toISOString()
        }
      }
    }

    case CART_ACTIONS.REMOVE_ITEM: {
      const { itemId } = action.payload
      return {
        ...state,
        items: state.items.filter(item => item.id !== itemId),
        validationResults: Object.fromEntries(
          Object.entries(state.validationResults).filter(([key]) => key !== itemId)
        ),
        updatedAt: new Date().toISOString()
      }
    }

    case CART_ACTIONS.UPDATE_QUANTITY: {
      const { itemId, quantity } = action.payload
      if (quantity <= 0) {
        return enhancedCartReducer(state, {
          type: CART_ACTIONS.REMOVE_ITEM,
          payload: { itemId }
        })
      }

      const updatedItems = state.items.map(item =>
        item.id === itemId
          ? { ...item, quantity, updatedAt: new Date().toISOString() }
          : item
      )

      return {
        ...state,
        items: updatedItems,
        updatedAt: new Date().toISOString()
      }
    }

    case CART_ACTIONS.CLEAR_CART: {
      return {
        ...initialCartState,
        createdAt: state.createdAt
      }
    }

    case CART_ACTIONS.LOAD_CART: {
      return {
        ...state,
        ...action.payload,
        validationResults: {},
        recommendations: []
      }
    }

    case CART_ACTIONS.APPLY_DISCOUNT: {
      return {
        ...state,
        discount: action.payload,
        updatedAt: new Date().toISOString()
      }
    }

    case CART_ACTIONS.REMOVE_DISCOUNT: {
      return {
        ...state,
        discount: null,
        updatedAt: new Date().toISOString()
      }
    }

    case CART_ACTIONS.VALIDATE_ITEMS: {
      return {
        ...state,
        isValidating: action.payload.isValidating,
        lastValidated: action.payload.isValidating ? null : new Date().toISOString()
      }
    }

    case CART_ACTIONS.UPDATE_ITEM_VALIDATION: {
      const { itemId, validation } = action.payload
      return {
        ...state,
        validationResults: {
          ...state.validationResults,
          [itemId]: validation
        }
      }
    }

    case CART_ACTIONS.SET_RECOMMENDATIONS: {
      return {
        ...state,
        recommendations: action.payload
      }
    }

    case CART_ACTIONS.UPDATE_ITEM_DETAILS: {
      const { itemId, details } = action.payload
      const updatedItems = state.items.map(item =>
        item.id === itemId
          ? { ...item, ...details, updatedAt: new Date().toISOString() }
          : item
      )

      return {
        ...state,
        items: updatedItems,
        updatedAt: new Date().toISOString()
      }
    }

    default:
      return state
  }
}

// Enhanced Cart Provider
export function EnhancedCartProvider({ children }) {
  const [state, dispatch] = useReducer(enhancedCartReducer, initialCartState)
  const [isHydrated, setIsHydrated] = useState(false)

  // Hydration effect
  useEffect(() => {
    setIsHydrated(true)
  }, [])

  // Load cart from localStorage
  useEffect(() => {
    if (!isHydrated) return

    try {
      const savedCart = localStorage.getItem('twl-enhanced-cart')
      if (savedCart) {
        const parsedCart = JSON.parse(savedCart)
        dispatch({
          type: CART_ACTIONS.LOAD_CART,
          payload: parsedCart
        })
      }
    } catch (error) {
      console.error('Error loading enhanced cart from localStorage:', error)
    }
  }, [isHydrated])

  // Save cart to localStorage
  useEffect(() => {
    if (!isHydrated) return

    try {
      localStorage.setItem('twl-enhanced-cart', JSON.stringify(state))
    } catch (error) {
      console.error('Error saving enhanced cart to localStorage:', error)
    }
  }, [state, isHydrated])

  // Enhanced product finder using enterprise adapter
  const findProduct = useCallback(async (productId) => {
    try {
      return await productAdapter.getProductForCart(productId)
    } catch (error) {
      console.error('❌ Error finding product:', error)
      return null
    }
  }, [])

  // Enhanced add item function
  const addItem = useCallback(async (productId, size, quantity = 1) => {
    console.log('🛒 Enhanced addItem called:', { productId, size, quantity })
    
    try {
      const product = await findProduct(productId)
      
      if (!product) {
        console.error('❌ Cannot add item: Product not found')
        return { success: false, error: 'Product not found' }
      }

      if (!product.inStock) {
        console.error('❌ Cannot add item: Product out of stock')
        return { success: false, error: 'Product is out of stock' }
      }

      if (product.stockLevel && product.stockLevel < quantity) {
        console.error('❌ Cannot add item: Insufficient stock')
        return { success: false, error: `Only ${product.stockLevel} items available` }
      }

      dispatch({
        type: CART_ACTIONS.ADD_ITEM,
        payload: { productId, size, quantity, product }
      })

      // Trigger recommendations update
      updateRecommendations()

      return { success: true, product }
    } catch (error) {
      console.error('❌ Error adding item to cart:', error)
      return { success: false, error: error.message }
    }
  }, [findProduct])

  // Validate all cart items
  const validateCartItems = useCallback(async () => {
    if (state.items.length === 0) return

    dispatch({ type: CART_ACTIONS.VALIDATE_ITEMS, payload: { isValidating: true } })

    try {
      for (const item of state.items) {
        const validation = await productAdapter.validateCartItem(item)
        
        dispatch({
          type: CART_ACTIONS.UPDATE_ITEM_VALIDATION,
          payload: { itemId: item.id, validation }
        })

        // Update item details if product data has changed
        if (validation.product && validation.product._source === 'enterprise') {
          dispatch({
            type: CART_ACTIONS.UPDATE_ITEM_DETAILS,
            payload: {
              itemId: item.id,
              details: {
                price: validation.currentPrice,
                stockLevel: validation.stockLevel,
                inStock: validation.isValid,
                _source: 'enterprise'
              }
            }
          })
        }
      }
    } catch (error) {
      console.error('❌ Error validating cart items:', error)
    } finally {
      dispatch({ type: CART_ACTIONS.VALIDATE_ITEMS, payload: { isValidating: false } })
    }
  }, [state.items])

  // Update recommendations based on cart items
  const updateRecommendations = useCallback(async () => {
    if (state.items.length === 0) {
      dispatch({ type: CART_ACTIONS.SET_RECOMMENDATIONS, payload: [] })
      return
    }

    try {
      const recommendations = await productAdapter.getCartRecommendations(state.items, 4)
      dispatch({ type: CART_ACTIONS.SET_RECOMMENDATIONS, payload: recommendations })
    } catch (error) {
      console.error('❌ Error updating recommendations:', error)
    }
  }, [state.items])

  // Auto-validate cart items periodically
  useEffect(() => {
    if (state.items.length > 0) {
      validateCartItems()
      
      // Set up periodic validation (every 5 minutes)
      const interval = setInterval(validateCartItems, 5 * 60 * 1000)
      return () => clearInterval(interval)
    }
  }, [state.items.length, validateCartItems])

  // Calculate enhanced totals
  const getEnhancedSummary = useCallback(() => {
    const subtotal = state.items.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    const originalSubtotal = state.items.reduce((sum, item) => {
      const originalPrice = item.originalPrice || item.price
      return sum + (originalPrice * item.quantity)
    }, 0)
    const totalSavings = originalSubtotal - subtotal
    const discountAmount = state.discount ? (subtotal * state.discount.percentage / 100) : 0
    const tax = (subtotal - discountAmount) * 0.16 // 16% IVA for Mexico
    const shipping = subtotal > 1500 ? 0 : 150 // Free shipping over $1500 MXN
    const total = subtotal - discountAmount + tax + shipping

    return {
      itemsCount: state.items.reduce((sum, item) => sum + item.quantity, 0),
      subtotal,
      originalSubtotal,
      totalSavings,
      discountAmount,
      tax,
      shipping,
      total,
      hasValidationIssues: Object.values(state.validationResults).some(v => !v.isValid),
      hasPriceChanges: Object.values(state.validationResults).some(v => v.hasPriceChange),
      hasStockIssues: Object.values(state.validationResults).some(v => v.hasStockIssue)
    }
  }, [state.items, state.discount, state.validationResults])

  // Enhanced cart value
  const value = {
    // State
    cart: state,
    items: state.items,
    summary: getEnhancedSummary(),
    validationResults: state.validationResults,
    recommendations: state.recommendations,
    isValidating: state.isValidating,
    lastValidated: state.lastValidated,

    // Actions
    addItem,
    removeItem: (itemId) => dispatch({ type: CART_ACTIONS.REMOVE_ITEM, payload: { itemId } }),
    updateQuantity: (itemId, quantity) => dispatch({ type: CART_ACTIONS.UPDATE_QUANTITY, payload: { itemId, quantity } }),
    clearCart: () => dispatch({ type: CART_ACTIONS.CLEAR_CART }),
    applyDiscount: (discount) => dispatch({ type: CART_ACTIONS.APPLY_DISCOUNT, payload: discount }),
    removeDiscount: () => dispatch({ type: CART_ACTIONS.REMOVE_DISCOUNT }),

    // Enhanced functions
    validateCartItems,
    updateRecommendations,
    findProduct,

    // Utilities
    getItemsCount: () => state.items.reduce((sum, item) => sum + item.quantity, 0),
    getSubtotal: () => state.items.reduce((sum, item) => sum + (item.price * item.quantity), 0),
    getTotal: () => getEnhancedSummary().total,

    // Adapter metrics
    getAdapterMetrics: () => productAdapter.getMetrics()
  }

  return (
    <EnhancedCartContext.Provider value={value}>
      {children}
    </EnhancedCartContext.Provider>
  )
}

// Enhanced cart hook
export function useEnhancedCart() {
  const context = useContext(EnhancedCartContext)
  if (context === undefined) {
    throw new Error('useEnhancedCart must be used within an EnhancedCartProvider')
  }
  return context
}

export default EnhancedCartContext
