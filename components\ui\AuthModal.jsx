'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Link from 'next/link'
import { useAuth } from '@/contexts/AuthContext'

export default function AuthModal({ isOpen, onClose, initialMode = 'login' }) {
  const { login, register } = useAuth()
  
  const [mode, setMode] = useState(initialMode) // 'login' or 'register'
  const [isLoading, setIsLoading] = useState(false)
  const [scrollPosition, setScrollPosition] = useState(0)
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    firstName: '',
    lastName: '',
    confirmPassword: ''
  })
  const [errors, setErrors] = useState({})

  // Reset form when modal opens/closes and capture scroll position
  useEffect(() => {
    if (isOpen) {
      // Capture current scroll position when modal opens
      setScrollPosition(window.scrollY)
      setMode(initialMode)
      setFormData({
        email: '',
        password: '',
        firstName: '',
        lastName: '',
        confirmPassword: ''
      })
      setErrors({})
    }
  }, [isOpen, initialMode])

  const handleInputChange = (e) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.email) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Invalid email'
    }

    if (!formData.password) {
      newErrors.password = 'Password is required'
    } else if (formData.password.length < 6) {
      newErrors.password = 'Password must be at least 6 characters'
    }

    if (mode === 'register') {
      if (!formData.firstName) {
        newErrors.firstName = 'First name is required'
      }
      if (!formData.lastName) {
        newErrors.lastName = 'Last name is required'
      }
      if (!formData.confirmPassword) {
        newErrors.confirmPassword = 'Please confirm your password'
      } else if (formData.password !== formData.confirmPassword) {
        newErrors.confirmPassword = 'Passwords do not match'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setIsLoading(true)

    try {
      if (mode === 'login') {
        await login(formData.email, formData.password)
      } else {
        await register({
          email: formData.email,
          password: formData.password,
          firstName: formData.firstName,
          lastName: formData.lastName
        })
      }
      onClose() // Close modal on success
    } catch (error) {
      setErrors({
        submit: error.message || 'An error occurred. Please try again.'
      })
    } finally {
      setIsLoading(false)
    }
  }

  // Modal backdrop variants
  const backdropVariants = {
    hidden: { opacity: 0 },
    visible: { opacity: 1 }
  }

  // Modal content variants with bouncing effect
  const modalVariants = {
    hidden: { 
      opacity: 0, 
      scale: 0.8,
      y: -50
    },
    visible: { 
      opacity: 1, 
      scale: 1,
      y: 0,
      transition: {
        type: "spring",
        damping: 25,
        stiffness: 500,
        duration: 0.3
      }
    },
    exit: { 
      opacity: 0, 
      scale: 0.8,
      y: -50,
      transition: {
        duration: 0.2
      }
    }
  }

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          className="modal-overlay p-4"
          variants={backdropVariants}
          initial="hidden"
          animate="visible"
          exit="hidden"
        >
          {/* Backdrop */}
          <motion.div
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal Content - Split Layout */}
          <motion.div
            className="modal-content relative w-full max-w-4xl bg-pure-white dark:bg-dark-gray rounded-2xl shadow-2xl overflow-hidden flex"
            variants={modalVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
            style={{
              marginTop: `${Math.max(scrollPosition + 100, 50)}px` // Position modal 100px below current scroll position
            }}

          >
            {/* Left Side - Fashion Image */}
            <div className="hidden md:flex md:w-1/2 relative overflow-hidden">
              {/* Background Image - Dior Fashion */}
              <div className="absolute inset-0">
                <img
                  src="/dior-1.jpg"
                  alt="TWL Fashion Lifestyle"
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Overlay with TWL Branding */}
              <div className="absolute inset-0 bg-black/30 flex flex-col justify-between p-8">
                {/* Top Logo - Just the round TWL logo in original position */}
                <div className="flex items-center">
                  <div className="w-10 h-10 flex items-center justify-center">
                    <img
                      src="/twl.svg"
                      alt="TWL Logo"
                      className="w-full h-full"
                    />
                  </div>
                </div>

                {/* Bottom Quote */}
                <div className="text-white">
                  <h2 className="text-3xl font-godber font-bold mb-4 leading-tight tracking-godber-md">
                    "Luxury meets streetwear"
                  </h2>
                  <p className="text-lg font-poppins opacity-90">
                    — The White Laces
                  </p>
                </div>
              </div>
            </div>

            {/* Right Side - Auth Form */}
            <div className="w-full md:w-1/2 flex flex-col relative">
              {/* Close Button */}
              <button
                onClick={onClose}
                className="absolute top-4 right-4 w-8 h-8 flex items-center justify-center rounded-full bg-neutral-100 dark:bg-neutral-800 hover:bg-neutral-200 dark:hover:bg-neutral-700 transition-colors z-10 focus:outline-none focus:ring-2 focus:ring-primary/20 focus:bg-neutral-200 dark:focus:bg-neutral-700"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>

              {/* Modal Header */}
              <div className="p-8 pb-0">
                <div className="text-center">
                  <h2 className="text-3xl font-godber font-bold text-pure-black dark:text-pure-white mb-2 flex items-center justify-center gap-3 tracking-godber-md">
                    {mode === 'login' ? 'Bienvenido de Vuelta' : (
                      <>
                        Únete a
                        <img
                          src="/logotwl.svg"
                          alt="The White Laces"
                          className="h-8 w-auto"
                        />
                      </>
                    )}
                  </h2>
                  <p className="text-text-gray dark:text-neutral-400 font-poppins">
                    {mode === 'login' ? (
                      <>¿No tienes cuenta? <button onClick={() => setMode('register')} className="text-pure-black dark:text-pure-white font-medium hover:text-primary transition-colors focus:outline-none focus:text-primary">Regístrate</button></>
                    ) : (
                      <>¿Ya tienes cuenta? <button onClick={() => setMode('login')} className="text-pure-black dark:text-pure-white font-medium hover:text-primary transition-colors focus:outline-none focus:text-primary">Inicia sesión</button></>
                    )}
                  </p>
                </div>
              </div>

              {/* Modal Body */}
              <div className="p-8 flex-1 overflow-y-auto">
              <form onSubmit={handleSubmit} className="space-y-6">
                {/* Register Fields */}
                {mode === 'register' && (
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <input
                        type="text"
                        name="firstName"
                        value={formData.firstName}
                        onChange={handleInputChange}
                        className={`w-full px-0 py-3 border-0 border-b-2 bg-transparent text-pure-black dark:text-pure-white placeholder-text-gray dark:placeholder-neutral-400 focus:outline-none focus:border-primary focus:shadow-none focus:ring-0 outline-none transition-all duration-300 font-poppins text-sm ${
                          errors.firstName ? 'border-red-500' : 'border-neutral-200 dark:border-neutral-700'
                        }`}
                        style={{ outline: 'none', boxShadow: 'none' }}
                        placeholder="Nombre"
                      />
                      {errors.firstName && (
                        <p className="text-red-500 text-xs">{errors.firstName}</p>
                      )}
                    </div>
                    
                    <div className="space-y-2">
                      <input
                        type="text"
                        name="lastName"
                        value={formData.lastName}
                        onChange={handleInputChange}
                        className={`w-full px-0 py-3 border-0 border-b-2 bg-transparent text-pure-black dark:text-pure-white placeholder-text-gray dark:placeholder-neutral-400 focus:outline-none focus:border-primary focus:shadow-none focus:ring-0 outline-none transition-all duration-300 font-poppins text-sm ${
                          errors.lastName ? 'border-red-500' : 'border-neutral-200 dark:border-neutral-700'
                        }`}
                        style={{ outline: 'none', boxShadow: 'none' }}
                        placeholder="Apellido"
                      />
                      {errors.lastName && (
                        <p className="text-red-500 text-xs">{errors.lastName}</p>
                      )}
                    </div>
                  </div>
                )}

                {/* Email */}
                <div className="space-y-2">
                  <input
                    type="email"
                    name="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    className={`w-full px-0 py-3 border-0 border-b-2 bg-transparent text-pure-black dark:text-pure-white placeholder-text-gray dark:placeholder-neutral-400 focus:outline-none focus:border-primary focus:shadow-none focus:ring-0 outline-none transition-all duration-300 font-poppins ${
                      errors.email ? 'border-red-500' : 'border-neutral-200 dark:border-neutral-700'
                    }`}
                    style={{ outline: 'none', boxShadow: 'none' }}
                    placeholder="Tu correo electrónico"
                  />
                  {errors.email && (
                    <p className="text-red-500 text-xs">{errors.email}</p>
                  )}
                </div>

                {/* Password */}
                <div className="space-y-2">
                  <input
                    type="password"
                    name="password"
                    value={formData.password}
                    onChange={handleInputChange}
                    className={`w-full px-0 py-3 border-0 border-b-2 bg-transparent text-pure-black dark:text-pure-white placeholder-text-gray dark:placeholder-neutral-400 focus:outline-none focus:border-primary focus:shadow-none focus:ring-0 outline-none transition-all duration-300 font-poppins ${
                      errors.password ? 'border-red-500' : 'border-neutral-200 dark:border-neutral-700'
                    }`}
                    style={{ outline: 'none', boxShadow: 'none' }}
                    placeholder="Contraseña"
                  />
                  {errors.password && (
                    <p className="text-red-500 text-xs">{errors.password}</p>
                  )}
                </div>

                {/* Confirm Password */}
                {mode === 'register' && (
                  <div className="space-y-2">
                    <input
                      type="password"
                      name="confirmPassword"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className={`w-full px-0 py-3 border-0 border-b-2 bg-transparent text-pure-black dark:text-pure-white placeholder-text-gray dark:placeholder-neutral-400 focus:outline-none focus:border-primary focus:shadow-none focus:ring-0 outline-none transition-all duration-300 font-poppins ${
                        errors.confirmPassword ? 'border-red-500' : 'border-neutral-200 dark:border-neutral-700'
                      }`}
                      style={{ outline: 'none', boxShadow: 'none' }}
                      placeholder="Confirmar contraseña"
                    />
                    {errors.confirmPassword && (
                      <p className="text-red-500 text-xs">{errors.confirmPassword}</p>
                    )}
                  </div>
                )}

                {/* Remember Me & Forgot Password */}
                {mode === 'login' && (
                  <div className="flex items-center justify-between">
                    <label className="flex items-center space-x-2 cursor-pointer">
                      <input
                        type="checkbox"
                        className="w-4 h-4 text-primary border-neutral-300 dark:border-neutral-600 rounded focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all duration-300"
                      />
                      <span className="text-sm text-text-gray dark:text-neutral-400 font-poppins">Recordarme</span>
                    </label>
                    <Link href="/forgot-password" className="text-sm text-pure-black dark:text-pure-white hover:text-primary transition-colors font-poppins focus:outline-none focus:text-primary">
                      ¿Olvidaste tu contraseña?
                    </Link>
                  </div>
                )}

                {/* Submit Error */}
                {errors.submit && (
                  <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                    <p className="text-red-600 dark:text-red-400 text-sm font-poppins">{errors.submit}</p>
                  </div>
                )}

                {/* Submit Button - TWL Style with BLACK text on colored backgrounds */}
                <button
                  type="submit"
                  disabled={isLoading}
                  className="w-full bg-primary text-pure-black py-3 rounded-xl font-poppins font-medium transition-all duration-300 hover:bg-dark-gray hover:text-pure-white disabled:opacity-50 disabled:cursor-not-allowed shadow-minimal hover:shadow-minimal-hover focus:outline-none focus:ring-2 focus:ring-primary/30 focus:bg-dark-gray focus:text-pure-white"
                >
                  {isLoading ? (
                    <motion.div
                      className="w-5 h-5 border-2 border-current border-t-transparent rounded-full mx-auto"
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
                    />
                  ) : (
                    mode === 'login' ? 'Iniciar Sesión' : 'Crear Cuenta'
                  )}
                </button>

                {/* Terms */}
                {mode === 'register' && (
                  <div className="text-center">
                    <p className="text-xs text-text-gray dark:text-neutral-400 font-poppins">
                      Al crear una cuenta, aceptas nuestros{' '}
                      <Link href="/terms" className="text-pure-black dark:text-pure-white hover:text-primary transition-colors focus:outline-none focus:text-primary">
                        Términos de Servicio
                      </Link>{' '}
                      y{' '}
                      <Link href="/privacy" className="text-pure-black dark:text-pure-white hover:text-primary transition-colors focus:outline-none focus:text-primary">
                        Política de Privacidad
                      </Link>
                    </p>
                  </div>
                )}
              </form>
              </div>
            </div>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
