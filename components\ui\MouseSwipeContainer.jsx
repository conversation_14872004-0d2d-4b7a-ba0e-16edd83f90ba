'use client'

import { useRef, useState, useEffect } from 'react'

/**
 * MouseSwipeContainer - Universal swipe/drag component for TWL website
 * Supports both mouse drag and touch swipe interactions
 * Can be used for product galleries, carousels, and any horizontal scrolling content
 */
export default function MouseSwipeContainer({ 
  children, 
  className = '', 
  onSwipeLeft, 
  onSwipeRight,
  enableMouseDrag = true,
  enableTouchSwipe = true,
  swipeThreshold = 50,
  dragSensitivity = 1,
  showScrollbar = false,
  ...props 
}) {
  const containerRef = useRef(null)
  const [isDragging, setIsDragging] = useState(false)
  const [startX, setStartX] = useState(0)
  const [scrollLeft, setScrollLeft] = useState(0)
  const [dragDistance, setDragDistance] = useState(0)

  // Mouse drag handlers
  const handleMouseDown = (e) => {
    if (!enableMouseDrag) return
    
    setIsDragging(true)
    setStartX(e.pageX - containerRef.current.offsetLeft)
    setScrollLeft(containerRef.current.scrollLeft)
    setDragDistance(0)
    
    // Prevent text selection during drag
    e.preventDefault()
    document.body.style.userSelect = 'none'
  }

  const handleMouseMove = (e) => {
    if (!isDragging || !enableMouseDrag) return
    
    e.preventDefault()
    const x = e.pageX - containerRef.current.offsetLeft
    const walk = (x - startX) * dragSensitivity
    const newScrollLeft = scrollLeft - walk
    
    containerRef.current.scrollLeft = newScrollLeft
    setDragDistance(Math.abs(walk))
  }

  const handleMouseUp = (e) => {
    if (!enableMouseDrag) return
    
    setIsDragging(false)
    document.body.style.userSelect = ''
    
    // Trigger swipe callbacks if drag distance exceeds threshold
    if (dragDistance > swipeThreshold) {
      const x = e.pageX - containerRef.current.offsetLeft
      const walk = x - startX
      
      if (walk > 0 && onSwipeRight) {
        onSwipeRight()
      } else if (walk < 0 && onSwipeLeft) {
        onSwipeLeft()
      }
    }
  }

  const handleMouseLeave = () => {
    if (isDragging) {
      setIsDragging(false)
      document.body.style.userSelect = ''
    }
  }

  // Touch handlers
  const [touchStartX, setTouchStartX] = useState(0)
  const [touchStartY, setTouchStartY] = useState(0)

  const handleTouchStart = (e) => {
    if (!enableTouchSwipe) return
    
    const touch = e.touches[0]
    setTouchStartX(touch.clientX)
    setTouchStartY(touch.clientY)
  }

  const handleTouchMove = (e) => {
    if (!enableTouchSwipe) return
    
    // Prevent default scrolling behavior for horizontal swipes
    const touch = e.touches[0]
    const deltaX = Math.abs(touch.clientX - touchStartX)
    const deltaY = Math.abs(touch.clientY - touchStartY)
    
    if (deltaX > deltaY) {
      e.preventDefault()
    }
  }

  const handleTouchEnd = (e) => {
    if (!enableTouchSwipe) return
    
    const touch = e.changedTouches[0]
    const deltaX = touch.clientX - touchStartX
    const deltaY = touch.clientY - touchStartY
    
    // Only trigger swipe if horizontal movement is greater than vertical
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > swipeThreshold) {
      if (deltaX > 0 && onSwipeRight) {
        onSwipeRight()
      } else if (deltaX < 0 && onSwipeLeft) {
        onSwipeLeft()
      }
    }
  }

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      document.body.style.userSelect = ''
    }
  }, [])

  return (
    <div
      ref={containerRef}
      className={`
        ${className}
        ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}
        ${!showScrollbar ? 'scrollbar-hide' : ''}
        select-none
      `}
      onMouseDown={handleMouseDown}
      onMouseMove={handleMouseMove}
      onMouseUp={handleMouseUp}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      style={{
        WebkitOverflowScrolling: 'touch',
        scrollBehavior: isDragging ? 'auto' : 'smooth'
      }}
      {...props}
    >
      {children}
    </div>
  )
}

/**
 * Usage Examples:
 * 
 * // Basic horizontal scrolling container
 * <MouseSwipeContainer className="flex gap-4 overflow-x-auto">
 *   {items.map(item => <div key={item.id}>{item.content}</div>)}
 * </MouseSwipeContainer>
 * 
 * // With swipe callbacks
 * <MouseSwipeContainer 
 *   onSwipeLeft={() => nextSlide()}
 *   onSwipeRight={() => prevSlide()}
 *   className="flex gap-4 overflow-x-auto"
 * >
 *   {slides}
 * </MouseSwipeContainer>
 * 
 * // Touch-only for mobile
 * <MouseSwipeContainer 
 *   enableMouseDrag={false}
 *   enableTouchSwipe={true}
 *   className="flex gap-4 overflow-x-auto"
 * >
 *   {content}
 * </MouseSwipeContainer>
 */
