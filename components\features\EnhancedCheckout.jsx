'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useCart } from '@/contexts/CartContext'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import AnimatedInput from '@/components/ui/AnimatedInput'
import Badge from '@/components/ui/Badge'
import AnimatedLoader from '@/components/ui/AnimatedLoader'
import { 
  CheckIcon, 
  CreditCardIcon, 
  TruckIcon, 
  UserIcon, 
  ExclamationTriangleIcon,
  ShoppingBagIcon 
} from '@heroicons/react/24/outline'

const checkoutSteps = [
  { id: 'shipping', name: 'Envío', icon: TruckIcon },
  { id: 'payment', name: 'Pago', icon: CreditCardIcon },
  { id: 'review', name: 'Revisar', icon: CheckIcon }
]

export default function EnhancedCheckout({ onOrderComplete, isProcessing }) {
  const router = useRouter()
  const { items: cartItems, getTotalPrice, getCartItems } = useCart()
  const [currentStep, setCurrentStep] = useState('shipping')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  const [cart, setCart] = useState(null)
  const [paymentIntent, setPaymentIntent] = useState(null)
  const [orderSummary, setOrderSummary] = useState(null)
  const [discountCode, setDiscountCode] = useState('')
  const [discountApplied, setDiscountApplied] = useState(null)

  const [checkoutData, setCheckoutData] = useState({
    shippingAddress: {
      firstName: '',
      lastName: '',
      email: '',
      phone: '',
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'México'
    },
    billingAddress: {
      sameAsShipping: true,
      firstName: '',
      lastName: '',
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'México'
    },
    paymentMethod: {
      type: 'card',
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      cardholderName: ''
    }
  })

  // Load cart and create payment intent
  useEffect(() => {
    loadCart()
  }, [cartItems])

  const loadCart = async () => {
    try {
      setLoading(true)

      // Get cart from CartContext with full product data
      const cartWithProducts = getCartItems()
      const cartData = {
        items: cartWithProducts,
        totalPrice: getTotalPrice(),
        totalItems: cartItems.length
      }

      setCart(cartData)

      // Create payment intent if cart has items
      if (cartWithProducts && cartWithProducts.length > 0) {
        const sessionId = localStorage.getItem('sessionId') || generateSessionId()
        await createPaymentIntent(sessionId)
      }
    } catch (error) {
      console.error('Error loading cart:', error)
      setError('Error loading cart')
    } finally {
      setLoading(false)
    }
  }

  const createPaymentIntent = async (sessionId) => {
    try {
      const response = await fetch('/api/payments/create-intent', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          currency: 'mxn',
          discountCode: discountCode || null
        })
      })
      
      const data = await response.json()
      
      if (response.ok) {
        setPaymentIntent(data.paymentIntent)
        setOrderSummary(data.orderSummary)
        if (data.orderSummary.discountDetails) {
          setDiscountApplied(data.orderSummary.discountDetails)
        }
      } else {
        setError(data.error || 'Error creating payment intent')
      }
    } catch (error) {
      console.error('Error creating payment intent:', error)
      setError('Error preparing payment')
    }
  }

  const generateSessionId = () => {
    const sessionId = `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    localStorage.setItem('sessionId', sessionId)
    return sessionId
  }

  const handleInputChange = (section, field, value) => {
    setCheckoutData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setError(null)
  }

  const validateStep = (step) => {
    switch (step) {
      case 'shipping':
        const shipping = checkoutData.shippingAddress
        return shipping.firstName && shipping.lastName && shipping.email && 
               shipping.street && shipping.city && shipping.state && shipping.zipCode
      
      case 'payment':
        const payment = checkoutData.paymentMethod
        return payment.cardNumber && payment.expiryMonth && payment.expiryYear && 
               payment.cvv && payment.cardholderName
      
      default:
        return true
    }
  }

  const nextStep = () => {
    if (!validateStep(currentStep)) {
      setError('Por favor completa todos los campos requeridos')
      return
    }

    const stepIndex = checkoutSteps.findIndex(step => step.id === currentStep)
    if (stepIndex < checkoutSteps.length - 1) {
      setCurrentStep(checkoutSteps[stepIndex + 1].id)
      setError(null)
    }
  }

  const prevStep = () => {
    const stepIndex = checkoutSteps.findIndex(step => step.id === currentStep)
    if (stepIndex > 0) {
      setCurrentStep(checkoutSteps[stepIndex - 1].id)
      setError(null)
    }
  }

  const applyDiscountCode = async () => {
    if (!discountCode.trim()) return
    
    try {
      setLoading(true)
      const sessionId = localStorage.getItem('sessionId')
      await createPaymentIntent(sessionId)
    } catch (error) {
      setError('Error applying discount code')
    } finally {
      setLoading(false)
    }
  }

  const handleCheckout = async () => {
    setLoading(true)
    setError(null)

    try {
      // Create order
      const sessionId = localStorage.getItem('sessionId')
      const orderResponse = await fetch('/api/orders', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          sessionId,
          shippingAddress: checkoutData.shippingAddress,
          billingAddress: checkoutData.billingAddress.sameAsShipping 
            ? checkoutData.shippingAddress 
            : checkoutData.billingAddress,
          paymentMethod: checkoutData.paymentMethod,
          paymentIntentId: paymentIntent?.id,
          discountCode: discountApplied?.code || null
        })
      })

      const orderData = await orderResponse.json()

      if (orderResponse.ok) {
        // Clear cart from localStorage
        localStorage.removeItem('sessionId')
        
        // Call success callback
        onOrderComplete?.(orderData.order)
        
        // Redirect to order confirmation
        router.push(`/order-confirmation?order=${orderData.order.orderNumber}`)
      } else {
        setError(orderData.error || 'Error creating order')
      }
    } catch (error) {
      console.error('Checkout error:', error)
      setError('Error processing order. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Redirect if cart is empty
  if (!loading && (!cart || !cart.items || cart.items.length === 0)) {
    return (
      <div className="text-center py-12">
        <ShoppingBagIcon className="h-16 w-16 text-warm-camel mx-auto mb-4" />
        <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
          Tu carrito está vacío
        </h2>
        <p className="text-warm-camel mb-6">
          Agrega algunos productos antes de proceder al checkout
        </p>
        <Button onClick={() => router.push('/search')}>
          Explorar Productos
        </Button>
      </div>
    )
  }

  if (loading && !cart) {
    return (
      <div className="flex justify-center py-12">
        <AnimatedLoader />
      </div>
    )
  }

  return (
    <div className="max-w-6xl mx-auto">
      {/* Progress Steps */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          {checkoutSteps.map((step, index) => {
            const isActive = step.id === currentStep
            const isCompleted = checkoutSteps.findIndex(s => s.id === currentStep) > index
            const StepIcon = step.icon
            
            return (
              <div key={step.id} className="flex items-center">
                <div className={`
                  flex items-center justify-center w-12 h-12 rounded-full border-2 transition-all
                  ${isActive 
                    ? 'border-lime-500 bg-lime-500 text-black' 
                    : isCompleted 
                    ? 'border-green-500 bg-green-500 text-white'
                    : 'border-warm-camel text-warm-camel'
                  }
                `}>
                  {isCompleted ? (
                    <CheckIcon className="h-6 w-6" />
                  ) : (
                    <StepIcon className="h-6 w-6" />
                  )}
                </div>
                <span className={`ml-3 font-medium ${
                  isActive ? 'text-lime-500' : isCompleted ? 'text-green-500' : 'text-warm-camel'
                }`}>
                  {step.name}
                </span>
                
                {index < checkoutSteps.length - 1 && (
                  <div className={`w-16 h-0.5 mx-4 ${
                    isCompleted ? 'bg-green-500' : 'bg-warm-camel/30'
                  }`} />
                )}
              </div>
            )
          })}
        </div>
      </div>

      {/* Error Display */}
      {error && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg flex items-center gap-3"
        >
          <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
          <span className="text-red-500">{error}</span>
        </motion.div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content */}
        <div className="lg:col-span-2">
          <Card>
            <CardContent className="p-6">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {currentStep === 'shipping' && (
                    <ShippingStep 
                      data={checkoutData.shippingAddress}
                      onChange={(field, value) => handleInputChange('shippingAddress', field, value)}
                    />
                  )}
                  
                  {currentStep === 'payment' && (
                    <PaymentStep 
                      data={checkoutData.paymentMethod}
                      onChange={(field, value) => handleInputChange('paymentMethod', field, value)}
                    />
                  )}
                  
                  {currentStep === 'review' && (
                    <ReviewStep 
                      checkoutData={checkoutData}
                      cart={cart}
                      orderSummary={orderSummary}
                    />
                  )}
                </motion.div>
              </AnimatePresence>

              {/* Navigation Buttons */}
              <div className="flex justify-between mt-8">
                <Button
                  variant="outline"
                  onClick={prevStep}
                  disabled={currentStep === 'shipping'}
                >
                  Anterior
                </Button>
                
                {currentStep === 'review' ? (
                  <Button
                    onClick={handleCheckout}
                    disabled={loading || isProcessing}
                    className="bg-lime-500 hover:bg-lime-600 text-black"
                  >
                    {loading || isProcessing ? (
                      <>
                        <AnimatedLoader className="mr-2" />
                        Procesando...
                      </>
                    ) : (
                      'Confirmar Pedido'
                    )}
                  </Button>
                ) : (
                  <Button
                    onClick={nextStep}
                    className="bg-lime-500 hover:bg-lime-600 text-black"
                  >
                    Siguiente
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Order Summary Sidebar */}
        <div className="lg:col-span-1">
          <OrderSummary 
            cart={cart}
            orderSummary={orderSummary}
            discountCode={discountCode}
            discountApplied={discountApplied}
            onDiscountCodeChange={setDiscountCode}
            onApplyDiscount={applyDiscountCode}
            loading={loading}
          />
        </div>
      </div>
    </div>
  )
}

// Shipping Step Component
function ShippingStep({ data, onChange }) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
          Dirección de Envío
        </h3>
        <p className="text-warm-camel">
          Ingresa la dirección donde quieres recibir tu pedido
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AnimatedInput
          label="Nombre"
          value={data.firstName}
          onChange={(e) => onChange('firstName', e.target.value)}
          required
        />
        <AnimatedInput
          label="Apellido"
          value={data.lastName}
          onChange={(e) => onChange('lastName', e.target.value)}
          required
        />
      </div>

      <AnimatedInput
        label="Email"
        type="email"
        value={data.email}
        onChange={(e) => onChange('email', e.target.value)}
        required
      />

      <AnimatedInput
        label="Teléfono"
        type="tel"
        value={data.phone}
        onChange={(e) => onChange('phone', e.target.value)}
        required
      />

      <AnimatedInput
        label="Dirección"
        placeholder="Calle y número"
        value={data.street}
        onChange={(e) => onChange('street', e.target.value)}
        required
      />

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AnimatedInput
          label="Ciudad"
          value={data.city}
          onChange={(e) => onChange('city', e.target.value)}
          required
        />
        <AnimatedInput
          label="Estado"
          value={data.state}
          onChange={(e) => onChange('state', e.target.value)}
          required
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AnimatedInput
          label="Código Postal"
          value={data.zipCode}
          onChange={(e) => onChange('zipCode', e.target.value)}
          required
        />
        <AnimatedInput
          label="País"
          value={data.country}
          onChange={(e) => onChange('country', e.target.value)}
          disabled
        />
      </div>
    </div>
  )
}

// Payment Step Component
function PaymentStep({ data, onChange }) {
  const formatCardNumber = (value) => {
    const cleaned = value.replace(/\D/g, '')
    const formatted = cleaned.replace(/(\d{4})(?=\d)/g, '$1 ')
    return formatted.slice(0, 19) // Max 16 digits + 3 spaces
  }

  const handleCardNumberChange = (e) => {
    const formatted = formatCardNumber(e.target.value)
    onChange('cardNumber', formatted)
  }

  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
          Método de Pago
        </h3>
        <p className="text-warm-camel">
          Ingresa los datos de tu tarjeta de crédito o débito
        </p>
      </div>

      <AnimatedInput
        label="Nombre del Titular"
        placeholder="Nombre como aparece en la tarjeta"
        value={data.cardholderName}
        onChange={(e) => onChange('cardholderName', e.target.value)}
        required
      />

      <AnimatedInput
        label="Número de Tarjeta"
        placeholder="1234 5678 9012 3456"
        value={data.cardNumber}
        onChange={handleCardNumberChange}
        required
      />

      <div className="grid grid-cols-3 gap-4">
        <div>
          <label className="block text-sm font-medium text-warm-camel mb-2">
            Mes
          </label>
          <select
            value={data.expiryMonth}
            onChange={(e) => onChange('expiryMonth', e.target.value)}
            className="w-full p-3 border border-soft-steel-gray dark:border-mist-gray rounded-lg bg-white dark:bg-fog-black text-forest-emerald dark:text-light-cloud-gray focus:ring-2 focus:ring-lime-500 focus:border-transparent"
            required
          >
            <option value="">Mes</option>
            {Array.from({ length: 12 }, (_, i) => (
              <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                {String(i + 1).padStart(2, '0')}
              </option>
            ))}
          </select>
        </div>

        <div>
          <label className="block text-sm font-medium text-warm-camel mb-2">
            Año
          </label>
          <select
            value={data.expiryYear}
            onChange={(e) => onChange('expiryYear', e.target.value)}
            className="w-full p-3 border border-soft-steel-gray dark:border-mist-gray rounded-lg bg-white dark:bg-fog-black text-forest-emerald dark:text-light-cloud-gray focus:ring-2 focus:ring-lime-500 focus:border-transparent"
            required
          >
            <option value="">Año</option>
            {Array.from({ length: 10 }, (_, i) => (
              <option key={i} value={new Date().getFullYear() + i}>
                {new Date().getFullYear() + i}
              </option>
            ))}
          </select>
        </div>

        <AnimatedInput
          label="CVV"
          placeholder="123"
          value={data.cvv}
          onChange={(e) => onChange('cvv', e.target.value.slice(0, 4))}
          maxLength={4}
          required
        />
      </div>

      {/* Demo Card Info */}
      <div className="p-4 bg-lime-500/10 border border-lime-500/30 rounded-lg">
        <p className="text-sm font-medium text-forest-emerald dark:text-light-cloud-gray mb-2">
          💳 Tarjeta de prueba:
        </p>
        <div className="text-sm text-warm-camel space-y-1">
          <p><strong>Número:</strong> 4242 4242 4242 4242</p>
          <p><strong>Fecha:</strong> 12/27 • <strong>CVV:</strong> 123</p>
          <p><strong>Titular:</strong> Test User</p>
        </div>
      </div>
    </div>
  )
}

// Review Step Component
function ReviewStep({ checkoutData, cart, orderSummary }) {
  return (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
          Revisar Pedido
        </h3>
        <p className="text-warm-camel">
          Verifica que toda la información sea correcta antes de confirmar
        </p>
      </div>

      {/* Order Items */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-4">
            Productos ({cart?.items?.length || 0})
          </h4>
          <div className="space-y-4">
            {cart?.items?.map((item, index) => (
              <div key={index} className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gradient-to-br from-warm-camel/20 to-lime-500/20 rounded-lg flex items-center justify-center overflow-hidden">
                  {item.product?.image || item.product?.images?.[0] ? (
                    <img
                      src={item.product.image || item.product.images[0]}
                      alt={item.product.name}
                      className="w-full h-full object-cover rounded-lg"
                    />
                  ) : (
                    <div className="w-8 h-8 text-gray-400">
                      <svg fill="currentColor" viewBox="0 0 24 24">
                        <path d="M21 16v-2l-8-5V3.5c0-.83-.67-1.5-1.5-1.5S10 2.67 10 3.5V9l-8 5v2c0 .55.45 1 1 1h16c.55 0 1-.45 1-1zM12 10.5l6 3.75H6l6-3.75z"/>
                      </svg>
                    </div>
                  )}
                </div>
                <div className="flex-1">
                  <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                    {item.product?.name || 'Product'}
                  </p>
                  <p className="text-sm text-warm-camel">
                    Talla {item.size} • Cantidad: {item.quantity}
                  </p>
                  {item.product?.brand && (
                    <p className="text-xs text-warm-camel">
                      {item.product.brand.name}
                    </p>
                  )}
                </div>
                <p className="font-medium text-lime-500">
                  ${((item.unitPrice || 0) * item.quantity).toLocaleString()} MXN
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Shipping Address */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
            Dirección de Envío
          </h4>
          <div className="text-sm text-warm-camel space-y-1">
            <p className="font-medium">{checkoutData.shippingAddress.firstName} {checkoutData.shippingAddress.lastName}</p>
            <p>{checkoutData.shippingAddress.street}</p>
            <p>{checkoutData.shippingAddress.city}, {checkoutData.shippingAddress.state} {checkoutData.shippingAddress.zipCode}</p>
            <p>{checkoutData.shippingAddress.country}</p>
            <p>{checkoutData.shippingAddress.phone}</p>
            <p>{checkoutData.shippingAddress.email}</p>
          </div>
        </CardContent>
      </Card>

      {/* Payment Method */}
      <Card>
        <CardContent className="p-4">
          <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
            Método de Pago
          </h4>
          <div className="text-sm text-warm-camel">
            <p className="flex items-center gap-2">
              <CreditCardIcon className="h-4 w-4" />
              **** **** **** {checkoutData.paymentMethod.cardNumber.replace(/\s/g, '').slice(-4)}
            </p>
            <p>{checkoutData.paymentMethod.cardholderName}</p>
            <p>Expira: {checkoutData.paymentMethod.expiryMonth}/{checkoutData.paymentMethod.expiryYear}</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}

// Order Summary Component
function OrderSummary({
  cart,
  orderSummary,
  discountCode,
  discountApplied,
  onDiscountCodeChange,
  onApplyDiscount,
  loading
}) {
  return (
    <Card className="sticky top-6">
      <CardContent className="p-6">
        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
          Resumen del Pedido
        </h3>

        {/* Items Summary */}
        <div className="space-y-3 mb-6">
          {cart?.items?.map((item, index) => (
            <div key={index} className="flex justify-between text-sm">
              <div className="flex-1">
                <p className="text-forest-emerald dark:text-light-cloud-gray">
                  {item.product?.name || 'Product'} × {item.quantity}
                </p>
                <p className="text-xs text-warm-camel">
                  Talla {item.size}
                </p>
              </div>
              <p className="text-forest-emerald dark:text-light-cloud-gray">
                ${((item.unitPrice || 0) * item.quantity).toLocaleString()}
              </p>
            </div>
          ))}
        </div>

        {/* Discount Code */}
        <div className="mb-6">
          <label className="block text-sm font-medium text-warm-camel mb-2">
            Código de Descuento
          </label>
          <div className="flex gap-2">
            <input
              type="text"
              value={discountCode}
              onChange={(e) => onDiscountCodeChange(e.target.value)}
              placeholder="Ingresa código"
              className="flex-1 p-2 border border-soft-steel-gray dark:border-mist-gray rounded-lg bg-white dark:bg-fog-black text-forest-emerald dark:text-light-cloud-gray text-sm focus:ring-2 focus:ring-lime-500 focus:border-transparent"
            />
            <Button
              onClick={onApplyDiscount}
              disabled={!discountCode.trim() || loading}
              size="sm"
              variant="outline"
            >
              Aplicar
            </Button>
          </div>
          {discountApplied && (
            <p className="text-xs text-green-500 mt-1">
              ✓ {discountApplied.description} aplicado
            </p>
          )}
        </div>

        {/* Order Totals */}
        {orderSummary && (
          <div className="space-y-3 border-t border-warm-camel/20 pt-4">
            <div className="flex justify-between text-sm">
              <span className="text-warm-camel">Subtotal:</span>
              <span className="text-forest-emerald dark:text-light-cloud-gray">
                ${orderSummary.subtotal.toLocaleString()} MXN
              </span>
            </div>

            {orderSummary.discountAmount > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-warm-camel">Descuento:</span>
                <span className="text-green-500">
                  -${orderSummary.discountAmount.toLocaleString()} MXN
                </span>
              </div>
            )}

            <div className="flex justify-between text-sm">
              <span className="text-warm-camel">Envío:</span>
              <span className="text-forest-emerald dark:text-light-cloud-gray">
                {orderSummary.shippingAmount === 0 ? 'Gratis' : `$${orderSummary.shippingAmount.toLocaleString()} MXN`}
              </span>
            </div>

            <div className="flex justify-between text-sm">
              <span className="text-warm-camel">IVA (16%):</span>
              <span className="text-forest-emerald dark:text-light-cloud-gray">
                ${orderSummary.taxAmount.toLocaleString()} MXN
              </span>
            </div>

            <div className="border-t border-warm-camel/20 pt-3 flex justify-between font-semibold">
              <span className="text-forest-emerald dark:text-light-cloud-gray">Total:</span>
              <span className="text-lime-500 text-lg">
                ${orderSummary.totalAmount.toLocaleString()} MXN
              </span>
            </div>
          </div>
        )}

        {/* Security Badge */}
        <div className="mt-6 p-3 bg-green-500/10 border border-green-500/30 rounded-lg">
          <div className="flex items-center gap-2">
            <CheckIcon className="h-4 w-4 text-green-500" />
            <span className="text-xs text-green-500 font-medium">
              Pago 100% seguro y encriptado
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
