'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

export default function RecentlyViewed() {
  const { recentlyViewed, clearRecentlyViewed } = useUserPreferences()
  const { addToCart } = useCart()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()

  const handleAddToCart = (product) => {
    // For recently viewed, we'll add with a default size
    // In a real app, this might open a size selector modal
    const defaultSize = product.sizes?.[0] || 'M'
    addToCart(product, defaultSize, 1)
  }

  const handleWishlistToggle = (product) => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product)
    }
  }

  const formatTimeAgo = (dateString) => {
    const date = new Date(dateString)
    const now = new Date()
    const diffInHours = Math.floor((now - date) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Hace menos de 1 hora'
    if (diffInHours < 24) return `Hace ${diffInHours} hora${diffInHours > 1 ? 's' : ''}`
    
    const diffInDays = Math.floor(diffInHours / 24)
    if (diffInDays < 7) return `Hace ${diffInDays} día${diffInDays > 1 ? 's' : ''}`
    
    const diffInWeeks = Math.floor(diffInDays / 7)
    return `Hace ${diffInWeeks} semana${diffInWeeks > 1 ? 's' : ''}`
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
            Vistos Recientemente
          </h2>
          <p className="text-warm-camel mt-1">
            Productos que has visto en los últimos 30 días
          </p>
        </div>
        
        {recentlyViewed.length > 0 && (
          <AnimatedButton
            variant="ghost"
            onClick={() => {
              if (confirm('¿Estás seguro de que quieres limpiar tu historial?')) {
                clearRecentlyViewed()
              }
            }}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
              </svg>
            }
          >
            Limpiar Historial
          </AnimatedButton>
        )}
      </div>

      {/* Recently Viewed Products */}
      {recentlyViewed.length > 0 ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <AnimatePresence>
            {recentlyViewed.map((product, index) => (
              <motion.div
                key={`${product.id}-${index}`}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card variant="glass" className="group hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-0">
                    {/* Product Image */}
                    <div className="relative overflow-hidden rounded-t-lg">
                      <TransitionLink href={`/product/${product.id}`}>
                        <motion.img
                          src={product.image}
                          alt={product.name}
                          className="w-full h-48 object-cover group-hover:scale-105 transition-transform duration-300"
                          whileHover={{ scale: 1.05 }}
                        />
                      </TransitionLink>
                      
                      {/* Wishlist Button */}
                      <motion.button
                        onClick={() => handleWishlistToggle(product)}
                        className={`absolute top-3 right-3 p-2 rounded-full backdrop-blur-sm transition-all duration-200 ${
                          isInWishlist(product.id)
                            ? 'bg-rich-gold text-forest-emerald'
                            : 'bg-white/20 text-white hover:bg-white/30'
                        }`}
                        whileHover={{ scale: 1.1 }}
                        whileTap={{ scale: 0.9 }}
                      >
                        <svg className="w-4 h-4" fill={isInWishlist(product.id) ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      </motion.button>

                      {/* View Time Badge */}
                      <div className="absolute bottom-3 left-3">
                        <span className="bg-black/50 text-white text-xs px-2 py-1 rounded-full backdrop-blur-sm">
                          {formatTimeAgo(product.viewedAt)}
                        </span>
                      </div>
                    </div>

                    {/* Product Info */}
                    <div className="p-4 space-y-3">
                      <div>
                        <TransitionLink href={`/product/${product.id}`}>
                          <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray group-hover:text-rich-gold transition-colors duration-200 line-clamp-2">
                            {product.name}
                          </h3>
                        </TransitionLink>
                        <p className="text-warm-camel text-sm">{product.brand}</p>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <p className="text-lg font-bold text-forest-emerald dark:text-light-cloud-gray">
                            ${product.price.toLocaleString()} MXN
                          </p>
                          {product.originalPrice && product.originalPrice > product.price && (
                            <p className="text-sm text-warm-camel line-through">
                              ${product.originalPrice.toLocaleString()} MXN
                            </p>
                          )}
                        </div>

                        {/* Product Tags */}
                        <div className="flex flex-wrap gap-1">
                          {product.tags?.slice(0, 2).map((tag) => (
                            <span
                              key={tag}
                              className="text-xs px-2 py-1 bg-warm-camel/20 text-warm-camel rounded-full"
                            >
                              {tag}
                            </span>
                          ))}
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 pt-2">
                        <TransitionLink href={`/product/${product.id}`} className="flex-1">
                          <AnimatedButton
                            variant="outline"
                            size="sm"
                            className="w-full"
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                              </svg>
                            }
                          >
                            Ver Detalles
                          </AnimatedButton>
                        </TransitionLink>

                        <AnimatedButton
                          variant="primary"
                          size="sm"
                          onClick={() => handleAddToCart(product)}
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                            </svg>
                          }
                        >
                          Agregar
                        </AnimatedButton>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </div>
      ) : (
        /* Empty State */
        <Card variant="glass">
          <CardContent className="p-12 text-center">
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.5 }}
            >
              <div className="text-6xl mb-6">👀</div>
              <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-3">
                No has visto productos recientemente
              </h3>
              <p className="text-warm-camel mb-8 max-w-md mx-auto">
                Explora nuestra colección de zapatos de lujo y tus productos vistos aparecerán aquí automáticamente
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <TransitionLink href="/shop">
                  <AnimatedButton
                    variant="primary"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    }
                  >
                    Explorar Tienda
                  </AnimatedButton>
                </TransitionLink>
                
                <TransitionLink href="/limited-editions">
                  <AnimatedButton
                    variant="outline"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    }
                  >
                    Ediciones Limitadas
                  </AnimatedButton>
                </TransitionLink>
              </div>
            </motion.div>
          </CardContent>
        </Card>
      )}

      {/* Statistics */}
      {recentlyViewed.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
        >
          <Card variant="glass">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                Estadísticas de Navegación
              </h3>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="text-2xl font-bold text-rich-gold">
                    {recentlyViewed.length}
                  </div>
                  <div className="text-warm-camel text-sm">
                    Productos Vistos
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-rich-gold">
                    {new Set(recentlyViewed.map(p => p.brand)).size}
                  </div>
                  <div className="text-warm-camel text-sm">
                    Marcas Exploradas
                  </div>
                </div>
                
                <div className="text-center">
                  <div className="text-2xl font-bold text-rich-gold">
                    {new Set(recentlyViewed.map(p => p.category)).size}
                  </div>
                  <div className="text-warm-camel text-sm">
                    Categorías Vistas
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </div>
  )
}
