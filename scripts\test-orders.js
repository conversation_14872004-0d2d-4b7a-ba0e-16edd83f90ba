#!/usr/bin/env node

/**
 * TWL Order System Testing Script
 * Tests the complete order flow from cart to confirmation
 */

const https = require('https');
const http = require('http');

class OrderTester {
  constructor(baseUrl = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
    this.sessionId = `test_session_${Date.now()}`;
    this.results = [];
  }

  async makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const url = new URL(path, this.baseUrl);
      const isHttps = url.protocol === 'https:';
      const client = isHttps ? https : http;

      const options = {
        hostname: url.hostname,
        port: url.port || (isHttps ? 443 : 80),
        path: url.pathname + url.search,
        method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'TWL-Order-Tester/1.0'
        }
      };

      if (data && method !== 'GET') {
        const jsonData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(jsonData);
      }

      const req = client.request(options, (res) => {
        let body = '';
        
        res.on('data', (chunk) => {
          body += chunk;
        });

        res.on('end', () => {
          try {
            const jsonBody = body ? JSON.parse(body) : {};
            resolve({
              status: res.statusCode,
              headers: res.headers,
              body: jsonBody
            });
          } catch (error) {
            resolve({
              status: res.statusCode,
              headers: res.headers,
              body: body,
              parseError: error.message
            });
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (data && method !== 'GET') {
        req.write(JSON.stringify(data));
      }

      req.end();
    });
  }

  async testEndpoint(name, path, expectedStatus = 200, method = 'GET', data = null) {
    console.log(`🔍 Testing ${name}...`);
    
    try {
      const startTime = Date.now();
      const response = await this.makeRequest(path, method, data);
      const duration = Date.now() - startTime;

      const success = response.status === expectedStatus;
      const result = {
        name,
        path,
        method,
        expectedStatus,
        actualStatus: response.status,
        duration,
        success,
        response: response.body
      };

      this.results.push(result);

      if (success) {
        console.log(`✅ ${name} - ${response.status} (${duration}ms)`);
        return response.body;
      } else {
        console.log(`❌ ${name} - Expected ${expectedStatus}, got ${response.status} (${duration}ms)`);
        if (response.body && response.body.error) {
          console.log(`   Error: ${response.body.error}`);
        }
        return null;
      }
    } catch (error) {
      console.log(`❌ ${name} - Request failed: ${error.message}`);
      const result = {
        name,
        path,
        method,
        expectedStatus,
        actualStatus: 'ERROR',
        duration: 0,
        success: false,
        error: error.message
      };
      this.results.push(result);
      return null;
    }
  }

  async runOrderFlowTest() {
    console.log('🚀 Starting TWL Order Flow Test\n');
    console.log(`Base URL: ${this.baseUrl}`);
    console.log(`Session ID: ${this.sessionId}\n`);

    // Step 1: Add items to cart
    console.log('📦 Step 1: Adding items to cart...');
    
    const cartItem = {
      sessionId: this.sessionId,
      productId: 'test-product-1',
      size: '42',
      color: 'Blanco',
      quantity: 1
    };

    const addToCartResult = await this.testEndpoint(
      'Add Item to Cart',
      '/api/cart',
      201,
      'POST',
      cartItem
    );

    if (!addToCartResult) {
      console.log('❌ Cannot proceed without cart items');
      return;
    }

    // Step 2: Get cart contents
    console.log('\n🛒 Step 2: Retrieving cart...');
    
    const cartResult = await this.testEndpoint(
      'Get Cart',
      `/api/cart?sessionId=${this.sessionId}`
    );

    // Step 3: Create payment intent
    console.log('\n💳 Step 3: Creating payment intent...');
    
    const paymentIntentData = {
      sessionId: this.sessionId,
      currency: 'mxn'
    };

    const paymentIntentResult = await this.testEndpoint(
      'Create Payment Intent',
      '/api/payments/create-intent',
      200,
      'POST',
      paymentIntentData
    );

    // Step 4: Get payment methods
    console.log('\n💰 Step 4: Getting payment methods...');
    
    await this.testEndpoint(
      'Get Payment Methods',
      '/api/payments/create-intent?country=MX'
    );

    // Step 5: Create order
    console.log('\n📋 Step 5: Creating order...');
    
    const orderData = {
      sessionId: this.sessionId,
      shippingAddress: {
        firstName: 'Juan',
        lastName: 'Pérez',
        email: '<EMAIL>',
        phone: '+52 55 1234 5678',
        street: 'Av. Reforma 123',
        city: 'Ciudad de México',
        state: 'CDMX',
        zipCode: '06600',
        country: 'México'
      },
      billingAddress: {
        firstName: 'Juan',
        lastName: 'Pérez',
        street: 'Av. Reforma 123',
        city: 'Ciudad de México',
        state: 'CDMX',
        zipCode: '06600',
        country: 'México'
      },
      paymentMethod: {
        type: 'card',
        cardNumber: '****************',
        expiryMonth: '12',
        expiryYear: '2027',
        cvv: '123',
        cardholderName: 'Juan Pérez'
      },
      paymentIntentId: paymentIntentResult?.paymentIntent?.id || 'test_intent'
    };

    const orderResult = await this.testEndpoint(
      'Create Order',
      '/api/orders',
      201,
      'POST',
      orderData
    );

    if (orderResult && orderResult.order) {
      const orderId = orderResult.order.id;
      
      // Step 6: Get order details
      console.log('\n📄 Step 6: Retrieving order details...');
      
      await this.testEndpoint(
        'Get Order Details',
        `/api/orders/${orderId}`
      );

      // Step 7: Update order status (admin function)
      console.log('\n🔄 Step 7: Updating order status...');
      
      const updateData = {
        status: 'processing',
        notes: 'Order is being prepared'
      };

      await this.testEndpoint(
        'Update Order Status',
        `/api/orders/${orderId}`,
        200,
        'PUT',
        updateData
      );
    }

    // Step 8: Test order listing
    console.log('\n📋 Step 8: Testing order listing...');
    
    await this.testEndpoint(
      'List Orders',
      '/api/orders?userId=test-user&limit=10'
    );

    this.printSummary();
  }

  async runErrorTests() {
    console.log('\n🧪 Running Error Handling Tests...\n');

    // Test empty cart checkout
    await this.testEndpoint(
      'Empty Cart Checkout',
      '/api/orders',
      400,
      'POST',
      {
        sessionId: 'empty_session',
        shippingAddress: {},
        billingAddress: {},
        paymentMethod: {}
      }
    );

    // Test invalid product in cart
    await this.testEndpoint(
      'Invalid Product in Cart',
      '/api/cart',
      404,
      'POST',
      {
        sessionId: this.sessionId,
        productId: 'non-existent-product',
        size: '42',
        quantity: 1
      }
    );

    // Test missing required fields
    await this.testEndpoint(
      'Missing Required Fields',
      '/api/orders',
      400,
      'POST',
      {
        sessionId: this.sessionId
        // Missing required fields
      }
    );

    // Test invalid order ID
    await this.testEndpoint(
      'Invalid Order ID',
      '/api/orders/invalid-order-id',
      404
    );
  }

  printSummary() {
    console.log('\n📊 Order Flow Test Summary\n');
    
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n`);

    if (failedTests > 0) {
      console.log('❌ Failed Tests:');
      this.results
        .filter(r => !r.success)
        .forEach(result => {
          console.log(`   - ${result.name}: ${result.actualStatus} (expected ${result.expectedStatus})`);
          if (result.error) {
            console.log(`     Error: ${result.error}`);
          }
        });
      console.log('');
    }

    // Performance summary
    const avgDuration = this.results
      .filter(r => r.duration > 0)
      .reduce((sum, r) => sum + r.duration, 0) / this.results.filter(r => r.duration > 0).length;
    
    if (avgDuration) {
      console.log(`⚡ Average Response Time: ${avgDuration.toFixed(0)}ms`);
    }

    // Recommendations
    console.log('\n💡 Next Steps:');
    
    if (failedTests === 0) {
      console.log('   🎉 All tests passed! Order system is working correctly.');
      console.log('   🚀 Ready for production deployment.');
    } else {
      console.log('   🔧 Fix failing endpoints before proceeding.');
      console.log('   📋 Check database connection and migrations.');
      console.log('   🔍 Review error messages above for specific issues.');
    }
    
    console.log('   📊 Run database setup: npm run db:migrate && npm run db:seed');
    console.log('   🛍️ Test checkout flow: npm run dev');
    console.log('   🔄 Restart development server if needed');
  }
}

// Run tests
async function main() {
  const args = process.argv.slice(2);
  const baseUrl = args[0] || 'http://localhost:3001';
  
  const tester = new OrderTester(baseUrl);
  
  await tester.runOrderFlowTest();
  await tester.runErrorTests();
}

main().catch(error => {
  console.error('❌ Order test runner failed:', error);
  process.exit(1);
});
