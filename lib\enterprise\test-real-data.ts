/**
 * TWL Enterprise System - Real Data Testing
 * Comprehensive testing with actual product data from C:\2.MY_APP\TWL\V2\public\products
 */

import { initializeTWLSystem, shutdownTWLSystem } from './TWLEnterpriseSystem'
import fs from 'fs'
import path from 'path'

interface TestResults {
  totalProducts: number
  totalImages: number
  totalVideos: number
  scanDuration: number
  averageLoadTime: number
  cacheHitRate: number
  errors: string[]
  sampleProducts: any[]
}

async function testWithRealData(): Promise<TestResults> {
  console.log('🚀 Testing TWL Enterprise System with Real Product Data')
  console.log('=' .repeat(60))

  const results: TestResults = {
    totalProducts: 0,
    totalImages: 0,
    totalVideos: 0,
    scanDuration: 0,
    averageLoadTime: 0,
    cacheHitRate: 0,
    errors: [],
    sampleProducts: []
  }

  try {
    // 1. Initialize system with real data
    console.log('1. 🔄 Initializing enterprise system with real data...')
    const startTime = Date.now()
    
    const system = await initializeTWLSystem({
      productsBasePath: 'public/products',
      enableCache: true,
      enableAutoScan: true,
      environment: 'development',
      logLevel: 'info',
      enableMetrics: true
    })

    const initDuration = Date.now() - startTime
    console.log(`✅ System initialized in ${initDuration}ms`)

    // 2. Check system health
    console.log('\n2. 🏥 Checking system health...')
    const health = system.getHealth()
    console.log(`System Status: ${health.status}`)
    console.log(`Products Loaded: ${health.metrics.productsLoaded}`)
    console.log(`Cache Hit Rate: ${health.metrics.cacheHitRate}%`)
    console.log(`Memory Usage: ${health.metrics.memoryUsage}MB`)

    results.totalProducts = health.metrics.productsLoaded
    results.cacheHitRate = health.metrics.cacheHitRate

    // 3. Test product scanning performance
    console.log('\n3. 🔍 Testing product scanning performance...')
    const scanStart = Date.now()
    await system.triggerScan()
    results.scanDuration = Date.now() - scanStart
    console.log(`✅ Full scan completed in ${results.scanDuration}ms`)

    // 4. Test individual product loading
    console.log('\n4. 📦 Testing individual product loading...')
    const allProducts = await system.getAllProducts()
    console.log(`Total products found: ${allProducts.length}`)
    
    if (allProducts.length > 0) {
      // Test loading first 10 products
      const sampleSize = Math.min(10, allProducts.length)
      const loadTimes: number[] = []

      for (let i = 0; i < sampleSize; i++) {
        const product = allProducts[i]
        const loadStart = Date.now()
        
        try {
          const loadedProduct = await system.getProduct(product.id)
          const loadTime = Date.now() - loadStart
          loadTimes.push(loadTime)
          
          if (loadedProduct) {
            results.sampleProducts.push({
              id: product.id,
              name: product.name,
              brand: product.brand.name,
              category: product.category.name,
              images: product.media.images.length,
              videos: product.media.videos.length,
              loadTime
            })
            
            results.totalImages += product.media.images.length
            results.totalVideos += product.media.videos.length
          }
          
          console.log(`  ✅ ${product.name} - ${loadTime}ms`)
        } catch (error) {
          console.log(`  ❌ ${product.id} - Error: ${error.message}`)
          results.errors.push(`Product ${product.id}: ${error.message}`)
        }
      }

      results.averageLoadTime = loadTimes.reduce((a, b) => a + b, 0) / loadTimes.length
      console.log(`Average load time: ${results.averageLoadTime.toFixed(2)}ms`)
    }

    // 5. Test search functionality
    console.log('\n5. 🔎 Testing search functionality...')
    
    const searchTests = [
      { query: 'Nike', description: 'Nike products' },
      { query: 'Gucci', description: 'Gucci products' },
      { query: 'Air Force', description: 'Air Force sneakers' },
      { query: 'Limited Edition', description: 'Limited edition items' }
    ]

    for (const test of searchTests) {
      try {
        const searchStart = Date.now()
        const searchResults = await system.searchProducts(test.query, {}, 1, 5)
        const searchTime = Date.now() - searchStart
        
        if (searchResults) {
          console.log(`  ✅ "${test.query}" - ${searchResults.total} results in ${searchTime}ms`)
        } else {
          console.log(`  ❌ "${test.query}" - No results`)
        }
      } catch (error) {
        console.log(`  ❌ "${test.query}" - Error: ${error.message}`)
        results.errors.push(`Search "${test.query}": ${error.message}`)
      }
    }

    // 6. Test filtering
    console.log('\n6. 🎛️ Testing advanced filtering...')
    
    try {
      const filterStart = Date.now()
      const filteredResults = await system.searchProducts('', {
        brands: ['nike'],
        inStockOnly: true,
        sortBy: 'price',
        sortOrder: 'asc'
      }, 1, 10)
      const filterTime = Date.now() - filterStart
      
      if (filteredResults) {
        console.log(`  ✅ Nike products (in stock, sorted by price) - ${filteredResults.total} results in ${filterTime}ms`)
      }
    } catch (error) {
      console.log(`  ❌ Filtering test failed: ${error.message}`)
      results.errors.push(`Filtering test: ${error.message}`)
    }

    // 7. Test cache performance
    console.log('\n7. ⚡ Testing cache performance...')
    
    if (allProducts.length > 0) {
      const testProduct = allProducts[0]
      
      // First load (cache miss)
      const firstLoadStart = Date.now()
      await system.getProduct(testProduct.id)
      const firstLoadTime = Date.now() - firstLoadStart
      
      // Second load (cache hit)
      const secondLoadStart = Date.now()
      await system.getProduct(testProduct.id)
      const secondLoadTime = Date.now() - secondLoadStart
      
      const improvement = ((firstLoadTime - secondLoadTime) / firstLoadTime * 100).toFixed(1)
      console.log(`  ✅ Cache performance: ${firstLoadTime}ms → ${secondLoadTime}ms (${improvement}% improvement)`)
    }

    // 8. Test system metrics
    console.log('\n8. 📊 Testing system metrics...')
    const metrics = system.getMetrics()
    console.log(`  System uptime: ${Math.round(metrics.system.uptime / 1000)}s`)
    console.log(`  Total requests: ${metrics.performance.totalRequests}`)
    console.log(`  Cache hit rate: ${metrics.cache.hitRate}%`)
    console.log(`  Memory usage: ${metrics.cache.memoryUsage}MB`)

    // 9. Test specific product structure parsing
    console.log('\n9. 🏗️ Testing product structure parsing...')
    
    if (allProducts.length > 0) {
      const sampleProduct = allProducts[0]
      console.log(`  Sample product structure:`)
      console.log(`    ID: ${sampleProduct.id}`)
      console.log(`    Name: ${sampleProduct.name}`)
      console.log(`    Brand: ${sampleProduct.brand.name}`)
      console.log(`    Category: ${sampleProduct.category.name}`)
      console.log(`    Price: $${sampleProduct.pricing.currentPrice}`)
      console.log(`    Images: ${sampleProduct.media.images.length}`)
      console.log(`    Videos: ${sampleProduct.media.videos.length}`)
      console.log(`    In Stock: ${sampleProduct.inventory.inStock}`)
      console.log(`    Limited Edition: ${sampleProduct.details.isLimitedEdition}`)
    }

    // 10. Test error handling
    console.log('\n10. 🛡️ Testing error handling...')
    
    try {
      await system.getProduct('non-existent-product-id')
      console.log(`  ❌ Error handling test failed - should have thrown error`)
    } catch (error) {
      console.log(`  ✅ Error handling works correctly: ${error.message}`)
    }

    console.log('\n' + '=' .repeat(60))
    console.log('🎉 Real Data Testing Completed Successfully!')
    
    await shutdownTWLSystem()
    
    return results

  } catch (error) {
    console.error('❌ Testing failed:', error)
    results.errors.push(`System error: ${error.message}`)
    return results
  }
}

async function analyzeProductStructure() {
  console.log('\n📁 Analyzing Real Product Directory Structure...')
  
  const productsPath = 'public/products'
  const stats = {
    categories: 0,
    brands: 0,
    products: 0,
    images: 0,
    videos: 0,
    descriptions: 0
  }

  function scanDirectory(dirPath: string, level: number = 0) {
    try {
      const items = fs.readdirSync(dirPath)
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item)
        const stat = fs.statSync(itemPath)
        
        if (stat.isDirectory()) {
          if (level === 0) stats.categories++
          if (level === 1) stats.brands++
          if (level >= 2) stats.products++
          
          scanDirectory(itemPath, level + 1)
        } else {
          if (item.endsWith('.webp') || item.endsWith('.jpg') || item.endsWith('.png')) {
            stats.images++
          } else if (item.endsWith('.mp4') || item.endsWith('.webm')) {
            stats.videos++
          } else if (item === 'Description.txt') {
            stats.descriptions++
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning ${dirPath}:`, error.message)
    }
  }

  scanDirectory(productsPath)
  
  console.log(`📊 Directory Structure Analysis:`)
  console.log(`  Categories: ${stats.categories}`)
  console.log(`  Brands: ${stats.brands}`)
  console.log(`  Product folders: ${stats.products}`)
  console.log(`  Images: ${stats.images}`)
  console.log(`  Videos: ${stats.videos}`)
  console.log(`  Description files: ${stats.descriptions}`)
  
  return stats
}

async function runCompleteTest() {
  console.log('🧪 TWL Enterprise System - Complete Real Data Test')
  console.log('Testing with actual product data from your directory')
  console.log('=' .repeat(80))

  // Analyze structure first
  const structureStats = await analyzeProductStructure()
  
  // Run system tests
  const testResults = await testWithRealData()
  
  // Generate report
  console.log('\n📋 FINAL TEST REPORT')
  console.log('=' .repeat(40))
  console.log(`✅ Products Scanned: ${testResults.totalProducts}`)
  console.log(`✅ Images Found: ${testResults.totalImages}`)
  console.log(`✅ Videos Found: ${testResults.totalVideos}`)
  console.log(`✅ Scan Duration: ${testResults.scanDuration}ms`)
  console.log(`✅ Average Load Time: ${testResults.averageLoadTime.toFixed(2)}ms`)
  console.log(`✅ Cache Hit Rate: ${testResults.cacheHitRate}%`)
  console.log(`❌ Errors: ${testResults.errors.length}`)
  
  if (testResults.errors.length > 0) {
    console.log('\n🚨 Errors encountered:')
    testResults.errors.forEach(error => console.log(`  - ${error}`))
  }
  
  console.log('\n🎯 Performance Analysis:')
  console.log(`  Response Time: ${testResults.averageLoadTime < 100 ? '✅' : '⚠️'} ${testResults.averageLoadTime.toFixed(2)}ms (target: <100ms)`)
  console.log(`  Cache Efficiency: ${testResults.cacheHitRate > 80 ? '✅' : '⚠️'} ${testResults.cacheHitRate}% (target: >80%)`)
  console.log(`  Error Rate: ${testResults.errors.length === 0 ? '✅' : '❌'} ${testResults.errors.length} errors`)
  
  console.log('\n🎉 Test completed! Enterprise system is ready for production.')
  
  return {
    structureStats,
    testResults
  }
}

// Run the test if this file is executed directly
if (require.main === module) {
  runCompleteTest().catch(console.error)
}

export { runCompleteTest, testWithRealData, analyzeProductStructure }
