'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import UserGeneratedContent from '@/components/features/UserGeneratedContent'
import SocialWishlist from '@/components/features/SocialWishlist'
import GamificationSystem from '@/components/features/GamificationSystem'

export default function SocialPage() {
  const [activeTab, setActiveTab] = useState('feed') // feed, wishlists, progress

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Feed Social TWL
          </h1>
          <p className="text-warm-camel text-lg max-w-3xl mx-auto">
            Conecta con la comunidad sneakerhead más vibrante de México
          </p>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-8"
        >
          <div className="flex flex-wrap gap-2 justify-center">
            {[
              { id: 'feed', label: 'Feed', icon: '📱' },
              { id: 'wishlists', label: 'Listas Sociales', icon: '💖' },
              { id: 'progress', label: 'Mi Progreso', icon: '🏆' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'bg-rich-gold text-forest-emerald shadow-lg'
                    : 'bg-warm-camel/10 text-warm-camel hover:bg-warm-camel/20'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Tab Content */}
        <motion.div
          key={activeTab}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          {activeTab === 'feed' && (
            <div className="space-y-8">
              {/* Create Post Section */}
              <Card variant="glass">
                <CardContent className="p-6">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-rich-gold to-warm-camel rounded-full flex items-center justify-center text-2xl">
                      👤
                    </div>
                    <div className="flex-1">
                      <button className="w-full text-left px-4 py-3 bg-warm-camel/10 rounded-lg text-warm-camel hover:bg-warm-camel/20 transition-colors">
                        ¿Qué sneakers estás usando hoy?
                      </button>
                    </div>
                    <AnimatedButton
                      variant="primary"
                      size="sm"
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      }
                    >
                      Foto
                    </AnimatedButton>
                  </div>
                </CardContent>
              </Card>

              {/* User Generated Content */}
              <UserGeneratedContent showCreatePost={false} />
            </div>
          )}

          {activeTab === 'wishlists' && (
            <SocialWishlist />
          )}

          {activeTab === 'progress' && (
            <GamificationSystem />
          )}
        </motion.div>

        {/* Floating Action Button */}
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          transition={{ delay: 0.5 }}
          className="fixed bottom-8 right-8 z-40"
        >
          <button className="w-14 h-14 bg-rich-gold text-forest-emerald rounded-full shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center hover:scale-110">
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
            </svg>
          </button>
        </motion.div>
      </div>
    </div>
  )
}
