'use client'

import { useState, useRef, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { MicrophoneIcon, StopIcon } from '@heroicons/react/24/outline'
import Button from '@/components/ui/Button'

export default function VoiceSearch({ onResults, className = '' }) {
  const router = useRouter()
  const [isListening, setIsListening] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [isSupported, setIsSupported] = useState(false)
  const [error, setError] = useState('')
  const recognitionRef = useRef(null)

  useEffect(() => {
    // Check if speech recognition is supported
    if (typeof window !== 'undefined') {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      if (SpeechRecognition) {
        setIsSupported(true)
        
        // Initialize speech recognition
        const recognition = new SpeechRecognition()
        recognition.continuous = false
        recognition.interimResults = true
        recognition.lang = 'es-MX' // Mexican Spanish
        
        recognition.onstart = () => {
          setIsListening(true)
          setError('')
        }
        
        recognition.onresult = (event) => {
          let finalTranscript = ''
          let interimTranscript = ''
          
          for (let i = event.resultIndex; i < event.results.length; i++) {
            const transcript = event.results[i][0].transcript
            if (event.results[i].isFinal) {
              finalTranscript += transcript
            } else {
              interimTranscript += transcript
            }
          }
          
          setTranscript(finalTranscript || interimTranscript)
          
          if (finalTranscript) {
            handleVoiceSearch(finalTranscript)
          }
        }
        
        recognition.onerror = (event) => {
          setError(`Error de reconocimiento: ${event.error}`)
          setIsListening(false)
        }
        
        recognition.onend = () => {
          setIsListening(false)
        }
        
        recognitionRef.current = recognition
      }
    }
    
    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.abort()
      }
    }
  }, [])

  const handleVoiceSearch = async (searchText) => {
    try {
      setError('')
      
      // Process the voice input with AI to extract search intent
      const processedQuery = await processVoiceInput(searchText)
      
      // Perform search
      const searchParams = new URLSearchParams()
      searchParams.set('q', processedQuery.query)
      
      // Add extracted filters
      if (processedQuery.brand) {
        searchParams.set('brands', processedQuery.brand)
      }
      if (processedQuery.category) {
        searchParams.set('categories', processedQuery.category)
      }
      if (processedQuery.gender) {
        searchParams.set('gender', processedQuery.gender)
      }
      if (processedQuery.color) {
        searchParams.set('colors', processedQuery.color)
      }
      if (processedQuery.priceRange) {
        if (processedQuery.priceRange.min) {
          searchParams.set('priceMin', processedQuery.priceRange.min.toString())
        }
        if (processedQuery.priceRange.max) {
          searchParams.set('priceMax', processedQuery.priceRange.max.toString())
        }
      }
      
      // Navigate to search results
      router.push(`/search?${searchParams.toString()}`)
      
      // Call callback if provided
      if (onResults) {
        onResults(processedQuery)
      }
      
    } catch (error) {
      console.error('Voice search error:', error)
      setError('Error procesando búsqueda por voz')
    }
  }

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setTranscript('')
      setError('')
      recognitionRef.current.start()
    }
  }

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop()
    }
  }

  if (!isSupported) {
    return null // Don't render if not supported
  }

  return (
    <div className={`voice-search ${className}`}>
      <div className="flex items-center gap-3">
        <Button
          onClick={isListening ? stopListening : startListening}
          variant={isListening ? "primary" : "outline"}
          size="sm"
          className={`
            relative overflow-hidden transition-all duration-300
            ${isListening ? 'animate-pulse bg-lime-500 hover:bg-lime-600' : ''}
          `}
        >
          {isListening ? (
            <StopIcon className="h-4 w-4" />
          ) : (
            <MicrophoneIcon className="h-4 w-4" />
          )}
          <span className="ml-2">
            {isListening ? 'Escuchando...' : 'Buscar por voz'}
          </span>
          
          {isListening && (
            <div className="absolute inset-0 bg-lime-400 opacity-30 animate-ping" />
          )}
        </Button>
        
        {transcript && (
          <div className="flex-1 min-w-0">
            <p className="text-sm text-warm-camel truncate">
              "{transcript}"
            </p>
          </div>
        )}
      </div>
      
      {error && (
        <p className="text-sm text-red-500 mt-2">
          {error}
        </p>
      )}
      
      {isListening && (
        <div className="mt-2">
          <div className="flex items-center gap-2">
            <div className="flex space-x-1">
              <div className="w-2 h-2 bg-lime-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }} />
              <div className="w-2 h-2 bg-lime-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }} />
              <div className="w-2 h-2 bg-lime-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }} />
            </div>
            <span className="text-xs text-warm-camel">
              Habla ahora...
            </span>
          </div>
        </div>
      )}
    </div>
  )
}

// AI-powered voice input processing
async function processVoiceInput(voiceText) {
  try {
    // Simple NLP processing for Spanish voice commands
    const text = voiceText.toLowerCase().trim()
    
    const result = {
      query: voiceText,
      brand: null,
      category: null,
      gender: null,
      color: null,
      priceRange: null
    }

    // Brand detection
    const brandMappings = {
      'gucci': 'gucci',
      'louis vuitton': 'louis-vuitton',
      'dior': 'dior',
      'balenciaga': 'balenciaga',
      'nike': 'nike',
      'adidas': 'adidas',
      'jordan': 'jordan',
      'prada': 'prada',
      'bottega veneta': 'bottega-veneta',
      'golden goose': 'golden-goose',
      'off white': 'off-white',
      'miu miu': 'miu-miu'
    }

    Object.entries(brandMappings).forEach(([keyword, slug]) => {
      if (text.includes(keyword)) {
        result.brand = slug
      }
    })

    // Category detection
    const categoryMappings = {
      'sneakers': 'sneakers',
      'tenis': 'sneakers',
      'zapatillas': 'sneakers',
      'sandalias': 'sandals',
      'slides': 'sandals',
      'chanclas': 'sandals',
      'botas': 'boots',
      'botines': 'boots',
      'zapatos formales': 'formal-shoes',
      'zapatos de vestir': 'formal-shoes',
      'mocasines': 'formal-shoes',
      'casuales': 'casual-shoes'
    }

    Object.entries(categoryMappings).forEach(([keyword, slug]) => {
      if (text.includes(keyword)) {
        result.category = slug
      }
    })

    // Gender detection
    if (text.includes('hombre') || text.includes('masculino') || text.includes('para él')) {
      result.gender = 'men'
    } else if (text.includes('mujer') || text.includes('femenino') || text.includes('para ella')) {
      result.gender = 'women'
    } else if (text.includes('niños') || text.includes('infantil')) {
      result.gender = 'kids'
    }

    // Color detection
    const colorMappings = {
      'blanco': 'Blanco',
      'negro': 'Negro',
      'rojo': 'Rojo',
      'azul': 'Azul',
      'verde': 'Verde',
      'amarillo': 'Amarillo',
      'rosa': 'Rosa',
      'morado': 'Morado',
      'café': 'Café',
      'marrón': 'Café',
      'gris': 'Gris',
      'beige': 'Beige',
      'dorado': 'Dorado',
      'plateado': 'Plateado'
    }

    Object.entries(colorMappings).forEach(([keyword, color]) => {
      if (text.includes(keyword)) {
        result.color = color
      }
    })

    // Price range detection
    const pricePatterns = [
      /menos de (\d+)/,
      /menor a (\d+)/,
      /bajo (\d+)/,
      /más de (\d+)/,
      /mayor a (\d+)/,
      /sobre (\d+)/,
      /entre (\d+) y (\d+)/,
      /de (\d+) a (\d+)/
    ]

    pricePatterns.forEach(pattern => {
      const match = text.match(pattern)
      if (match) {
        if (match[2]) {
          // Range pattern
          result.priceRange = {
            min: parseInt(match[1]),
            max: parseInt(match[2])
          }
        } else {
          // Single value pattern
          const value = parseInt(match[1])
          if (text.includes('menos') || text.includes('menor') || text.includes('bajo')) {
            result.priceRange = { max: value }
          } else {
            result.priceRange = { min: value }
          }
        }
      }
    })

    // Clean up the query by removing detected filters
    let cleanQuery = voiceText
    if (result.brand) {
      Object.keys(brandMappings).forEach(brand => {
        cleanQuery = cleanQuery.replace(new RegExp(brand, 'gi'), '').trim()
      })
    }
    if (result.category) {
      Object.keys(categoryMappings).forEach(category => {
        cleanQuery = cleanQuery.replace(new RegExp(category, 'gi'), '').trim()
      })
    }

    result.query = cleanQuery || voiceText

    return result

  } catch (error) {
    console.error('Error processing voice input:', error)
    return {
      query: voiceText,
      brand: null,
      category: null,
      gender: null,
      color: null,
      priceRange: null
    }
  }
}
