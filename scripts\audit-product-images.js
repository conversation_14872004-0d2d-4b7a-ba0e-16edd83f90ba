#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// Import the products database
const { mockProducts } = require('../lib/data/products.js')

console.log('🔍 CYTTE SUPPLIER PRODUCT IMAGE AUDIT')
console.log('=====================================\n')

// Check if image files exist
function checkImageExists(imagePath) {
  const fullPath = path.join(process.cwd(), 'public', imagePath.replace('/products/', 'products/'))
  return fs.existsSync(fullPath)
}

// Get all product folders
function getProductFolders() {
  const productsDir = path.join(process.cwd(), 'public', 'products')
  if (!fs.existsSync(productsDir)) {
    console.log('❌ Products directory not found!')
    return []
  }
  
  return fs.readdirSync(productsDir, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name)
}

// Audit database vs file system
function auditDatabase() {
  console.log('📊 DATABASE AUDIT RESULTS')
  console.log('=========================\n')
  
  let totalProducts = 0
  let validImages = 0
  let invalidImages = 0
  let missingImages = []
  
  mockProducts.forEach((product, index) => {
    totalProducts++
    console.log(`${index + 1}. ${product.name} (${product.brand})`)
    console.log(`   Category: ${product.category}`)
    console.log(`   SKU: ${product.sku}`)
    console.log(`   Images: ${product.images.length}`)
    
    product.images.forEach((imagePath, imgIndex) => {
      const exists = checkImageExists(imagePath)
      if (exists) {
        validImages++
        console.log(`   ✅ Image ${imgIndex + 1}: ${imagePath}`)
      } else {
        invalidImages++
        console.log(`   ❌ Image ${imgIndex + 1}: ${imagePath} (NOT FOUND)`)
        missingImages.push({
          product: product.name,
          sku: product.sku,
          imagePath: imagePath
        })
      }
    })
    console.log('')
  })
  
  return {
    totalProducts,
    validImages,
    invalidImages,
    missingImages
  }
}

// Audit file system vs database
function auditFileSystem() {
  console.log('📁 FILE SYSTEM AUDIT')
  console.log('====================\n')
  
  const productFolders = getProductFolders()
  console.log(`Found ${productFolders.length} product folders:\n`)
  
  productFolders.forEach(folder => {
    const folderPath = path.join(process.cwd(), 'public', 'products', folder)
    const subFolders = fs.readdirSync(folderPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name)
    
    console.log(`📂 ${folder}/ (${subFolders.length} products)`)
    
    subFolders.forEach(subFolder => {
      const subFolderPath = path.join(folderPath, subFolder)
      const images = fs.readdirSync(subFolderPath)
        .filter(file => file.endsWith('.webp') || file.endsWith('.jpg') || file.endsWith('.png'))
      
      console.log(`   📦 ${subFolder}/ (${images.length} images)`)
      
      // Check if this product is in database
      const dbProduct = mockProducts.find(p => 
        p.images.some(img => img.includes(subFolder))
      )
      
      if (dbProduct) {
        console.log(`   ✅ Mapped to: ${dbProduct.name}`)
      } else {
        console.log(`   ⚠️  Not mapped in database`)
      }
    })
    console.log('')
  })
}

// Generate mapping suggestions
function generateMappingSuggestions() {
  console.log('💡 MAPPING SUGGESTIONS')
  console.log('======================\n')
  
  const productFolders = getProductFolders()
  const unmappedProducts = []
  
  productFolders.forEach(folder => {
    const folderPath = path.join(process.cwd(), 'public', 'products', folder)
    const subFolders = fs.readdirSync(folderPath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name)
    
    subFolders.forEach(subFolder => {
      const dbProduct = mockProducts.find(p => 
        p.images.some(img => img.includes(subFolder))
      )
      
      if (!dbProduct) {
        const subFolderPath = path.join(folderPath, subFolder)
        const images = fs.readdirSync(subFolderPath)
          .filter(file => file.endsWith('.webp') || file.endsWith('.jpg') || file.endsWith('.png'))
        
        unmappedProducts.push({
          folder: folder,
          subFolder: subFolder,
          imageCount: images.length,
          suggestedBrand: folder.includes('nike') ? 'Nike' : 
                         folder.includes('gucci') ? 'Gucci' :
                         folder.includes('dior') ? 'Dior' :
                         folder.includes('balenciaga') ? 'Balenciaga' :
                         folder.includes('lv') ? 'Louis Vuitton' :
                         folder.includes('chanel') ? 'Chanel' :
                         folder.includes('louboutin') ? 'Christian Louboutin' :
                         folder.includes('miu-miu') ? 'Miu Miu' :
                         folder.includes('givenchy') ? 'Givenchy' :
                         folder.includes('valentino') ? 'Valentino' :
                         folder.includes('bottega') ? 'Bottega Veneta' :
                         folder.includes('burberry') ? 'Burberry' :
                         folder.includes('golden-goose') ? 'Golden Goose' :
                         'Unknown'
        })
      }
    })
  })
  
  console.log(`Found ${unmappedProducts.length} unmapped products:\n`)
  
  unmappedProducts.forEach((product, index) => {
    console.log(`${index + 1}. ${product.folder}/${product.subFolder}`)
    console.log(`   Brand: ${product.suggestedBrand}`)
    console.log(`   Images: ${product.imageCount}`)
    console.log(`   Suggested path: /products/${product.folder}/${product.subFolder}/image-1.webp`)
    console.log('')
  })
  
  return unmappedProducts
}

// Main audit function
function runAudit() {
  const dbAudit = auditDatabase()
  auditFileSystem()
  const unmappedProducts = generateMappingSuggestions()
  
  console.log('📈 SUMMARY REPORT')
  console.log('=================\n')
  console.log(`Total products in database: ${dbAudit.totalProducts}`)
  console.log(`Valid image paths: ${dbAudit.validImages}`)
  console.log(`Invalid image paths: ${dbAudit.invalidImages}`)
  console.log(`Unmapped products in file system: ${unmappedProducts.length}`)
  console.log('')
  
  if (dbAudit.invalidImages > 0) {
    console.log('❌ MISSING IMAGES:')
    dbAudit.missingImages.forEach(missing => {
      console.log(`   ${missing.product} (${missing.sku}): ${missing.imagePath}`)
    })
    console.log('')
  }
  
  const successRate = ((dbAudit.validImages / (dbAudit.validImages + dbAudit.invalidImages)) * 100).toFixed(1)
  console.log(`✅ Image mapping success rate: ${successRate}%`)
  
  if (successRate < 100) {
    console.log('\n🔧 RECOMMENDED ACTIONS:')
    console.log('1. Fix invalid image paths in database')
    console.log('2. Add missing products to database')
    console.log('3. Verify image file formats (prefer .webp)')
    console.log('4. Ensure consistent folder structure')
  } else {
    console.log('\n🎉 All images are properly mapped!')
  }
}

// Run the audit
runAudit()
