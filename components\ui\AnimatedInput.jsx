'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, useSpring, useMotionValue, useTransform } from 'framer-motion'

export default function AnimatedInput({
  type = 'text',
  placeholder = '',
  label = '',
  value = '',
  onChange,
  onFocus,
  onBlur,
  error = '',
  success = false,
  disabled = false,
  required = false,
  icon = null,
  className = '',
  ...props
}) {
  const [isFocused, setIsFocused] = useState(false)
  const [hasValue, setHasValue] = useState(false)
  const inputRef = useRef(null)
  
  // Spring animations for smooth interactions
  const springConfig = { stiffness: 300, damping: 30 }
  const scale = useSpring(1, springConfig)
  const borderOpacity = useSpring(0.3, springConfig)
  const labelY = useSpring(0, springConfig)
  const labelScale = useSpring(1, springConfig)
  const iconRotation = useSpring(0, springConfig)
  
  // Ripple effect
  const rippleX = useMotionValue(0)
  const rippleY = useMotionValue(0)
  const rippleScale = useSpring(0, { stiffness: 400, damping: 40 })
  
  // Breathing animation for focus state
  const breatheScale = useTransform(
    useMotionValue(0),
    [0, 1],
    [1, 1.02]
  )

  useEffect(() => {
    setHasValue(value && value.length > 0)
  }, [value])

  useEffect(() => {
    if (isFocused || hasValue) {
      labelY.set(-24)
      labelScale.set(0.85)
    } else {
      labelY.set(0)
      labelScale.set(1)
    }
  }, [isFocused, hasValue, labelY, labelScale])

  const handleFocus = (e) => {
    setIsFocused(true)
    scale.set(1.02)
    borderOpacity.set(1)
    iconRotation.set(5)
    
    // Ripple effect
    const rect = e.target.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    rippleX.set(x)
    rippleY.set(y)
    rippleScale.set(1)
    
    onFocus?.(e)
  }

  const handleBlur = (e) => {
    setIsFocused(false)
    scale.set(1)
    borderOpacity.set(0.3)
    iconRotation.set(0)
    rippleScale.set(0)
    
    onBlur?.(e)
  }

  const handleChange = (e) => {
    setHasValue(e.target.value.length > 0)
    onChange?.(e)
  }

  const getStateColors = () => {
    if (error) {
      return {
        border: 'border-warm-camel',
        focus: 'focus:border-warm-camel focus:ring-warm-camel/20',
        label: 'text-warm-camel',
        bg: 'bg-warm-camel/5'
      }
    }
    if (success) {
      return {
        border: 'border-forest-emerald',
        focus: 'focus:border-forest-emerald focus:ring-forest-emerald/20',
        label: 'text-forest-emerald',
        bg: 'bg-forest-emerald/5'
      }
    }
    return {
      border: 'border-soft-steel-gray dark:border-warm-camel/30',
      focus: 'focus:border-rich-gold focus:ring-rich-gold/20',
      label: 'text-warm-camel',
      bg: 'bg-light-cloud-gray/50 dark:bg-deep-pine/50'
    }
  }

  const stateColors = getStateColors()

  return (
    <div className={`relative ${className}`}>
      {/* Input Container */}
      <motion.div
        style={{ scale }}
        className="relative"
      >
        {/* Background with breathing effect */}
        <motion.div
          className={`absolute inset-0 rounded-lg ${stateColors.bg} transition-colors duration-300`}
          animate={{
            scale: isFocused ? [1, 1.01, 1] : 1
          }}
          transition={{
            duration: 2,
            repeat: isFocused ? Infinity : 0,
            ease: "easeInOut"
          }}
        />
        
        {/* Ripple Effect */}
        <motion.div
          className="absolute inset-0 rounded-lg overflow-hidden pointer-events-none"
        >
          <motion.div
            className="absolute w-2 h-2 bg-rich-gold/30 rounded-full"
            style={{
              x: rippleX,
              y: rippleY,
              scale: rippleScale,
              translateX: '-50%',
              translateY: '-50%'
            }}
          />
        </motion.div>

        {/* Input Field */}
        <div className="relative">
          {icon && (
            <motion.div
              style={{ rotate: iconRotation }}
              className="absolute left-3 top-1/2 transform -translate-y-1/2 text-warm-camel z-10"
            >
              {icon}
            </motion.div>
          )}
          
          <motion.input
            ref={inputRef}
            type={type}
            value={value}
            onChange={handleChange}
            onFocus={handleFocus}
            onBlur={handleBlur}
            disabled={disabled}
            className={`
              w-full px-4 py-3 ${icon ? 'pl-10' : ''} 
              bg-transparent
              border-2 ${stateColors.border}
              rounded-lg
              text-forest-emerald dark:text-light-cloud-gray
              placeholder-transparent
              transition-all duration-300
              focus:outline-none focus:ring-2 ${stateColors.focus}
              disabled:opacity-50 disabled:cursor-not-allowed
              relative z-10
            `}
            placeholder={placeholder}
            {...props}
          />

          {/* Floating Label */}
          {label && (
            <motion.label
              style={{
                y: labelY,
                scale: labelScale,
                opacity: useTransform(borderOpacity, [0.3, 1], [0.7, 1])
              }}
              className={`
                absolute left-4 ${icon ? 'left-10' : ''} 
                pointer-events-none
                ${stateColors.label}
                font-medium
                transition-colors duration-300
                origin-left
                z-20
              `}
              onClick={() => inputRef.current?.focus()}
            >
              {label}
              {required && (
                <motion.span
                  className="text-warm-camel ml-1"
                  animate={{
                    scale: isFocused ? [1, 1.2, 1] : 1
                  }}
                  transition={{
                    duration: 0.5,
                    repeat: isFocused ? Infinity : 0,
                    repeatDelay: 2
                  }}
                >
                  *
                </motion.span>
              )}
            </motion.label>
          )}

          {/* Success/Error Icons */}
          <motion.div
            className="absolute right-3 top-1/2 transform -translate-y-1/2"
            initial={{ scale: 0, rotate: -180 }}
            animate={{
              scale: (success || error) ? 1 : 0,
              rotate: (success || error) ? 0 : -180
            }}
            transition={{ type: "spring", stiffness: 300, damping: 30 }}
          >
            {success && (
              <motion.div
                className="text-forest-emerald"
                animate={{
                  scale: [1, 1.2, 1]
                }}
                transition={{
                  duration: 0.6,
                  ease: "easeInOut"
                }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
              </motion.div>
            )}
            {error && (
              <motion.div
                className="text-warm-camel"
                animate={{
                  rotate: [0, -10, 10, 0]
                }}
                transition={{
                  duration: 0.5,
                  ease: "easeInOut"
                }}
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </motion.div>
            )}
          </motion.div>
        </div>

        {/* Animated Border Glow */}
        <motion.div
          className="absolute inset-0 rounded-lg border-2 border-rich-gold pointer-events-none"
          style={{
            opacity: useTransform(borderOpacity, [0.3, 1], [0, 0.6])
          }}
          animate={{
            boxShadow: isFocused 
              ? [
                  '0 0 0 0 rgba(255, 215, 0, 0)',
                  '0 0 0 4px rgba(255, 215, 0, 0.1)',
                  '0 0 0 0 rgba(255, 215, 0, 0)'
                ]
              : '0 0 0 0 rgba(255, 215, 0, 0)'
          }}
          transition={{
            duration: 2,
            repeat: isFocused ? Infinity : 0,
            ease: "easeInOut"
          }}
        />
      </motion.div>

      {/* Error Message */}
      <motion.div
        initial={{ opacity: 0, y: -10, height: 0 }}
        animate={{
          opacity: error ? 1 : 0,
          y: error ? 0 : -10,
          height: error ? 'auto' : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        {error && (
          <motion.p
            className="text-sm text-warm-camel mt-2 flex items-center gap-2"
            initial={{ x: -10 }}
            animate={{ x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <motion.span
              animate={{
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 0.5,
                repeat: Infinity,
                repeatDelay: 2
              }}
            >
              ⚠️
            </motion.span>
            {error}
          </motion.p>
        )}
      </motion.div>

      {/* Success Message */}
      <motion.div
        initial={{ opacity: 0, y: -10, height: 0 }}
        animate={{
          opacity: success && !error ? 1 : 0,
          y: success && !error ? 0 : -10,
          height: success && !error ? 'auto' : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        {success && !error && (
          <motion.p
            className="text-sm text-forest-emerald mt-2 flex items-center gap-2"
            initial={{ x: -10 }}
            animate={{ x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <motion.span
              animate={{
                rotate: [0, 360]
              }}
              transition={{
                duration: 1,
                ease: "easeInOut"
              }}
            >
              ✅
            </motion.span>
            Campo válido
          </motion.p>
        )}
      </motion.div>
    </div>
  )
}
