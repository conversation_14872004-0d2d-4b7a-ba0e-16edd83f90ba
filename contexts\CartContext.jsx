'use client'

import { createContext, useContext, useReducer, useEffect } from 'react'
import { getProductById } from '@/data/products'

// Cart Context
const CartContext = createContext()

// Cart Actions
const CART_ACTIONS = {
  ADD_ITEM: 'ADD_ITEM',
  REMOVE_ITEM: 'REMOVE_ITEM',
  UPDATE_QUANTITY: 'UPDATE_QUANTITY',
  CLEAR_CART: 'CLEAR_CART',
  LOAD_CART: 'LOAD_CART',
  APPLY_DISCOUNT: 'APPLY_DISCOUNT',
  REMOVE_DISCOUNT: 'REMOVE_DISCOUNT'
}

// Cart Reducer
const cartReducer = (state, action) => {
  switch (action.type) {
    case CART_ACTIONS.ADD_ITEM: {
      const { productId, size, quantity = 1 } = action.payload
      const product = getProductById(productId)
      
      if (!product) return state
      
      const existingItemIndex = state.items.findIndex(
        item => item.productId === productId && item.size === size
      )
      
      if (existingItemIndex >= 0) {
        // Update existing item quantity
        const updatedItems = [...state.items]
        updatedItems[existingItemIndex].quantity += quantity
        
        return {
          ...state,
          items: updatedItems,
          updatedAt: new Date().toISOString()
        }
      } else {
        // Add new item
        const newItem = {
          id: `${productId}-${size}`,
          productId,
          size,
          quantity,
          price: product.price,
          addedAt: new Date().toISOString()
        }
        
        return {
          ...state,
          items: [...state.items, newItem],
          updatedAt: new Date().toISOString()
        }
      }
    }
    
    case CART_ACTIONS.REMOVE_ITEM: {
      const { itemId } = action.payload
      
      return {
        ...state,
        items: state.items.filter(item => item.id !== itemId),
        updatedAt: new Date().toISOString()
      }
    }
    
    case CART_ACTIONS.UPDATE_QUANTITY: {
      const { itemId, quantity } = action.payload
      
      if (quantity <= 0) {
        return cartReducer(state, {
          type: CART_ACTIONS.REMOVE_ITEM,
          payload: { itemId }
        })
      }
      
      const updatedItems = state.items.map(item =>
        item.id === itemId ? { ...item, quantity } : item
      )
      
      return {
        ...state,
        items: updatedItems,
        updatedAt: new Date().toISOString()
      }
    }
    
    case CART_ACTIONS.CLEAR_CART: {
      return {
        ...initialCartState,
        updatedAt: new Date().toISOString()
      }
    }
    
    case CART_ACTIONS.LOAD_CART: {
      return {
        ...state,
        ...action.payload
      }
    }
    
    case CART_ACTIONS.APPLY_DISCOUNT: {
      const { code, discount } = action.payload
      
      return {
        ...state,
        discount: {
          code,
          type: discount.type, // 'percentage' or 'fixed'
          value: discount.value,
          appliedAt: new Date().toISOString()
        },
        updatedAt: new Date().toISOString()
      }
    }
    
    case CART_ACTIONS.REMOVE_DISCOUNT: {
      return {
        ...state,
        discount: null,
        updatedAt: new Date().toISOString()
      }
    }
    
    default:
      return state
  }
}

// Initial Cart State
const initialCartState = {
  items: [],
  discount: null,
  createdAt: new Date().toISOString(),
  updatedAt: new Date().toISOString()
}

// Cart Provider Component
export function CartProvider({ children }) {
  const [state, dispatch] = useReducer(cartReducer, initialCartState)
  
  // Load cart from localStorage on mount
  useEffect(() => {
    const savedCart = localStorage.getItem('twl-cart')
    if (savedCart) {
      try {
        const parsedCart = JSON.parse(savedCart)
        dispatch({
          type: CART_ACTIONS.LOAD_CART,
          payload: parsedCart
        })
      } catch (error) {
        console.error('Error loading cart from localStorage:', error)
      }
    }
  }, [])
  
  // Save cart to localStorage whenever it changes
  useEffect(() => {
    localStorage.setItem('twl-cart', JSON.stringify(state))
  }, [state])
  
  // Cart Actions
  const addItem = (productId, size, quantity = 1) => {
    dispatch({
      type: CART_ACTIONS.ADD_ITEM,
      payload: { productId, size, quantity }
    })
  }
  
  const removeItem = (itemId) => {
    dispatch({
      type: CART_ACTIONS.REMOVE_ITEM,
      payload: { itemId }
    })
  }
  
  const updateQuantity = (itemId, quantity) => {
    dispatch({
      type: CART_ACTIONS.UPDATE_QUANTITY,
      payload: { itemId, quantity }
    })
  }
  
  const clearCart = () => {
    dispatch({ type: CART_ACTIONS.CLEAR_CART })
  }
  
  const applyDiscount = (code, discount) => {
    dispatch({
      type: CART_ACTIONS.APPLY_DISCOUNT,
      payload: { code, discount }
    })
  }
  
  const removeDiscount = () => {
    dispatch({ type: CART_ACTIONS.REMOVE_DISCOUNT })
  }
  
  // Cart Calculations
  const getItemsCount = () => {
    return state.items.reduce((total, item) => total + item.quantity, 0)
  }
  
  const getSubtotal = () => {
    return state.items.reduce((total, item) => {
      const product = getProductById(item.productId)
      return total + (product ? product.price * item.quantity : 0)
    }, 0)
  }
  
  const getDiscountAmount = () => {
    if (!state.discount) return 0
    
    const subtotal = getSubtotal()
    
    if (state.discount.type === 'percentage') {
      return subtotal * (state.discount.value / 100)
    } else if (state.discount.type === 'fixed') {
      return Math.min(state.discount.value, subtotal)
    }
    
    return 0
  }
  
  const getTax = () => {
    // 16% IVA for Mexico
    const taxableAmount = getSubtotal() - getDiscountAmount()
    return taxableAmount * 0.16
  }
  
  const getShipping = () => {
    const subtotal = getSubtotal()
    // Free shipping over $3000 MXN
    return subtotal >= 3000 ? 0 : 200
  }
  
  const getTotal = () => {
    return getSubtotal() - getDiscountAmount() + getTax() + getShipping()
  }
  
  const getCartSummary = () => {
    return {
      itemsCount: getItemsCount(),
      subtotal: getSubtotal(),
      discount: getDiscountAmount(),
      tax: getTax(),
      shipping: getShipping(),
      total: getTotal()
    }
  }
  
  // Get cart items with product details
  const getCartItems = () => {
    return state.items.map(item => {
      const product = getProductById(item.productId)
      return {
        ...item,
        product
      }
    }).filter(item => item.product) // Filter out items with missing products
  }

  // Get order history (mock data for demo)
  const getOrderHistory = () => {
    // For now, return mock data - in real app, check authentication
    // if (!isAuthenticated) return []

    // Mock order history
    return [
      {
        id: 'ORD-001',
        date: '2024-01-15T10:00:00Z',
        status: 'Entregado',
        items: [
          {
            id: '1',
            name: 'Air Jordan 1 Retro High OG',
            size: '9',
            quantity: 1,
            price: 3500
          }
        ],
        subtotal: 3500,
        tax: 560,
        shipping: 0,
        total: 4060
      }
    ]
  }

  // Process checkout
  const processCheckout = async (checkoutData) => {
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Create order
      const order = {
        id: `ORD-${Date.now()}`,
        date: new Date().toISOString(),
        status: 'Procesando',
        items: state.items.map(item => ({
          id: item.id,
          name: item.name,
          brand: item.brand,
          size: item.size,
          quantity: item.quantity,
          price: item.price
        })),
        subtotal: getSubtotal(),
        tax: getTax(),
        shipping: getShipping(),
        discount: getDiscountAmount(),
        total: getTotal(),
        shippingAddress: checkoutData.shippingAddress,
        billingAddress: checkoutData.billingAddress,
        paymentMethod: checkoutData.paymentMethod
      }

      // Clear cart after successful checkout
      dispatch({ type: CART_ACTIONS.CLEAR_CART })

      return { success: true, order }
    } catch (error) {
      return { success: false, error: error.message }
    }
  }
  
  const value = {
    // State
    cart: state,
    items: getCartItems(),
    summary: getCartSummary(),
    
    // Actions
    addItem,
    removeItem,
    updateQuantity,
    clearCart,
    applyDiscount,
    removeDiscount,
    
    // Utilities
    getItemsCount,
    getSubtotal,
    getDiscountAmount,
    getTax,
    getShipping,
    getTotal,
    getOrderHistory,
    processCheckout
  }
  
  return (
    <CartContext.Provider value={value}>
      {children}
    </CartContext.Provider>
  )
}

// Custom hook to use cart context
export function useCart() {
  const context = useContext(CartContext)
  
  if (!context) {
    throw new Error('useCart must be used within a CartProvider')
  }
  
  return context
}
