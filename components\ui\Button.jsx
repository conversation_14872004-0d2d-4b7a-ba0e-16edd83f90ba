'use client'

import { forwardRef, useState } from 'react'
import { motion, useSpring } from 'framer-motion'
import { cn } from '@/lib/utils'

const Button = forwardRef(({
  className,
  variant = 'primary',
  size = 'default',
  asChild = false,
  children,
  disabled = false,
  loading = false,
  icon,
  iconPosition = 'left',
  ...props
}, ref) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-primary focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95'

  const variants = {
    primary: 'bg-primary hover:bg-primary-dark text-jet-black shadow-md hover:shadow-neon-glow hover:scale-105',
    secondary: 'bg-secondary hover:bg-secondary-dark text-ice-white shadow-md hover:shadow-chrome-glow hover:scale-105',
    accent: 'bg-accent hover:bg-accent-dark text-jet-black shadow-md hover:shadow-platinum-glow hover:scale-105',
    ghost: 'bg-transparent hover:bg-platinum-silver/20 dark:hover:bg-chrome-metallic/10 text-text-secondary dark:text-text-tertiary',
    outline: 'border-2 border-primary text-primary hover:bg-primary hover:text-jet-black',
    chrome: 'bg-chrome-metallic hover:bg-chrome-metallic/90 text-ice-white shadow-md hover:shadow-chrome-glow hover:scale-105',
    platinum: 'bg-platinum-silver hover:bg-platinum-silver/90 text-jet-black shadow-md hover:shadow-platinum-glow hover:scale-105',
    graphite: 'bg-graphite-gray hover:bg-graphite-gray/90 text-ice-white shadow-md hover:shadow-lg hover:scale-105',
  }
  
  const sizes = {
    sm: 'h-9 px-3 text-sm rounded-md',
    default: 'h-11 px-6 py-3 rounded-lg',
    lg: 'h-12 px-8 py-4 text-lg rounded-lg',
    xl: 'h-14 px-10 py-5 text-xl rounded-xl',
    icon: 'h-11 w-11 rounded-lg',
  }

  const Component = asChild ? 'span' : 'button'

  return (
    <Component
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        className
      )}
      ref={ref}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg
          className="mr-2 h-4 w-4 animate-spin"
          xmlns="http://www.w3.org/2000/svg"
          fill="none"
          viewBox="0 0 24 24"
        >
          <circle
            className="opacity-25"
            cx="12"
            cy="12"
            r="10"
            stroke="currentColor"
            strokeWidth="4"
          />
          <path
            className="opacity-75"
            fill="currentColor"
            d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
          />
        </svg>
      )}
      
      {icon && iconPosition === 'left' && !loading && (
        <span className="mr-2">{icon}</span>
      )}
      
      {children}
      
      {icon && iconPosition === 'right' && !loading && (
        <span className="ml-2">{icon}</span>
      )}
    </Component>
  )
})

Button.displayName = 'Button'

export default Button
