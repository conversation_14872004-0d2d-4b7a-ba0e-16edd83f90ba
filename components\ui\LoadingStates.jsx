'use client'

import { motion } from 'framer-motion'

// Generic Loading Spinner
export function LoadingSpinner({ size = 'md', color = 'primary' }) {
  const sizes = {
    sm: 'w-4 h-4',
    md: 'w-6 h-6',
    lg: 'w-8 h-8',
    xl: 'w-12 h-12'
  }

  const colors = {
    primary: 'border-primary',
    white: 'border-white',
    gray: 'border-gray-400'
  }

  return (
    <motion.div
      className={`${sizes[size]} border-2 ${colors[color]} border-t-transparent rounded-full`}
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
    />
  )
}

// Button Loading State
export function ButtonLoading({ children, isLoading, className = '', ...props }) {
  return (
    <button
      className={`relative ${className} ${isLoading ? 'cursor-not-allowed opacity-70' : ''}`}
      disabled={isLoading}
      {...props}
    >
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center">
          <LoadingSpinner size="sm" color="white" />
        </div>
      )}
      <span className={isLoading ? 'opacity-0' : 'opacity-100'}>
        {children}
      </span>
    </button>
  )
}

// Card Loading State
export function CardLoading({ className = '' }) {
  return (
    <div className={`animate-pulse ${className}`}>
      <div className="bg-light-gray dark:bg-neutral-700 rounded-xl p-6 space-y-4">
        {/* Image placeholder */}
        <div className="bg-neutral-300 dark:bg-neutral-600 rounded-lg h-48 w-full"></div>
        
        {/* Title placeholder */}
        <div className="space-y-2">
          <div className="bg-neutral-300 dark:bg-neutral-600 rounded h-4 w-3/4"></div>
          <div className="bg-neutral-300 dark:bg-neutral-600 rounded h-3 w-1/2"></div>
        </div>
        
        {/* Price placeholder */}
        <div className="flex justify-between items-center">
          <div className="bg-neutral-300 dark:bg-neutral-600 rounded h-4 w-1/4"></div>
          <div className="bg-neutral-300 dark:bg-neutral-600 rounded h-8 w-8"></div>
        </div>
      </div>
    </div>
  )
}

// Product Grid Loading State
export function ProductGridLoading({ count = 8 }) {
  return (
    <div className="grid grid-cols-2 gap-4 sm:gap-6 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 lg:gap-8 xl:gap-10">
      {[...Array(count)].map((_, index) => (
        <CardLoading key={index} />
      ))}
    </div>
  )
}

// Page Loading State
export function PageLoading() {
  return (
    <div className="min-h-screen bg-pure-white dark:bg-dark-gray flex items-center justify-center">
      <div className="text-center space-y-4">
        <motion.div
          className="w-16 h-16 border-4 border-primary border-t-transparent rounded-full mx-auto"
          animate={{ rotate: 360 }}
          transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
        />
        <motion.h2
          className="text-xl font-godber font-bold text-pure-black dark:text-pure-white tracking-godber-sm"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2 }}
        >
          Cargando...
        </motion.h2>
        <motion.p
          className="text-text-gray dark:text-neutral-400 font-poppins"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          Preparando tu experiencia TWL
        </motion.p>
      </div>
    </div>
  )
}

// Search Loading State
export function SearchLoading() {
  return (
    <div className="space-y-4 p-4">
      {[...Array(3)].map((_, index) => (
        <div key={index} className="animate-pulse flex items-center space-x-4">
          <div className="bg-neutral-300 dark:bg-neutral-600 rounded-lg h-16 w-16"></div>
          <div className="flex-1 space-y-2">
            <div className="bg-neutral-300 dark:bg-neutral-600 rounded h-4 w-3/4"></div>
            <div className="bg-neutral-300 dark:bg-neutral-600 rounded h-3 w-1/2"></div>
          </div>
        </div>
      ))}
    </div>
  )
}

// Form Loading State
export function FormLoading() {
  return (
    <div className="space-y-4 animate-pulse">
      {[...Array(4)].map((_, index) => (
        <div key={index} className="space-y-2">
          <div className="bg-neutral-300 dark:bg-neutral-600 rounded h-4 w-1/4"></div>
          <div className="bg-neutral-300 dark:bg-neutral-600 rounded-lg h-12 w-full"></div>
        </div>
      ))}
      <div className="bg-neutral-300 dark:bg-neutral-600 rounded-lg h-12 w-full"></div>
    </div>
  )
}

// Data Loading State with Message
export function DataLoading({ message = "Cargando datos..." }) {
  return (
    <div className="flex flex-col items-center justify-center py-12 space-y-4">
      <LoadingSpinner size="lg" />
      <p className="text-text-gray dark:text-neutral-400 font-poppins text-center">
        {message}
      </p>
    </div>
  )
}

// Inline Loading State
export function InlineLoading({ text = "Cargando..." }) {
  return (
    <div className="flex items-center space-x-2">
      <LoadingSpinner size="sm" />
      <span className="text-text-gray dark:text-neutral-400 font-poppins text-sm">
        {text}
      </span>
    </div>
  )
}

// Modal Loading State
export function ModalLoading() {
  return (
    <div className="flex items-center justify-center py-8">
      <div className="text-center space-y-4">
        <LoadingSpinner size="lg" />
        <p className="text-text-gray dark:text-neutral-400 font-poppins">
          Procesando...
        </p>
      </div>
    </div>
  )
}
