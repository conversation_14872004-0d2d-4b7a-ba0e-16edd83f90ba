// 🛍️ TWL INDIVIDUAL PRODUCT API ENDPOINT
// 🎯 RESTful API for single product operations

import { NextResponse } from 'next/server'
import { supabase, supabaseAdmin } from '@/lib/supabase'

// 📋 GET /api/products/[id] - Get single product by ID or slug
export async function GET(request, { params }) {
  try {
    const { id } = params
    const { searchParams } = new URL(request.url)
    const includeAnalytics = searchParams.get('analytics') === 'true'
    const includeRecommendations = searchParams.get('recommendations') === 'true'

    // Determine if ID is UUID or slug
    const isUUID = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id)
    const searchField = isUUID ? 'id' : 'slug'

    // Get product with related data
    const { data: product, error } = await supabase
      .from('products')
      .select(`
        *,
        brand:brands!inner(
          id,
          name,
          slug,
          logo_url,
          description,
          country,
          founded_year
        ),
        category:categories!inner(
          id,
          name,
          slug,
          description
        ),
        variants:product_variants(
          id,
          size,
          color,
          sku,
          stock_quantity,
          price_adjustment,
          active
        ),
        reviews:reviews(
          id,
          rating,
          title,
          content,
          images,
          verified_purchase,
          helpful_count,
          created_at,
          user:users(
            id,
            first_name,
            avatar_url
          )
        )
      `)
      .eq(searchField, id)
      .eq('status', 'active')
      .single()

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json(
          { error: 'Product not found' },
          { status: 404 }
        )
      }
      
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch product', details: error.message },
        { status: 500 }
      )
    }

    // Track product view (async, don't wait)
    trackProductView(product.id, request)

    // Get additional data if requested
    const additionalData = {}

    if (includeAnalytics) {
      additionalData.analytics = await getProductAnalytics(product.id)
    }

    if (includeRecommendations) {
      additionalData.recommendations = await getProductRecommendations(product.id)
    }

    // Calculate average rating and review stats
    const reviewStats = calculateReviewStats(product.reviews)

    return NextResponse.json({
      success: true,
      data: {
        ...product,
        reviewStats,
        ...additionalData
      }
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

// ✏️ PUT /api/products/[id] - Update product (Admin only)
export async function PUT(request, { params }) {
  try {
    const { id } = params
    const body = await request.json()

    // Validate product data
    const validationErrors = validateProductData(body)
    if (validationErrors.length > 0) {
      return NextResponse.json(
        { error: 'Validation failed', details: validationErrors },
        { status: 400 }
      )
    }

    // Check if product exists
    const { data: existingProduct, error: fetchError } = await supabase
      .from('products')
      .select('id, sku')
      .eq('id', id)
      .single()

    if (fetchError || !existingProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    // Check SKU uniqueness if changed
    if (body.sku && body.sku !== existingProduct.sku) {
      const { data: skuCheck } = await supabase
        .from('products')
        .select('id')
        .eq('sku', body.sku)
        .neq('id', id)
        .single()

      if (skuCheck) {
        return NextResponse.json(
          { error: 'Product with this SKU already exists' },
          { status: 409 }
        )
      }
    }

    // Update slug if name changed
    if (body.name) {
      body.slug = body.name
        .toLowerCase()
        .replace(/[^a-z0-9]+/g, '-')
        .replace(/(^-|-$)/g, '')
    }

    // Update product
    const updateData = {
      ...body,
      updated_at: new Date().toISOString()
    }

    const { data: product, error } = await supabaseAdmin
      .from('products')
      .update(updateData)
      .eq('id', id)
      .select(`
        *,
        brand:brands(name, slug, logo_url),
        category:categories(name, slug)
      `)
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to update product', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product updated successfully'
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

// 🗑️ DELETE /api/products/[id] - Delete product (Admin only)
export async function DELETE(request, { params }) {
  try {
    const { id } = params

    // Check if product exists
    const { data: existingProduct, error: fetchError } = await supabase
      .from('products')
      .select('id, name')
      .eq('id', id)
      .single()

    if (fetchError || !existingProduct) {
      return NextResponse.json(
        { error: 'Product not found' },
        { status: 404 }
      )
    }

    // Soft delete by updating status
    const { error } = await supabaseAdmin
      .from('products')
      .update({
        status: 'archived',
        updated_at: new Date().toISOString()
      })
      .eq('id', id)

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to delete product', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: 'Product deleted successfully'
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

// 🔧 Helper Functions

// Track product view
async function trackProductView(productId, request) {
  try {
    // Get user ID from auth header if available
    const authHeader = request.headers.get('authorization')
    let userId = null
    
    if (authHeader) {
      // Extract user ID from JWT token (simplified)
      // In production, properly verify the JWT
      try {
        const token = authHeader.replace('Bearer ', '')
        // This is a simplified approach - use proper JWT verification
        const payload = JSON.parse(atob(token.split('.')[1]))
        userId = payload.sub
      } catch (e) {
        // Invalid token, continue without user ID
      }
    }

    // Generate session ID from IP and User-Agent
    const ip = request.headers.get('x-forwarded-for') || 'unknown'
    const userAgent = request.headers.get('user-agent') || 'unknown'
    const sessionId = btoa(`${ip}-${userAgent}`).slice(0, 32)

    await supabase
      .from('product_views')
      .insert({
        product_id: productId,
        user_id: userId,
        session_id: sessionId,
        viewed_at: new Date().toISOString()
      })

  } catch (error) {
    console.error('Error tracking product view:', error)
    // Don't throw - view tracking shouldn't break the API
  }
}

// Get product analytics
async function getProductAnalytics(productId) {
  try {
    const [viewsResult, wishlistResult, ordersResult, recentViews] = await Promise.all([
      // Total views
      supabase
        .from('product_views')
        .select('id', { count: 'exact' })
        .eq('product_id', productId),
      
      // Wishlist count
      supabase
        .from('wishlist_items')
        .select('id', { count: 'exact' })
        .eq('product_id', productId),
      
      // Order count
      supabase
        .from('order_items')
        .select('id', { count: 'exact' })
        .eq('product_id', productId),
      
      // Recent views (last 30 days)
      supabase
        .from('product_views')
        .select('id', { count: 'exact' })
        .eq('product_id', productId)
        .gte('viewed_at', new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString())
    ])

    return {
      totalViews: viewsResult.count || 0,
      recentViews: recentViews.count || 0,
      wishlistCount: wishlistResult.count || 0,
      orderCount: ordersResult.count || 0
    }
  } catch (error) {
    console.error('Error getting product analytics:', error)
    return {
      totalViews: 0,
      recentViews: 0,
      wishlistCount: 0,
      orderCount: 0
    }
  }
}

// Get product recommendations
async function getProductRecommendations(productId, limit = 6) {
  try {
    const { data: recommendations, error } = await supabase
      .rpc('get_product_recommendations', {
        target_product_id: productId,
        limit_count: limit
      })

    if (error) {
      console.error('Error getting recommendations:', error)
      return []
    }

    return recommendations || []
  } catch (error) {
    console.error('Error getting product recommendations:', error)
    return []
  }
}

// Calculate review statistics
function calculateReviewStats(reviews) {
  if (!reviews || reviews.length === 0) {
    return {
      averageRating: 0,
      totalReviews: 0,
      ratingDistribution: { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
    }
  }

  const totalReviews = reviews.length
  const totalRating = reviews.reduce((sum, review) => sum + review.rating, 0)
  const averageRating = Math.round((totalRating / totalReviews) * 10) / 10

  const ratingDistribution = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
  reviews.forEach(review => {
    ratingDistribution[review.rating]++
  })

  return {
    averageRating,
    totalReviews,
    ratingDistribution
  }
}

// Validate product data
function validateProductData(data) {
  const errors = []

  if (data.name && data.name.trim().length < 3) {
    errors.push('Product name must be at least 3 characters long')
  }

  if (data.sku && data.sku.trim().length < 3) {
    errors.push('SKU must be at least 3 characters long')
  }

  if (data.price && data.price <= 0) {
    errors.push('Price must be greater than 0')
  }

  if (data.original_price && data.price && data.original_price < data.price) {
    errors.push('Original price cannot be less than current price')
  }

  if (data.stock_quantity && data.stock_quantity < 0) {
    errors.push('Stock quantity cannot be negative')
  }

  const validGenders = ['men', 'women', 'unisex', 'kids']
  if (data.gender && !validGenders.includes(data.gender)) {
    errors.push(`Gender must be one of: ${validGenders.join(', ')}`)
  }

  const validStatuses = ['active', 'inactive', 'draft', 'archived']
  if (data.status && !validStatuses.includes(data.status)) {
    errors.push(`Status must be one of: ${validStatuses.join(', ')}`)
  }

  return errors
}
