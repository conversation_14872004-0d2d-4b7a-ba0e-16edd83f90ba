'use client'

import { useState } from 'react'
import Image from 'next/image'
import Link from 'next/link'
import { motion } from 'framer-motion'

const brands = [
  {
    name: 'Nike',
    logo: '/logos/Nike swoosh logo.png',
    category: 'Sportswear'
  },
  {
    name: 'Adidas',
    logo: '/logos/Adidas Logo.png',
    category: 'Sportswear'
  },
  {
    name: 'Jordan',
    logo: '/logos/Air Jordan logo.png',
    category: 'Basketball'
  },
  {
    name: 'New Balance',
    logo: '/logos/New Balance logo.png',
    category: 'Running'
  },
  {
    name: 'G<PERSON>',
    logo: '/logos/Gucci logo.png',
    category: 'Luxury'
  },
  {
    name: '<PERSON><PERSON>',
    logo: '/logos/Dior logo.png',
    category: 'Luxury'
  },
  {
    name: '<PERSON><PERSON>ciaga',
    logo: '/logos/Balenciaga.png',
    category: 'High Fashion'
  },
  {
    name: 'Off-White',
    logo: '/logos/Offwhite logo.png',
    category: 'Streetwear'
  },
  {
    name: 'Supreme',
    logo: '/logos/Supreme logo.png',
    category: 'Streetwear'
  },
  {
    name: '<PERSON>',
    logo: '/logos/LV.png',
    category: 'Lu<PERSON>'
  },
  {
    name: 'Prada',
    logo: '/logos/Prada.png',
    category: 'Luxury'
  },
  {
    name: 'Chanel',
    logo: '/logos/Chanel.png',
    category: 'Luxury'
  },
  {
    name: 'YSL',
    logo: '/logos/YSL.png',
    category: 'Luxury'
  },
  {
    name: 'Valentino',
    logo: '/logos/Valentino.png',
    category: 'Luxury'
  },
  {
    name: 'Givenchy',
    logo: '/logos/Givenchy.png',
    category: 'High Fashion'
  },
  {
    name: 'Bottega Veneta',
    logo: '/logos/Bottega Venetta logo.png',
    category: 'Luxury'
  },
  {
    name: 'Burberry',
    logo: '/logos/Burberry.png',
    category: 'Luxury'
  },
  {
    name: 'Miu Miu',
    logo: '/logos/Miu Miu logo.png',
    category: 'Luxury'
  }
]

export default function BrandShowcase() {
  const [hoveredBrand, setHoveredBrand] = useState(null)

  // Function to generate brand URL slug
  const getBrandSlug = (brandName) => {
    return brandName.toLowerCase().replace(/\s+/g, '-').replace(/[^a-z0-9-]/g, '')
  }

  // Function to handle brand click
  const handleBrandClick = (brand) => {
    console.log(`🔗 Redirecting to catalog for brand: ${brand.name}`)
  }

  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-4 md:gap-6">
      {brands.slice(0, 18).map((brand, index) => (
        <motion.div
          key={brand.name}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3, delay: index * 0.1 }}
          className="group cursor-pointer"
          onMouseEnter={() => setHoveredBrand(brand.name)}
          onMouseLeave={() => setHoveredBrand(null)}
          onClick={() => handleBrandClick(brand)}
        >
          <Link
            href={`/shop?brand=${getBrandSlug(brand.name)}`}
            className="block"
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              className="bg-pure-white rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 p-4 md:p-6 h-20 md:h-24 flex items-center justify-center border border-light-gray hover:border-lime-green"
            >
              <div className="relative w-full h-full flex items-center justify-center">
                <Image
                  src={brand.logo}
                  alt={`${brand.name} logo`}
                  fill
                  className="object-contain filter group-hover:brightness-110 transition-all duration-300"
                  sizes="(max-width: 768px) 50vw, (max-width: 1200px) 25vw, 16vw"
                />
              </div>
            </motion.div>

            {/* Brand info on hover */}
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{
                opacity: hoveredBrand === brand.name ? 1 : 0,
                height: hoveredBrand === brand.name ? 'auto' : 0
              }}
              transition={{ duration: 0.2 }}
              className="text-center mt-2 overflow-hidden"
            >
              <p className="font-semibold text-pure-black dark:text-pure-white text-sm font-poppins">
                {brand.name}
              </p>
              <p className="text-xs text-text-gray font-poppins">
                {brand.category}
              </p>
            </motion.div>
          </Link>
        </motion.div>
      ))}
    </div>
  )
}
