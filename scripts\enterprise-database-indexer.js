#!/usr/bin/env node

/**
 * ENTERPRISE DATABASE INDEXER
 * 
 * PERFECTIONIST ENGINEERING SOLUTION for TWL
 * 
 * Features:
 * - Scans complete CYTTE product structure
 * - Creates enterprise-grade database schema
 * - Indexes all products with multi-dimensional segmentation
 * - Generates search-optimized product catalog
 * - Supports multi-language and collaboration mapping
 * - Enterprise-grade performance and scalability
 */

const fs = require('fs').promises;
const path = require('path');
const crypto = require('crypto');
const ULTIMATE_SCHEMA = require('./ultimate-perfectionist-database-schema');

class EnterpriseDatabaseIndexer {
  constructor() {
    this.productsBasePath = path.join(process.cwd(), 'public', 'products');
    this.outputPath = path.join(process.cwd(), 'database');
    
    this.productIndex = new Map();
    this.categoryIndex = new Map();
    this.brandIndex = new Map();
    this.familyIndex = new Map();
    this.collaborationIndex = new Map();
    this.genderIndex = new Map();
    this.searchIndex = new Map();
    
    this.stats = {
      totalProducts: 0,
      totalImages: 0,
      totalVideos: 0,
      categoriesIndexed: 0,
      brandsIndexed: 0,
      collaborationsIndexed: 0,
      startTime: Date.now()
    };
    
    this.logFile = null;
    
    // Enterprise Schema Definitions
    this.schema = {
      categories: {
        '1. SNEAKERS': { id: 1, name_es: 'Zapatillas', name_en: 'Sneakers', priority: 1 },
        '2. SANDALS': { id: 2, name_es: 'Sandalias', name_en: 'Sandals', priority: 2 },
        '3. FORMAL': { id: 3, name_es: 'Formales', name_en: 'Formal', priority: 3 },
        '4. CASUAL': { id: 4, name_es: 'Casuales', name_en: 'Casual', priority: 4 },
        '5. KIDS': { id: 5, name_es: 'Niños', name_en: 'Kids', priority: 5 }
      },
      
      brands: {
        '1. NIKE Limited Edition': { id: 1, name: 'Nike', tier: 'Premium Athletic', priority: 1 },
        '2. ADIDAS Limited Edition': { id: 2, name: 'Adidas', tier: 'Premium Athletic', priority: 2 },
        '3. HERMES': { id: 3, name: 'Hermès', tier: 'Ultra Luxury', priority: 3 },
        '4. GUCCI': { id: 4, name: 'Gucci', tier: 'Luxury Fashion', priority: 4 },
        '5. DIOR': { id: 5, name: 'Dior', tier: 'Couture', priority: 5 },
        '6. LV': { id: 6, name: 'Louis Vuitton', tier: 'Luxury Fashion', priority: 6 },
        '7. BALENCIAGA': { id: 7, name: 'Balenciaga', tier: 'Avant-garde', priority: 7 },
        '8. CHANEL': { id: 8, name: 'Chanel', tier: 'Classic Luxury', priority: 8 },
        '9. LOUBOUTIN': { id: 9, name: 'Christian Louboutin', tier: 'Signature Luxury', priority: 9 },
        '10. OFF WHITE': { id: 10, name: 'Off-White', tier: 'Streetwear Luxury', priority: 10 }
      },
      
      genders: {
        '1. MIXTE': { id: 1, name_es: 'Unisex', name_en: 'Unisex', code: 'U' },
        '2. MEN': { id: 2, name_es: 'Hombres', name_en: 'Men', code: 'M' },
        '3. WOMEN': { id: 3, name_es: 'Mujeres', name_en: 'Women', code: 'W' }
      },
      
      collaborations: {
        // Fashion Houses
        'GUCCI': { id: 1, name: 'Gucci', type: 'Fashion House', tier: 'Ultra Luxury' },
        'LV': { id: 2, name: 'Louis Vuitton', type: 'Fashion House', tier: 'Ultra Luxury' },
        'DIOR': { id: 3, name: 'Dior', type: 'Fashion House', tier: 'Ultra Luxury' },
        'CHANEL': { id: 4, name: 'Chanel', type: 'Fashion House', tier: 'Ultra Luxury' },
        'HERMES': { id: 5, name: 'Hermès', type: 'Fashion House', tier: 'Ultra Luxury' },

        // Streetwear Brands
        'SUPREME': { id: 6, name: 'Supreme', type: 'Streetwear', tier: 'Premium' },
        'STUSSY': { id: 7, name: 'Stüssy', type: 'Streetwear', tier: 'Premium' },
        'BAPE': { id: 8, name: 'A Bathing Ape', type: 'Streetwear', tier: 'Premium' },
        'OFF WHITE': { id: 9, name: 'Off-White', type: 'Streetwear Luxury', tier: 'Luxury' },
        'FEAR OF GOD': { id: 10, name: 'Fear of God', type: 'Streetwear Luxury', tier: 'Luxury' },
        'FOG': { id: 11, name: 'Fear of God Essentials', type: 'Streetwear', tier: 'Premium' },

        // Artists & Musicians
        'TRAVIS SCOTT': { id: 12, name: 'Travis Scott', type: 'Artist', tier: 'Celebrity' },
        'DRAKE': { id: 13, name: 'Drake', type: 'Artist', tier: 'Celebrity' },
        'NOCTA': { id: 14, name: 'NOCTA (Drake)', type: 'Artist Brand', tier: 'Celebrity' },
        'BAD BUNNY': { id: 15, name: 'Bad Bunny', type: 'Artist', tier: 'Celebrity' },
        'J BALVIN': { id: 16, name: 'J Balvin', type: 'Artist', tier: 'Celebrity' },
        'TYLER THE CREATOR': { id: 17, name: 'Tyler, The Creator', type: 'Artist', tier: 'Celebrity' },
        'GOLF WANG': { id: 18, name: 'Golf Wang', type: 'Artist Brand', tier: 'Celebrity' },
        'PHARRELL': { id: 19, name: 'Pharrell Williams', type: 'Artist', tier: 'Celebrity' },
        'KANYE WEST': { id: 20, name: 'Kanye West', type: 'Artist', tier: 'Celebrity' },
        'YEEZY': { id: 21, name: 'Yeezy', type: 'Artist Brand', tier: 'Celebrity' },

        // Designers
        'VIRGIL ABLOH': { id: 22, name: 'Virgil Abloh', type: 'Designer', tier: 'Legacy' },
        'FRAGMENT': { id: 23, name: 'Fragment Design', type: 'Designer', tier: 'Premium' },
        'HIROSHI FUJIWARA': { id: 24, name: 'Hiroshi Fujiwara', type: 'Designer', tier: 'Premium' },
        'SACAI': { id: 25, name: 'Sacai', type: 'Japanese Fashion', tier: 'Luxury' },
        'CHITOSE ABE': { id: 26, name: 'Chitose Abe', type: 'Designer', tier: 'Luxury' },

        // Retailers & Boutiques
        'UNION LA': { id: 27, name: 'Union LA', type: 'Boutique', tier: 'Premium' },
        'UNDEFEATED': { id: 28, name: 'Undefeated', type: 'Boutique', tier: 'Premium' },
        'KITH': { id: 29, name: 'Kith', type: 'Boutique', tier: 'Premium' },
        'CONCEPTS': { id: 30, name: 'Concepts', type: 'Boutique', tier: 'Premium' },

        // Outdoor & Sports
        'THE NORTH FACE': { id: 31, name: 'The North Face', type: 'Outdoor', tier: 'Premium' },
        'STONE ISLAND': { id: 32, name: 'Stone Island', type: 'Technical', tier: 'Luxury' },

        // Jewelry & Accessories
        'TIFFANY': { id: 33, name: 'Tiffany & Co', type: 'Jewelry', tier: 'Ultra Luxury' },
        'CARTIER': { id: 34, name: 'Cartier', type: 'Jewelry', tier: 'Ultra Luxury' },

        // Fashion Brands
        'JACQUEMUS': { id: 35, name: 'Jacquemus', type: 'French Fashion', tier: 'Luxury' },
        'LEVIS': { id: 36, name: "Levi's", type: 'Denim', tier: 'Classic' },
        'CARHARTT': { id: 37, name: 'Carhartt WIP', type: 'Workwear', tier: 'Premium' }
      },

      limitedEditions: {
        'CHICAGO': { id: 1, name: 'Chicago', colorway: 'White/Black/Red', nickname: 'Chicagos' },
        'BRED': { id: 2, name: 'Bred', colorway: 'Black/Red', nickname: 'Breds' },
        'ROYAL': { id: 3, name: 'Royal', colorway: 'Black/Royal Blue', nickname: 'Royals' },
        'SHADOW': { id: 4, name: 'Shadow', colorway: 'Black/Grey', nickname: 'Shadows' },
        'SHATTERED BACKBOARD': { id: 5, name: 'Shattered Backboard', colorway: 'Orange/Black', nickname: 'SBB' },
        'FRAGMENT': { id: 6, name: 'Fragment', colorway: 'White/Royal Blue', nickname: 'Frags' },
        'TRAVIS SCOTT': { id: 7, name: 'Travis Scott', colorway: 'Brown/Black', nickname: 'Cactus Jack' },
        'OFF WHITE': { id: 8, name: 'Off-White', colorway: 'Various', nickname: 'The Ten' },
        'UNION': { id: 9, name: 'Union LA', colorway: 'Various', nickname: 'Union' },
        'DIOR': { id: 10, name: 'Dior', colorway: 'Grey/White', nickname: 'Dior Jordans' }
      },

      materials: {
        'LEATHER': { id: 1, name_es: 'Cuero', name_en: 'Leather', premium: true },
        'SUEDE': { id: 2, name_es: 'Ante', name_en: 'Suede', premium: true },
        'CANVAS': { id: 3, name_es: 'Lona', name_en: 'Canvas', premium: false },
        'MESH': { id: 4, name_es: 'Malla', name_en: 'Mesh', premium: false },
        'PATENT': { id: 5, name_es: 'Charol', name_en: 'Patent Leather', premium: true },
        'NUBUCK': { id: 6, name_es: 'Nobuk', name_en: 'Nubuck', premium: true },
        'FLYKNIT': { id: 7, name_es: 'Flyknit', name_en: 'Flyknit', premium: true },
        'PRIMEKNIT': { id: 8, name_es: 'Primeknit', name_en: 'Primeknit', premium: true }
      },

      colors: {
        'BLACK': { id: 1, name_es: 'Negro', name_en: 'Black', hex: '#000000' },
        'WHITE': { id: 2, name_es: 'Blanco', name_en: 'White', hex: '#FFFFFF' },
        'RED': { id: 3, name_es: 'Rojo', name_en: 'Red', hex: '#FF0000' },
        'BLUE': { id: 4, name_es: 'Azul', name_en: 'Blue', hex: '#0000FF' },
        'GREEN': { id: 5, name_es: 'Verde', name_en: 'Green', hex: '#00FF00' },
        'YELLOW': { id: 6, name_es: 'Amarillo', name_en: 'Yellow', hex: '#FFFF00' },
        'ORANGE': { id: 7, name_es: 'Naranja', name_en: 'Orange', hex: '#FFA500' },
        'PURPLE': { id: 8, name_es: 'Morado', name_en: 'Purple', hex: '#800080' },
        'PINK': { id: 9, name_es: 'Rosa', name_en: 'Pink', hex: '#FFC0CB' },
        'GREY': { id: 10, name_es: 'Gris', name_en: 'Grey', hex: '#808080' },
        'BROWN': { id: 11, name_es: 'Marrón', name_en: 'Brown', hex: '#A52A2A' },
        'GOLD': { id: 12, name_es: 'Dorado', name_en: 'Gold', hex: '#FFD700' },
        'SILVER': { id: 13, name_es: 'Plateado', name_en: 'Silver', hex: '#C0C0C0' }
      }
    };
  }

  async initialize() {
    console.log('🗄️ ENTERPRISE DATABASE INDEXER - TWL PERFECTIONIST EDITION');
    console.log('============================================================');
    console.log('📊 CREATING ENTERPRISE-GRADE PRODUCT DATABASE');
    console.log('🔍 MULTI-DIMENSIONAL SEGMENTATION & INDEXING');
    console.log('🎯 SEARCH OPTIMIZATION & PERFORMANCE ANALYTICS');
    console.log('');
    
    // Create output directories
    await this.createDirectories();
    
    // Initialize logging
    await this.initializeLogging();
    
    this.log('✅ Enterprise Database Indexer initialized successfully');
  }

  async createDirectories() {
    const dirs = [
      this.outputPath,
      path.join(this.outputPath, 'indexes'),
      path.join(this.outputPath, 'schemas'),
      path.join(this.outputPath, 'analytics'),
      path.join(process.cwd(), 'logs')
    ];
    
    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`✅ Database directory ready: ${path.relative(process.cwd(), dir)}`);
      } catch (error) {
        console.log(`📁 Directory exists: ${path.relative(process.cwd(), dir)}`);
      }
    }
  }

  async initializeLogging() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.logFile = path.join(process.cwd(), 'logs', `database-indexing-${timestamp}.log`);
    
    await this.log('=== ENTERPRISE DATABASE INDEXING SESSION STARTED ===');
    await this.log(`Timestamp: ${new Date().toISOString()}`);
    await this.log(`Products Path: ${this.productsBasePath}`);
    await this.log(`Output Path: ${this.outputPath}`);
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    console.log(message);
    
    if (this.logFile) {
      try {
        await fs.appendFile(this.logFile, logEntry);
      } catch (error) {
        console.error('❌ Logging error:', error.message);
      }
    }
  }

  async scanAndIndexProducts() {
    await this.log('🔍 Scanning and indexing all products...');
    
    await this.scanDirectoryRecursive(this.productsBasePath, []);
    
    this.stats.totalProducts = this.productIndex.size;
    
    await this.log(`📊 Indexing complete: ${this.stats.totalProducts} products indexed`);
    
    return this.productIndex;
  }

  async scanDirectoryRecursive(dirPath, pathComponents) {
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      const directories = items.filter(item => item.isDirectory());
      const files = items.filter(item => item.isFile());
      
      // Check if this is a product folder (contains images/videos)
      const hasProductFiles = files.some(file =>
        /\.(jpg|jpeg|png|webp|mp4|mov)$/i.test(file.name)
      );

      // Index ANY folder with product files, regardless of depth
      // This handles edge cases like products without gender folders,
      // different brand structures, etc.
      if (hasProductFiles && pathComponents.length >= 2) {
        await this.indexProduct(dirPath, pathComponents, files);
      }
      
      // Recursively scan subdirectories
      for (const dir of directories) {
        const subPath = path.join(dirPath, dir.name);
        const newComponents = [...pathComponents, dir.name];
        await this.scanDirectoryRecursive(subPath, newComponents);
      }
      
    } catch (error) {
      await this.log(`⚠️  Error scanning ${dirPath}: ${error.message}`);
    }
  }

  async indexProduct(productPath, pathComponents, files) {
    try {
      // Extract product information from path
      const productInfo = this.extractProductInfo(pathComponents, files);
      
      // Generate unique product ID
      const productId = this.generateProductId(productInfo);
      
      // Create comprehensive product record
      const productRecord = {
        id: productId,
        sku: productInfo.sku,
        path: productPath,
        pathComponents,

        // Hierarchical Classification
        category: productInfo.category,
        brand: productInfo.brand,
        productFamily: productInfo.productFamily,
        collaboration: productInfo.collaboration,
        gender: productInfo.gender,

        // Enhanced Classification
        limitedEdition: this.extractLimitedEdition(productInfo),
        artistEdition: this.extractArtistEdition(productInfo),
        designerEdition: this.extractDesignerEdition(productInfo),
        colorway: this.extractColorway(productInfo),
        materials: this.extractMaterials(productInfo),
        releaseYear: this.extractReleaseYear(productInfo),

        // ULTIMATE SCHEMA CLASSIFICATIONS
        // Size & Fit
        sizing: this.extractSizing(productInfo),
        fitRecommendation: this.extractFitRecommendation(productInfo),

        // Condition & Authenticity
        condition: this.extractCondition(productInfo),
        authenticity: this.extractAuthenticity(productInfo),
        boxCondition: this.extractBoxCondition(productInfo),

        // Rarity & Market
        rarityScore: this.calculateRarityScore(productInfo),
        hypeLevel: this.calculateHypeLevel(productInfo),
        productionNumbers: this.estimateProductionNumbers(productInfo),

        // Technical Specifications
        technologies: this.extractTechnologies(productInfo),
        soleType: this.extractSoleType(productInfo),
        closureType: this.extractClosureType(productInfo),
        performanceFeatures: this.extractPerformanceFeatures(productInfo),

        // Cultural & Historical
        culturalSignificance: this.extractCulturalSignificance(productInfo),
        celebrityEndorsements: this.extractCelebrityEndorsements(productInfo),
        historicalImportance: this.extractHistoricalImportance(productInfo),

        // Seasonal & Trends
        seasonSuitability: this.extractSeasonSuitability(productInfo),
        trendCategory: this.extractTrendCategory(productInfo),

        // Sustainability & Ethics
        sustainabilityScore: this.calculateSustainabilityScore(productInfo),
        ethicalProduction: this.extractEthicalProduction(productInfo),

        // Investment & Collectibility
        investmentGrade: this.calculateInvestmentGrade(productInfo),
        collectibilityScore: this.calculateCollectibilityScore(productInfo),
        priceAppreciation: this.calculatePriceAppreciation(productInfo),

        // Regional & Localization
        mexicanRelevance: this.calculateMexicanRelevance(productInfo),
        regionalPreferences: this.extractRegionalPreferences(productInfo),

        // Social Media & Virality
        viralityPotential: this.calculateViralityPotential(productInfo),
        socialMediaMentions: this.extractSocialMediaMentions(productInfo),

        // Advanced Search Dimensions
        occasionSuitability: this.extractOccasionSuitability(productInfo),
        lifestyleMatching: this.extractLifestyleMatching(productInfo),
        weatherAppropriateness: this.extractWeatherAppropriateness(productInfo),

        // Competitive Intelligence
        competitivePosition: this.analyzeCompetitivePosition(productInfo),
        marketComparison: this.generateMarketComparison(productInfo),

        // Collaboration Details
        collaborationType: this.getCollaborationType(productInfo),
        collaborationTier: this.getCollaborationTier(productInfo),

        // Brand Classification
        brandTier: this.getBrandTier(productInfo),
        brandType: this.getBrandType(productInfo),

        // Media Assets
        images: files.filter(f => /\.(jpg|jpeg|png|webp)$/i.test(f.name)).length,
        videos: files.filter(f => /\.(mp4|mov)$/i.test(f.name)).length,
        mediaFiles: files.map(f => f),

        // Search Optimization
        searchKeywords: this.generateSearchKeywords(productInfo),
        displayName: this.generateDisplayName(productInfo),

        // Multi-language Support
        names: {
          es: this.generateSpanishName(productInfo),
          en: this.generateEnglishName(productInfo)
        },

        // Product Attributes
        isExclusive: this.isExclusiveProduct(productInfo),
        isCollaboration: !!productInfo.collaboration,
        isLimitedEdition: this.isLimitedEditionProduct(productInfo),
        isArtistEdition: this.isArtistEditionProduct(productInfo),

        // Analytics
        indexedAt: new Date().toISOString(),
        popularity: 0,
        viewCount: 0,
        trendingScore: this.calculateTrendingScore(productInfo),

        // E-commerce
        price: null, // To be populated later
        priceRange: this.estimatePriceRange(productInfo),
        availability: 'in_stock',
        featured: this.isFeaturedProduct(productInfo),

        // SEO & Marketing
        seoTitle: this.generateSEOTitle(productInfo),
        seoDescription: this.generateSEODescription(productInfo),
        marketingTags: this.generateMarketingTags(productInfo)
      };
      
      // Store in main index
      this.productIndex.set(productId, productRecord);
      
      // Update category indexes
      this.updateCategoryIndexes(productRecord);
      
      // Update search index
      this.updateSearchIndex(productRecord);
      
      this.stats.totalImages += productRecord.images;
      this.stats.totalVideos += productRecord.videos;
      
      await this.log(`✅ Indexed: ${productRecord.displayName} (${productRecord.sku})`);
      
    } catch (error) {
      await this.log(`❌ Failed to index product at ${productPath}: ${error.message}`);
    }
  }

  extractProductInfo(pathComponents, files) {
    // Handle FLEXIBLE CYTTE hierarchy with edge cases
    const category = pathComponents[0] || 'unknown';
    const brand = pathComponents[1] || 'unknown';

    let productFamily = null;
    let collaboration = null;
    let gender = 'MIXTE'; // Default to unisex
    let sku = 'unknown';

    // EDGE CASE 1: Direct product under brand (no gender folder)
    // Example: /1. SNEAKERS/1. NIKE Limited Edition/FQ6891-001 -- Bode x Nike
    if (pathComponents.length === 3) {
      sku = pathComponents[2];
      // Extract collaboration from SKU name
      const skuParts = sku.split(' -- ');
      if (skuParts.length > 1) {
        collaboration = this.extractCollaborationFromName(skuParts[1]);
      }
      return { category, brand, productFamily, collaboration, gender, sku, files };
    }

    // EDGE CASE 2: Non-numbered brand structure
    // Example: /1. SNEAKERS/Common Project/13402148663024
    if (!brand.match(/^\d+\./)) {
      sku = pathComponents[pathComponents.length - 1];
      return { category, brand, productFamily, collaboration, gender, sku, files };
    }

    // STANDARD HIERARCHY ANALYSIS
    for (let i = 2; i < pathComponents.length; i++) {
      const component = pathComponents[i];

      // Skip 'webp' folders (extra media folders)
      if (component.toLowerCase() === 'webp') {
        continue;
      }

      // Check for gender
      if (/(MIXTE|MEN|WOMEN)/i.test(component)) {
        gender = component;

        // Look for collaboration after gender
        if (i + 1 < pathComponents.length) {
          const nextComponent = pathComponents[i + 1];

          if (/^\d+\.\s*/.test(nextComponent)) {
            collaboration = nextComponent.replace(/^\d+\.\s*/, '').toUpperCase();
          }

          // SKU is usually the last meaningful component
          sku = pathComponents[pathComponents.length - 1];
          if (sku.toLowerCase() === 'webp') {
            sku = pathComponents[pathComponents.length - 2];
          }
        }
        break;
      }

      // Check for product family (numbered components before gender)
      if (!productFamily && /^\d+\./.test(component) && !/(MIXTE|MEN|WOMEN)/i.test(component)) {
        productFamily = component;
      }
    }

    // If no gender found, look for direct collaboration patterns
    if (gender === 'MIXTE' && !collaboration) {
      for (let i = 2; i < pathComponents.length; i++) {
        const component = pathComponents[i];
        if (/^\d+\.\s*/.test(component)) {
          const cleanName = component.replace(/^\d+\.\s*/, '').toUpperCase();
          if (this.isKnownCollaboration(cleanName)) {
            collaboration = cleanName;
            break;
          }
        }
      }
    }

    // Final SKU extraction if not found
    if (sku === 'unknown') {
      sku = pathComponents[pathComponents.length - 1];
      if (sku.toLowerCase() === 'webp') {
        sku = pathComponents[pathComponents.length - 2];
      }
    }

    // Enhanced collaboration detection from all components
    if (!collaboration) {
      collaboration = this.detectCollaborationFromPath(pathComponents);
    }

    return {
      category,
      brand,
      productFamily,
      collaboration,
      gender,
      sku,
      files
    };
  }

  extractCollaborationFromName(name) {
    const collaborationKeywords = [
      'GUCCI', 'LV', 'SUPREME', 'STUSSY', 'THE NORTH FACE', 'BAPE', 'DIOR',
      'OFF WHITE', 'TIFFANY', 'SACAI', 'TRAVIS SCOTT', 'DRAKE', 'NOCTA',
      'BAD BUNNY', 'VIRGIL ABLOH', 'FRAGMENT', 'UNION LA', 'UNDEFEATED',
      'BODE', 'HERMES', 'CHANEL', 'BALENCIAGA', 'PRADA'
    ];

    const upperName = name.toUpperCase();
    for (const keyword of collaborationKeywords) {
      if (upperName.includes(keyword)) {
        return keyword;
      }
    }
    return null;
  }

  isKnownCollaboration(name) {
    const collaborations = [
      'GUCCI', 'LV', 'SUPREME', 'STUSSY', 'THE NORTH FACE', 'BAPE', 'DIOR',
      'OFF WHITE', 'TIFFANY', 'SACAI', 'TRAVIS SCOTT', 'DRAKE', 'NOCTA',
      'BAD BUNNY', 'VIRGIL ABLOH', 'FRAGMENT', 'UNION LA', 'UNDEFEATED',
      'BODE', 'HERMES', 'CHANEL', 'BALENCIAGA', 'PRADA', 'OTHERS'
    ];
    return collaborations.includes(name);
  }

  detectCollaborationFromPath(pathComponents) {
    const allText = pathComponents.join(' ').toUpperCase();

    const collaborationKeywords = [
      'GUCCI', 'LV', 'SUPREME', 'STUSSY', 'THE NORTH FACE', 'BAPE', 'DIOR',
      'OFF WHITE', 'TIFFANY', 'SACAI', 'TRAVIS SCOTT', 'DRAKE', 'NOCTA',
      'BAD BUNNY', 'VIRGIL ABLOH', 'FRAGMENT', 'UNION LA', 'UNDEFEATED',
      'BODE', 'HERMES', 'CHANEL', 'BALENCIAGA', 'PRADA'
    ];

    for (const keyword of collaborationKeywords) {
      if (allText.includes(keyword)) {
        return keyword;
      }
    }
    return null;
  }

  generateProductId(productInfo) {
    // Create TRULY unique ID based on full path + timestamp
    const fullPath = productInfo.files.join('|') + productInfo.sku + productInfo.brand + productInfo.category + productInfo.gender + (productInfo.productFamily || '') + (productInfo.collaboration || '');
    const timestamp = Date.now().toString();
    const randomSuffix = Math.random().toString(36).substring(2, 8);
    const idString = `${fullPath}-${timestamp}-${randomSuffix}`;
    return crypto.createHash('md5').update(idString).digest('hex').substring(0, 12);
  }

  generateSearchKeywords(productInfo) {
    const keywords = new Set();

    // Basic product information
    keywords.add(productInfo.sku.toLowerCase());
    keywords.add(productInfo.brand.toLowerCase().replace(/\d+\.\s*/, ''));
    keywords.add(productInfo.category.toLowerCase().replace(/\d+\.\s*/, ''));

    // Product family keywords
    if (productInfo.productFamily) {
      const family = productInfo.productFamily.toLowerCase().replace(/\d+\.\s*/, '');
      keywords.add(family);

      // Add family-specific keywords
      if (family.includes('air force')) {
        keywords.add('af1');
        keywords.add('air force 1');
        keywords.add('force');
      }
      if (family.includes('air jordan')) {
        keywords.add('jordan');
        keywords.add('aj');
        keywords.add('jumpman');
      }
      if (family.includes('lebron')) {
        keywords.add('lebron james');
        keywords.add('king james');
        keywords.add('lbj');
      }
    }

    // Collaboration keywords
    if (productInfo.collaboration) {
      const collab = productInfo.collaboration.toLowerCase();
      keywords.add(collab);

      // Add collaboration-specific keywords
      const collabData = this.schema.collaborations[productInfo.collaboration.toUpperCase()];
      if (collabData) {
        keywords.add(collabData.name.toLowerCase());
        keywords.add(collabData.type.toLowerCase());

        // Artist-specific keywords
        if (collabData.type === 'Artist') {
          if (collab.includes('travis scott')) {
            keywords.add('cactus jack');
            keywords.add('travis');
            keywords.add('scott');
            keywords.add('la flame');
          }
          if (collab.includes('drake')) {
            keywords.add('nocta');
            keywords.add('ovo');
            keywords.add('6 god');
          }
          if (collab.includes('bad bunny')) {
            keywords.add('benito');
            keywords.add('reggaeton');
          }
          if (collab.includes('pharrell')) {
            keywords.add('pharrell williams');
            keywords.add('nerd');
            keywords.add('human race');
          }
        }

        // Designer-specific keywords
        if (collabData.type === 'Designer') {
          if (collab.includes('virgil')) {
            keywords.add('virgil abloh');
            keywords.add('off white');
            keywords.add('the ten');
          }
          if (collab.includes('fragment')) {
            keywords.add('hiroshi fujiwara');
            keywords.add('fragment design');
            keywords.add('lightning bolt');
          }
        }
      }
    }

    // Gender keywords
    keywords.add(productInfo.gender.toLowerCase().replace(/\d+\.\s*/, ''));
    if (productInfo.gender.includes('MIXTE')) {
      keywords.add('unisex');
      keywords.add('mixed');
    }

    // Brand-specific keywords
    const brandName = productInfo.brand.toLowerCase();
    if (brandName.includes('nike')) {
      keywords.add('nike');
      keywords.add('swoosh');
      keywords.add('athletic');
      keywords.add('sportswear');
      keywords.add('just do it');
    }

    if (brandName.includes('adidas')) {
      keywords.add('adidas');
      keywords.add('three stripes');
      keywords.add('trefoil');
      keywords.add('boost');
    }

    if (brandName.includes('gucci')) {
      keywords.add('gucci');
      keywords.add('luxury');
      keywords.add('italian');
      keywords.add('fashion house');
      keywords.add('ace');
      keywords.add('rython');
    }

    if (brandName.includes('dior')) {
      keywords.add('dior');
      keywords.add('christian dior');
      keywords.add('couture');
      keywords.add('b23');
      keywords.add('b27');
    }

    if (brandName.includes('lv') || brandName.includes('louis vuitton')) {
      keywords.add('louis vuitton');
      keywords.add('lv');
      keywords.add('monogram');
      keywords.add('damier');
      keywords.add('trainer');
    }

    if (brandName.includes('balenciaga')) {
      keywords.add('balenciaga');
      keywords.add('triple s');
      keywords.add('speed');
      keywords.add('phantom');
      keywords.add('avant garde');
    }

    if (brandName.includes('chanel')) {
      keywords.add('chanel');
      keywords.add('coco chanel');
      keywords.add('quilted');
      keywords.add('classic');
    }

    // Extract colors from SKU or path
    this.extractColorKeywords(productInfo, keywords);

    // Extract materials
    this.extractMaterialKeywords(productInfo, keywords);

    // Extract limited edition keywords
    this.extractLimitedEditionKeywords(productInfo, keywords);

    // Extract year/season keywords
    this.extractTemporalKeywords(productInfo, keywords);

    // Category-specific keywords
    if (productInfo.category.includes('SNEAKERS')) {
      keywords.add('sneakers');
      keywords.add('zapatillas');
      keywords.add('shoes');
      keywords.add('kicks');
      keywords.add('footwear');
    }

    if (productInfo.category.includes('SANDALS')) {
      keywords.add('sandals');
      keywords.add('sandalias');
      keywords.add('slides');
      keywords.add('mules');
    }

    return Array.from(keywords);
  }

  extractColorKeywords(productInfo, keywords) {
    const pathString = productInfo.sku.toLowerCase() + ' ' + productInfo.files.join(' ').toLowerCase();

    // Check for color indicators in SKU and filenames
    for (const [colorKey, colorData] of Object.entries(this.schema.colors)) {
      if (pathString.includes(colorKey.toLowerCase()) ||
          pathString.includes(colorData.name_en.toLowerCase()) ||
          pathString.includes(colorData.name_es.toLowerCase())) {
        keywords.add(colorData.name_en.toLowerCase());
        keywords.add(colorData.name_es.toLowerCase());
        keywords.add(colorKey.toLowerCase());
      }
    }

    // Special colorway keywords
    if (pathString.includes('chicago') || pathString.includes('white red black')) {
      keywords.add('chicago');
      keywords.add('chicagos');
      keywords.add('white red black');
    }

    if (pathString.includes('bred') || pathString.includes('black red')) {
      keywords.add('bred');
      keywords.add('breds');
      keywords.add('black red');
    }

    if (pathString.includes('royal') || pathString.includes('black blue')) {
      keywords.add('royal');
      keywords.add('royals');
      keywords.add('black blue');
    }
  }

  extractMaterialKeywords(productInfo, keywords) {
    const pathString = productInfo.sku.toLowerCase() + ' ' + productInfo.files.join(' ').toLowerCase();

    for (const [materialKey, materialData] of Object.entries(this.schema.materials)) {
      if (pathString.includes(materialKey.toLowerCase()) ||
          pathString.includes(materialData.name_en.toLowerCase()) ||
          pathString.includes(materialData.name_es.toLowerCase())) {
        keywords.add(materialData.name_en.toLowerCase());
        keywords.add(materialData.name_es.toLowerCase());
        keywords.add(materialKey.toLowerCase());

        if (materialData.premium) {
          keywords.add('premium');
          keywords.add('luxury material');
        }
      }
    }
  }

  extractLimitedEditionKeywords(productInfo, keywords) {
    const pathString = productInfo.sku.toLowerCase() + ' ' + productInfo.files.join(' ').toLowerCase();

    for (const [editionKey, editionData] of Object.entries(this.schema.limitedEditions)) {
      if (pathString.includes(editionKey.toLowerCase()) ||
          pathString.includes(editionData.name.toLowerCase()) ||
          pathString.includes(editionData.nickname.toLowerCase())) {
        keywords.add(editionData.name.toLowerCase());
        keywords.add(editionData.nickname.toLowerCase());
        keywords.add('limited edition');
        keywords.add('exclusive');
        keywords.add(editionData.colorway.toLowerCase());
      }
    }
  }

  extractTemporalKeywords(productInfo, keywords) {
    const currentYear = new Date().getFullYear();
    const pathString = productInfo.sku.toLowerCase();

    // Extract years from SKU
    for (let year = 2020; year <= currentYear + 1; year++) {
      if (pathString.includes(year.toString())) {
        keywords.add(year.toString());
        keywords.add(`${year} release`);
      }
    }

    // Season keywords
    keywords.add('2024');
    keywords.add('2025');
    keywords.add('latest');
    keywords.add('new release');
  }

  // Enhanced Classification Methods
  extractLimitedEdition(productInfo) {
    const pathString = (productInfo.sku + ' ' + productInfo.files.join(' ')).toLowerCase();

    for (const [editionKey, editionData] of Object.entries(this.schema.limitedEditions)) {
      if (pathString.includes(editionKey.toLowerCase()) ||
          pathString.includes(editionData.name.toLowerCase())) {
        return {
          name: editionData.name,
          nickname: editionData.nickname,
          colorway: editionData.colorway
        };
      }
    }

    return null;
  }

  extractArtistEdition(productInfo) {
    if (!productInfo.collaboration) return null;

    const collabData = this.schema.collaborations[productInfo.collaboration.toUpperCase()];
    if (collabData && collabData.type === 'Artist') {
      return {
        artist: collabData.name,
        type: 'Artist Collaboration'
      };
    }

    return null;
  }

  extractDesignerEdition(productInfo) {
    if (!productInfo.collaboration) return null;

    const collabData = this.schema.collaborations[productInfo.collaboration.toUpperCase()];
    if (collabData && collabData.type === 'Designer') {
      return {
        designer: collabData.name,
        type: 'Designer Collaboration'
      };
    }

    return null;
  }

  extractColorway(productInfo) {
    const pathString = (productInfo.sku + ' ' + productInfo.files.join(' ')).toLowerCase();
    const colors = [];

    // Enhanced color detection from SKU patterns
    const colorPatterns = {
      'white': ['white', 'blanc', 'blanco', '100', 'wht'],
      'black': ['black', 'noir', 'negro', '001', 'blk'],
      'red': ['red', 'rouge', 'rojo', 'bred', '600'],
      'blue': ['blue', 'bleu', 'azul', 'royal', '400'],
      'green': ['green', 'vert', 'verde', '300'],
      'yellow': ['yellow', 'jaune', 'amarillo', '700'],
      'orange': ['orange', 'naranja', '800'],
      'purple': ['purple', 'violet', 'morado', '500'],
      'pink': ['pink', 'rose', 'rosa', '601'],
      'grey': ['grey', 'gray', 'gris', '002', '003'],
      'brown': ['brown', 'marron', 'marrón', '200'],
      'gold': ['gold', 'dorado', '900'],
      'silver': ['silver', 'plateado', '001']
    };

    // Check SKU for color codes
    for (const [color, patterns] of Object.entries(colorPatterns)) {
      for (const pattern of patterns) {
        if (pathString.includes(pattern)) {
          colors.push(color.charAt(0).toUpperCase() + color.slice(1));
          break;
        }
      }
    }

    // Special colorway detection for known models
    if (pathString.includes('chicago') || (pathString.includes('white') && pathString.includes('red') && pathString.includes('black'))) {
      return 'Chicago (White/Black/Red)';
    }

    if (pathString.includes('bred') || (pathString.includes('black') && pathString.includes('red'))) {
      return 'Bred (Black/Red)';
    }

    if (pathString.includes('royal') || (pathString.includes('black') && pathString.includes('blue'))) {
      return 'Royal (Black/Blue)';
    }

    // Return detected colors or intelligent default
    if (colors.length > 0) {
      return colors.join('/');
    }

    // Intelligent default based on brand/collaboration
    if (productInfo.collaboration?.includes('GUCCI')) {
      return 'Green/Red/White';
    }

    if (productInfo.collaboration?.includes('LV')) {
      return 'Brown/Gold';
    }

    if (productInfo.collaboration?.includes('DIOR')) {
      return 'Grey/White';
    }

    return 'White/Black'; // Most common sneaker colorway
  }

  extractMaterials(productInfo) {
    const pathString = (productInfo.sku + ' ' + productInfo.files.join(' ')).toLowerCase();
    const materials = [];

    // Enhanced material detection patterns
    const materialPatterns = {
      'Leather': ['leather', 'cuero', 'leder', 'pelle'],
      'Suede': ['suede', 'ante', 'daim', 'scamosciato'],
      'Canvas': ['canvas', 'lona', 'toile', 'tela'],
      'Mesh': ['mesh', 'malla', 'filet', 'rete'],
      'Patent Leather': ['patent', 'charol', 'vernis', 'verniciato'],
      'Nubuck': ['nubuck', 'nobuk', 'nubuk'],
      'Flyknit': ['flyknit', 'fly knit'],
      'Primeknit': ['primeknit', 'prime knit'],
      'Knit': ['knit', 'punto', 'tricot', 'maglia'],
      'Rubber': ['rubber', 'goma', 'caoutchouc', 'gomma']
    };

    // Check for material keywords
    for (const [material, patterns] of Object.entries(materialPatterns)) {
      for (const pattern of patterns) {
        if (pathString.includes(pattern)) {
          materials.push(material);
          break;
        }
      }
    }

    // Brand-specific material intelligence
    if (materials.length === 0) {
      // Luxury brands typically use premium materials
      if (productInfo.brand.includes('GUCCI') || productInfo.brand.includes('LV') ||
          productInfo.brand.includes('DIOR') || productInfo.brand.includes('CHANEL')) {
        materials.push('Premium Leather');
      }

      // Nike tech materials
      else if (productInfo.brand.includes('NIKE')) {
        if (productInfo.productFamily?.includes('FLYKNIT')) {
          materials.push('Flyknit');
        } else if (productInfo.productFamily?.includes('AIR FORCE')) {
          materials.push('Leather');
        } else if (productInfo.productFamily?.includes('JORDAN')) {
          materials.push('Leather', 'Synthetic');
        } else {
          materials.push('Synthetic Leather');
        }
      }

      // Adidas materials
      else if (productInfo.brand.includes('ADIDAS')) {
        if (productInfo.productFamily?.includes('PRIMEKNIT')) {
          materials.push('Primeknit');
        } else {
          materials.push('Synthetic Leather');
        }
      }

      // Balenciaga typically uses mixed materials
      else if (productInfo.brand.includes('BALENCIAGA')) {
        materials.push('Mesh', 'Synthetic');
      }

      // Default for other brands
      else {
        materials.push('Synthetic Leather');
      }
    }

    return materials;
  }

  extractReleaseYear(productInfo) {
    const currentYear = new Date().getFullYear();
    const pathString = productInfo.sku.toLowerCase();

    for (let year = 2020; year <= currentYear + 1; year++) {
      if (pathString.includes(year.toString())) {
        return year;
      }
    }

    return currentYear; // Default to current year
  }

  getCollaborationType(productInfo) {
    if (!productInfo.collaboration) return null;

    const collabData = this.schema.collaborations[productInfo.collaboration.toUpperCase()];
    return collabData ? collabData.type : 'Unknown';
  }

  getCollaborationTier(productInfo) {
    if (!productInfo.collaboration) return null;

    const collabData = this.schema.collaborations[productInfo.collaboration.toUpperCase()];
    return collabData ? collabData.tier : 'Standard';
  }

  getBrandTier(productInfo) {
    const brandData = this.schema.brands[productInfo.brand];
    return brandData ? brandData.tier : 'Standard';
  }

  getBrandType(productInfo) {
    if (productInfo.brand.includes('NIKE') || productInfo.brand.includes('ADIDAS')) {
      return 'Athletic';
    }
    if (productInfo.brand.includes('GUCCI') || productInfo.brand.includes('LV') ||
        productInfo.brand.includes('DIOR') || productInfo.brand.includes('CHANEL')) {
      return 'Luxury Fashion';
    }
    return 'Fashion';
  }

  isExclusiveProduct(productInfo) {
    return !!(productInfo.collaboration ||
             productInfo.brand.includes('Limited') ||
             this.extractLimitedEdition(productInfo));
  }

  isLimitedEditionProduct(productInfo) {
    return !!this.extractLimitedEdition(productInfo);
  }

  isArtistEditionProduct(productInfo) {
    return !!this.extractArtistEdition(productInfo);
  }

  calculateTrendingScore(productInfo) {
    let score = 0;

    // Collaboration bonus
    if (productInfo.collaboration) score += 50;

    // Artist collaboration bonus
    if (this.extractArtistEdition(productInfo)) score += 30;

    // Limited edition bonus
    if (this.isLimitedEditionProduct(productInfo)) score += 40;

    // Brand tier bonus
    const brandTier = this.getBrandTier(productInfo);
    if (brandTier === 'Ultra Luxury') score += 25;
    else if (brandTier === 'Luxury Fashion') score += 20;
    else if (brandTier === 'Premium Athletic') score += 15;

    // Recent release bonus
    const releaseYear = this.extractReleaseYear(productInfo);
    if (releaseYear >= 2024) score += 20;

    return Math.min(score, 100); // Cap at 100
  }

  estimatePriceRange(productInfo) {
    const brandTier = this.getBrandTier(productInfo);
    const isCollab = !!productInfo.collaboration;
    const isLimited = this.isLimitedEditionProduct(productInfo);

    let basePrice = 100;

    // Brand tier pricing
    if (brandTier === 'Ultra Luxury') basePrice = 800;
    else if (brandTier === 'Luxury Fashion') basePrice = 600;
    else if (brandTier === 'Premium Athletic') basePrice = 200;

    // Collaboration multiplier
    if (isCollab) basePrice *= 1.5;

    // Limited edition multiplier
    if (isLimited) basePrice *= 1.3;

    return {
      min: Math.round(basePrice * 0.8),
      max: Math.round(basePrice * 1.5),
      currency: 'USD'
    };
  }

  generateSEOTitle(productInfo) {
    const displayName = this.generateDisplayName(productInfo);
    const category = productInfo.category.replace(/^\d+\.\s*/, '');
    return `${displayName} - Luxury ${category} | TWL`;
  }

  generateSEODescription(productInfo) {
    const brand = this.schema.brands[productInfo.brand]?.name || productInfo.brand;
    const category = productInfo.category.replace(/^\d+\.\s*/, '').toLowerCase();
    const collab = productInfo.collaboration ? ` x ${productInfo.collaboration}` : '';

    return `Shop authentic ${brand}${collab} ${category} at TWL. Premium luxury footwear with fast shipping to Mexico. SKU: ${productInfo.sku}`;
  }

  generateMarketingTags(productInfo) {
    const tags = [];

    if (this.isLimitedEditionProduct(productInfo)) tags.push('Limited Edition');
    if (this.isArtistEditionProduct(productInfo)) tags.push('Artist Collaboration');
    if (productInfo.collaboration) tags.push('Exclusive');
    if (this.getBrandTier(productInfo) === 'Ultra Luxury') tags.push('Ultra Luxury');
    if (this.extractReleaseYear(productInfo) >= 2024) tags.push('New Release');

    return tags;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 1: SIZE & FIT
  extractSizing(productInfo) {
    // Extract available sizes from product path/files
    const pathString = (productInfo.sku + ' ' + productInfo.files.join(' ')).toLowerCase();
    const availableSizes = [];

    // Enhanced size patterns
    const sizePatterns = [
      /size[\s-]?(\d+(?:\.\d+)?)/gi,
      /us[\s-]?(\d+(?:\.\d+)?)/gi,
      /(\d+(?:\.\d+)?)us/gi,
      /(\d+(?:\.\d+)?)\s*\.jpg/gi,
      /(\d+(?:\.\d+)?)\s*\.webp/gi,
      /(\d+(?:\.\d+)?)\s*\.png/gi
    ];

    for (const pattern of sizePatterns) {
      const matches = pathString.matchAll(pattern);
      for (const match of matches) {
        const size = parseFloat(match[1]);
        if (size >= 4 && size <= 18) { // Valid US shoe size range
          availableSizes.push(size);
        }
      }
    }

    // If no sizes found, generate typical size range based on gender
    if (availableSizes.length === 0) {
      if (productInfo.gender?.includes('WOMEN')) {
        // Women's typical range
        return {
          availableSizes: [5.5, 6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10],
          sizeRange: ULTIMATE_SCHEMA.sizing.ranges,
          defaultSystem: 'US',
          estimated: true
        };
      } else {
        // Men's/Unisex typical range
        return {
          availableSizes: [7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12],
          sizeRange: ULTIMATE_SCHEMA.sizing.ranges,
          defaultSystem: 'US',
          estimated: true
        };
      }
    }

    return {
      availableSizes: [...new Set(availableSizes)].sort((a, b) => a - b),
      sizeRange: ULTIMATE_SCHEMA.sizing.ranges,
      defaultSystem: 'US',
      estimated: false
    };
  }

  extractFitRecommendation(productInfo) {
    const brand = productInfo.brand.toLowerCase();

    // Brand-specific fit recommendations
    if (brand.includes('nike')) {
      if (productInfo.productFamily?.includes('AIR FORCE')) {
        return ULTIMATE_SCHEMA.sizing.fitTypes.TRUE_TO_SIZE;
      }
      if (productInfo.productFamily?.includes('JORDAN')) {
        return ULTIMATE_SCHEMA.sizing.fitTypes.TRUE_TO_SIZE;
      }
    }

    if (brand.includes('adidas')) {
      return ULTIMATE_SCHEMA.sizing.fitTypes.HALF_SIZE_UP;
    }

    if (brand.includes('balenciaga')) {
      return ULTIMATE_SCHEMA.sizing.fitTypes.RUNS_LARGE;
    }

    return ULTIMATE_SCHEMA.sizing.fitTypes.TRUE_TO_SIZE;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 2: CONDITION & AUTHENTICITY
  extractCondition(productInfo) {
    // For new products from CYTTE, assume deadstock
    return ULTIMATE_SCHEMA.condition.grades.DEADSTOCK;
  }

  extractAuthenticity(productInfo) {
    // TWL authenticated for all CYTTE products
    return ULTIMATE_SCHEMA.condition.authenticity.TWL_AUTHENTICATED;
  }

  extractBoxCondition(productInfo) {
    // Assume original box for new products
    return ULTIMATE_SCHEMA.condition.boxCondition.ORIGINAL_BOX;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 3: RARITY & MARKET
  calculateRarityScore(productInfo) {
    let score = 3; // Default: Common

    // Limited edition bonus
    if (this.isLimitedEditionProduct(productInfo)) score += 3;

    // Collaboration bonus
    if (productInfo.collaboration) score += 2;

    // Artist collaboration bonus
    if (this.isArtistEditionProduct(productInfo)) score += 2;

    // Brand tier bonus
    const brandTier = this.getBrandTier(productInfo);
    if (brandTier === 'Ultra Luxury') score += 2;

    // Cap at maximum rarity
    score = Math.min(score, 10);

    // Map to rarity levels
    if (score >= 9) return ULTIMATE_SCHEMA.rarity.scores.ULTRA_RARE;
    if (score >= 8) return ULTIMATE_SCHEMA.rarity.scores.EXTREMELY_RARE;
    if (score >= 7) return ULTIMATE_SCHEMA.rarity.scores.VERY_RARE;
    if (score >= 6) return ULTIMATE_SCHEMA.rarity.scores.RARE;
    if (score >= 5) return ULTIMATE_SCHEMA.rarity.scores.LIMITED;

    return ULTIMATE_SCHEMA.rarity.scores.COMMON;
  }

  calculateHypeLevel(productInfo) {
    let hypeScore = 2; // Default: No Hype

    // Artist collaborations = instant hype
    if (this.isArtistEditionProduct(productInfo)) hypeScore += 4;

    // Specific high-hype collaborations
    if (productInfo.collaboration) {
      const collab = productInfo.collaboration.toLowerCase();
      if (collab.includes('travis scott')) hypeScore += 4;
      if (collab.includes('off white') || collab.includes('virgil')) hypeScore += 3;
      if (collab.includes('supreme')) hypeScore += 3;
      if (collab.includes('fragment')) hypeScore += 2;
    }

    // Jordan hype
    if (productInfo.productFamily?.includes('JORDAN')) hypeScore += 2;

    // Limited edition hype
    if (this.isLimitedEditionProduct(productInfo)) hypeScore += 2;

    // Cap at maximum hype
    hypeScore = Math.min(hypeScore, 10);

    if (hypeScore >= 9) return ULTIMATE_SCHEMA.rarity.hypeLevel.GRAIL;
    if (hypeScore >= 8) return ULTIMATE_SCHEMA.rarity.hypeLevel.ULTRA_HYPE;
    if (hypeScore >= 6) return ULTIMATE_SCHEMA.rarity.hypeLevel.HIGH_HYPE;
    if (hypeScore >= 4) return ULTIMATE_SCHEMA.rarity.hypeLevel.MODERATE_HYPE;

    return ULTIMATE_SCHEMA.rarity.hypeLevel.LOW_HYPE;
  }

  estimateProductionNumbers(productInfo) {
    const rarityScore = this.calculateRarityScore(productInfo);
    return rarityScore.production;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 4: TECHNICAL SPECIFICATIONS
  extractTechnologies(productInfo) {
    const technologies = [];
    const pathString = (productInfo.sku + ' ' + productInfo.productFamily + ' ' + productInfo.brand).toLowerCase();

    // Nike technologies
    if (pathString.includes('air max')) technologies.push(ULTIMATE_SCHEMA.technical.technologies.AIR_MAX);
    if (pathString.includes('zoom')) technologies.push(ULTIMATE_SCHEMA.technical.technologies.ZOOM_AIR);
    if (pathString.includes('react')) technologies.push(ULTIMATE_SCHEMA.technical.technologies.REACT);
    if (pathString.includes('flyknit')) technologies.push(ULTIMATE_SCHEMA.technical.technologies.FLYKNIT);

    // Adidas technologies
    if (pathString.includes('boost')) technologies.push(ULTIMATE_SCHEMA.technical.technologies.BOOST);
    if (pathString.includes('primeknit')) technologies.push(ULTIMATE_SCHEMA.technical.technologies.PRIMEKNIT);

    return technologies;
  }

  extractSoleType(productInfo) {
    const pathString = productInfo.sku.toLowerCase();

    // Default to rubber for most sneakers
    if (productInfo.category.includes('SNEAKERS')) {
      return ULTIMATE_SCHEMA.technical.soleTypes.RUBBER;
    }

    return ULTIMATE_SCHEMA.technical.soleTypes.EVA;
  }

  extractClosureType(productInfo) {
    const pathString = productInfo.sku.toLowerCase();

    // Most sneakers use laces
    if (productInfo.category.includes('SNEAKERS')) {
      return ULTIMATE_SCHEMA.technical.closureTypes.LACES;
    }

    // Sandals are typically slip-on
    if (productInfo.category.includes('SANDALS')) {
      return ULTIMATE_SCHEMA.technical.closureTypes.SLIP_ON;
    }

    return ULTIMATE_SCHEMA.technical.closureTypes.LACES;
  }

  extractPerformanceFeatures(productInfo) {
    const features = [];

    if (productInfo.productFamily?.includes('JORDAN')) {
      features.push('Basketball Performance');
    }

    if (productInfo.productFamily?.includes('AIR MAX')) {
      features.push('Running Performance');
    }

    if (productInfo.category.includes('FORMAL')) {
      features.push('Dress Shoe');
    }

    return features;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 5: CULTURAL & HISTORICAL
  extractCulturalSignificance(productInfo) {
    const significance = [];

    if (productInfo.productFamily?.includes('JORDAN')) {
      significance.push(ULTIMATE_SCHEMA.cultural.significance.BASKETBALL);
      significance.push(ULTIMATE_SCHEMA.cultural.significance.HIP_HOP_CULTURE);
    }

    if (productInfo.brand.includes('NIKE') && productInfo.productFamily?.includes('DUNK')) {
      significance.push(ULTIMATE_SCHEMA.cultural.significance.SKATEBOARDING);
    }

    if (productInfo.collaboration?.includes('SUPREME') || productInfo.collaboration?.includes('STUSSY')) {
      significance.push(ULTIMATE_SCHEMA.cultural.significance.STREET_STYLE);
    }

    return significance;
  }

  extractCelebrityEndorsements(productInfo) {
    const celebrities = [];

    if (productInfo.productFamily?.includes('JORDAN')) {
      celebrities.push(ULTIMATE_SCHEMA.cultural.celebrities.MICHAEL_JORDAN);
    }

    if (productInfo.collaboration?.includes('TRAVIS SCOTT')) {
      celebrities.push(ULTIMATE_SCHEMA.cultural.celebrities.TRAVIS_SCOTT);
    }

    if (productInfo.collaboration?.includes('VIRGIL') || productInfo.collaboration?.includes('OFF WHITE')) {
      celebrities.push(ULTIMATE_SCHEMA.cultural.celebrities.VIRGIL_ABLOH);
    }

    return celebrities;
  }

  extractHistoricalImportance(productInfo) {
    let importance = 1; // Default low importance

    // Jordan 1 = historically significant
    if (productInfo.productFamily?.includes('JORDAN') && productInfo.sku.includes('1')) {
      importance = 10;
    }

    // Air Force 1 = historically significant
    if (productInfo.productFamily?.includes('AIR FORCE')) {
      importance = 8;
    }

    // First collaborations = historically important
    if (productInfo.collaboration?.includes('GUCCI') || productInfo.collaboration?.includes('LV')) {
      importance = 9;
    }

    return importance;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 6: SEASONAL & TRENDS
  extractSeasonSuitability(productInfo) {
    // Most sneakers are all-season
    if (productInfo.category.includes('SNEAKERS')) {
      return ULTIMATE_SCHEMA.seasonal.suitability.ALL_SEASON;
    }

    // Sandals are spring/summer
    if (productInfo.category.includes('SANDALS')) {
      return ULTIMATE_SCHEMA.seasonal.suitability.SPRING_SUMMER;
    }

    return ULTIMATE_SCHEMA.seasonal.suitability.ALL_SEASON;
  }

  extractTrendCategory(productInfo) {
    // Jordan = Retro
    if (productInfo.productFamily?.includes('JORDAN')) {
      return ULTIMATE_SCHEMA.seasonal.trendCategories.RETRO;
    }

    // Balenciaga = Futuristic
    if (productInfo.brand.includes('BALENCIAGA')) {
      return ULTIMATE_SCHEMA.seasonal.trendCategories.FUTURISTIC;
    }

    // Minimalist luxury brands
    if (productInfo.brand.includes('CHANEL') || productInfo.brand.includes('HERMES')) {
      return ULTIMATE_SCHEMA.seasonal.trendCategories.MINIMALIST;
    }

    return ULTIMATE_SCHEMA.seasonal.trendCategories.RETRO;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 7: SUSTAINABILITY & ETHICS
  calculateSustainabilityScore(productInfo) {
    let score = 5; // Default neutral score

    // Luxury brands often have better sustainability practices
    const brandTier = this.getBrandTier(productInfo);
    if (brandTier === 'Ultra Luxury') score += 2;

    // Recent releases more likely to be sustainable
    const releaseYear = this.extractReleaseYear(productInfo);
    if (releaseYear >= 2024) score += 1;

    return Math.min(score, 10);
  }

  extractEthicalProduction(productInfo) {
    // Assume ethical production for luxury brands
    const brandTier = this.getBrandTier(productInfo);
    if (brandTier === 'Ultra Luxury') {
      return {
        certified: true,
        certifications: ['Fair Trade', 'Ethical Manufacturing']
      };
    }

    return {
      certified: false,
      certifications: []
    };
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 8: INVESTMENT & COLLECTIBILITY
  calculateInvestmentGrade(productInfo) {
    const rarityScore = this.calculateRarityScore(productInfo);
    const hypeLevel = this.calculateHypeLevel(productInfo);

    // Ultra rare + high hype = Blue Chip
    if (rarityScore.score >= 8 && hypeLevel.score >= 8) {
      return ULTIMATE_SCHEMA.investment.grades.BLUE_CHIP;
    }

    // High rarity or hype = Growth
    if (rarityScore.score >= 6 || hypeLevel.score >= 6) {
      return ULTIMATE_SCHEMA.investment.grades.GROWTH;
    }

    // Artist collaborations = Speculative
    if (this.isArtistEditionProduct(productInfo)) {
      return ULTIMATE_SCHEMA.investment.grades.SPECULATIVE;
    }

    return ULTIMATE_SCHEMA.investment.grades.DEFENSIVE;
  }

  calculateCollectibilityScore(productInfo) {
    let score = 4; // Default: Standard

    // Limited edition bonus
    if (this.isLimitedEditionProduct(productInfo)) score += 2;

    // Artist collaboration bonus
    if (this.isArtistEditionProduct(productInfo)) score += 2;

    // Historical importance bonus
    const historical = this.extractHistoricalImportance(productInfo);
    if (historical >= 8) score += 2;

    score = Math.min(score, 10);

    if (score >= 9) return ULTIMATE_SCHEMA.investment.collectibility.MUSEUM_QUALITY;
    if (score >= 7) return ULTIMATE_SCHEMA.investment.collectibility.COLLECTORS_ITEM;
    if (score >= 5) return ULTIMATE_SCHEMA.investment.collectibility.ENTHUSIAST;

    return ULTIMATE_SCHEMA.investment.collectibility.STANDARD;
  }

  calculatePriceAppreciation(productInfo) {
    const investmentGrade = this.calculateInvestmentGrade(productInfo);
    const rarityScore = this.calculateRarityScore(productInfo);

    let appreciation = 1.0; // No appreciation

    if (investmentGrade.name === 'Blue Chip') appreciation = 1.5;
    if (investmentGrade.name === 'Growth') appreciation = 2.0;
    if (investmentGrade.name === 'Speculative') appreciation = 3.0;

    return {
      expectedAnnualAppreciation: `${Math.round((appreciation - 1) * 100)}%`,
      timeHorizon: '3-5 years',
      riskLevel: investmentGrade.risk
    };
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 9: REGIONAL & LOCALIZATION
  calculateMexicanRelevance(productInfo) {
    let relevance = 5; // Default neutral relevance

    // Soccer culture relevance
    if (productInfo.brand.includes('NIKE') || productInfo.brand.includes('ADIDAS')) {
      relevance += 2; // Strong soccer culture connection
    }

    // Reggaeton culture (artist collaborations)
    if (productInfo.collaboration?.includes('BAD BUNNY') || productInfo.collaboration?.includes('J BALVIN')) {
      relevance += 3;
    }

    // Luxury fashion relevance
    if (this.getBrandTier(productInfo) === 'Ultra Luxury') {
      relevance += 2; // Mexicans love luxury
    }

    return Math.min(relevance, 10);
  }

  extractRegionalPreferences(productInfo) {
    return {
      mexicanPopularity: this.calculateMexicanRelevance(productInfo),
      preferredSizes: ULTIMATE_SCHEMA.regional.sizing.MEXICAN_PREFERENCE,
      culturalRelevance: this.calculateMexicanRelevance(productInfo) >= 7
    };
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 10: SOCIAL MEDIA & VIRALITY
  calculateViralityPotential(productInfo) {
    let potential = 1.0; // Base potential

    // Celebrity collaborations = viral
    if (this.isArtistEditionProduct(productInfo)) {
      potential *= ULTIMATE_SCHEMA.socialMedia.viralityFactors.CELEBRITY_ENDORSEMENT.multiplier;
    }

    // Limited editions = shareable
    if (this.isLimitedEditionProduct(productInfo)) {
      potential *= 1.5;
    }

    // High hype = viral potential
    const hypeLevel = this.calculateHypeLevel(productInfo);
    if (hypeLevel.score >= 8) {
      potential *= 2.0;
    }

    return Math.min(potential, 10.0);
  }

  extractSocialMediaMentions(productInfo) {
    const mentions = [];

    // Generate likely hashtags
    const brand = this.schema.brands[productInfo.brand]?.name || productInfo.brand;
    mentions.push(`#${brand.replace(/\s+/g, '')}`);

    if (productInfo.productFamily) {
      mentions.push(`#${productInfo.productFamily.replace(/\d+\.\s*/, '').replace(/\s+/g, '')}`);
    }

    if (productInfo.collaboration) {
      mentions.push(`#${productInfo.collaboration.replace(/\s+/g, '')}`);
    }

    mentions.push('#TWL');
    mentions.push('#LuxurySneakers');
    mentions.push('#StreetStyle');

    return mentions;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 11: ADVANCED SEARCH DIMENSIONS
  extractOccasionSuitability(productInfo) {
    const occasions = [];

    // Sneakers = casual, sport
    if (productInfo.category.includes('SNEAKERS')) {
      occasions.push(ULTIMATE_SCHEMA.occasions.CASUAL);
      occasions.push(ULTIMATE_SCHEMA.occasions.SPORT);

      // Luxury sneakers = smart casual
      if (this.getBrandTier(productInfo) === 'Ultra Luxury') {
        occasions.push(ULTIMATE_SCHEMA.occasions.SMART_CASUAL);
      }
    }

    // Formal shoes = business, formal
    if (productInfo.category.includes('FORMAL')) {
      occasions.push(ULTIMATE_SCHEMA.occasions.BUSINESS);
      occasions.push(ULTIMATE_SCHEMA.occasions.FORMAL);
    }

    // Sandals = casual, party
    if (productInfo.category.includes('SANDALS')) {
      occasions.push(ULTIMATE_SCHEMA.occasions.CASUAL);
      occasions.push(ULTIMATE_SCHEMA.occasions.PARTY);
    }

    return occasions;
  }

  extractLifestyleMatching(productInfo) {
    const lifestyles = [];

    // Athletic brands = athlete lifestyle
    if (productInfo.brand.includes('NIKE') || productInfo.brand.includes('ADIDAS')) {
      lifestyles.push('Athlete');
    }

    // Luxury brands = professional lifestyle
    if (this.getBrandTier(productInfo) === 'Ultra Luxury') {
      lifestyles.push('Professional');
      lifestyles.push('Creative');
    }

    // Collaborations = creative lifestyle
    if (productInfo.collaboration) {
      lifestyles.push('Creative');
    }

    // Default lifestyle
    lifestyles.push('Student');

    return [...new Set(lifestyles)];
  }

  extractWeatherAppropriateness(productInfo) {
    const weather = [];

    // Most sneakers = all weather
    if (productInfo.category.includes('SNEAKERS')) {
      weather.push('Dry Weather');
      weather.push('Mild Weather');
    }

    // Sandals = warm weather
    if (productInfo.category.includes('SANDALS')) {
      weather.push('Hot Weather');
      weather.push('Beach Weather');
    }

    return weather;
  }

  // ULTIMATE SCHEMA EXTRACTION METHODS - PART 12: COMPETITIVE INTELLIGENCE
  analyzeCompetitivePosition(productInfo) {
    const priceRange = this.estimatePriceRange(productInfo);
    const rarityScore = this.calculateRarityScore(productInfo);

    let position = 'Standard';

    if (rarityScore.score >= 8 && priceRange.max >= 1000) {
      position = 'Ultra Premium';
    } else if (rarityScore.score >= 6 && priceRange.max >= 500) {
      position = 'Premium';
    } else if (priceRange.max <= 200) {
      position = 'Value';
    }

    return {
      position,
      competitiveAdvantage: this.getCompetitiveAdvantage(productInfo),
      marketDifferentiator: this.getMarketDifferentiator(productInfo)
    };
  }

  generateMarketComparison(productInfo) {
    return {
      platforms: Object.keys(ULTIMATE_SCHEMA.competitive.platforms),
      estimatedMarketPrice: this.estimatePriceRange(productInfo),
      pricePosition: ULTIMATE_SCHEMA.competitive.priceComparison.AT_MARKET,
      availability: 'Limited to TWL'
    };
  }

  getCompetitiveAdvantage(productInfo) {
    const advantages = [];

    if (this.isLimitedEditionProduct(productInfo)) {
      advantages.push('Exclusive Limited Edition');
    }

    if (this.extractAuthenticity(productInfo).trust_score >= 9) {
      advantages.push('Guaranteed Authenticity');
    }

    if (this.calculateMexicanRelevance(productInfo) >= 7) {
      advantages.push('Cultural Relevance');
    }

    return advantages;
  }

  getMarketDifferentiator(productInfo) {
    if (this.isArtistEditionProduct(productInfo)) {
      return 'Artist Collaboration Exclusive';
    }

    if (this.getBrandTier(productInfo) === 'Ultra Luxury') {
      return 'Ultra Luxury Positioning';
    }

    if (this.calculateMexicanRelevance(productInfo) >= 8) {
      return 'Mexican Market Specialist';
    }

    return 'Premium Quality Assurance';
  }

  generateDisplayName(productInfo) {
    let name = '';
    
    // Brand
    const brandName = this.schema.brands[productInfo.brand]?.name || productInfo.brand;
    name += brandName;
    
    // Product Family
    if (productInfo.productFamily) {
      name += ` ${productInfo.productFamily.replace(/^\d+\.\s*/, '')}`;
    }
    
    // Collaboration
    if (productInfo.collaboration) {
      const collabName = this.schema.collaborations[productInfo.collaboration.toUpperCase()]?.name || productInfo.collaboration;
      name += ` x ${collabName}`;
    }
    
    // SKU
    name += ` (${productInfo.sku})`;
    
    return name;
  }

  generateSpanishName(productInfo) {
    // Generate Spanish product name
    const brandName = this.schema.brands[productInfo.brand]?.name || productInfo.brand;
    let name = brandName;
    
    if (productInfo.productFamily) {
      name += ` ${productInfo.productFamily.replace(/^\d+\.\s*/, '')}`;
    }
    
    if (productInfo.collaboration) {
      const collabName = this.schema.collaborations[productInfo.collaboration.toUpperCase()]?.name || productInfo.collaboration;
      name += ` x ${collabName}`;
    }
    
    return name;
  }

  generateEnglishName(productInfo) {
    // Same as display name for now
    return this.generateDisplayName(productInfo);
  }

  isFeaturedProduct(productInfo) {
    // Mark collaborations and limited editions as featured
    return !!(productInfo.collaboration || 
             productInfo.brand.includes('Limited') ||
             productInfo.productFamily?.includes('JORDAN'));
  }

  updateCategoryIndexes(productRecord) {
    // Update category index
    if (!this.categoryIndex.has(productRecord.category)) {
      this.categoryIndex.set(productRecord.category, []);
    }
    this.categoryIndex.get(productRecord.category).push(productRecord.id);
    
    // Update brand index
    if (!this.brandIndex.has(productRecord.brand)) {
      this.brandIndex.set(productRecord.brand, []);
    }
    this.brandIndex.get(productRecord.brand).push(productRecord.id);
    
    // Update family index
    if (productRecord.productFamily) {
      if (!this.familyIndex.has(productRecord.productFamily)) {
        this.familyIndex.set(productRecord.productFamily, []);
      }
      this.familyIndex.get(productRecord.productFamily).push(productRecord.id);
    }
    
    // Update collaboration index
    if (productRecord.collaboration) {
      if (!this.collaborationIndex.has(productRecord.collaboration)) {
        this.collaborationIndex.set(productRecord.collaboration, []);
      }
      this.collaborationIndex.get(productRecord.collaboration).push(productRecord.id);
    }
    
    // Update gender index
    if (!this.genderIndex.has(productRecord.gender)) {
      this.genderIndex.set(productRecord.gender, []);
    }
    this.genderIndex.get(productRecord.gender).push(productRecord.id);
  }

  updateSearchIndex(productRecord) {
    // Add to search index for each keyword
    for (const keyword of productRecord.searchKeywords) {
      if (!this.searchIndex.has(keyword)) {
        this.searchIndex.set(keyword, []);
      }
      this.searchIndex.get(keyword).push(productRecord.id);
    }
  }

  async generateDatabaseExports() {
    await this.log('📊 Generating database exports...');

    // Export main product catalog
    await this.exportProductCatalog();

    // Export category indexes
    await this.exportCategoryIndexes();

    // Export search indexes
    await this.exportSearchIndexes();

    // Export analytics data
    await this.exportAnalytics();

    // Export database schema
    await this.exportSchema();

    await this.log('✅ Database exports complete');
  }

  async exportProductCatalog() {
    const catalog = {
      metadata: {
        totalProducts: this.stats.totalProducts,
        totalImages: this.stats.totalImages,
        totalVideos: this.stats.totalVideos,
        generatedAt: new Date().toISOString(),
        version: '1.0.0'
      },
      products: Object.fromEntries(this.productIndex)
    };

    const catalogPath = path.join(this.outputPath, 'product-catalog.json');
    await fs.writeFile(catalogPath, JSON.stringify(catalog, null, 2));

    await this.log(`📄 Product catalog exported: ${catalogPath}`);
  }

  async exportCategoryIndexes() {
    const indexes = {
      categories: Object.fromEntries(this.categoryIndex),
      brands: Object.fromEntries(this.brandIndex),
      families: Object.fromEntries(this.familyIndex),
      collaborations: Object.fromEntries(this.collaborationIndex),
      genders: Object.fromEntries(this.genderIndex)
    };

    const indexPath = path.join(this.outputPath, 'indexes', 'category-indexes.json');
    await fs.writeFile(indexPath, JSON.stringify(indexes, null, 2));

    await this.log(`📄 Category indexes exported: ${indexPath}`);
  }

  async exportSearchIndexes() {
    const searchData = {
      keywords: Object.fromEntries(this.searchIndex),
      totalKeywords: this.searchIndex.size,
      generatedAt: new Date().toISOString()
    };

    const searchPath = path.join(this.outputPath, 'indexes', 'search-index.json');
    await fs.writeFile(searchPath, JSON.stringify(searchData, null, 2));

    await this.log(`📄 Search index exported: ${searchPath}`);
  }

  async exportAnalytics() {
    const analytics = {
      summary: {
        totalProducts: this.stats.totalProducts,
        totalImages: this.stats.totalImages,
        totalVideos: this.stats.totalVideos,
        categoriesIndexed: this.categoryIndex.size,
        brandsIndexed: this.brandIndex.size,
        collaborationsIndexed: this.collaborationIndex.size
      },

      categoryBreakdown: {},
      brandBreakdown: {},
      collaborationBreakdown: {},

      topKeywords: this.getTopKeywords(20),
      featuredProducts: this.getFeaturedProducts(),

      generatedAt: new Date().toISOString()
    };

    // Generate category breakdown
    for (const [category, productIds] of this.categoryIndex) {
      analytics.categoryBreakdown[category] = {
        productCount: productIds.length,
        percentage: Math.round((productIds.length / this.stats.totalProducts) * 100)
      };
    }

    // Generate brand breakdown
    for (const [brand, productIds] of this.brandIndex) {
      analytics.brandBreakdown[brand] = {
        productCount: productIds.length,
        percentage: Math.round((productIds.length / this.stats.totalProducts) * 100)
      };
    }

    // Generate collaboration breakdown
    for (const [collab, productIds] of this.collaborationIndex) {
      analytics.collaborationBreakdown[collab] = {
        productCount: productIds.length,
        percentage: Math.round((productIds.length / this.stats.totalProducts) * 100)
      };
    }

    const analyticsPath = path.join(this.outputPath, 'analytics', 'product-analytics.json');
    await fs.writeFile(analyticsPath, JSON.stringify(analytics, null, 2));

    await this.log(`📄 Analytics exported: ${analyticsPath}`);
  }

  async exportSchema() {
    const schemaExport = {
      ...this.schema,
      metadata: {
        version: '1.0.0',
        generatedAt: new Date().toISOString(),
        description: 'TWL Enterprise Product Database Schema'
      }
    };

    const schemaPath = path.join(this.outputPath, 'schemas', 'product-schema.json');
    await fs.writeFile(schemaPath, JSON.stringify(schemaExport, null, 2));

    await this.log(`📄 Schema exported: ${schemaPath}`);
  }

  getTopKeywords(limit = 20) {
    const keywordCounts = [];

    for (const [keyword, productIds] of this.searchIndex) {
      keywordCounts.push({
        keyword,
        productCount: productIds.length,
        products: productIds
      });
    }

    return keywordCounts
      .sort((a, b) => b.productCount - a.productCount)
      .slice(0, limit);
  }

  getFeaturedProducts() {
    const featured = [];

    for (const [id, product] of this.productIndex) {
      if (product.featured) {
        featured.push({
          id,
          displayName: product.displayName,
          brand: product.brand,
          collaboration: product.collaboration,
          category: product.category
        });
      }
    }

    return featured;
  }

  async generateFinalReport() {
    const duration = Math.round((Date.now() - this.stats.startTime) / 1000);

    await this.log('\n🎉 ENTERPRISE DATABASE INDEXING COMPLETE!');
    await this.log('===========================================');
    await this.log(`⏱️  Duration: ${Math.floor(duration / 60)}m ${duration % 60}s`);
    await this.log(`📦 Total products indexed: ${this.stats.totalProducts}`);
    await this.log(`🖼️  Total images: ${this.stats.totalImages}`);
    await this.log(`🎥 Total videos: ${this.stats.totalVideos}`);
    await this.log(`📂 Categories: ${this.categoryIndex.size}`);
    await this.log(`🏢 Brands: ${this.brandIndex.size}`);
    await this.log(`🤝 Collaborations: ${this.collaborationIndex.size}`);
    await this.log(`🔍 Search keywords: ${this.searchIndex.size}`);

    await this.log(`\n📄 Database files generated:`);
    await this.log(`   📊 Product catalog: database/product-catalog.json`);
    await this.log(`   📋 Category indexes: database/indexes/category-indexes.json`);
    await this.log(`   🔍 Search index: database/indexes/search-index.json`);
    await this.log(`   📈 Analytics: database/analytics/product-analytics.json`);
    await this.log(`   🏗️  Schema: database/schemas/product-schema.json`);

    await this.log(`\n📄 Full log: ${this.logFile}`);
    await this.log('\n🚀 Enterprise database ready for TWL e-commerce!');

    return {
      duration,
      stats: this.stats,
      indexes: {
        products: this.productIndex.size,
        categories: this.categoryIndex.size,
        brands: this.brandIndex.size,
        collaborations: this.collaborationIndex.size,
        searchKeywords: this.searchIndex.size
      }
    };
  }
}

// Main execution
async function main() {
  const indexer = new EnterpriseDatabaseIndexer();

  try {
    await indexer.initialize();

    // Scan and index all products
    await indexer.scanAndIndexProducts();

    // Generate database exports
    await indexer.generateDatabaseExports();

    // Generate final report
    const report = await indexer.generateFinalReport();

    console.log('\n🎉 ENTERPRISE DATABASE INDEXING SUCCESS!');
    console.log('Multi-dimensional product catalog ready for TWL!');

    return report;

  } catch (error) {
    console.error('❌ Enterprise database indexing failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = EnterpriseDatabaseIndexer;
