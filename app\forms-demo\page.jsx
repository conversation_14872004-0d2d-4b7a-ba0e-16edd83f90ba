'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import AnimatedInput from '@/components/ui/AnimatedInput'
import AnimatedCheckbox from '@/components/ui/AnimatedCheckbox'
import AnimatedSelect from '@/components/ui/AnimatedSelect'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'

export default function FormsDemo() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    country: '',
    newsletter: false,
    terms: false,
    notifications: false
  })
  
  const [errors, setErrors] = useState({})
  const [success, setSuccess] = useState({})

  const countryOptions = [
    { value: 'mx', label: 'México', description: 'Estados Unidos Mexicanos' },
    { value: 'us', label: 'Estados Unidos', description: 'United States of America' },
    { value: 'ca', label: 'Canadá', description: 'Canada' },
    { value: 'br', label: 'Brasil', description: 'República Federativa do Brasil' },
    { value: 'ar', label: 'Argentina', description: 'República Argentina' },
    { value: 'co', label: 'Colombia', description: 'República de Colombia' }
  ]

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    
    // Clear errors when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
    
    // Real-time validation
    validateField(field, value)
  }

  const validateField = (field, value) => {
    let error = ''
    let isSuccess = false

    switch (field) {
      case 'name':
        if (!value) {
          error = 'El nombre es requerido'
        } else if (value.length < 2) {
          error = 'El nombre debe tener al menos 2 caracteres'
        } else {
          isSuccess = true
        }
        break
      
      case 'email':
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
        if (!value) {
          error = 'El email es requerido'
        } else if (!emailRegex.test(value)) {
          error = 'Ingresa un email válido'
        } else {
          isSuccess = true
        }
        break
      
      case 'phone':
        const phoneRegex = /^[\+]?[1-9][\d]{0,15}$/
        if (value && !phoneRegex.test(value.replace(/\s/g, ''))) {
          error = 'Ingresa un teléfono válido'
        } else if (value) {
          isSuccess = true
        }
        break
      
      case 'country':
        if (!value) {
          error = 'Selecciona un país'
        } else {
          isSuccess = true
        }
        break
    }

    setErrors(prev => ({ ...prev, [field]: error }))
    setSuccess(prev => ({ ...prev, [field]: isSuccess }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    // Validate all fields
    Object.keys(formData).forEach(field => {
      if (typeof formData[field] === 'string') {
        validateField(field, formData[field])
      }
    })
    
    // Check if form is valid
    const hasErrors = Object.values(errors).some(error => error !== '')
    const requiredFields = ['name', 'email', 'country']
    const missingRequired = requiredFields.some(field => !formData[field])
    
    if (!hasErrors && !missingRequired && formData.terms) {
      alert('¡Formulario enviado exitosamente! 🎉')
    }
  }

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Formularios Animados
          </h1>
          <p className="text-warm-camel text-lg max-w-2xl mx-auto">
            Experimenta nuestros componentes de formulario con micro-interacciones sofisticadas 
            y animaciones naturales que mejoran la experiencia del usuario.
          </p>
        </motion.div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          
          {/* Form Demo */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.2 }}
          >
            <Card variant="glass">
              <CardContent className="p-8">
                <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                  Formulario de Registro
                </h2>
                
                <form onSubmit={handleSubmit} className="space-y-6">
                  
                  {/* Name Input */}
                  <AnimatedInput
                    label="Nombre Completo"
                    placeholder="Ingresa tu nombre"
                    value={formData.name}
                    onChange={(e) => handleInputChange('name', e.target.value)}
                    error={errors.name}
                    success={success.name}
                    required
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                      </svg>
                    }
                  />
                  
                  {/* Email Input */}
                  <AnimatedInput
                    type="email"
                    label="Correo Electrónico"
                    placeholder="<EMAIL>"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    error={errors.email}
                    success={success.email}
                    required
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                      </svg>
                    }
                  />
                  
                  {/* Phone Input */}
                  <AnimatedInput
                    type="tel"
                    label="Teléfono (Opcional)"
                    placeholder="+52 55 1234 5678"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    error={errors.phone}
                    success={success.phone}
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                      </svg>
                    }
                  />
                  
                  {/* Country Select */}
                  <AnimatedSelect
                    label="País"
                    placeholder="Selecciona tu país"
                    options={countryOptions}
                    value={formData.country}
                    onChange={(value) => handleInputChange('country', value)}
                    error={errors.country}
                    success={success.country}
                    required
                  />
                  
                  {/* Checkboxes */}
                  <div className="space-y-4">
                    <AnimatedCheckbox
                      checked={formData.newsletter}
                      onChange={(e) => handleInputChange('newsletter', e.target.checked)}
                      label="Suscribirse al newsletter"
                      description="Recibe ofertas exclusivas y novedades de The White Laces"
                      variant="default"
                    />
                    
                    <AnimatedCheckbox
                      checked={formData.notifications}
                      onChange={(e) => handleInputChange('notifications', e.target.checked)}
                      label="Notificaciones push"
                      description="Recibe notificaciones sobre nuevos lanzamientos y descuentos"
                      variant="success"
                    />
                    
                    <AnimatedCheckbox
                      checked={formData.terms}
                      onChange={(e) => handleInputChange('terms', e.target.checked)}
                      label="Acepto los términos y condiciones"
                      description="Es necesario aceptar los términos para continuar"
                      variant="warning"
                      required
                    />
                  </div>
                  
                  {/* Submit Button */}
                  <div className="pt-4">
                    <AnimatedButton
                      type="submit"
                      variant="primary"
                      size="lg"
                      className="w-full"
                      disabled={!formData.terms}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 19l9 2-9-18-9 18 9-2zm0 0v-8" />
                        </svg>
                      }
                      iconPosition="right"
                    >
                      Enviar Formulario
                    </AnimatedButton>
                  </div>
                  
                </form>
              </CardContent>
            </Card>
          </motion.div>
          
          {/* Features Demo */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: 0.4 }}
            className="space-y-6"
          >
            
            {/* Button Variants */}
            <Card variant="glass">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  Variantes de Botones
                </h3>
                <div className="space-y-3">
                  <AnimatedButton variant="primary" className="w-full">
                    Botón Primario
                  </AnimatedButton>
                  <AnimatedButton variant="secondary" className="w-full">
                    Botón Secundario
                  </AnimatedButton>
                  <AnimatedButton variant="outline" className="w-full">
                    Botón Outline
                  </AnimatedButton>
                  <AnimatedButton variant="ghost" className="w-full">
                    Botón Ghost
                  </AnimatedButton>
                  <AnimatedButton variant="danger" className="w-full">
                    Botón Peligro
                  </AnimatedButton>
                </div>
              </CardContent>
            </Card>
            
            {/* Button Sizes */}
            <Card variant="glass">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  Tamaños de Botones
                </h3>
                <div className="space-y-3">
                  <AnimatedButton size="sm" className="w-full">
                    Pequeño
                  </AnimatedButton>
                  <AnimatedButton size="default" className="w-full">
                    Por Defecto
                  </AnimatedButton>
                  <AnimatedButton size="lg" className="w-full">
                    Grande
                  </AnimatedButton>
                  <AnimatedButton size="xl" className="w-full">
                    Extra Grande
                  </AnimatedButton>
                </div>
              </CardContent>
            </Card>
            
            {/* Special States */}
            <Card variant="glass">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  Estados Especiales
                </h3>
                <div className="space-y-3">
                  <AnimatedButton 
                    loading 
                    className="w-full"
                    disabled
                  >
                    Cargando...
                  </AnimatedButton>
                  <AnimatedButton 
                    disabled 
                    className="w-full"
                  >
                    Deshabilitado
                  </AnimatedButton>
                  <AnimatedButton 
                    variant="primary"
                    className="w-full"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                    }
                  >
                    Con Icono
                  </AnimatedButton>
                </div>
              </CardContent>
            </Card>
            
          </motion.div>
        </div>
        
        {/* Features List */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mt-12"
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6 text-center">
                Características de los Micro-Interacciones
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {[
                  {
                    icon: "✨",
                    title: "Animaciones Fluidas",
                    description: "Transiciones suaves con spring physics para una sensación natural"
                  },
                  {
                    icon: "🎯",
                    title: "Feedback Visual",
                    description: "Estados claros de éxito, error y validación en tiempo real"
                  },
                  {
                    icon: "💫",
                    title: "Efectos Ripple",
                    description: "Ondas expansivas que siguen el cursor para mejor feedback táctil"
                  },
                  {
                    icon: "🌊",
                    title: "Breathing Effects",
                    description: "Animaciones de respiración que dan vida a los elementos"
                  },
                  {
                    icon: "⚡",
                    title: "Micro-Interacciones",
                    description: "Pequeños detalles que mejoran significativamente la UX"
                  },
                  {
                    icon: "🎨",
                    title: "Paleta Natural",
                    description: "Colores orgánicos que reflejan la identidad de marca"
                  }
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.8 + index * 0.1 }}
                    className="text-center"
                  >
                    <div className="text-3xl mb-3">{feature.icon}</div>
                    <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                      {feature.title}
                    </h3>
                    <p className="text-warm-camel text-sm">
                      {feature.description}
                    </p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
        
      </div>
    </div>
  )
}
