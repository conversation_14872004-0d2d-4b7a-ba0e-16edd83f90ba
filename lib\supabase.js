// Supabase client configuration for The White Laces
import { createClient } from '@supabase/supabase-js'

// Supabase configuration
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables')
}

// Client-side Supabase client (with RLS)
export const supabase = createClient(supabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true
  },
  realtime: {
    params: {
      eventsPerSecond: 10
    }
  }
})

// Server-side Supabase client (bypasses RLS)
export const supabaseAdmin = supabaseServiceKey 
  ? createClient(supabaseUrl, supabaseServiceKey, {
      auth: {
        autoRefreshToken: false,
        persistSession: false
      }
    })
  : null

// Database table names
export const TABLES = {
  USERS: 'users',
  BRANDS: 'brands',
  CATEGORIES: 'categories',
  PRODUCTS: 'products',
  PRODUCT_VARIANTS: 'product_variants',
  SUPPLIERS: 'suppliers',
  SUPPLIER_PRODUCTS: 'supplier_products',
  CARTS: 'carts',
  WISHLISTS: 'wishlists',
  WISHLIST_ITEMS: 'wishlist_items',
  ORDERS: 'orders',
  ORDER_ITEMS: 'order_items',
  REVIEWS: 'reviews',
  UGC_POSTS: 'ugc_posts',
  SEARCH_LOGS: 'search_logs',
  PRODUCT_VIEWS: 'product_views',
  COLLECTIONS: 'collections',
  COLLECTION_PRODUCTS: 'collection_products'
}

// Helper functions for common database operations
export const dbHelpers = {
  // Get user profile
  async getUserProfile(userId) {
    const { data, error } = await supabase
      .from(TABLES.USERS)
      .select('*')
      .eq('id', userId)
      .single()
    
    if (error) throw error
    return data
  },

  // Get products with filters
  async getProducts(filters = {}) {
    let query = supabase
      .from(TABLES.PRODUCTS)
      .select(`
        *,
        brands:brand_id(name, slug, logo_url),
        categories:category_id(name, slug)
      `)
      .eq('status', 'active')
      .eq('in_stock', true)

    // Apply filters
    if (filters.brand) {
      query = query.eq('brands.slug', filters.brand)
    }
    if (filters.category) {
      query = query.eq('categories.slug', filters.category)
    }
    if (filters.gender) {
      query = query.eq('gender', filters.gender)
    }
    if (filters.featured) {
      query = query.eq('featured', true)
    }
    if (filters.priceMin) {
      query = query.gte('price', filters.priceMin)
    }
    if (filters.priceMax) {
      query = query.lte('price', filters.priceMax)
    }

    // Sorting
    if (filters.sortBy) {
      switch (filters.sortBy) {
        case 'price_asc':
          query = query.order('price', { ascending: true })
          break
        case 'price_desc':
          query = query.order('price', { ascending: false })
          break
        case 'newest':
          query = query.order('created_at', { ascending: false })
          break
        case 'rating':
          query = query.order('rating', { ascending: false })
          break
        default:
          query = query.order('created_at', { ascending: false })
      }
    }

    // Pagination
    if (filters.limit) {
      query = query.limit(filters.limit)
    }
    if (filters.offset) {
      query = query.range(filters.offset, filters.offset + (filters.limit || 20) - 1)
    }

    const { data, error } = await query
    if (error) throw error
    return data
  },

  // Get single product by slug
  async getProductBySlug(slug) {
    const { data, error } = await supabase
      .from(TABLES.PRODUCTS)
      .select(`
        *,
        brands:brand_id(name, slug, logo_url, description),
        categories:category_id(name, slug),
        product_variants(*)
      `)
      .eq('slug', slug)
      .eq('status', 'active')
      .single()
    
    if (error) throw error
    return data
  },

  // Get user's cart
  async getUserCart(userId) {
    const { data, error } = await supabase
      .from(TABLES.CARTS)
      .select('*')
      .eq('user_id', userId)
      .single()
    
    if (error && error.code !== 'PGRST116') throw error
    return data
  },

  // Get user's wishlists
  async getUserWishlists(userId) {
    const { data, error } = await supabase
      .from(TABLES.WISHLISTS)
      .select(`
        *,
        wishlist_items(
          *,
          products(*)
        )
      `)
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
    
    if (error) throw error
    return data
  },

  // Search products
  async searchProducts(query, filters = {}) {
    let searchQuery = supabase
      .from(TABLES.PRODUCTS)
      .select(`
        *,
        brands:brand_id(name, slug, logo_url),
        categories:category_id(name, slug)
      `)
      .eq('status', 'active')
      .textSearch('search_vector', query)

    // Apply additional filters
    Object.entries(filters).forEach(([key, value]) => {
      if (value) {
        searchQuery = searchQuery.eq(key, value)
      }
    })

    const { data, error } = await searchQuery
      .order('rating', { ascending: false })
      .limit(50)
    
    if (error) throw error
    return data
  },

  // Log search query
  async logSearch(userId, query, searchType = 'text', filters = {}, resultsCount = 0) {
    const { error } = await supabase
      .from(TABLES.SEARCH_LOGS)
      .insert({
        user_id: userId,
        query,
        search_type: searchType,
        filters,
        results_count: resultsCount
      })
    
    if (error) console.error('Error logging search:', error)
  },

  // Track product view
  async trackProductView(productId, userId = null, sessionId = null) {
    const { error } = await supabase
      .from(TABLES.PRODUCT_VIEWS)
      .insert({
        product_id: productId,
        user_id: userId,
        session_id: sessionId
      })
    
    if (error) console.error('Error tracking product view:', error)
  }
}

// Real-time subscriptions
export const subscriptions = {
  // Subscribe to cart changes
  subscribeToCart(userId, callback) {
    return supabase
      .channel(`cart:${userId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: TABLES.CARTS,
        filter: `user_id=eq.${userId}`
      }, callback)
      .subscribe()
  },

  // Subscribe to order updates
  subscribeToOrders(userId, callback) {
    return supabase
      .channel(`orders:${userId}`)
      .on('postgres_changes', {
        event: '*',
        schema: 'public',
        table: TABLES.ORDERS,
        filter: `user_id=eq.${userId}`
      }, callback)
      .subscribe()
  }
}

export default supabase
