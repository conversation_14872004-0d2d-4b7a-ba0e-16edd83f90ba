'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'

export default function LuxuryMobileCategoryNav({ 
  categories = [], 
  activeCategory = 'all', 
  onCategoryChange,
  className = '' 
}) {
  const [isDragging, setIsDragging] = useState(false)
  const scrollRef = useRef(null)
  const [canScrollLeft, setCanScrollLeft] = useState(false)
  const [canScrollRight, setCanScrollRight] = useState(true)

  // Check scroll position to show/hide scroll indicators
  const checkScrollPosition = () => {
    if (scrollRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } = scrollRef.current
      setCanScrollLeft(scrollLeft > 0)
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 10)
    }
  }

  useEffect(() => {
    checkScrollPosition()
    const scrollElement = scrollRef.current
    if (scrollElement) {
      scrollElement.addEventListener('scroll', checkScrollPosition)
      return () => scrollElement.removeEventListener('scroll', checkScrollPosition)
    }
  }, [categories])

  // Haptic feedback for luxury interactions
  const hapticFeedback = (type = 'light') => {
    if (navigator.vibrate) {
      const patterns = {
        light: [5],
        medium: [10],
        heavy: [15]
      }
      navigator.vibrate(patterns[type])
    }
  }

  const handleCategorySelect = (categoryId) => {
    hapticFeedback('light')
    onCategoryChange?.(categoryId)
  }

  const scrollToDirection = (direction) => {
    if (scrollRef.current) {
      const scrollAmount = 200
      const newScrollLeft = direction === 'left' 
        ? scrollRef.current.scrollLeft - scrollAmount
        : scrollRef.current.scrollLeft + scrollAmount
      
      scrollRef.current.scrollTo({
        left: newScrollLeft,
        behavior: 'smooth'
      })
    }
  }

  // Default categories if none provided
  const defaultCategories = [
    { id: 'all', name: 'Todos', icon: '👟', count: 493 },
    { id: 'sneakers', name: 'Sneakers', icon: '👟', count: 400 },
    { id: 'sandals', name: 'Sandalias', icon: '🩴', count: 55 },
    { id: 'formal', name: 'Formal', icon: '👞', count: 2 },
    { id: 'casual', name: 'Casual', icon: '👟', count: 34 },
    { id: 'kids', name: 'Niños', icon: '👶', count: 2 }
  ]

  const displayCategories = categories.length > 0 ? categories : defaultCategories

  return (
    <div className={`relative ${className}`}>
      {/* Left scroll indicator */}
      <AnimatePresence>
        {canScrollLeft && (
          <motion.button
            initial={{ opacity: 0, x: -10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -10 }}
            onClick={() => scrollToDirection('left')}
            className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 bg-white/90 dark:bg-black/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center touch-manipulation"
          >
            <svg className="w-4 h-4 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </motion.button>
        )}
      </AnimatePresence>

      {/* Category scroll container */}
      <div
        ref={scrollRef}
        className="flex gap-3 overflow-x-auto scrollbar-hide px-4 py-3"
        style={{
          scrollSnapType: 'x mandatory',
          WebkitOverflowScrolling: 'touch'
        }}
        onTouchStart={() => setIsDragging(true)}
        onTouchEnd={() => setTimeout(() => setIsDragging(false), 100)}
      >
        {displayCategories.map((category, index) => {
          const isActive = activeCategory === category.id
          
          return (
            <motion.button
              key={category.id}
              onClick={() => handleCategorySelect(category.id)}
              className={`flex-shrink-0 flex items-center gap-3 px-5 py-4 rounded-2xl transition-all duration-300 min-w-fit min-h-[48px] touch-manipulation ${
                isActive
                  ? 'bg-lime-green text-black shadow-lg scale-105'
                  : 'bg-white/80 dark:bg-black/80 text-black/70 dark:text-white/70 hover:bg-white dark:hover:bg-black/90 hover:text-black dark:hover:text-white shadow-sm'
              }`}
              style={{ scrollSnapAlign: 'start' }}
              whileTap={{ scale: isDragging ? 1 : 0.95 }}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05, duration: 0.3 }}
            >
              {/* Category icon */}
              <span className="text-xl">{category.icon}</span>

              {/* Category name */}
              <span className={`font-medium text-base whitespace-nowrap ${
                isActive ? 'text-black font-semibold' : ''
              }`}>
                {category.name}
              </span>
              
              {/* Product count */}
              {category.count !== undefined && (
                <span className={`text-sm px-2.5 py-1 rounded-full font-medium ${
                  isActive
                    ? 'bg-black/10 text-black'
                    : 'bg-black/10 dark:bg-white/10 text-black/50 dark:text-white/50'
                }`}>
                  {category.count}
                </span>
              )}
              
              {/* Active indicator */}
              {isActive && (
                <motion.div
                  layoutId="activeCategoryIndicator"
                  className="absolute inset-0 bg-lime-green rounded-2xl -z-10"
                  transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
                />
              )}
            </motion.button>
          )
        })}
      </div>

      {/* Right scroll indicator */}
      <AnimatePresence>
        {canScrollRight && (
          <motion.button
            initial={{ opacity: 0, x: 10 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 10 }}
            onClick={() => scrollToDirection('right')}
            className="absolute right-0 top-1/2 transform -translate-y-1/2 z-10 w-12 h-12 bg-white/90 dark:bg-black/90 backdrop-blur-sm rounded-full shadow-lg flex items-center justify-center touch-manipulation"
          >
            <svg className="w-4 h-4 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </motion.button>
        )}
      </AnimatePresence>
    </div>
  )
}
