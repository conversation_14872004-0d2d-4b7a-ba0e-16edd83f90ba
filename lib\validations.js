// Validation utilities for The White Laces

import { REGEX_PATTERNS } from './constants'

// Email validation
export const validateEmail = (email) => {
  if (!email) {
    return { isValid: false, error: 'El email es requerido' }
  }
  
  if (!REGEX_PATTERNS.EMAIL.test(email)) {
    return { isValid: false, error: 'Formato de email inválido' }
  }
  
  return { isValid: true, error: null }
}

// Password validation
export const validatePassword = (password) => {
  if (!password) {
    return { isValid: false, error: 'La contraseña es requerida' }
  }
  
  if (password.length < 8) {
    return { isValid: false, error: 'La contraseña debe tener al menos 8 caracteres' }
  }
  
  if (!/(?=.*[a-z])/.test(password)) {
    return { isValid: false, error: 'La contraseña debe contener al menos una letra minúscula' }
  }
  
  if (!/(?=.*[A-Z])/.test(password)) {
    return { isValid: false, error: 'La contraseña debe contener al menos una letra mayúscula' }
  }
  
  if (!/(?=.*\d)/.test(password)) {
    return { isValid: false, error: 'La contraseña debe contener al menos un número' }
  }
  
  return { isValid: true, error: null }
}

// Phone validation (Mexican format)
export const validatePhone = (phone) => {
  if (!phone) {
    return { isValid: false, error: 'El teléfono es requerido' }
  }
  
  if (!REGEX_PATTERNS.PHONE.test(phone)) {
    return { isValid: false, error: 'Formato de teléfono inválido (ej: 55 1234 5678)' }
  }
  
  return { isValid: true, error: null }
}

// Name validation
export const validateName = (name, fieldName = 'nombre') => {
  if (!name) {
    return { isValid: false, error: `El ${fieldName} es requerido` }
  }
  
  if (name.length < 2) {
    return { isValid: false, error: `El ${fieldName} debe tener al menos 2 caracteres` }
  }
  
  if (name.length > 50) {
    return { isValid: false, error: `El ${fieldName} no puede tener más de 50 caracteres` }
  }
  
  if (!/^[a-zA-ZáéíóúÁÉÍÓÚñÑ\s]+$/.test(name)) {
    return { isValid: false, error: `El ${fieldName} solo puede contener letras y espacios` }
  }
  
  return { isValid: true, error: null }
}

// Postal code validation (Mexican format)
export const validatePostalCode = (postalCode) => {
  if (!postalCode) {
    return { isValid: false, error: 'El código postal es requerido' }
  }
  
  if (!REGEX_PATTERNS.POSTAL_CODE.test(postalCode)) {
    return { isValid: false, error: 'Formato de código postal inválido (5 dígitos)' }
  }
  
  return { isValid: true, error: null }
}

// Credit card validation
export const validateCreditCard = (cardNumber) => {
  if (!cardNumber) {
    return { isValid: false, error: 'El número de tarjeta es requerido' }
  }
  
  // Remove spaces and dashes
  const cleanCardNumber = cardNumber.replace(/[\s\-]/g, '')
  
  if (!/^\d{16}$/.test(cleanCardNumber)) {
    return { isValid: false, error: 'El número de tarjeta debe tener 16 dígitos' }
  }
  
  // Luhn algorithm validation
  if (!luhnCheck(cleanCardNumber)) {
    return { isValid: false, error: 'Número de tarjeta inválido' }
  }
  
  return { isValid: true, error: null }
}

// CVV validation
export const validateCVV = (cvv) => {
  if (!cvv) {
    return { isValid: false, error: 'El CVV es requerido' }
  }
  
  if (!/^\d{3,4}$/.test(cvv)) {
    return { isValid: false, error: 'El CVV debe tener 3 o 4 dígitos' }
  }
  
  return { isValid: true, error: null }
}

// Expiry date validation
export const validateExpiryDate = (expiryDate) => {
  if (!expiryDate) {
    return { isValid: false, error: 'La fecha de vencimiento es requerida' }
  }
  
  const [month, year] = expiryDate.split('/')
  
  if (!month || !year) {
    return { isValid: false, error: 'Formato de fecha inválido (MM/YY)' }
  }
  
  const monthNum = parseInt(month, 10)
  const yearNum = parseInt(year, 10)
  
  if (monthNum < 1 || monthNum > 12) {
    return { isValid: false, error: 'Mes inválido' }
  }
  
  const currentYear = new Date().getFullYear() % 100
  const currentMonth = new Date().getMonth() + 1
  
  if (yearNum < currentYear || (yearNum === currentYear && monthNum < currentMonth)) {
    return { isValid: false, error: 'La tarjeta ha expirado' }
  }
  
  return { isValid: true, error: null }
}

// Address validation
export const validateAddress = (address) => {
  const errors = {}
  
  // Street address
  if (!address.street) {
    errors.street = 'La dirección es requerida'
  } else if (address.street.length < 5) {
    errors.street = 'La dirección debe tener al menos 5 caracteres'
  }
  
  // City
  if (!address.city) {
    errors.city = 'La ciudad es requerida'
  } else if (address.city.length < 2) {
    errors.city = 'La ciudad debe tener al menos 2 caracteres'
  }
  
  // State
  if (!address.state) {
    errors.state = 'El estado es requerido'
  }
  
  // Postal code
  const postalCodeValidation = validatePostalCode(address.postalCode)
  if (!postalCodeValidation.isValid) {
    errors.postalCode = postalCodeValidation.error
  }
  
  // Country
  if (!address.country) {
    errors.country = 'El país es requerido'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Product review validation
export const validateReview = (review) => {
  const errors = {}
  
  // Rating
  if (!review.rating) {
    errors.rating = 'La calificación es requerida'
  } else if (review.rating < 1 || review.rating > 5) {
    errors.rating = 'La calificación debe estar entre 1 y 5'
  }
  
  // Title
  if (!review.title) {
    errors.title = 'El título es requerido'
  } else if (review.title.length < 5) {
    errors.title = 'El título debe tener al menos 5 caracteres'
  } else if (review.title.length > 100) {
    errors.title = 'El título no puede tener más de 100 caracteres'
  }
  
  // Comment
  if (!review.comment) {
    errors.comment = 'El comentario es requerido'
  } else if (review.comment.length < 10) {
    errors.comment = 'El comentario debe tener al menos 10 caracteres'
  } else if (review.comment.length > 1000) {
    errors.comment = 'El comentario no puede tener más de 1000 caracteres'
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Search query validation
export const validateSearchQuery = (query) => {
  if (!query) {
    return { isValid: false, error: 'La búsqueda no puede estar vacía' }
  }
  
  if (query.length < 2) {
    return { isValid: false, error: 'La búsqueda debe tener al menos 2 caracteres' }
  }
  
  if (query.length > 100) {
    return { isValid: false, error: 'La búsqueda no puede tener más de 100 caracteres' }
  }
  
  return { isValid: true, error: null }
}

// File validation (for image uploads)
export const validateImageFile = (file, maxSizeMB = 10) => {
  if (!file) {
    return { isValid: false, error: 'No se ha seleccionado ningún archivo' }
  }
  
  // Check file type
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    return { isValid: false, error: 'Formato de archivo no válido. Use JPG, PNG o WebP' }
  }
  
  // Check file size
  const maxSizeBytes = maxSizeMB * 1024 * 1024
  if (file.size > maxSizeBytes) {
    return { isValid: false, error: `El archivo es demasiado grande. Máximo ${maxSizeMB}MB` }
  }
  
  return { isValid: true, error: null }
}

// Luhn algorithm for credit card validation
function luhnCheck(cardNumber) {
  let sum = 0
  let isEven = false
  
  for (let i = cardNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cardNumber.charAt(i), 10)
    
    if (isEven) {
      digit *= 2
      if (digit > 9) {
        digit -= 9
      }
    }
    
    sum += digit
    isEven = !isEven
  }
  
  return sum % 10 === 0
}

// Validate form data
export const validateForm = (data, validationRules) => {
  const errors = {}
  
  for (const field in validationRules) {
    const rules = validationRules[field]
    const value = data[field]
    
    for (const rule of rules) {
      const result = rule(value)
      if (!result.isValid) {
        errors[field] = result.error
        break // Stop at first error for this field
      }
    }
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  }
}

// Common validation rule builders
export const required = (fieldName) => (value) => {
  if (!value || (typeof value === 'string' && value.trim() === '')) {
    return { isValid: false, error: `${fieldName} es requerido` }
  }
  return { isValid: true, error: null }
}

export const minLength = (min, fieldName) => (value) => {
  if (value && value.length < min) {
    return { isValid: false, error: `${fieldName} debe tener al menos ${min} caracteres` }
  }
  return { isValid: true, error: null }
}

export const maxLength = (max, fieldName) => (value) => {
  if (value && value.length > max) {
    return { isValid: false, error: `${fieldName} no puede tener más de ${max} caracteres` }
  }
  return { isValid: true, error: null }
}

export const pattern = (regex, errorMessage) => (value) => {
  if (value && !regex.test(value)) {
    return { isValid: false, error: errorMessage }
  }
  return { isValid: true, error: null }
}
