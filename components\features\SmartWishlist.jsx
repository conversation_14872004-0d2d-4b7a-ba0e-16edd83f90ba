'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useWishlist } from '@/contexts/WishlistContext'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'

export default function SmartWishlist({ onClose }) {
  const { wishlistItems, removeFromWishlist, clearWishlist } = useWishlist()
  const { stylePreferences } = useUserPreferences()
  const [smartInsights, setSmartInsights] = useState(null)
  const [priceAlerts, setPriceAlerts] = useState([])
  const [outfitSuggestions, setOutfitSuggestions] = useState([])
  const [isAnalyzing, setIsAnalyzing] = useState(true)

  useEffect(() => {
    if (wishlistItems.length > 0) {
      analyzeWishlist()
    } else {
      setIsAnalyzing(false)
    }
  }, [wishlistItems])

  const analyzeWishlist = async () => {
    setIsAnalyzing(true)
    
    // Simulate AI analysis
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Generate smart insights
    const insights = generateSmartInsights()
    const alerts = generatePriceAlerts()
    const suggestions = generateOutfitSuggestions()
    
    setSmartInsights(insights)
    setPriceAlerts(alerts)
    setOutfitSuggestions(suggestions)
    setIsAnalyzing(false)
  }

  const generateSmartInsights = () => {
    const brands = [...new Set(wishlistItems.map(item => item.brand))]
    const categories = [...new Set(wishlistItems.map(item => item.category || 'sneakers'))]
    const avgPrice = wishlistItems.reduce((sum, item) => sum + item.price, 0) / wishlistItems.length
    const totalValue = wishlistItems.reduce((sum, item) => sum + item.price, 0)
    
    // Style analysis
    const colorAnalysis = analyzeColors()
    const styleAnalysis = analyzeStyles()
    const budgetAnalysis = analyzeBudget()
    
    return {
      summary: {
        totalItems: wishlistItems.length,
        totalValue,
        avgPrice,
        topBrand: brands.reduce((a, b) => 
          wishlistItems.filter(item => item.brand === a).length > 
          wishlistItems.filter(item => item.brand === b).length ? a : b
        ),
        dominantCategory: categories[0]
      },
      colorAnalysis,
      styleAnalysis,
      budgetAnalysis,
      recommendations: generatePersonalizedRecommendations()
    }
  }

  const analyzeColors = () => {
    const colorKeywords = ['negro', 'blanco', 'rojo', 'azul', 'verde', 'amarillo', 'rosa', 'gris']
    const colorCounts = {}
    
    wishlistItems.forEach(item => {
      colorKeywords.forEach(color => {
        if (item.name.toLowerCase().includes(color)) {
          colorCounts[color] = (colorCounts[color] || 0) + 1
        }
      })
    })
    
    const dominantColor = Object.keys(colorCounts).reduce((a, b) => 
      colorCounts[a] > colorCounts[b] ? a : b, 'neutro'
    )
    
    return {
      dominantColor,
      colorDistribution: colorCounts,
      insight: `Prefieres colores ${dominantColor}s - considera agregar más variedad`
    }
  }

  const analyzeStyles = () => {
    const styleKeywords = {
      'casual': ['casual', 'diario', 'cómodo'],
      'formal': ['formal', 'elegante', 'vestir'],
      'deportivo': ['deportivo', 'running', 'gym', 'athletic'],
      'lujo': ['lujo', 'premium', 'designer']
    }
    
    const styleCounts = {}
    
    wishlistItems.forEach(item => {
      Object.keys(styleKeywords).forEach(style => {
        if (styleKeywords[style].some(keyword => 
          item.name.toLowerCase().includes(keyword) || 
          item.category?.toLowerCase().includes(keyword)
        )) {
          styleCounts[style] = (styleCounts[style] || 0) + 1
        }
      })
    })
    
    const dominantStyle = Object.keys(styleCounts).reduce((a, b) => 
      styleCounts[a] > styleCounts[b] ? a : b, 'casual'
    )
    
    return {
      dominantStyle,
      styleDistribution: styleCounts,
      insight: `Tu estilo es principalmente ${dominantStyle} - ${getStyleAdvice(dominantStyle)}`
    }
  }

  const getStyleAdvice = (style) => {
    const advice = {
      'casual': 'perfecto para el día a día, considera agregar opciones más formales',
      'formal': 'excelente para ocasiones especiales, agrega opciones casuales',
      'deportivo': 'ideal para actividades físicas, considera estilos más versátiles',
      'lujo': 'sofisticado y exclusivo, equilibra con opciones más accesibles'
    }
    return advice[style] || 'mantén la variedad en tu colección'
  }

  const analyzeBudget = () => {
    const prices = wishlistItems.map(item => item.price).sort((a, b) => a - b)
    const median = prices[Math.floor(prices.length / 2)]
    const min = Math.min(...prices)
    const max = Math.max(...prices)
    
    let budgetCategory = 'medio'
    if (median < 2000) budgetCategory = 'económico'
    else if (median > 8000) budgetCategory = 'premium'
    
    return {
      budgetCategory,
      priceRange: { min, max, median },
      insight: `Tu rango de precios es ${budgetCategory} (${min.toLocaleString()} - ${max.toLocaleString()} MXN)`
    }
  }

  const generatePersonalizedRecommendations = () => {
    return [
      {
        type: 'diversification',
        title: 'Diversifica tu colección',
        description: 'Considera agregar zapatos de diferentes estilos para más versatilidad',
        action: 'Ver sugerencias'
      },
      {
        type: 'budget',
        title: 'Optimiza tu presupuesto',
        description: 'Hay opciones similares a mejor precio en tu wishlist',
        action: 'Ver alternativas'
      },
      {
        type: 'timing',
        title: 'Momento ideal para comprar',
        description: 'Algunos artículos podrían tener descuentos pronto',
        action: 'Ver predicciones'
      }
    ]
  }

  const generatePriceAlerts = () => {
    return wishlistItems.slice(0, 3).map(item => ({
      ...item,
      alertType: Math.random() > 0.5 ? 'discount' : 'stock',
      message: Math.random() > 0.5 
        ? `Posible descuento del 15% en los próximos 7 días`
        : `Stock limitado - solo quedan 3 unidades`,
      confidence: Math.random() * 0.3 + 0.7
    }))
  }

  const generateOutfitSuggestions = () => {
    return [
      {
        id: 1,
        title: 'Look Casual de Fin de Semana',
        items: wishlistItems.slice(0, 1),
        description: 'Perfecto para salidas informales',
        confidence: 0.92
      },
      {
        id: 2,
        title: 'Outfit Deportivo',
        items: wishlistItems.slice(1, 2),
        description: 'Ideal para actividades físicas',
        confidence: 0.88
      }
    ]
  }

  const shareWishlist = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: 'Mi Wishlist de The White Laces',
          text: `¡Mira mi wishlist con ${wishlistItems.length} productos increíbles! 👟✨`,
          url: window.location.href
        })
      } catch (error) {
        console.error('Error sharing:', error)
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href)
      alert('¡Link copiado al portapapeles!')
    }
  }

  if (wishlistItems.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <Card variant="glass" className="max-w-md w-full" onClick={(e) => e.stopPropagation()}>
          <CardContent className="p-8 text-center">
            <div className="text-6xl mb-4">💝</div>
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Tu Wishlist está Vacía
            </h3>
            <p className="text-warm-camel mb-6">
              Comienza a agregar productos que te gusten para recibir recomendaciones personalizadas.
            </p>
            <AnimatedButton variant="primary" onClick={onClose}>
              Explorar Productos
            </AnimatedButton>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <Card variant="glass" className="max-w-6xl w-full max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <CardContent className="p-8">
          
          {/* Header */}
          <div className="flex items-center justify-between mb-8">
            <div>
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                Mi Wishlist Inteligente
              </h2>
              <p className="text-warm-camel">
                {wishlistItems.length} productos • Análisis con IA
              </p>
            </div>
            <div className="flex gap-2">
              <AnimatedButton
                variant="secondary"
                onClick={shareWishlist}
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                  </svg>
                }
              >
                Compartir
              </AnimatedButton>
              <AnimatedButton
                variant="ghost"
                onClick={onClose}
                icon={
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                }
              />
            </div>
          </div>

          {isAnalyzing ? (
            /* Loading Analysis */
            <div className="text-center py-12">
              <motion.div
                className="w-16 h-16 border-4 border-rich-gold border-t-transparent rounded-full mx-auto mb-4"
                animate={{ rotate: 360 }}
                transition={{
                  duration: 1,
                  repeat: Infinity,
                  ease: "linear"
                }}
              />
              <p className="text-warm-camel">
                Analizando tu wishlist con IA...
              </p>
            </div>
          ) : (
            <div className="space-y-8">
              
              {/* Smart Insights */}
              {smartInsights && (
                <Card variant="default">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4 flex items-center gap-2">
                      <span>🧠</span>
                      Análisis Inteligente
                    </h3>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                      <div className="text-center p-4 bg-warm-camel/10 rounded-lg">
                        <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                          {smartInsights.summary.totalItems}
                        </div>
                        <div className="text-sm text-warm-camel">Productos</div>
                      </div>
                      
                      <div className="text-center p-4 bg-warm-camel/10 rounded-lg">
                        <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                          ${smartInsights.summary.totalValue.toLocaleString()}
                        </div>
                        <div className="text-sm text-warm-camel">Valor Total</div>
                      </div>
                      
                      <div className="text-center p-4 bg-warm-camel/10 rounded-lg">
                        <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                          {smartInsights.summary.topBrand}
                        </div>
                        <div className="text-sm text-warm-camel">Marca Favorita</div>
                      </div>
                      
                      <div className="text-center p-4 bg-warm-camel/10 rounded-lg">
                        <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray capitalize">
                          {smartInsights.styleAnalysis.dominantStyle}
                        </div>
                        <div className="text-sm text-warm-camel">Estilo Principal</div>
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div className="p-4 bg-rich-gold/10 rounded-lg">
                        <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                          💡 Insights Personalizados
                        </h4>
                        <ul className="space-y-2 text-sm text-warm-camel">
                          <li>• {smartInsights.colorAnalysis.insight}</li>
                          <li>• {smartInsights.styleAnalysis.insight}</li>
                          <li>• {smartInsights.budgetAnalysis.insight}</li>
                        </ul>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Price Alerts */}
              {priceAlerts.length > 0 && (
                <Card variant="default">
                  <CardContent className="p-6">
                    <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4 flex items-center gap-2">
                      <span>🚨</span>
                      Alertas de Precio
                    </h3>
                    
                    <div className="space-y-3">
                      {priceAlerts.map((alert, index) => (
                        <motion.div
                          key={alert.id}
                          initial={{ opacity: 0, x: -20 }}
                          animate={{ opacity: 1, x: 0 }}
                          transition={{ delay: index * 0.1 }}
                          className="flex items-center gap-4 p-4 bg-warm-camel/10 rounded-lg"
                        >
                          <div className="w-16 h-16 bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray rounded-lg flex items-center justify-center">
                            <span className="text-xs text-warm-camel font-medium text-center">
                              {alert.name.slice(0, 10)}...
                            </span>
                          </div>
                          
                          <div className="flex-1">
                            <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                              {alert.name}
                            </h4>
                            <p className="text-warm-camel text-sm">{alert.brand}</p>
                            <p className="text-rich-gold text-sm font-medium">{alert.message}</p>
                          </div>
                          
                          <div className="text-right">
                            <Badge variant={alert.alertType === 'discount' ? 'success' : 'warning'} size="sm">
                              {alert.alertType === 'discount' ? 'Descuento' : 'Stock Bajo'}
                            </Badge>
                            <div className="text-xs text-warm-camel mt-1">
                              {Math.round(alert.confidence * 100)}% confianza
                            </div>
                          </div>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Wishlist Items */}
              <Card variant="default">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray">
                      Mis Productos Guardados
                    </h3>
                    <AnimatedButton
                      variant="ghost"
                      size="sm"
                      onClick={clearWishlist}
                      className="text-red-500 hover:text-red-400"
                    >
                      Limpiar Todo
                    </AnimatedButton>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {wishlistItems.map((item, index) => (
                      <motion.div
                        key={item.id}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                        className="group"
                      >
                        <Card variant="glass" className="hover:shadow-lg transition-all duration-300">
                          <CardContent className="p-4">
                            <div className="relative mb-3">
                              <div className="aspect-square bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray rounded-lg flex items-center justify-center">
                                <span className="text-warm-camel font-medium text-center px-2">
                                  {item.name}
                                </span>
                              </div>
                              
                              <motion.button
                                onClick={() => removeFromWishlist(item.id)}
                                className="absolute top-2 right-2 p-1 bg-red-500 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                              >
                                <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                </svg>
                              </motion.button>
                            </div>

                            <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm mb-1 line-clamp-2">
                              {item.name}
                            </h4>
                            <p className="text-warm-camel text-xs mb-2">{item.brand}</p>
                            <p className="text-forest-emerald dark:text-light-cloud-gray font-bold">
                              ${item.price.toLocaleString()} MXN
                            </p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </CardContent>
      </Card>
    </motion.div>
  )
}
