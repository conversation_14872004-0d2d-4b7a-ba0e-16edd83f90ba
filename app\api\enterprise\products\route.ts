/**
 * Next.js API Routes: /api/enterprise/products
 * Enterprise product search and batch operations
 */

import { NextRequest, NextResponse } from 'next/server'
import { getTWLSystem } from '@/lib/enterprise/TWLEnterpriseSystem'

/**
 * GET /api/enterprise/products - Search products with filters
 */
export async function GET(request: NextRequest) {
  try {
    // Get enterprise system instance
    const system = getTWLSystem()
    
    // Ensure system is initialized
    if (!system.isReady()) {
      await system.initialize()
    }
    
    // Use the enterprise API
    const api = system.getAPI()
    return await api.getProducts(request)
    
  } catch (error) {
    console.error('Enterprise API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: { 
          code: 'INTERNAL_ERROR', 
          message: 'Internal server error' 
        } 
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/enterprise/products - Batch product retrieval
 */
export async function POST(request: NextRequest) {
  try {
    // Get enterprise system instance
    const system = getTWLSystem()
    
    // Ensure system is initialized
    if (!system.isReady()) {
      await system.initialize()
    }
    
    // Use the enterprise API
    const api = system.getAPI()
    return await api.getProductsBatch(request)
    
  } catch (error) {
    console.error('Enterprise API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: { 
          code: 'INTERNAL_ERROR', 
          message: 'Internal server error' 
        } 
      },
      { status: 500 }
    )
  }
}
