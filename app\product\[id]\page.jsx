'use client'

import { useState, useEffect } from 'react'
import { useRouter, useParams } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { ArrowLeft, Heart, Share2, ShoppingCart, Star, Truck, Shield, RotateCcw, Info, Ruler, Package, Award } from 'lucide-react'
import Image from 'next/image'
import { useWishlist } from '@/contexts/WishlistContext'
import { useCart } from '@/contexts/CartContext'
import { getProductById } from '@/lib/data/products'
import { loadRealProduct } from '@/lib/real-products-loader'
import SwipeableProductImages from '@/components/ui/SwipeableProductImages'
import ShareModal from '@/components/ui/ShareModal'
import MouseSwipeContainer from '@/components/ui/MouseSwipeContainer'

export default function ProductPage() {
  console.log('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥 PRODUCT PAGE COMPONENT EXECUTING!')
  console.log('🔥🔥🔥 RUNNING ON:', typeof window !== 'undefined' ? 'CLIENT' : 'SERVER')

  const router = useRouter()
  const params = useParams()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()
  const { addItem } = useCart()

  console.log('🔥🔥🔥 PARAMS:', params)
  console.log('🔥🔥🔥 PARAMS.ID:', params?.id)

  // TEST REAL PRODUCT LOADER IMMEDIATELY
  console.log('🔥🔥🔥 TESTING REAL PRODUCT LOADER IMMEDIATELY!')
  if (params?.id) {
    console.log('🔥🔥🔥 CALLING loadRealProduct SYNCHRONOUSLY...')
    try {
      // Call the real product loader immediately to test it
      loadRealProduct(params.id).then(result => {
        console.log('🔥🔥🔥 REAL PRODUCT LOADER RESULT:', result ? 'SUCCESS' : 'NULL')
        if (result) {
          console.log('🔥🔥🔥 PRODUCT NAME:', result.name)
          console.log('🔥🔥🔥 PRODUCT IMAGES COUNT:', result.images?.length)
          console.log('🔥🔥🔥 PRODUCT MODELS COUNT:', result.models?.length)
        }
      }).catch(error => {
        console.error('🔥🔥🔥 REAL PRODUCT LOADER ERROR:', error)
      })
    } catch (error) {
      console.error('🔥🔥🔥 SYNC CALL ERROR:', error)
    }
  }

  // State management - OPTIMIZED FOR TRANSITIONS
  const [product, setProduct] = useState(null)
  const [selectedImage, setSelectedImage] = useState(0)
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedModel, setSelectedModel] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState('description')
  const [showModelSelector, setShowModelSelector] = useState(false)
  const [showShareModal, setShowShareModal] = useState(false)
  const [loadingError, setLoadingError] = useState(null)

  // Enhanced mock data for demo with comprehensive product details and model variants
  const mockProduct = {
    id: params.id,
    name: "Nike Limited Edition Air Force 'Gucci'",
    brand: "Nike x Gucci",
    price: 2850,
    originalPrice: 3200,
    rating: 4.8,
    reviewCount: 127,
    images: [
      'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=800&fit=crop',
      'https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=800&h=800&fit=crop'
    ],
    models: [
      {
        id: 0,
        name: "Clásico Negro/Oro",
        images: [
          'https://images.unsplash.com/photo-1549298916-b41d501d3772?w=800&h=800&fit=crop',
          'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=800&h=800&fit=crop'
        ],
        price: 2850,
        originalPrice: 3200,
        inStock: true
      },
      {
        id: 1,
        name: "Edición Blanco/Verde",
        images: [
          'https://images.unsplash.com/photo-1595950653106-6c9ebd614d3a?w=800&h=800&fit=crop',
          'https://images.unsplash.com/photo-1551107696-a4b0c5a0d9a2?w=800&h=800&fit=crop'
        ],
        price: 2950,
        originalPrice: 3300,
        inStock: true
      },
      {
        id: 2,
        name: "Exclusivo Rojo/Oro",
        images: [
          'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=800&h=800&fit=crop',
          'https://images.unsplash.com/photo-1600185365483-26d7a4cc7519?w=800&h=800&fit=crop'
        ],
        price: 3150,
        originalPrice: 3500,
        inStock: false
      }
    ],
    sizes: ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
    description: "Colaboración exclusiva entre Nike y Gucci. Diseño premium con materiales de lujo.",
    fullDescription: "Esta colaboración única entre Nike y Gucci representa la fusión perfecta entre el streetwear contemporáneo y la elegancia italiana. Cada par está meticulosamente elaborado con materiales de la más alta calidad, incluyendo cuero premium italiano y detalles en oro de 18 quilates. El diseño incorpora elementos icónicos de ambas marcas, creando una pieza verdaderamente exclusiva para coleccionistas y entusiastas de la moda.",
    features: ['Edición Limitada', 'Materiales Premium', 'Diseño Exclusivo'],
    materials: ['Cuero Italiano Premium', 'Detalles en Oro 18k', 'Suela de Goma Especializada'],
    colors: ['Negro/Oro', 'Blanco/Verde', 'Rojo/Oro'],
    type: 'sneaker',
    subType: 'low-top',
    gender: 'MIXTE',
    isLimited: true,
    isCollaboration: true,
    inStock: true,
    stockCount: 12,
    sku: 'NK-GC-AF1-2024',
    releaseDate: '2024-03-15',
    careInstructions: [
      'Limpiar con paño húmedo',
      'No sumergir en agua',
      'Usar protector de cuero',
      'Almacenar en lugar seco'
    ]
  }

  // Clean product name (remove folder references and long descriptions)
  const cleanProductName = (name) => {
    console.log('🔍🔍🔍 CLEAN PRODUCT NAME CALLED WITH:', name, 'TYPE:', typeof name, 'LENGTH:', name?.length)

    if (!name) {
      console.log('🔍🔍🔍 NO NAME PROVIDED, RETURNING DEFAULT')
      return 'Producto'
    }

    console.log('🔍 CLEANING PRODUCT NAME:', name)

    // Remove folder reference numbers (like "1. ")
    const cleanName = name.replace(/^\d+\.\s*/, '')
    console.log('🔍 AFTER REMOVING FOLDER NUMBERS:', cleanName)

    // If the name is very long (likely a full description), extract just the product title
    if (cleanName.length > 10) {
      console.log('🔍 NAME IS LONG, EXTRACTING TITLE...')

      // Try to extract the clean title from the beginning of the description
      // Look for the first sentence or phrase before descriptive text
      let extractedTitle = cleanName

      // Split by common separators and take the first meaningful part
      const separators = [' está decorado', ' con un diseño', ' presenta', ' incluye', ' cuenta con', '. ']
      for (const separator of separators) {
        if (extractedTitle.includes(separator)) {
          extractedTitle = extractedTitle.split(separator)[0].trim()
          console.log('🔍 SPLIT BY "' + separator + '":', extractedTitle)
          break
        }
      }

      // If still too long, try to extract brand + model pattern
      if (extractedTitle.length > 60) {
        console.log('🔍 TITLE TOO LONG, TRYING BRAND MODEL MATCH:', extractedTitle)
        const brandModelMatch = extractedTitle.match(/^(Nike|Adidas|Jordan|Gucci|LV|Dior|Louis Vuitton)\s+([A-Z\-\s]+?(?:Air Force|Jordan|Dunk|Yeezy|Stan Smith|x\s+[A-Z\-\s]+))\s*(?:\d+\s*)?(Low|High|Mid)?/i)
        if (brandModelMatch) {
          const parts = [brandModelMatch[1], brandModelMatch[2], brandModelMatch[3]].filter(Boolean)
          extractedTitle = parts.join(' ').trim()
          console.log('🔍 BRAND MODEL MATCH FOUND:', extractedTitle)
        } else {
          console.log('🔍 NO BRAND MODEL MATCH, KEEPING ORIGINAL')
        }
      }

      // REMOVED PROBLEMATIC CLEANUP - Don't remove "Black" or other color words
      // The previous cleanup was too aggressive and removed important parts of product names
      console.log('🔍 FINAL EXTRACTED TITLE (before cleanup):', extractedTitle)

      // Only remove quotes and very specific unwanted patterns
      extractedTitle = extractedTitle
        .replace(/\s+"[^"]*".*$/i, '') // Remove quoted descriptions at the end
        .replace(/\s+de\s+color.*$/i, '') // Remove Spanish color descriptions
        .trim()

      console.log('🔍 FINAL EXTRACTED TITLE (after cleanup):', extractedTitle)
      return extractedTitle
    }

    console.log('🔍 RETURNING CLEAN NAME:', cleanName)
    return cleanName
  }

  // Debug the product structure when product changes
  useEffect(() => {
    if (product) {
      console.log('🔥🔥🔥 CRITICAL DEBUG - PRODUCT MODELS:')
      console.log('🔥🔥🔥 product.models:', product.models)
      console.log('🔥🔥🔥 models length:', product.models?.length)
      console.log('🔥🔥🔥 Should show second row?', product.models && product.models.length > 1)
      console.log('🔥🔥🔥 FULL PRODUCT OBJECT:', JSON.stringify(product, null, 2))
    } else {
      console.log('🔥🔥🔥 PRODUCT IS NULL/UNDEFINED!')
    }
  }, [product])

  // MINIMAL TEST - JUST USEEFFECT
  console.log('🔥🔥🔥 ABOUT TO DECLARE USEEFFECT!')
  useEffect(() => {
    console.log('🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯🎯 MINIMAL USEEFFECT WORKING!')
    console.log('🎯🎯🎯 USEEFFECT IS RUNNING ON CLIENT!')
  }, [])
  console.log('🔥🔥🔥 USEEFFECT DECLARED!')

  // OPTIMIZED PRODUCT LOADING - ENTERPRISE GRADE
  useEffect(() => {
    console.log('🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥🔥 MAIN USEEFFECT RUNNING!')
    console.log('🔥🔥🔥 PARAMS:', params)
    console.log('🔥🔥🔥 PARAMS.ID:', params.id)

    if (!params.id) {
      console.log('🔥🔥🔥 NO PARAMS.ID - RETURNING EARLY!')
      setLoadingError('No product ID provided')
      setIsLoading(false)
      return
    }

    console.log('🔥🔥🔥 PARAMS.ID EXISTS - CONTINUING!')

    // Start loading immediately, don't wait for isMounted
    const loadProduct = async () => {
      try {
        setIsLoading(true)
        setLoadingError(null)

        // Load real product with corrected image paths
        console.log('🔥🔥🔥 ABOUT TO CALL loadRealProduct WITH:', params.id)
        console.log('🔥🔥🔥 loadRealProduct FUNCTION TYPE:', typeof loadRealProduct)
        console.log('🔥🔥🔥 loadRealProduct FUNCTION EXISTS:', !!loadRealProduct)

        let productData = null

        // Force call the function directly
        console.log('🔥🔥🔥 CALLING loadRealProduct NOW...')
        try {
          console.log('🔥🔥🔥 BEFORE AWAIT CALL')
          productData = await loadRealProduct(params.id)
          console.log('🔥🔥🔥 AFTER AWAIT CALL')
          console.log('🔥🔥🔥 PRODUCT LOADED RESULT:', productData ? 'SUCCESS' : 'NULL')
          if (productData) {
            console.log('🔥🔥🔥 PRODUCT NAME:', productData.name)
            console.log('🔥🔥🔥 PRODUCT IMAGES COUNT:', productData.images?.length)
            console.log('🔥🔥🔥 PRODUCT MODELS COUNT:', productData.models?.length)
            console.log('🔥🔥🔥 SETTING REAL PRODUCT DATA TO STATE!')
          } else {
            console.log('🔥🔥🔥 PRODUCT DATA IS NULL OR UNDEFINED')
          }
        } catch (loadError) {
          console.error('🔥🔥🔥 ERROR IN loadRealProduct:', loadError)
          console.error('🔥🔥🔥 ERROR STACK:', loadError.stack)
          // Don't throw, just continue with fallback
        }

        // If no real product found, use mock product as fallback
        if (!productData) {
          console.log('🔥🔥🔥 USING MOCK PRODUCT AS FALLBACK')
          productData = { ...mockProduct, id: params.id }
        } else {
          console.log('🔥🔥🔥 USING REAL PRODUCT DATA!')
        }

        console.log('🔥🔥🔥 CALLING setProduct WITH:', productData ? 'REAL DATA' : 'NULL')
        setProduct(productData)
        console.log('🔥🔥🔥 setProduct CALLED SUCCESSFULLY!')
      } catch (error) {
        console.error('Error loading product:', error)
        setLoadingError(error.message)
        setProduct({ ...mockProduct, id: params.id })
      } finally {
        setIsLoading(false)
      }
    }

    loadProduct()
  }, [params.id]) // Only depend on params.id, not isMounted



  const handleAddToCart = async () => {
    if (!selectedSize) {
      alert('Por favor selecciona una talla')
      return
    }

    try {
      // Get the selected model information
      const currentModel = product.models ? product.models[selectedModel] : null
      const modelInfo = currentModel ? {
        modelId: currentModel.id,
        modelName: currentModel.name,
        modelImage: currentModel.images?.[0],
        modelColors: currentModel.colors
      } : null

      console.log('🛒 Adding to cart with model info:', {
        productId: product.id,
        selectedSize,
        quantity,
        selectedModel,
        modelInfo
      })

      await addItem(product.id, selectedSize, quantity, modelInfo)

      // Show success toast with model information
      const modelName = currentModel ? ` (${currentModel.colors?.[0] || currentModel.name})` : ''
      showToast({
        type: 'success',
        title: '¡Agregado al carrito!',
        message: `${product.name}${modelName} - Talla ${selectedSize}`,
        duration: 3000
      })
    } catch (error) {
      console.error('Error adding item to cart:', error)
      showToast({
        type: 'error',
        title: 'Error',
        message: 'No se pudo agregar el producto al carrito',
        duration: 3000
      })
    }
  }

  // Toast notification function
  const showToast = ({ type, title, message, duration = 3000 }) => {
    const toast = document.createElement('div')
    toast.className = `fixed top-4 right-4 z-50 max-w-sm w-full bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-xl shadow-lg transform transition-all duration-300 ease-in-out translate-x-full opacity-0`

    const bgColor = type === 'success' ? 'bg-lime-green' : 'bg-red-500'
    const icon = type === 'success' ? '✓' : '✕'

    toast.innerHTML = `
      <div class="p-4">
        <div class="flex items-start">
          <div class="flex-shrink-0">
            <div class="w-8 h-8 ${bgColor} rounded-full flex items-center justify-center text-black font-bold">
              ${icon}
            </div>
          </div>
          <div class="ml-3 flex-1">
            <p class="text-sm font-semibold text-gray-900 dark:text-white">${title}</p>
            <p class="text-sm text-gray-600 dark:text-gray-300 mt-1">${message}</p>
          </div>
          <button class="ml-4 text-gray-400 hover:text-gray-600 dark:hover:text-gray-200" onclick="this.parentElement.parentElement.parentElement.remove()">
            <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
            </svg>
          </button>
        </div>
      </div>
    `

    document.body.appendChild(toast)

    // Animate in
    setTimeout(() => {
      toast.classList.remove('translate-x-full', 'opacity-0')
      toast.classList.add('translate-x-0', 'opacity-100')
    }, 100)

    // Auto remove
    setTimeout(() => {
      toast.classList.add('translate-x-full', 'opacity-0')
      setTimeout(() => {
        if (toast.parentNode) {
          toast.parentNode.removeChild(toast)
        }
      }, 300)
    }, duration)
  }

  const handleWishlistToggle = () => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product)
    }
  }

  // OPTIMIZED LOADING STATE - ENTERPRISE GRADE
  if (isLoading || !product) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 pt-16 flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-lime-green mx-auto"></div>
          <p className="text-gray-600 dark:text-gray-400">Cargando producto...</p>
          {loadingError && (
            <p className="text-red-500 text-sm">Error: {loadingError}</p>
          )}
        </div>
      </div>
    )
  }



  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 pt-16">
      <div className="max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 pb-16">
        
        {/* Back Button - Mobile Optimized */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between py-3 mb-2"
        >
          <button
            onClick={() => router.back()}
            className="flex items-center gap-2 text-gray-700 dark:text-gray-300 hover:text-black dark:hover:text-white transition-all duration-300 group"
          >
            <div className="w-8 h-8 bg-lime-green rounded-full flex items-center justify-center group-hover:bg-lime-green/90 transition-all duration-300 shadow-md group-hover:shadow-lg group-hover:scale-105">
              <ArrowLeft className="w-4 h-4 text-black" />
            </div>
            <span className="font-medium">Volver</span>
          </button>

          <button
            onClick={() => setShowShareModal(true)}
            className="p-2 text-gray-600 dark:text-gray-400 hover:text-lime-green transition-colors rounded-full hover:bg-gray-100 dark:hover:bg-gray-800"
          >
            <Share2 className="w-5 h-5" />
          </button>
        </motion.div>

        {/* Main Content Grid - Desktop Reference Design Layout */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 lg:gap-12 xl:gap-16">
          
          {/* Product Images - Mobile First with Enhanced Gallery */}
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5 }}
            className="order-1"
          >
            {/* Main Product Image */}
            <div className="relative mb-4">
              <SwipeableProductImages
                images={product.models ? product.models[selectedModel]?.images || product.images : product.images}
                videos={product.models ? product.models[selectedModel]?.videos || product.videos || [] : product.videos || []}
                productName={cleanProductName(product.name)}
                selectedImage={selectedImage}
                onImageChange={setSelectedImage}
              />

              {/* Image Counter */}
              <div className="absolute bottom-4 right-4 bg-black/70 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium">
                {selectedImage + 1} / {(product.models ? product.models[selectedModel]?.images?.length : product.images?.length) || 1}
              </div>
            </div>

            {/* DUAL-LAYER THUMBNAIL SYSTEM - Desktop Reference Design */}
            <div className="mb-6 space-y-4">

              {/* FIRST ROW - Current Model Thumbnails with Slider */}
              <div className="relative">
                <div className="flex items-center gap-2">
                  {/* Left Arrow */}
                  <button
                    onClick={() => {
                      const container = document.getElementById('current-model-thumbnails')
                      container.scrollBy({ left: -200, behavior: 'smooth' })
                    }}
                    className="hidden lg:flex flex-shrink-0 w-8 h-8 items-center justify-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full shadow-sm hover:shadow-md transition-all duration-200 z-10"
                  >
                    <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                    </svg>
                  </button>

                  {/* Scrollable Thumbnail Container with Mouse Swipe */}
                  <MouseSwipeContainer
                    id="current-model-thumbnails"
                    className="flex gap-2 overflow-x-auto pb-2 flex-1"
                    enableMouseDrag={true}
                    enableTouchSwipe={true}
                    swipeThreshold={30}
                    dragSensitivity={1.2}
                  >
                    {/* Video Thumbnails FIRST */}
                    {(product.models ? product.models[selectedModel]?.videos || product.videos || [] : product.videos || []).map((video, index) => {
                      // Handle both string URLs and video objects with custom thumbnails
                      const videoSrc = typeof video === 'string' ? video : video.src
                      const videoThumbnail = typeof video === 'object' && video.thumbnail ? video.thumbnail : null

                      return (
                        <button
                          key={`vid-${index}`}
                          onClick={() => setSelectedImage(index)}
                          className={`relative flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 rounded-lg overflow-hidden border-2 transition-all duration-200 touch-manipulation ${
                            selectedImage === index
                              ? 'border-lime-green shadow-lg shadow-lime-green/25 ring-2 ring-lime-green/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          }`}
                        >
                          {videoThumbnail ? (
                            // Use custom thumbnail image
                            <img
                              src={videoThumbnail}
                              alt="Video thumbnail"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            // Fallback to browser video thumbnail
                            <video className="w-full h-full object-cover" muted>
                              <source src={videoSrc} type="video/mp4" />
                            </video>
                          )}
                          <div className="absolute inset-0 flex items-center justify-center bg-black/30">
                            <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 24 24">
                              <path d="M8 5v14l11-7z"/>
                            </svg>
                          </div>
                        </button>
                      )
                    })}

                    {/* Image Thumbnails AFTER videos */}
                    {(product.models ? product.models[selectedModel]?.images || product.images : product.images).map((image, index) => {
                      const imageIndex = (product.models ? product.models[selectedModel]?.videos?.length || product.videos?.length || 0 : product.videos?.length || 0) + index
                      return (
                        <button
                          key={`img-${index}`}
                          onClick={() => setSelectedImage(imageIndex)}
                          className={`flex-shrink-0 w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 rounded-lg overflow-hidden border-2 transition-all duration-200 touch-manipulation ${
                            selectedImage === imageIndex
                              ? 'border-lime-green shadow-lg shadow-lime-green/25 ring-2 ring-lime-green/20'
                              : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                          }`}
                        >
                          <img
                            src={image}
                            alt={`${cleanProductName(product.name)} vista ${imageIndex + 1}`}
                            className="w-full h-full object-cover hover:scale-105 transition-transform duration-200"
                          />
                        </button>
                      )
                    })}
                  </MouseSwipeContainer>

                  {/* Right Arrow */}
                  <button
                    onClick={() => {
                      const container = document.getElementById('current-model-thumbnails')
                      container.scrollBy({ left: 200, behavior: 'smooth' })
                    }}
                    className="hidden lg:flex flex-shrink-0 w-8 h-8 items-center justify-center bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-full shadow-sm hover:shadow-md transition-all duration-200 z-10"
                  >
                    <svg className="w-4 h-4 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </button>
                </div>
              </div>

              {/* SECOND ROW - Model Variants ("Modelos") */}
              {product.models && product.models.length > 1 && (
                <div>
                  <div className="flex items-center justify-between mb-3">
                    <h3 className="text-base lg:text-lg font-semibold text-gray-900 dark:text-white font-poppins">
                      Modelos:
                    </h3>
                  </div>

                  <MouseSwipeContainer className="flex gap-2 overflow-x-auto pb-2">
                    {product.models.map((model, index) => (
                      <button
                        key={model.id}
                        onClick={() => {
                          setSelectedModel(index)
                          setSelectedImage(0)
                        }}
                        className={`relative flex-shrink-0 group transition-all duration-200 touch-manipulation ${
                          selectedModel === index
                            ? 'ring-2 ring-lime-green rounded-lg'
                            : ''
                        }`}
                      >
                        {/* Model Thumbnail */}
                        <div className={`w-16 h-16 sm:w-20 sm:h-20 lg:w-24 lg:h-24 rounded-lg overflow-hidden border-2 transition-all duration-200 ${
                          selectedModel === index
                            ? 'border-lime-green shadow-lg shadow-lime-green/25'
                            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
                        }`}>
                          <img
                            src={model.images[0]}
                            alt={model.name}
                            className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-200"
                          />
                        </div>

                        {/* Selected Indicator */}
                        {selectedModel === index && (
                          <div className="absolute -top-1 -right-1 w-5 h-5 bg-lime-green rounded-full flex items-center justify-center border-2 border-white dark:border-gray-900">
                            <svg className="w-3 h-3 text-black" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                        )}

                        {/* Model Name */}
                        <div className="absolute bottom-0 left-0 right-0 bg-black/70 text-white text-xs px-1 py-0.5 rounded-b-lg">
                          <span className="font-medium truncate block">{model.name}</span>
                        </div>

                        {/* Color Indicator */}
                        <div className="absolute top-1 left-1 text-xs bg-white/90 dark:bg-gray-800/90 px-1 py-0.5 rounded text-gray-700 dark:text-gray-300">
                          {model.colors?.[0] || 'Color'}
                        </div>
                      </button>
                    ))}
                  </MouseSwipeContainer>
                </div>
              )}
            </div>


          </motion.div>

          {/* Product Info - Desktop Reference Design */}
          <motion.div
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="order-2 space-y-4 lg:space-y-6"
          >


            {/* SKU - Matching Screenshot */}
            <div className="text-sm text-text-gray font-poppins">
              SKU: {product.sku || 'BD7700-222'}
            </div>

            {/* Product Name - Matching Screenshot Typography */}
            <div className="space-y-1">
              <h1 className="text-2xl lg:text-3xl xl:text-4xl font-bold text-pure-black dark:text-pure-white leading-tight font-poppins">
                NIKE GUCCI AIR FORCE
              </h1>
              <h2 className="text-lg lg:text-xl text-text-gray font-poppins">
                NIKE Limited Edition
              </h2>
              <p className="text-sm text-text-gray font-poppins">
                NIKE GUCCI AIR FORCE
              </p>
            </div>

            {/* Rating & Reviews - Yellow Stars */}
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                {[...Array(5)].map((_, i) => (
                  <Star
                    key={i}
                    className={`w-4 h-4 ${
                      i < Math.floor(4.8)
                        ? 'text-yellow-400 fill-current'
                        : 'text-gray-300'
                    }`}
                  />
                ))}
              </div>
              <span className="text-sm text-text-gray font-poppins">
                4.8 (127 reseñas)
              </span>
            </div>

            {/* Price - Matching Screenshot Design */}
            <div className="flex items-baseline gap-3">
              <span className="text-4xl lg:text-5xl font-bold text-lime-green-dark font-poppins">
                $210
              </span>
              <span className="text-xl lg:text-2xl text-text-gray line-through font-poppins">
                $280
              </span>
              <span className="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded font-poppins">
                -25%
              </span>
            </div>

            {/* Orange Badges - Matching Screenshot */}
            <div className="flex flex-wrap gap-2">
              <span className="bg-orange-400 text-pure-black text-xs font-semibold px-3 py-1 rounded-full font-poppins">
                Materiales Premium
              </span>
              <span className="bg-orange-400 text-pure-black text-xs font-semibold px-3 py-1 rounded-full font-poppins">
                Colaboración Exclusiva
              </span>
              <span className="bg-orange-400 text-pure-black text-xs font-semibold px-3 py-1 rounded-full font-poppins">
                Edición Limitada
              </span>
            </div>

            {/* Size Selection - Desktop Reference Design */}
            <div className="space-y-4">
              <h3 className="text-base lg:text-lg font-semibold text-gray-900 dark:text-white font-poppins">
                Talla:
              </h3>
              <div className="grid grid-cols-4 sm:grid-cols-5 lg:grid-cols-6 xl:grid-cols-8 gap-3">
                {(product.sizes || []).map((size) => (
                  <button
                    key={size}
                    onClick={() => {
                      console.log('🔥 SIZE CLICKED:', size)
                      setSelectedSize(size)
                      console.log('🔥 SELECTED SIZE SET TO:', size)
                    }}
                    className={`py-3 lg:py-4 text-sm lg:text-base font-semibold rounded-lg border-2 transition-all min-h-[44px] lg:min-h-[52px] touch-manipulation font-poppins ${
                      selectedSize === size
                        ? 'border-lime-green bg-lime-green text-black shadow-lg'
                        : 'border-gray-200 dark:border-gray-700 text-gray-900 dark:text-white hover:border-lime-green hover:shadow-md'
                    }`}
                  >
                    {size}
                  </button>
                ))}
              </div>
            </div>

            {/* Quantity */}
            <div className="flex items-center gap-4">
              <span className="text-sm font-medium text-gray-900 dark:text-white">
                Cantidad:
              </span>
              <div className="flex items-center border border-gray-200 dark:border-gray-700 rounded-lg">
                <button
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  className="px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white min-w-[44px] min-h-[44px] touch-manipulation flex items-center justify-center"
                >
                  -
                </button>
                <span className="px-4 py-3 text-gray-900 dark:text-white min-w-[44px] text-center">
                  {quantity}
                </span>
                <button
                  onClick={() => setQuantity(quantity + 1)}
                  className="px-4 py-3 text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white min-w-[44px] min-h-[44px] touch-manipulation flex items-center justify-center"
                >
                  +
                </button>
              </div>
            </div>

            {/* Action Buttons - Desktop Reference Design */}
            <div className="space-y-4 pt-6">
              <button
                onClick={handleAddToCart}
                disabled={!selectedSize}
                className="w-full bg-lime-green text-pure-black font-bold py-4 lg:py-5 px-6 lg:px-8 rounded-xl hover:bg-lime-green/90 hover:shadow-lg transition-all disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-3 min-h-[52px] lg:min-h-[60px] touch-manipulation text-lg lg:text-xl font-poppins tracking-wide"
              >
                <ShoppingCart className="w-6 h-6 lg:w-7 lg:h-7" />
                AGRÉGALO
              </button>

              <div className="flex gap-3">
                <button
                  onClick={handleWishlistToggle}
                  className={`flex-1 border-2 font-medium py-4 px-6 rounded-xl transition-all flex items-center justify-center gap-2 min-h-[52px] lg:min-h-[60px] touch-manipulation font-poppins ${
                    isInWishlist(product.id)
                      ? 'border-red-500 text-red-500 bg-red-50 dark:bg-red-900/20'
                      : 'border-border-gray text-text-gray hover:border-lime-green hover:text-lime-green'
                  }`}
                >
                  <Heart className={`w-5 h-5 ${isInWishlist(product.id) ? 'fill-current' : ''}`} />
                  <span>Lista de Deseos</span>
                </button>

                <button
                  onClick={() => setShowShareModal(true)}
                  className="flex-1 border-2 border-border-gray text-text-gray hover:border-lime-green hover:text-lime-green font-medium py-4 px-6 rounded-xl transition-all flex items-center justify-center gap-2 min-h-[52px] lg:min-h-[60px] touch-manipulation font-poppins"
                >
                  <Share2 className="w-5 h-5" />
                  <span>Compartir</span>
                </button>
              </div>

              {/* Payment Methods - Desktop Reference Design */}
              <div className="pt-4 border-t border-gray-200 dark:border-gray-700">
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-3 font-poppins">
                  Métodos de pago aceptados:
                </p>
                <div className="flex items-center gap-3 flex-wrap">
                  <div className="flex items-center gap-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2">
                    <div className="w-8 h-5 bg-gradient-to-r from-red-500 to-orange-500 rounded-sm flex items-center justify-center">
                      <span className="text-white text-xs font-bold">MC</span>
                    </div>
                    <span className="text-xs text-gray-600 dark:text-gray-400">Mastercard</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2">
                    <div className="w-8 h-5 bg-blue-600 rounded-sm flex items-center justify-center">
                      <span className="text-white text-xs font-bold">V</span>
                    </div>
                    <span className="text-xs text-gray-600 dark:text-gray-400">Visa</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2">
                    <div className="w-8 h-5 bg-blue-500 rounded-sm flex items-center justify-center">
                      <span className="text-white text-xs font-bold">PP</span>
                    </div>
                    <span className="text-xs text-gray-600 dark:text-gray-400">PayPal</span>
                  </div>
                  <div className="flex items-center gap-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg px-3 py-2">
                    <div className="w-8 h-5 bg-orange-500 rounded-sm flex items-center justify-center">
                      <span className="text-white text-xs font-bold">₿</span>
                    </div>
                    <span className="text-xs text-gray-600 dark:text-gray-400">Bitcoin</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Trust Badges */}
            <div className="grid grid-cols-3 gap-6 pt-6 border-t border-gray-200 dark:border-gray-700">
              <div className="text-center">
                <Truck className="w-7 h-7 sm:w-8 sm:h-8 text-lime-green mx-auto mb-2" />
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Envío Gratis</p>
              </div>
              <div className="text-center">
                <Shield className="w-7 h-7 sm:w-8 sm:h-8 text-lime-green mx-auto mb-2" />
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">Garantía</p>
              </div>
              <div className="text-center">
                <RotateCcw className="w-7 h-7 sm:w-8 sm:h-8 text-lime-green mx-auto mb-2" />
                <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">30 días</p>
              </div>
            </div>

            {/* Enhanced Product Information Tabs */}
            <div className="pt-6 border-t border-gray-200 dark:border-gray-700">
              {/* Tab Navigation */}
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 rounded-lg p-1 mb-6">
                <button
                  onClick={() => setActiveTab('description')}
                  className={`flex-1 py-3 px-4 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center gap-2 min-h-[44px] touch-manipulation ${
                    activeTab === 'description'
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <Info className="w-4 h-4" />
                  Descripción Completa
                </button>
                <button
                  onClick={() => setActiveTab('sizing')}
                  className={`flex-1 py-3 px-4 text-sm font-medium rounded-md transition-all duration-200 flex items-center justify-center gap-2 min-h-[44px] touch-manipulation ${
                    activeTab === 'sizing'
                      ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  <Ruler className="w-4 h-4" />
                  Guía de Tallas
                </button>
              </div>

              {/* Tab Content */}
              <AnimatePresence mode="wait">
                {activeTab === 'description' && (
                  <motion.div
                    key="description"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6 pb-8"
                  >
                    {/* Full Description */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                        Descripción del Producto
                      </h3>
                      <p className="text-gray-600 dark:text-gray-400 leading-relaxed mb-4">
                        {product.fullDescription || product.description}
                      </p>
                    </div>

                    {/* Materials */}
                    {product.materials && product.materials.length > 0 && (
                      <div>
                        <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                          <Package className="w-4 h-4 text-lime-green" />
                          Materiales
                        </h4>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2">
                          {product.materials.map((material, index) => (
                            <div
                              key={index}
                              className="bg-gray-50 dark:bg-gray-800 rounded-lg p-3 text-sm text-gray-700 dark:text-gray-300"
                            >
                              {material}
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Care Instructions */}
                    {product.careInstructions && product.careInstructions.length > 0 && (
                      <div>
                        <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                          <Shield className="w-4 h-4 text-lime-green" />
                          Cuidado y Mantenimiento
                        </h4>
                        <ul className="space-y-2">
                          {product.careInstructions.map((instruction, index) => (
                            <li
                              key={index}
                              className="flex items-start gap-2 text-sm text-gray-600 dark:text-gray-400"
                            >
                              <span className="w-1.5 h-1.5 bg-lime-green rounded-full mt-2 flex-shrink-0"></span>
                              {instruction}
                            </li>
                          ))}
                        </ul>
                      </div>
                    )}

                    {/* Product Details */}
                    <div className="grid grid-cols-2 gap-4 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <div>
                        <span className="text-sm font-medium text-gray-900 dark:text-white">SKU:</span>
                        <p className="text-sm text-gray-600 dark:text-gray-400">{product.sku}</p>
                      </div>
                      {product.releaseDate && (
                        <div>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">Lanzamiento:</span>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {new Date(product.releaseDate).toLocaleDateString('es-MX')}
                          </p>
                        </div>
                      )}
                      {product.gender && (
                        <div>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">Género:</span>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {product.gender === 'MIXTE' ? 'Unisex' : product.gender}
                          </p>
                        </div>
                      )}
                      {product.type && (
                        <div>
                          <span className="text-sm font-medium text-gray-900 dark:text-white">Tipo:</span>
                          <p className="text-sm text-gray-600 dark:text-gray-400 capitalize">
                            {product.subType || product.type}
                          </p>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}

                {activeTab === 'sizing' && (
                  <motion.div
                    key="sizing"
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                    className="space-y-6 pb-8"
                  >
                    {/* Size Guide Header */}
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                        <Ruler className="w-5 h-5 text-lime-green" />
                        Guía de Tallas
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
                        Encuentra tu talla perfecta con nuestra guía de medidas.
                      </p>
                    </div>

                    {/* Size Chart */}
                    <div className="overflow-x-auto">
                      <table className="w-full text-sm">
                        <thead>
                          <tr className="border-b border-gray-200 dark:border-gray-700">
                            <th className="text-left py-3 px-2 font-medium text-gray-900 dark:text-white">US</th>
                            <th className="text-left py-3 px-2 font-medium text-gray-900 dark:text-white">EU</th>
                            <th className="text-left py-3 px-2 font-medium text-gray-900 dark:text-white">UK</th>
                            <th className="text-left py-3 px-2 font-medium text-gray-900 dark:text-white">CM</th>
                          </tr>
                        </thead>
                        <tbody>
                          {product.sizes?.map((euSize) => {
                            // Convert EU size to other formats
                            const euNum = parseFloat(euSize)
                            const usSize = (euNum - 33).toFixed(euSize.includes('.') ? 1 : 0)
                            const ukSize = (euNum - 34).toFixed(euSize.includes('.') ? 1 : 0)
                            const cmSize = (euNum * 0.67 + 2).toFixed(1)

                            return { us: usSize, eu: euSize, uk: ukSize, cm: cmSize }
                          }).map((size, index) => (
                            <tr key={index} className="border-b border-gray-100 dark:border-gray-800">
                              <td className="py-3 px-2 text-gray-700 dark:text-gray-300">{size.us}</td>
                              <td className="py-3 px-2 text-gray-700 dark:text-gray-300">{size.eu}</td>
                              <td className="py-3 px-2 text-gray-700 dark:text-gray-300">{size.uk}</td>
                              <td className="py-3 px-2 text-gray-700 dark:text-gray-300">{size.cm}</td>
                            </tr>
                          ))}
                        </tbody>
                      </table>
                    </div>

                    {/* Sizing Tips */}
                    <div className="bg-lime-green/5 border border-lime-green/20 rounded-lg p-4">
                      <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3 flex items-center gap-2">
                        <Award className="w-4 h-4 text-lime-green" />
                        Consejos de Tallaje
                      </h4>
                      <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                        <li className="flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-lime-green rounded-full mt-2 flex-shrink-0"></span>
                          Mide tu pie al final del día cuando esté más hinchado
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-lime-green rounded-full mt-2 flex-shrink-0"></span>
                          Usa calcetines similares a los que planeas usar con el zapato
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-lime-green rounded-full mt-2 flex-shrink-0"></span>
                          Si estás entre tallas, elige la talla más grande
                        </li>
                        <li className="flex items-start gap-2">
                          <span className="w-1.5 h-1.5 bg-lime-green rounded-full mt-2 flex-shrink-0"></span>
                          Considera el ancho de tu pie para mayor comodidad
                        </li>
                      </ul>
                    </div>

                    {/* How to Measure */}
                    <div>
                      <h4 className="text-md font-semibold text-gray-900 dark:text-white mb-3">
                        Cómo Medir tu Pie
                      </h4>
                      <div className="space-y-3 text-sm text-gray-600 dark:text-gray-400">
                        <div className="flex items-start gap-3">
                          <span className="bg-lime-green text-black rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">1</span>
                          <p>Coloca una hoja de papel en el suelo contra una pared</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <span className="bg-lime-green text-black rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">2</span>
                          <p>Pon tu pie en el papel con el talón contra la pared</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <span className="bg-lime-green text-black rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">3</span>
                          <p>Marca el punto más largo de tu pie en el papel</p>
                        </div>
                        <div className="flex items-start gap-3">
                          <span className="bg-lime-green text-black rounded-full w-6 h-6 flex items-center justify-center text-xs font-bold flex-shrink-0 mt-0.5">4</span>
                          <p>Mide la distancia desde la pared hasta la marca en centímetros</p>
                        </div>
                      </div>
                    </div>
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Share Modal */}
      <ShareModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        product={product}
        currentUrl={typeof window !== 'undefined' ? window.location.href : ''}
        selectedModel={selectedModel}
      />
    </div>
  )
}
