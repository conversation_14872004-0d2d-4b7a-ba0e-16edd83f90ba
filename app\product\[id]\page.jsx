'use client'

import { useState, useEffect } from 'react'
import { useParams } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { getProductById } from '@/lib/data/products'
import { useCart } from '@/contexts/CartContext'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import SizeRecommendation from '@/components/features/SizeRecommendation'
import ARTryOn from '@/components/features/ARTryOn'
import { formatPrice } from '@/lib/utils'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedLoader from '@/components/ui/AnimatedLoader'

export default function ProductDetailPage() {
  const params = useParams()
  const { addItem } = useCart()
  const { addToRecentlyViewed } = useUserPreferences()
  const [product, setProduct] = useState(null)
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedImage, setSelectedImage] = useState(0)
  const [quantity, setQuantity] = useState(1)
  const [isLoading, setIsLoading] = useState(true)
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [showSizeRecommendation, setShowSizeRecommendation] = useState(false)
  const [showARTryOn, setShowARTryOn] = useState(false)

  useEffect(() => {
    // Simulate loading
    setTimeout(() => {
      const foundProduct = getProductById(params.id)
      setProduct(foundProduct)
      setIsLoading(false)

      // Add to recently viewed if product exists
      if (foundProduct) {
        addToRecentlyViewed({
          ...foundProduct,
          viewedAt: new Date().toISOString()
        })
      }
    }, 1000)
  }, [params.id, addToRecentlyViewed])

  const handleAddToCart = async () => {
    if (!selectedSize) {
      alert('Por favor selecciona una talla')
      return
    }

    setIsAddingToCart(true)
    
    // Simulate API call
    setTimeout(() => {
      addItem(product.id, selectedSize, quantity)
      setIsAddingToCart(false)
      
      // Reset form
      setSelectedSize('')
      setQuantity(1)
      
      // Show success message (in a real app, you'd use a toast)
      alert('¡Producto agregado al carrito!')
    }, 500)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20 flex items-center justify-center">
        <AnimatedLoader variant="morph" size="lg" text="Cargando producto..." />
      </div>
    )
  }

  if (!product) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">😕</div>
          <h1 className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
            Producto no encontrado
          </h1>
          <p className="text-warm-camel mb-6">
            El producto que buscas no existe o ha sido removido.
          </p>
          <Button variant="primary" href="/shop">
            Volver a la Tienda
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Breadcrumb */}
        <motion.nav
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center gap-2 text-sm text-warm-camel mb-8"
        >
          <a href="/" className="hover:text-rich-gold transition-colors">Inicio</a>
          <span>/</span>
          <a href="/shop" className="hover:text-rich-gold transition-colors">Tienda</a>
          <span>/</span>
          <span className="text-forest-emerald dark:text-light-cloud-gray">{product.name}</span>
        </motion.nav>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          
          {/* Product Images */}
          <motion.div
            initial={{ opacity: 0, x: -50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6 }}
          >
            {/* Main Image */}
            <div className="aspect-square bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray rounded-2xl mb-4 flex items-center justify-center overflow-hidden">
              <motion.div
                key={selectedImage}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.3 }}
                className="w-full h-full flex items-center justify-center"
              >
                <span className="text-warm-camel text-lg font-medium">
                  {product.name}
                </span>
              </motion.div>
            </div>

            {/* Thumbnail Images */}
            <div className="grid grid-cols-4 gap-2">
              {[...Array(4)].map((_, index) => (
                <motion.button
                  key={index}
                  onClick={() => setSelectedImage(index)}
                  className={`aspect-square rounded-lg overflow-hidden ${
                    selectedImage === index 
                      ? 'ring-2 ring-rich-gold' 
                      : 'ring-1 ring-warm-camel/30'
                  }`}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <div className="w-full h-full bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray flex items-center justify-center">
                    <span className="text-warm-camel text-xs">
                      {index + 1}
                    </span>
                  </div>
                </motion.button>
              ))}
            </div>
          </motion.div>

          {/* Product Info */}
          <motion.div
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
            className="space-y-6"
          >
            
            {/* Badges */}
            <div className="flex flex-wrap gap-2">
              {product.isLimited && (
                <Badge variant="limited" size="sm" pulse>
                  Edición Limitada
                </Badge>
              )}
              {product.isNew && (
                <Badge variant="success" size="sm">
                  Nuevo
                </Badge>
              )}
              {product.isVip && (
                <Badge variant="vip" size="sm">
                  VIP
                </Badge>
              )}
              {product.isExclusive && (
                <Badge variant="exclusive" size="sm">
                  Exclusivo
                </Badge>
              )}
            </div>

            {/* Brand & Name */}
            <div>
              <p className="text-warm-camel font-medium mb-2">{product.brand}</p>
              <h1 className="text-3xl lg:text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
                {product.name}
              </h1>
              <p className="text-warm-camel leading-relaxed">
                {product.description}
              </p>
            </div>

            {/* Price */}
            <div className="flex items-center gap-4">
              <span className="text-3xl font-bold text-rich-gold">
                {formatPrice(product.price, 'MXN', 'es-MX')}
              </span>
              {product.originalPrice && (
                <span className="text-xl text-warm-camel line-through">
                  {formatPrice(product.originalPrice, 'MXN', 'es-MX')}
                </span>
              )}
            </div>

            {/* Size Selection */}
            <div>
              <div className="flex items-center justify-between mb-3">
                <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray">
                  Talla
                </h3>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowSizeRecommendation(true)}
                  className="text-rich-gold hover:text-rich-gold/80"
                >
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                  </svg>
                  Recomendación IA
                </Button>
              </div>
              <div className="grid grid-cols-6 gap-2">
                {product.sizes.map((size) => (
                  <motion.button
                    key={size}
                    onClick={() => setSelectedSize(size)}
                    className={`py-3 px-4 rounded-lg border transition-all ${
                      selectedSize === size
                        ? 'bg-rich-gold text-forest-emerald border-rich-gold shadow-gold-glow'
                        : 'border-warm-camel text-warm-camel hover:border-rich-gold hover:text-rich-gold'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    {size}
                  </motion.button>
                ))}
              </div>
            </div>

            {/* Quantity */}
            <div>
              <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-3">
                Cantidad
              </h3>
              <div className="flex items-center gap-3">
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setQuantity(Math.max(1, quantity - 1))}
                  disabled={quantity <= 1}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                  </svg>
                </Button>
                
                <span className="w-12 text-center font-semibold text-forest-emerald dark:text-light-cloud-gray">
                  {quantity}
                </span>
                
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => setQuantity(quantity + 1)}
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                </Button>
              </div>
            </div>

            {/* Add to Cart */}
            <div className="space-y-3">
              <Button
                variant="primary"
                size="lg"
                className="w-full"
                onClick={handleAddToCart}
                disabled={isAddingToCart || !selectedSize}
              >
                {isAddingToCart ? (
                  <div className="flex items-center gap-2">
                    <AnimatedLoader variant="dots" size="sm" />
                    Agregando...
                  </div>
                ) : (
                  `Agregar al Carrito - ${formatPrice(product.price * quantity, 'MXN', 'es-MX')}`
                )}
              </Button>

              <div className="grid grid-cols-2 gap-3">
                <Button
                  variant="secondary"
                  size="lg"
                  onClick={() => setShowARTryOn(true)}
                  className="flex items-center gap-2"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                  </svg>
                  Probar AR
                </Button>

                <Button variant="secondary" size="lg">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                  </svg>
                  Favoritos
                </Button>
              </div>
            </div>

            {/* Product Features */}
            <Card variant="default">
              <CardContent className="p-6">
                <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  Características
                </h3>
                <ul className="space-y-2">
                  {product.features.map((feature, index) => (
                    <motion.li
                      key={index}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: index * 0.1 }}
                      className="flex items-center gap-2 text-warm-camel"
                    >
                      <div className="w-1.5 h-1.5 bg-rich-gold rounded-full" />
                      {feature}
                    </motion.li>
                  ))}
                </ul>
              </CardContent>
            </Card>

            {/* Product Info */}
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="text-warm-camel">SKU:</span>
                <span className="text-forest-emerald dark:text-light-cloud-gray ml-2">
                  {product.sku}
                </span>
              </div>
              <div>
                <span className="text-warm-camel">Stock:</span>
                <span className="text-forest-emerald dark:text-light-cloud-gray ml-2">
                  {product.stock} disponibles
                </span>
              </div>
              <div>
                <span className="text-warm-camel">Valoración:</span>
                <span className="text-forest-emerald dark:text-light-cloud-gray ml-2">
                  ⭐ {product.rating} ({product.reviews} reseñas)
                </span>
              </div>
              <div>
                <span className="text-warm-camel">Lanzamiento:</span>
                <span className="text-forest-emerald dark:text-light-cloud-gray ml-2">
                  {new Date(product.releaseDate).toLocaleDateString('es-MX')}
                </span>
              </div>
            </div>
          </motion.div>
        </div>
      </div>

      {/* Size Recommendation Modal */}
      {showSizeRecommendation && (
        <SizeRecommendation
          product={product}
          onSizeSelect={(size) => setSelectedSize(size)}
          onClose={() => setShowSizeRecommendation(false)}
        />
      )}

      {/* AR Try-On Modal */}
      {showARTryOn && (
        <ARTryOn
          product={product}
          onClose={() => setShowARTryOn(false)}
        />
      )}
    </div>
  )
}
