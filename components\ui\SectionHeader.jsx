'use client'

import { cn } from '@/lib/utils'

export default function SectionHeader({
  title,
  subtitle,
  description,
  align = 'center',
  size = 'default',
  className,
  decorativeElements = false
}) {
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  }

  const sizeClasses = {
    sm: 'text-xl sm:text-2xl md:text-3xl',
    default: 'text-2xl sm:text-3xl md:text-4xl lg:text-5xl',
    lg: 'text-3xl sm:text-4xl md:text-5xl lg:text-6xl',
    xl: 'text-4xl sm:text-5xl md:text-6xl lg:text-7xl'
  }

  return (
    <div className={cn(alignClasses[align], 'max-w-4xl mx-auto px-4 sm:px-6 lg:px-8', className)}>
      {/* Mobile-optimized Decorative Elements for HOTTEST DROPS */}
      {decorativeElements && (
        <div className="flex items-center justify-center mb-4 sm:mb-6">
          <div className="w-3 h-3 sm:w-4 sm:h-4 bg-[#BFFF00] rounded-full mr-2 sm:mr-3 opacity-80"></div>
          <div className="w-4 h-4 sm:w-6 sm:h-6 bg-[#BFFF00] rounded-full opacity-60"></div>
        </div>
      )}

      <h2 className={cn(
        'font-bold text-[#1f2937] mb-4 sm:mb-6 tracking-wide',
        'font-inter leading-tight',
        sizeClasses[size]
      )}>
        {title}
      </h2>

      {/* Mobile-first Multi-line description support */}
      {description && (
        <div className="space-y-1 sm:space-y-2 max-w-2xl mx-auto">
          {Array.isArray(description) ? (
            description.map((line, index) => (
              <p key={index} className="text-[#6b7280] text-sm sm:text-base md:text-lg leading-relaxed">
                {line}
              </p>
            ))
          ) : (
            <p className="text-[#6b7280] text-sm sm:text-base md:text-lg leading-relaxed">
              {description}
            </p>
          )}
        </div>
      )}

      {/* Legacy subtitle support - mobile optimized */}
      {subtitle && !description && (
        <p className="text-[#6b7280] text-sm sm:text-base md:text-lg leading-relaxed max-w-2xl mx-auto">
          {subtitle}
        </p>
      )}
    </div>
  )
}
