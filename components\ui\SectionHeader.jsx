'use client'

export default function SectionHeader({ 
  title, 
  subtitle, 
  align = 'left', 
  size = 'md',
  className = '' 
}) {
  const alignClasses = {
    left: 'text-left',
    center: 'text-center',
    right: 'text-right'
  }

  const sizeClasses = {
    sm: 'text-2xl',
    md: 'text-3xl',
    lg: 'text-4xl',
    xl: 'text-5xl'
  }

  return (
    <div className={`${alignClasses[align]} ${className}`}>
      <h2 className={`font-godber font-bold text-pure-black dark:text-pure-white tracking-godber-sm ${sizeClasses[size]} mb-4`}>
        {title}
      </h2>
      {subtitle && (
        <p className="text-text-gray dark:text-neutral-400 font-poppins text-lg">
          {subtitle}
        </p>
      )}
    </div>
  )
}
