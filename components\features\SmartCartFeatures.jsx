'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { useCart } from '@/contexts/CartContext'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'

export default function SmartCartFeatures() {
  const { items: cartItems, addItem, updateQuantity } = useCart()
  const { stylePreferences, addToRecentlyViewed } = useUserPreferences()
  const [bundleSuggestions, setBundleSuggestions] = useState([])
  const [outfitSuggestions, setOutfitSuggestions] = useState([])
  const [sizeAlerts, setSizeAlerts] = useState([])
  const [priceAlerts, setPriceAlerts] = useState([])
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  useEffect(() => {
    if (cartItems && cartItems.length > 0) {
      analyzeCartForSuggestions()
    }
  }, [cartItems])

  const analyzeCartForSuggestions = async () => {
    setIsAnalyzing(true)
    
    // Simulate AI analysis
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    const bundles = generateBundleSuggestions()
    const outfits = generateOutfitSuggestions()
    const alerts = generateSizeAlerts()
    const prices = generatePriceAlerts()
    
    setBundleSuggestions(bundles)
    setOutfitSuggestions(outfits)
    setSizeAlerts(alerts)
    setPriceAlerts(prices)
    
    setIsAnalyzing(false)
  }

  const generateBundleSuggestions = () => {
    const suggestions = []
    
    // Mock bundle suggestions based on cart items
    if (cartItems && cartItems.some(item => item.category === 'sneakers')) {
      suggestions.push({
        id: 'bundle1',
        title: 'Complete Sneaker Care Kit',
        description: 'Protege tus sneakers con este kit completo',
        items: [
          { id: 'care1', name: 'Crep Protect Spray', price: 450, image: '/placeholder.jpg' },
          { id: 'care2', name: 'Cleaning Kit Premium', price: 650, image: '/placeholder.jpg' },
          { id: 'care3', name: 'Sneaker Shields', price: 350, image: '/placeholder.jpg' }
        ],
        originalPrice: 1450,
        bundlePrice: 1200,
        savings: 250,
        match: 95
      })
    }

    if (cartItems && cartItems.some(item => item.brand === 'Nike')) {
      suggestions.push({
        id: 'bundle2',
        title: 'Nike Essentials Pack',
        description: 'Completa tu look Nike con estos accesorios',
        items: [
          { id: 'acc1', name: 'Nike Dri-FIT Socks', price: 280, image: '/placeholder.jpg' },
          { id: 'acc2', name: 'Nike Cap', price: 650, image: '/placeholder.jpg' },
          { id: 'acc3', name: 'Nike Backpack', price: 1200, image: '/placeholder.jpg' }
        ],
        originalPrice: 2130,
        bundlePrice: 1800,
        savings: 330,
        match: 88
      })
    }

    return suggestions.slice(0, 2)
  }

  const generateOutfitSuggestions = () => {
    const suggestions = []
    
    // Mock outfit suggestions
    if (cartItems && cartItems.length > 0) {
      suggestions.push({
        id: 'outfit1',
        title: 'Streetwear Completo',
        description: 'Completa tu look streetwear con estas piezas',
        items: [
          { id: 'cloth1', name: 'Oversized Hoodie', price: 1200, image: '/placeholder.jpg', category: 'clothing' },
          { id: 'cloth2', name: 'Cargo Pants', price: 1500, image: '/placeholder.jpg', category: 'clothing' },
          { id: 'cloth3', name: 'Bucket Hat', price: 450, image: '/placeholder.jpg', category: 'accessories' }
        ],
        totalPrice: 3150,
        styleMatch: 92,
        occasion: 'Casual/Street'
      })

      suggestions.push({
        id: 'outfit2',
        title: 'Look Minimalista',
        description: 'Estilo clean y sofisticado',
        items: [
          { id: 'cloth4', name: 'Basic White Tee', price: 450, image: '/placeholder.jpg', category: 'clothing' },
          { id: 'cloth5', name: 'Slim Jeans', price: 1100, image: '/placeholder.jpg', category: 'clothing' },
          { id: 'cloth6', name: 'Minimalist Watch', price: 2200, image: '/placeholder.jpg', category: 'accessories' }
        ],
        totalPrice: 3750,
        styleMatch: 87,
        occasion: 'Casual/Work'
      })
    }

    return suggestions
  }

  const generateSizeAlerts = () => {
    const alerts = []

    if (!cartItems) return alerts

    cartItems.forEach(item => {
      if (item.stock && item.stock[item.selectedSize] < 3) {
        alerts.push({
          id: `size-${item.id}`,
          productId: item.id,
          productName: item.name,
          size: item.selectedSize,
          stock: item.stock[item.selectedSize],
          type: 'low_stock',
          message: `Solo quedan ${item.stock[item.selectedSize]} unidades en talla ${item.selectedSize}`
        })
      }
    })

    return alerts
  }

  const generatePriceAlerts = () => {
    const alerts = []

    if (!cartItems) return alerts

    // Mock price drop alerts
    cartItems.forEach(item => {
      if (Math.random() > 0.7) { // 30% chance of price drop
        const discount = Math.floor(Math.random() * 20) + 5 // 5-25% discount
        const newPrice = Math.floor(item.price * (1 - discount / 100))
        
        alerts.push({
          id: `price-${item.id}`,
          productId: item.id,
          productName: item.name,
          originalPrice: item.price,
          newPrice: newPrice,
          discount: discount,
          type: 'price_drop',
          message: `¡Precio reducido! Ahorra $${item.price - newPrice} MXN`
        })
      }
    })

    return alerts
  }

  const addBundleToCart = (bundle) => {
    bundle.items.forEach(item => {
      addItem(item.id, 'M', 1) // Use addItem with default size
    })
  }

  const addOutfitToCart = (outfit) => {
    outfit.items.forEach(item => {
      addItem(item.id, 'M', 1) // Use addItem with default size
    })
  }

  if (!cartItems || cartItems.length === 0) {
    return null
  }

  return (
    <div className="space-y-6">
      {/* AI Analysis Loading */}
      {isAnalyzing && (
        <Card variant="glass">
          <CardContent className="p-6">
            <div className="flex items-center gap-4">
              <motion.div
                className="w-8 h-8 border-2 border-[#BFFF00] border-t-transparent rounded-full"
                animate={{ rotate: 360 }}
                transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
              />
              <div>
                <h3 className="font-semibold text-gray-800">Analizando tu carrito con IA...</h3>
                <p className="text-sm text-gray-600">Generando recomendaciones personalizadas</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Price Alerts */}
      {priceAlerts.length > 0 && (
        <Card variant="default" className="border-orange-200 bg-orange-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <span className="text-2xl">💰</span>
              <h3 className="text-lg font-bold text-orange-800">¡Alertas de Precio!</h3>
            </div>
            <div className="space-y-3">
              {priceAlerts.map(alert => (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center justify-between p-3 bg-white rounded-lg border border-orange-200"
                >
                  <div>
                    <h4 className="font-semibold text-gray-800">{alert.productName}</h4>
                    <p className="text-sm text-orange-700">{alert.message}</p>
                    <div className="flex items-center gap-2 mt-1">
                      <span className="text-sm line-through text-gray-500">${alert.originalPrice}</span>
                      <span className="font-bold text-orange-600">${alert.newPrice}</span>
                      <Badge variant="sale" size="sm">{alert.discount}% OFF</Badge>
                    </div>
                  </div>
                  <AnimatedButton variant="primary" size="sm">
                    Actualizar Precio
                  </AnimatedButton>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Size Alerts */}
      {sizeAlerts.length > 0 && (
        <Card variant="default" className="border-red-200 bg-red-50">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <span className="text-2xl">⚠️</span>
              <h3 className="text-lg font-bold text-red-800">Alertas de Stock</h3>
            </div>
            <div className="space-y-3">
              {sizeAlerts.map(alert => (
                <motion.div
                  key={alert.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  className="flex items-center justify-between p-3 bg-white rounded-lg border border-red-200"
                >
                  <div>
                    <h4 className="font-semibold text-gray-800">{alert.productName}</h4>
                    <p className="text-sm text-red-700">{alert.message}</p>
                  </div>
                  <div className="flex gap-2">
                    <AnimatedButton variant="outline" size="sm">
                      Cambiar Talla
                    </AnimatedButton>
                    <AnimatedButton variant="primary" size="sm">
                      Comprar Ahora
                    </AnimatedButton>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Bundle Suggestions */}
      {bundleSuggestions.length > 0 && (
        <Card variant="default">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <span className="text-2xl">📦</span>
              <h3 className="text-lg font-bold text-gray-800">Bundles Recomendados</h3>
              <Badge variant="accent" size="sm">AI Powered</Badge>
            </div>
            
            <div className="space-y-4">
              {bundleSuggestions.map(bundle => (
                <motion.div
                  key={bundle.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-gray-800">{bundle.title}</h4>
                      <p className="text-sm text-gray-600">{bundle.description}</p>
                    </div>
                    <Badge variant="success" size="sm">{bundle.match}% match</Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-2 mb-4">
                    {bundle.items.map(item => (
                      <div key={item.id} className="text-center">
                        <div className="w-16 h-16 bg-gray-100 rounded-lg mx-auto mb-2"></div>
                        <p className="text-xs text-gray-600">{item.name}</p>
                        <p className="text-xs font-semibold">${item.price}</p>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-2">
                        <span className="text-sm line-through text-gray-500">${bundle.originalPrice}</span>
                        <span className="font-bold text-[#BFFF00]">${bundle.bundlePrice}</span>
                        <Badge variant="sale" size="sm">Ahorra ${bundle.savings}</Badge>
                      </div>
                    </div>
                    <AnimatedButton 
                      variant="accent" 
                      size="sm"
                      onClick={() => addBundleToCart(bundle)}
                    >
                      Agregar Bundle
                    </AnimatedButton>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Outfit Suggestions */}
      {outfitSuggestions.length > 0 && (
        <Card variant="default">
          <CardContent className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <span className="text-2xl">👔</span>
              <h3 className="text-lg font-bold text-gray-800">Completa tu Look</h3>
              <Badge variant="accent" size="sm">Style AI</Badge>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {outfitSuggestions.map(outfit => (
                <motion.div
                  key={outfit.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                >
                  <div className="flex justify-between items-start mb-3">
                    <div>
                      <h4 className="font-semibold text-gray-800">{outfit.title}</h4>
                      <p className="text-sm text-gray-600">{outfit.description}</p>
                      <p className="text-xs text-gray-500 mt-1">Para: {outfit.occasion}</p>
                    </div>
                    <Badge variant="success" size="sm">{outfit.styleMatch}% match</Badge>
                  </div>
                  
                  <div className="space-y-2 mb-4">
                    {outfit.items.map(item => (
                      <div key={item.id} className="flex items-center gap-3">
                        <div className="w-8 h-8 bg-gray-100 rounded"></div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">{item.name}</p>
                          <p className="text-xs text-gray-500">{item.category}</p>
                        </div>
                        <span className="text-sm font-semibold">${item.price}</span>
                      </div>
                    ))}
                  </div>
                  
                  <div className="flex justify-between items-center">
                    <span className="font-bold text-gray-800">Total: ${outfit.totalPrice}</span>
                    <AnimatedButton 
                      variant="outline" 
                      size="sm"
                      onClick={() => addOutfitToCart(outfit)}
                    >
                      Agregar Look
                    </AnimatedButton>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
