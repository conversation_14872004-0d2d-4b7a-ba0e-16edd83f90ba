'use client'

import { useEffect, useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import MobileHeader from './MobileHeader'

export default function MobileLayout({ children, showBottomPadding = true }) {
  const [isMobile, setIsMobile] = useState(false)
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false)

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }

    const handleResize = () => {
      checkMobile()
      
      // Detect virtual keyboard on mobile
      if (window.innerWidth < 1024) {
        const initialHeight = window.innerHeight
        const currentHeight = window.innerHeight
        setIsKeyboardOpen(initialHeight - currentHeight > 150)
      }
    }

    checkMobile()
    window.addEventListener('resize', handleResize)
    
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  if (!isMobile) {
    return children
  }

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine">
      <MobileHeader />
      
      {/* Main Content */}
      <main className={`pt-16 ${showBottomPadding && !isKeyboardOpen ? 'pb-20' : ''} transition-all duration-300`}>
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.4 }}
        >
          {children}
        </motion.div>
      </main>

      {/* Safe Area for iOS */}
      <div className="h-safe-area-inset-bottom bg-transparent"></div>
    </div>
  )
}

// Mobile-specific components
export function MobileContainer({ children, className = '', noPadding = false }) {
  return (
    <div className={`${noPadding ? '' : 'px-4'} ${className}`}>
      {children}
    </div>
  )
}

export function MobileSection({ children, className = '', title, subtitle, action }) {
  return (
    <section className={`py-6 ${className}`}>
      {(title || subtitle || action) && (
        <div className="flex items-center justify-between mb-4 px-4">
          <div>
            {title && (
              <h2 className="text-xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray">
                {title}
              </h2>
            )}
            {subtitle && (
              <p className="text-sm text-warm-camel mt-1">
                {subtitle}
              </p>
            )}
          </div>
          {action && (
            <div>
              {action}
            </div>
          )}
        </div>
      )}
      <div className="px-4">
        {children}
      </div>
    </section>
  )
}

export function MobileGrid({ children, cols = 2, gap = 4, className = '' }) {
  const gridCols = {
    1: 'grid-cols-1',
    2: 'grid-cols-2',
    3: 'grid-cols-3',
    4: 'grid-cols-4'
  }

  const gridGap = {
    2: 'gap-2',
    3: 'gap-3',
    4: 'gap-4',
    6: 'gap-6'
  }

  return (
    <div className={`grid ${gridCols[cols]} ${gridGap[gap]} ${className}`}>
      {children}
    </div>
  )
}

export function MobileCard({ children, className = '', padding = true, glass = true }) {
  return (
    <motion.div
      className={`
        ${glass ? 'glass' : 'bg-white dark:bg-gray-800'} 
        ${padding ? 'p-4' : ''} 
        rounded-2xl border border-white/10 
        ${className}
      `}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      {children}
    </motion.div>
  )
}

export function MobileButton({ 
  children, 
  variant = 'primary', 
  size = 'md', 
  fullWidth = false,
  disabled = false,
  loading = false,
  icon,
  onClick,
  className = '',
  ...props 
}) {
  const variants = {
    primary: 'bg-primary text-white hover:bg-primary-dark',
    secondary: 'bg-secondary text-white hover:bg-secondary-dark',
    ghost: 'text-neutral-900 dark:text-white hover:bg-white/10',
    danger: 'bg-error text-white hover:bg-error-dark'
  }

  const sizes = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-3 text-base',
    lg: 'px-6 py-4 text-lg'
  }

  return (
    <motion.button
      onClick={onClick}
      disabled={disabled || loading}
      className={`
        ${variants[variant]}
        ${sizes[size]}
        ${fullWidth ? 'w-full' : ''}
        ${disabled || loading ? 'opacity-50 cursor-not-allowed' : ''}
        rounded-xl font-medium transition-all duration-200
        flex items-center justify-center gap-2
        ${className}
      `}
      whileHover={{ scale: disabled || loading ? 1 : 1.02 }}
      whileTap={{ scale: disabled || loading ? 1 : 0.98 }}
      {...props}
    >
      <AnimatePresence mode="wait">
        {loading ? (
          <motion.div
            key="loading"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin"
          />
        ) : (
          <motion.div
            key="content"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="flex items-center gap-2"
          >
            {icon && <span>{icon}</span>}
            {children}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.button>
  )
}

export function MobileBottomSheet({ isOpen, onClose, children, title }) {
  return (
    <AnimatePresence>
      {isOpen && (
        <>
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 z-50"
            onClick={onClose}
          />
          
          {/* Bottom Sheet */}
          <motion.div
            initial={{ y: '100%' }}
            animate={{ y: 0 }}
            exit={{ y: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 300 }}
            className="fixed bottom-0 left-0 right-0 z-50 glass backdrop-blur-xl rounded-t-3xl max-h-[90vh] overflow-hidden"
          >
            {/* Handle */}
            <div className="flex justify-center pt-3 pb-2">
              <div className="w-12 h-1 bg-warm-camel/30 rounded-full"></div>
            </div>
            
            {/* Header */}
            {title && (
              <div className="px-6 pb-4 border-b border-white/10">
                <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray">
                  {title}
                </h3>
              </div>
            )}
            
            {/* Content */}
            <div className="overflow-y-auto max-h-[calc(90vh-100px)]">
              {children}
            </div>
          </motion.div>
        </>
      )}
    </AnimatePresence>
  )
}
