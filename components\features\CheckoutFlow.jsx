'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import { Card, CardContent } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import AnimatedLoader from '@/components/ui/AnimatedLoader'

const checkoutSteps = [
  { id: 'shipping', name: 'Env<PERSON>', icon: '📦' },
  { id: 'payment', name: 'Pago', icon: '💳' },
  { id: 'review', name: 'Revisar', icon: '✅' }
]

export default function CheckoutFlow({ isOpen, onClose, onSuccess }) {
  const { user, isAuthenticated } = useAuth()
  const { items, summary, processCheckout } = useCart()
  const [currentStep, setCurrentStep] = useState('shipping')
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)
  
  const [checkoutData, setCheckoutData] = useState({
    shippingAddress: {
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
      email: user?.email || '',
      phone: user?.phone || '',
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'México'
    },
    billingAddress: {
      sameAsShipping: true,
      firstName: '',
      lastName: '',
      street: '',
      city: '',
      state: '',
      zipCode: '',
      country: 'México'
    },
    paymentMethod: {
      type: 'card',
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      cardholderName: ''
    }
  })

  const handleInputChange = (section, field, value) => {
    setCheckoutData(prev => ({
      ...prev,
      [section]: {
        ...prev[section],
        [field]: value
      }
    }))
    setError(null)
  }

  const validateStep = (step) => {
    switch (step) {
      case 'shipping':
        const shipping = checkoutData.shippingAddress
        return shipping.firstName && shipping.lastName && shipping.email && 
               shipping.street && shipping.city && shipping.state && shipping.zipCode
      
      case 'payment':
        const payment = checkoutData.paymentMethod
        return payment.cardNumber && payment.expiryMonth && payment.expiryYear && 
               payment.cvv && payment.cardholderName
      
      default:
        return true
    }
  }

  const nextStep = () => {
    if (!validateStep(currentStep)) {
      setError('Por favor completa todos los campos requeridos')
      return
    }

    const stepIndex = checkoutSteps.findIndex(step => step.id === currentStep)
    if (stepIndex < checkoutSteps.length - 1) {
      setCurrentStep(checkoutSteps[stepIndex + 1].id)
    }
  }

  const prevStep = () => {
    const stepIndex = checkoutSteps.findIndex(step => step.id === currentStep)
    if (stepIndex > 0) {
      setCurrentStep(checkoutSteps[stepIndex - 1].id)
    }
  }

  const handleCheckout = async () => {
    setLoading(true)
    setError(null)

    try {
      const result = await processCheckout(checkoutData)
      
      if (result.success) {
        onSuccess?.(result.order)
        onClose()
      } else {
        setError(result.error || 'Error al procesar el pedido')
      }
    } catch (err) {
      setError('Error inesperado. Por favor intenta de nuevo.')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  const renderShippingStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
          Dirección de Envío
        </h3>
        <p className="text-warm-camel">
          Ingresa la dirección donde quieres recibir tu pedido
        </p>
      </div>

      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <Input
            type="text"
            placeholder="Nombre"
            value={checkoutData.shippingAddress.firstName}
            onChange={(e) => handleInputChange('shippingAddress', 'firstName', e.target.value)}
            required
          />
          <Input
            type="text"
            placeholder="Apellido"
            value={checkoutData.shippingAddress.lastName}
            onChange={(e) => handleInputChange('shippingAddress', 'lastName', e.target.value)}
            required
          />
        </div>

        <Input
          type="email"
          placeholder="Email"
          value={checkoutData.shippingAddress.email}
          onChange={(e) => handleInputChange('shippingAddress', 'email', e.target.value)}
          required
        />

        <Input
          type="tel"
          placeholder="Teléfono"
          value={checkoutData.shippingAddress.phone}
          onChange={(e) => handleInputChange('shippingAddress', 'phone', e.target.value)}
          required
        />

        <Input
          type="text"
          placeholder="Dirección (Calle y número)"
          value={checkoutData.shippingAddress.street}
          onChange={(e) => handleInputChange('shippingAddress', 'street', e.target.value)}
          required
        />

        <div className="grid grid-cols-2 gap-4">
          <Input
            type="text"
            placeholder="Ciudad"
            value={checkoutData.shippingAddress.city}
            onChange={(e) => handleInputChange('shippingAddress', 'city', e.target.value)}
            required
          />
          <Input
            type="text"
            placeholder="Estado"
            value={checkoutData.shippingAddress.state}
            onChange={(e) => handleInputChange('shippingAddress', 'state', e.target.value)}
            required
          />
        </div>

        <div className="grid grid-cols-2 gap-4">
          <Input
            type="text"
            placeholder="Código Postal"
            value={checkoutData.shippingAddress.zipCode}
            onChange={(e) => handleInputChange('shippingAddress', 'zipCode', e.target.value)}
            required
          />
          <Input
            type="text"
            placeholder="País"
            value={checkoutData.shippingAddress.country}
            onChange={(e) => handleInputChange('shippingAddress', 'country', e.target.value)}
            disabled
          />
        </div>
      </div>
    </div>
  )

  const renderPaymentStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
          Método de Pago
        </h3>
        <p className="text-warm-camel">
          Ingresa los datos de tu tarjeta de crédito o débito
        </p>
      </div>

      <div className="space-y-4">
        <Input
          type="text"
          placeholder="Nombre del titular"
          value={checkoutData.paymentMethod.cardholderName}
          onChange={(e) => handleInputChange('paymentMethod', 'cardholderName', e.target.value)}
          required
        />

        <Input
          type="text"
          placeholder="Número de tarjeta"
          value={checkoutData.paymentMethod.cardNumber}
          onChange={(e) => handleInputChange('paymentMethod', 'cardNumber', e.target.value)}
          required
        />

        <div className="grid grid-cols-3 gap-4">
          <select
            value={checkoutData.paymentMethod.expiryMonth}
            onChange={(e) => handleInputChange('paymentMethod', 'expiryMonth', e.target.value)}
            className="input-glass rounded-lg px-4 py-3 text-forest-emerald dark:text-light-cloud-gray focus:outline-none focus:ring-2 focus:ring-rich-gold"
            required
          >
            <option value="">Mes</option>
            {Array.from({ length: 12 }, (_, i) => (
              <option key={i + 1} value={String(i + 1).padStart(2, '0')}>
                {String(i + 1).padStart(2, '0')}
              </option>
            ))}
          </select>

          <select
            value={checkoutData.paymentMethod.expiryYear}
            onChange={(e) => handleInputChange('paymentMethod', 'expiryYear', e.target.value)}
            className="input-glass rounded-lg px-4 py-3 text-forest-emerald dark:text-light-cloud-gray focus:outline-none focus:ring-2 focus:ring-rich-gold"
            required
          >
            <option value="">Año</option>
            {Array.from({ length: 10 }, (_, i) => (
              <option key={i} value={new Date().getFullYear() + i}>
                {new Date().getFullYear() + i}
              </option>
            ))}
          </select>

          <Input
            type="text"
            placeholder="CVV"
            value={checkoutData.paymentMethod.cvv}
            onChange={(e) => handleInputChange('paymentMethod', 'cvv', e.target.value)}
            maxLength={4}
            required
          />
        </div>

        {/* Demo Card Info */}
        <div className="p-4 bg-rich-gold/10 border border-rich-gold/30 rounded-lg">
          <p className="text-xs text-forest-emerald dark:text-light-cloud-gray font-medium mb-2">
            Tarjeta de prueba:
          </p>
          <p className="text-xs text-warm-camel">
            Número: 4242 4242 4242 4242<br />
            Fecha: 12/27 • CVV: 123<br />
            Titular: Test User
          </p>
        </div>
      </div>
    </div>
  )

  const renderReviewStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
          Revisar Pedido
        </h3>
        <p className="text-warm-camel">
          Verifica que toda la información sea correcta antes de confirmar
        </p>
      </div>

      {/* Order Items */}
      <Card variant="glass">
        <CardContent className="p-4">
          <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
            Productos ({items.length})
          </h4>
          <div className="space-y-3">
            {items.map((item, index) => (
              <div key={index} className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-warm-camel/20 to-rich-gold/20 rounded-lg flex items-center justify-center">
                  <span className="text-xs text-forest-emerald">IMG</span>
                </div>
                <div className="flex-1">
                  <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                    {item.product.name}
                  </p>
                  <p className="text-sm text-warm-camel">
                    Talla {item.size} • Cantidad: {item.quantity}
                  </p>
                </div>
                <p className="font-medium text-rich-gold">
                  ${(item.price * item.quantity).toLocaleString()} MXN
                </p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Shipping Address */}
      <Card variant="glass">
        <CardContent className="p-4">
          <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
            Dirección de Envío
          </h4>
          <div className="text-sm text-warm-camel">
            <p>{checkoutData.shippingAddress.firstName} {checkoutData.shippingAddress.lastName}</p>
            <p>{checkoutData.shippingAddress.street}</p>
            <p>{checkoutData.shippingAddress.city}, {checkoutData.shippingAddress.state} {checkoutData.shippingAddress.zipCode}</p>
            <p>{checkoutData.shippingAddress.country}</p>
            <p>{checkoutData.shippingAddress.phone}</p>
          </div>
        </CardContent>
      </Card>

      {/* Payment Method */}
      <Card variant="glass">
        <CardContent className="p-4">
          <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
            Método de Pago
          </h4>
          <div className="text-sm text-warm-camel">
            <p>**** **** **** {checkoutData.paymentMethod.cardNumber.slice(-4)}</p>
            <p>{checkoutData.paymentMethod.cardholderName}</p>
          </div>
        </CardContent>
      </Card>

      {/* Order Summary */}
      <Card variant="glass">
        <CardContent className="p-4">
          <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
            Resumen del Pedido
          </h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-warm-camel">Subtotal:</span>
              <span className="text-forest-emerald dark:text-light-cloud-gray">
                ${summary.subtotal.toLocaleString()} MXN
              </span>
            </div>
            {summary.discount > 0 && (
              <div className="flex justify-between">
                <span className="text-warm-camel">Descuento:</span>
                <span className="text-rich-gold">
                  -${summary.discount.toLocaleString()} MXN
                </span>
              </div>
            )}
            <div className="flex justify-between">
              <span className="text-warm-camel">Envío:</span>
              <span className="text-forest-emerald dark:text-light-cloud-gray">
                {summary.shipping === 0 ? 'Gratis' : `$${summary.shipping.toLocaleString()} MXN`}
              </span>
            </div>
            <div className="flex justify-between">
              <span className="text-warm-camel">IVA (16%):</span>
              <span className="text-forest-emerald dark:text-light-cloud-gray">
                ${summary.tax.toLocaleString()} MXN
              </span>
            </div>
            <div className="border-t border-warm-camel/20 pt-2 flex justify-between font-semibold">
              <span className="text-forest-emerald dark:text-light-cloud-gray">Total:</span>
              <span className="text-rich-gold text-lg">
                ${summary.total.toLocaleString()} MXN
              </span>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderStepContent = () => {
    switch (currentStep) {
      case 'shipping':
        return renderShippingStep()
      case 'payment':
        return renderPaymentStep()
      case 'review':
        return renderReviewStep()
      default:
        return renderShippingStep()
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="glass-card rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-warm-camel/20">
          <div>
            <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
              Finalizar Compra
            </h2>
            <p className="text-sm text-warm-camel">
              Paso {checkoutSteps.findIndex(s => s.id === currentStep) + 1} de {checkoutSteps.length}
            </p>
          </div>
          
          <Button variant="ghost" size="icon" onClick={onClose}>
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </Button>
        </div>

        <div className="flex h-full">
          {/* Steps Sidebar */}
          <div className="w-64 border-r border-warm-camel/20 p-6">
            <div className="space-y-4">
              {checkoutSteps.map((step, index) => {
                const isActive = step.id === currentStep
                const isCompleted = checkoutSteps.findIndex(s => s.id === currentStep) > index
                
                return (
                  <div
                    key={step.id}
                    className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                      isActive
                        ? 'bg-rich-gold/20 text-rich-gold'
                        : isCompleted
                        ? 'bg-forest-emerald/20 text-forest-emerald'
                        : 'text-warm-camel'
                    }`}
                  >
                    <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                      isActive
                        ? 'bg-rich-gold text-forest-emerald'
                        : isCompleted
                        ? 'bg-forest-emerald text-white'
                        : 'bg-warm-camel/20 text-warm-camel'
                    }`}>
                      {isCompleted ? '✓' : index + 1}
                    </div>
                    <span className="font-medium">{step.name}</span>
                  </div>
                )
              })}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Content */}
            <div className="flex-1 p-6 overflow-y-auto">
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  {renderStepContent()}
                </motion.div>
              </AnimatePresence>

              {/* Error Message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="mt-6 p-4 bg-warm-camel/10 border border-warm-camel/30 rounded-lg"
                >
                  <p className="text-sm text-warm-camel">{error}</p>
                </motion.div>
              )}
            </div>

            {/* Footer */}
            <div className="p-6 border-t border-warm-camel/20">
              <div className="flex items-center justify-between">
                <Button
                  variant="ghost"
                  onClick={prevStep}
                  disabled={currentStep === 'shipping'}
                >
                  Anterior
                </Button>

                <div className="flex gap-3">
                  {currentStep === 'review' ? (
                    <Button
                      variant="primary"
                      onClick={handleCheckout}
                      disabled={loading}
                      className="min-w-[140px]"
                    >
                      {loading ? (
                        <div className="flex items-center gap-2">
                          <AnimatedLoader variant="dots" size="sm" />
                          Procesando...
                        </div>
                      ) : (
                        'Confirmar Pedido'
                      )}
                    </Button>
                  ) : (
                    <Button variant="primary" onClick={nextStep}>
                      Siguiente
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
