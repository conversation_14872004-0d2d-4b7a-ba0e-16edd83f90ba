/**
 * TWL Enterprise System Integration Example
 * Shows how to integrate the enterprise system into your application
 */

import { initializeTWLSystem, getTWLSystem, shutdownTWLSystem } from '../TWLEnterpriseSystem'

/**
 * Example: Basic Integration
 */
export async function basicIntegrationExample() {
  console.log('🚀 TWL Enterprise System - Basic Integration Example')
  
  try {
    // 1. Initialize the system
    console.log('1. Initializing system...')
    const system = await initializeTWLSystem({
      productsBasePath: 'public/products',
      enableCache: true,
      enableAutoScan: true,
      environment: 'development'
    })
    
    // 2. Check system health
    console.log('2. Checking system health...')
    const health = system.getHealth()
    console.log('System Status:', health.status)
    console.log('Products Loaded:', health.metrics.productsLoaded)
    console.log('Cache Hit Rate:', health.metrics.cacheHitRate + '%')
    
    // 3. Load a specific product
    console.log('3. Loading specific product...')
    const product = await system.getProduct('sneakers-nike-mixte-air-force-bd7700-222')
    if (product) {
      console.log('Product Found:', product.name)
      console.log('Price:', product.pricing.currentPrice)
      console.log('Images:', product.media.images.length)
    } else {
      console.log('Product not found')
    }
    
    // 4. Search products
    console.log('4. Searching products...')
    const searchResults = await system.searchProducts('Nike', {
      brands: ['nike'],
      inStockOnly: true
    }, 1, 5)
    
    if (searchResults) {
      console.log('Search Results:', searchResults.total, 'products found')
      console.log('First 5 products:')
      searchResults.products.forEach((p, i) => {
        console.log(`  ${i + 1}. ${p.name} - $${p.pricing.currentPrice}`)
      })
    }
    
    // 5. Get all products
    console.log('5. Getting all products...')
    const allProducts = await system.getAllProducts()
    console.log('Total Products:', allProducts.length)
    
    // 6. Get system metrics
    console.log('6. System metrics:')
    const metrics = system.getMetrics()
    console.log('Uptime:', Math.round(metrics.system.uptime / 1000), 'seconds')
    console.log('Cache Hit Rate:', metrics.cache.hitRate + '%')
    console.log('Average Response Time:', metrics.performance.averageResponseTime + 'ms')
    
    console.log('✅ Basic integration example completed successfully!')
    
  } catch (error) {
    console.error('❌ Integration example failed:', error)
  }
}

/**
 * Example: Advanced Search and Filtering
 */
export async function advancedSearchExample() {
  console.log('🔍 TWL Enterprise System - Advanced Search Example')
  
  try {
    const system = getTWLSystem()
    
    // Advanced search with multiple filters
    const searchResults = await system.searchProducts('luxury sneakers', {
      brands: ['nike', 'gucci', 'dior'],
      categories: ['sneakers'],
      priceMin: 100,
      priceMax: 500,
      inStockOnly: true,
      isLimitedEdition: true,
      rating: 4.0,
      sortBy: 'price',
      sortOrder: 'desc'
    }, 1, 10)
    
    if (searchResults) {
      console.log('Advanced Search Results:')
      console.log('Total Found:', searchResults.total)
      console.log('Search Time:', searchResults.searchTime + 'ms')
      console.log('Facets:', searchResults.facets)
      
      console.log('Products:')
      searchResults.products.forEach((product, i) => {
        console.log(`${i + 1}. ${product.name}`)
        console.log(`   Brand: ${product.brand.name}`)
        console.log(`   Price: $${product.pricing.currentPrice}`)
        console.log(`   Rating: ${product.metadata.rating}/5`)
        console.log(`   In Stock: ${product.inventory.inStock}`)
        console.log('---')
      })
    }
    
  } catch (error) {
    console.error('❌ Advanced search example failed:', error)
  }
}

/**
 * Example: Performance Monitoring
 */
export async function performanceMonitoringExample() {
  console.log('📊 TWL Enterprise System - Performance Monitoring Example')
  
  try {
    const system = getTWLSystem()
    
    // Simulate multiple requests to generate metrics
    console.log('Simulating load...')
    const startTime = Date.now()
    
    const promises = []
    for (let i = 0; i < 50; i++) {
      promises.push(system.searchProducts('test', {}, 1, 10))
    }
    
    await Promise.all(promises)
    const duration = Date.now() - startTime
    
    console.log('Load simulation completed in', duration + 'ms')
    
    // Get updated metrics
    const metrics = system.getMetrics()
    console.log('Performance Metrics:')
    console.log('Total Requests:', metrics.performance.totalRequests)
    console.log('Average Response Time:', metrics.performance.averageResponseTime + 'ms')
    console.log('Error Rate:', metrics.performance.errorRate + '%')
    console.log('Cache Hit Rate:', metrics.cache.hitRate + '%')
    console.log('Memory Usage:', metrics.cache.memoryUsage + 'MB')
    
    // Health check
    const health = system.getHealth()
    console.log('System Health:', health.status)
    console.log('Component Status:', health.components)
    
  } catch (error) {
    console.error('❌ Performance monitoring example failed:', error)
  }
}

/**
 * Example: Error Handling and Recovery
 */
export async function errorHandlingExample() {
  console.log('🛡️ TWL Enterprise System - Error Handling Example')
  
  try {
    const system = getTWLSystem()
    
    // Test invalid product ID
    console.log('Testing invalid product ID...')
    const invalidProduct = await system.getProduct('invalid-product-id')
    console.log('Invalid product result:', invalidProduct) // Should be null
    
    // Test invalid search parameters
    console.log('Testing invalid search parameters...')
    const invalidSearch = await system.searchProducts('', {
      priceMin: -100,
      priceMax: -50
    })
    console.log('Invalid search handled gracefully:', !!invalidSearch)
    
    // Test system resilience
    console.log('Testing system resilience...')
    const health = system.getHealth()
    console.log('System remains healthy:', health.status === 'healthy')
    
  } catch (error) {
    console.error('❌ Error handling example failed:', error)
  }
}

/**
 * Example: Cache Performance
 */
export async function cachePerformanceExample() {
  console.log('⚡ TWL Enterprise System - Cache Performance Example')
  
  try {
    const system = getTWLSystem()
    
    const productId = 'sneakers-nike-mixte-air-force-bd7700-222'
    
    // First request (cache miss)
    console.log('First request (cache miss)...')
    const start1 = Date.now()
    const product1 = await system.getProduct(productId)
    const time1 = Date.now() - start1
    console.log('First request time:', time1 + 'ms')
    
    // Second request (cache hit)
    console.log('Second request (cache hit)...')
    const start2 = Date.now()
    const product2 = await system.getProduct(productId)
    const time2 = Date.now() - start2
    console.log('Second request time:', time2 + 'ms')
    
    console.log('Cache performance improvement:', Math.round((time1 - time2) / time1 * 100) + '%')
    
    // Check cache metrics
    const metrics = system.getMetrics()
    console.log('Cache Hit Rate:', metrics.cache.hitRate + '%')
    console.log('Cache Memory Usage:', metrics.cache.memoryUsage + 'MB')
    
  } catch (error) {
    console.error('❌ Cache performance example failed:', error)
  }
}

/**
 * Example: Complete Application Lifecycle
 */
export async function applicationLifecycleExample() {
  console.log('🔄 TWL Enterprise System - Application Lifecycle Example')
  
  try {
    // 1. Application Startup
    console.log('1. Application startup...')
    const system = await initializeTWLSystem({
      environment: 'production',
      enableCache: true,
      enableAutoScan: true,
      enableHealthChecks: true
    })
    
    // 2. Application Running
    console.log('2. Application running...')
    const health = system.getHealth()
    console.log('System ready:', system.isReady())
    console.log('System status:', health.status)
    
    // 3. Handle some requests
    console.log('3. Handling requests...')
    await system.searchProducts('Nike', {}, 1, 5)
    await system.getProduct('sneakers-nike-mixte-air-force-bd7700-222')
    
    // 4. Monitor performance
    console.log('4. Performance monitoring...')
    const metrics = system.getMetrics()
    console.log('Requests handled:', metrics.performance.totalRequests)
    
    // 5. Graceful shutdown
    console.log('5. Graceful shutdown...')
    await shutdownTWLSystem()
    console.log('System shutdown complete')
    
  } catch (error) {
    console.error('❌ Application lifecycle example failed:', error)
  }
}

/**
 * Run all examples
 */
export async function runAllExamples() {
  console.log('🎯 Running All TWL Enterprise System Examples')
  console.log('=' .repeat(60))
  
  await basicIntegrationExample()
  console.log('\n' + '=' .repeat(60))
  
  await advancedSearchExample()
  console.log('\n' + '=' .repeat(60))
  
  await performanceMonitoringExample()
  console.log('\n' + '=' .repeat(60))
  
  await errorHandlingExample()
  console.log('\n' + '=' .repeat(60))
  
  await cachePerformanceExample()
  console.log('\n' + '=' .repeat(60))
  
  // Note: Don't run applicationLifecycleExample here as it shuts down the system
  
  console.log('✅ All examples completed!')
}

// Export for use in other files
export default {
  basicIntegrationExample,
  advancedSearchExample,
  performanceMonitoringExample,
  errorHandlingExample,
  cachePerformanceExample,
  applicationLifecycleExample,
  runAllExamples
}
