'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { useTheme } from '@/components/theme/ThemeProvider'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
import { TransitionLink, useRouteTransition } from '@/components/transitions/RouteTransitionProvider'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import ShoppingCart from '@/components/features/ShoppingCart'
import AuthModal from '@/components/features/AuthModal'
import UserDashboard from '@/components/features/UserDashboard'
import { cn } from '@/lib/utils'

// Icons (you can replace with your preferred icon library)
const MenuIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
  </svg>
)

const CloseIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
)

const SearchIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
)

const HeartIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
  </svg>
)

const ShoppingBagIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
  </svg>
)

const UserIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
)

const SunIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
)

const MoonIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
  </svg>
)

export default function Header() {
  const router = useRouter()
  const { theme, toggleTheme } = useTheme()
  const { user, isAuthenticated } = useAuth()
  const { getItemsCount } = useCart()
  const { getTotalItemsCount } = useWishlist()
  const { isTransitioning } = useRouteTransition()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)
  const [isDashboardOpen, setIsDashboardOpen] = useState(false)



  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleNavigation = (href) => {
    // Use window.location.href since it works reliably with Next.js App Router
    window.location.href = href
  }

  const navigation = [
    { name: 'Tienda', href: '/shop' },
    { name: 'IA Features', href: '/ai-features' },
    { name: 'Social', href: '/social' },
    { name: 'Comunidad', href: '/community' },
    { name: 'Marcas', href: '/brands' },
    { name: 'Ediciones Limitadas', href: '/limited-editions' },
    { name: 'Revista', href: '/magazine' },
  ]

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-100 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <button
              onClick={() => handleNavigation('/')}
              className="flex items-center space-x-2 bg-transparent border-none cursor-pointer hover:opacity-80 transition-opacity"
            >
              <div className="w-8 h-8 bg-black rounded-full flex items-center justify-center">
                <span className="text-white font-bold text-lg">W</span>
              </div>
              <span className="text-xl font-playfair font-bold text-black hidden sm:block">
                The White Laces
              </span>
            </button>
          </div>

          {/* Center Search Bar */}
          <div className="hidden lg:flex flex-1 max-w-lg mx-8">
            <div className="relative w-full">
              <input
                type="text"
                placeholder="Buscar productos..."
                className="w-full px-4 py-2 pl-10 pr-4 text-gray-700 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
              />
              <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                <SearchIcon />
              </div>
            </div>
          </div>

          {/* Desktop Navigation - Moved to dropdown or simplified */}
          <nav className="hidden xl:flex items-center space-x-6">
            <button
              onClick={() => handleNavigation('/shop')}
              className="text-gray-700 hover:text-black transition-colors duration-300 font-medium cursor-pointer"
            >
              Tienda
            </button>
            <button
              onClick={() => handleNavigation('/brands')}
              className="text-gray-700 hover:text-black transition-colors duration-300 font-medium cursor-pointer"
            >
              Marcas
            </button>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-3">
            {/* Search - Mobile only */}
            <Button variant="ghost" size="icon" className="lg:hidden text-gray-700 hover:text-black">
              <SearchIcon />
            </Button>

            {/* Theme Toggle - Keep for functionality */}
            <Button variant="ghost" size="icon" onClick={toggleTheme} className="text-gray-700 hover:text-black">
              {theme === 'dark' ? <SunIcon /> : <MoonIcon />}
            </Button>

            {/* Wishlist */}
            <Button
              variant="ghost"
              size="icon"
              className="relative hidden sm:flex text-gray-700 hover:text-black"
              onClick={() => isAuthenticated ? setIsDashboardOpen(true) : setIsAuthModalOpen(true)}
            >
              <HeartIcon />
              {isAuthenticated && getTotalItemsCount() > 0 && (
                <Badge
                  variant="primary"
                  size="sm"
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-black text-white"
                >
                  {getTotalItemsCount()}
                </Badge>
              )}
            </Button>

            {/* Cart */}
            <Button
              variant="ghost"
              size="icon"
              className="relative text-gray-700 hover:text-black"
              onClick={() => setIsCartOpen(true)}
            >
              <ShoppingBagIcon />
              {getItemsCount() > 0 && (
                <Badge
                  variant="primary"
                  size="sm"
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-black text-white"
                >
                  {getItemsCount()}
                </Badge>
              )}
            </Button>

            {/* Account */}
            <Button
              variant="ghost"
              size="icon"
              className="hidden sm:flex text-gray-700 hover:text-black"
              onClick={() => isAuthenticated ? setIsDashboardOpen(true) : setIsAuthModalOpen(true)}
            >
              {isAuthenticated ? (
                <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center text-xs font-bold text-white">
                  {user?.firstName?.[0]}{user?.lastName?.[0]}
                </div>
              ) : (
                <UserIcon />
              )}
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <CloseIcon /> : <MenuIcon />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden glass backdrop-blur-xl border-t border-white/10">
          <div className="px-4 py-6 space-y-4">
            {navigation.map((item) => (
              <button
                key={item.name}
                onClick={() => {
                  setIsMenuOpen(false)
                  handleNavigation(item.href)
                }}
                className="block text-text-secondary dark:text-text-tertiary hover:text-primary dark:hover:text-primary transition-colors duration-300 font-medium py-2 hover:underline cursor-pointer px-2 rounded-md hover:bg-primary/10 bg-transparent border-none w-full text-left"
              >
                {item.name}
              </button>
            ))}
            
            <div className="pt-4 border-t border-white/10">
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="icon">
                  <SearchIcon />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="relative"
                  onClick={() => isAuthenticated ? setIsDashboardOpen(true) : setIsAuthModalOpen(true)}
                >
                  <HeartIcon />
                  {isAuthenticated && getTotalItemsCount() > 0 && (
                    <Badge
                      variant="primary"
                      size="sm"
                      className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs"
                    >
                      {getTotalItemsCount()}
                    </Badge>
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => isAuthenticated ? setIsDashboardOpen(true) : setIsAuthModalOpen(true)}
                >
                  {isAuthenticated ? (
                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs font-bold text-jet-black">
                      {user?.firstName?.[0]}{user?.lastName?.[0]}
                    </div>
                  ) : (
                    <UserIcon />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Shopping Cart Modal */}
      <ShoppingCart isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />

      {/* User Dashboard */}
      <UserDashboard
        isOpen={isDashboardOpen}
        onClose={() => setIsDashboardOpen(false)}
      />
    </header>
  )
}
