'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { useTheme } from '@/components/theme/ThemeProvider'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
import { TransitionLink, useRouteTransition } from '@/components/transitions/RouteTransitionProvider'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import ShoppingCart from '@/components/features/ShoppingCart'
import AuthModal from '@/components/ui/AuthModal'
import UserDashboard from '@/components/features/UserDashboard'
import VoiceSearch from '@/components/features/VoiceSearch'
import { cn } from '@/lib/utils'

// Icons (you can replace with your preferred icon library)
const MenuIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
  </svg>
)

const CloseIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
)

const SearchIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
)

const HeartIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
  </svg>
)

const ShoppingBagIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
  </svg>
)

const UserIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
)

const SunIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
)

const MoonIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
  </svg>
)

export default function Header() {
  const router = useRouter()
  const { theme, toggleTheme } = useTheme()
  const { user, isAuthenticated } = useAuth()
  const { getItemsCount } = useCart()
  const { getTotalItemsCount } = useWishlist()
  const { isTransitioning } = useRouteTransition()
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)
  const [isCartOpen, setIsCartOpen] = useState(false)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)
  const [isDashboardOpen, setIsDashboardOpen] = useState(false)



  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const handleNavigation = (href) => {
    console.log('Navigation clicked:', href) // Debug log
    try {
      // Use Next.js router for better navigation
      router.push(href)
    } catch (error) {
      console.error('Navigation error:', error)
      // Fallback to window.location
      window.location.href = href
    }
  }

  const navigation = [
    { name: 'Tienda', href: '/tienda' },
    { name: 'IA Features', href: '/ai-features' },
    { name: 'Social', href: '/social' },
    { name: 'Comunidad', href: '/community' },
    { name: 'Marcas', href: '/brands' },
    { name: 'Ediciones Limitadas', href: '/limited-editions' },
    { name: 'Revista', href: '/magazine' },
  ]

  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-100 shadow-sm">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          {/* Logo */}
          <div className="flex-shrink-0">
            <Link
              href="/"
              className="flex items-center space-x-3 cursor-pointer hover:opacity-80 transition-opacity group"
            >
              <div className="w-8 h-8 flex items-center justify-center group-hover:scale-110 transition-transform duration-200">
                <Image
                  src="/twl.svg"
                  alt="TWL Logo"
                  width={32}
                  height={32}
                  className="w-full h-full filter group-hover:brightness-110 transition-all duration-200"
                />
              </div>

              {/* Desktop: Full logo text */}
              <div className="hidden md:block group-hover:scale-105 transition-transform duration-200">
                <Image
                  src="/logotwl.svg"
                  alt="The White Laces"
                  width={150}
                  height={40}
                  className="h-7 w-auto filter group-hover:brightness-110 transition-all duration-200"
                  priority
                />
              </div>

              {/* Mobile: TWL text only */}
              <span className="text-xl font-bold text-black md:hidden group-hover:scale-105 transition-transform duration-200 tracking-wider">
                TWL
              </span>
            </Link>
          </div>

          {/* Center Search Bar */}
          <div className="hidden lg:flex flex-1 max-w-lg mx-8">
            <div className="relative w-full flex gap-2">
              <div className="relative flex-1">
                <input
                  type="text"
                  placeholder="Buscar productos..."
                  className="w-full px-4 py-2 pl-10 pr-4 text-gray-700 bg-gray-50 border border-gray-200 rounded-full focus:outline-none focus:ring-2 focus:ring-black focus:border-transparent"
                  onKeyPress={(e) => {
                    if (e.key === 'Enter') {
                      router.push(`/search?q=${encodeURIComponent(e.target.value)}`)
                    }
                  }}
                />
                <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                  <SearchIcon />
                </div>
              </div>
              <VoiceSearch className="flex-shrink-0" />
            </div>
          </div>

          {/* Desktop Navigation - Moved to dropdown or simplified */}
          <nav className="hidden xl:flex items-center space-x-6">
            <Link
              href="/tienda"
              className="text-gray-700 hover:text-black transition-colors duration-300 font-medium cursor-pointer"
            >
              Tienda
            </Link>
            <Link
              href="/brands"
              className="text-gray-700 hover:text-black transition-colors duration-300 font-medium cursor-pointer"
            >
              Marcas
            </Link>
          </nav>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-3">
            {/* Search - Mobile only */}
            <Button variant="ghost" size="icon" className="lg:hidden text-gray-700 hover:text-black">
              <SearchIcon />
            </Button>

            {/* Theme Toggle - Keep for functionality */}
            <Button variant="ghost" size="icon" onClick={toggleTheme} className="text-gray-700 hover:text-black">
              {theme === 'dark' ? <SunIcon /> : <MoonIcon />}
            </Button>

            {/* Wishlist */}
            <Button
              variant="ghost"
              size="icon"
              className="relative hidden sm:flex text-gray-700 hover:text-black"
              onClick={() => isAuthenticated ? setIsDashboardOpen(true) : setIsAuthModalOpen(true)}
            >
              <HeartIcon />
              {isAuthenticated && getTotalItemsCount() > 0 && (
                <Badge
                  variant="primary"
                  size="sm"
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-black text-white"
                >
                  {getTotalItemsCount()}
                </Badge>
              )}
            </Button>

            {/* Cart */}
            <Button
              variant="ghost"
              size="icon"
              className="relative text-gray-700 hover:text-black"
              onClick={() => setIsCartOpen(true)}
            >
              <ShoppingBagIcon />
              {getItemsCount() > 0 && (
                <Badge
                  variant="primary"
                  size="sm"
                  className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs bg-primary text-pure-black"
                >
                  {getItemsCount()}
                </Badge>
              )}
            </Button>

            {/* Account */}
            <Button
              variant="ghost"
              size="icon"
              className="hidden sm:flex text-gray-700 hover:text-black"
              onClick={() => {
                console.log('User icon clicked, isAuthenticated:', isAuthenticated)
                if (isAuthenticated) {
                  setIsDashboardOpen(true)
                } else {
                  setIsAuthModalOpen(true)
                }
              }}
            >
              {isAuthenticated ? (
                <div className="w-6 h-6 bg-black rounded-full flex items-center justify-center text-xs font-bold text-white">
                  {user?.firstName?.[0]}{user?.lastName?.[0]}
                </div>
              ) : (
                <UserIcon />
              )}
            </Button>

            {/* Mobile Menu Button */}
            <Button
              variant="ghost"
              size="icon"
              className="lg:hidden"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <CloseIcon /> : <MenuIcon />}
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="lg:hidden glass backdrop-blur-xl border-t border-white/10">
          <div className="px-4 py-6 space-y-4">
            {navigation.map((item) => (
              <Link
                key={item.name}
                href={item.href}
                onClick={() => setIsMenuOpen(false)}
                className="block text-text-secondary dark:text-text-tertiary hover:text-primary dark:hover:text-primary transition-colors duration-300 font-medium py-2 hover:underline cursor-pointer px-2 rounded-md hover:bg-primary/10 w-full text-left"
              >
                {item.name}
              </Link>
            ))}
            
            <div className="pt-4 border-t border-white/10">
              <div className="flex items-center space-x-4">
                <Button variant="ghost" size="icon">
                  <SearchIcon />
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  className="relative"
                  onClick={() => isAuthenticated ? setIsDashboardOpen(true) : setIsAuthModalOpen(true)}
                >
                  <HeartIcon />
                  {isAuthenticated && getTotalItemsCount() > 0 && (
                    <Badge
                      variant="primary"
                      size="sm"
                      className="absolute -top-1 -right-1 h-5 w-5 p-0 text-xs"
                    >
                      {getTotalItemsCount()}
                    </Badge>
                  )}
                </Button>
                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => {
                    console.log('Mobile user icon clicked, isAuthenticated:', isAuthenticated)
                    if (isAuthenticated) {
                      setIsDashboardOpen(true)
                    } else {
                      setIsAuthModalOpen(true)
                    }
                  }}
                >
                  {isAuthenticated ? (
                    <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-xs font-bold text-pure-black">
                      {user?.firstName?.[0]}{user?.lastName?.[0]}
                    </div>
                  ) : (
                    <UserIcon />
                  )}
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Shopping Cart Modal */}
      <ShoppingCart isOpen={isCartOpen} onClose={() => setIsCartOpen(false)} />

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />

      {/* User Dashboard */}
      <UserDashboard
        isOpen={isDashboardOpen}
        onClose={() => setIsDashboardOpen(false)}
      />
    </header>
  )
}
