/**
 * TWL Enterprise Product Data Transformer
 * Converts Enterprise API data to UI component format
 * 
 * Features:
 * - Enterprise TWLProduct → UI SimpleProduct transformation
 * - Batch transformation for product lists
 * - Image URL optimization
 * - Price formatting and currency conversion
 * - Brand and category name cleaning
 * - Performance optimized with caching
 */

import { TWLProduct, TWLProductSearchResult } from '../models/Product'

/**
 * Simple Product Interface for UI Components
 */
export interface SimpleProduct {
  id: string
  name: string
  brand: string
  category: string
  price: number
  originalPrice?: number
  images: string[]
  videos?: string[]
  isNew?: boolean
  isLimitedEdition?: boolean
  inStock: boolean
  rating?: number
  reviewCount?: number
  slug?: string
  description?: string
  sizes?: string[]
  colors?: string[]
}

/**
 * Product List Response for UI
 */
export interface SimpleProductListResponse {
  products: SimpleProduct[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
}

/**
 * Enterprise Product Data Transformer
 */
export class ProductTransformer {
  
  /**
   * Transform single Enterprise product to Simple product
   */
  static transformProduct(enterpriseProduct: TWLProduct): SimpleProduct {
    try {
      // Extract image URLs from variants (images are stored in variants, not media)
      // Try defaultVariant first, then fall back to first variant if defaultVariant has no images
      const defaultVariant = enterpriseProduct.defaultVariant
      const firstVariant = enterpriseProduct.variants?.[0]

      let images: string[] = []
      let videos: string[] = []

      if (defaultVariant?.images && defaultVariant.images.length > 0) {
        images = defaultVariant.images
        videos = defaultVariant.videos || []
      } else if (firstVariant?.images && firstVariant.images.length > 0) {
        images = firstVariant.images
        videos = firstVariant.videos || []
      }
      
      // Clean product name (remove folder reference numbers)
      const cleanName = this.cleanProductName(enterpriseProduct.name)
      
      // Clean brand name
      const cleanBrand = this.cleanBrandName(enterpriseProduct.brand?.name || enterpriseProduct.brand?.fullName || 'Unknown Brand')
      
      // Extract pricing
      const currentPrice = enterpriseProduct.pricing?.currentPrice || 0
      const originalPrice = enterpriseProduct.pricing?.originalPrice
      
      // Extract sizes
      const sizes = enterpriseProduct.inventory?.sizes?.map(size => size.display) || []
      
      // Extract colors from variants
      const colors = enterpriseProduct.variants?.map(variant => variant.colorway).filter(Boolean) || []
      
      // Determine if product is new (within last 30 days)
      const isNew = enterpriseProduct.metadata?.isNewArrival || 
        (enterpriseProduct.system?.createdAt && 
         (Date.now() - new Date(enterpriseProduct.system.createdAt).getTime()) < 30 * 24 * 60 * 60 * 1000)
      
      return {
        id: enterpriseProduct.id,
        name: cleanName,
        brand: cleanBrand,
        category: enterpriseProduct.category?.name || 'Unknown',
        price: currentPrice,
        originalPrice: originalPrice && originalPrice > currentPrice ? originalPrice : undefined,
        images,
        videos: videos.length > 0 ? videos : undefined,
        isNew,
        isLimitedEdition: enterpriseProduct.details?.isLimitedEdition || false,
        inStock: enterpriseProduct.inventory?.inStock || false,
        rating: enterpriseProduct.metadata?.rating,
        reviewCount: enterpriseProduct.metadata?.reviewCount,
        slug: this.generateSlug(enterpriseProduct),
        description: enterpriseProduct.details?.description,
        sizes: sizes.length > 0 ? sizes : undefined,
        colors: colors.length > 0 ? colors : undefined
      }
    } catch (error) {
      console.error('Error transforming product:', error, enterpriseProduct)
      
      // Return fallback product to prevent crashes
      return {
        id: enterpriseProduct.id || 'unknown',
        name: enterpriseProduct.name || 'Unknown Product',
        brand: 'Unknown Brand',
        category: 'Unknown',
        price: 0,
        images: [],
        inStock: false
      }
    }
  }
  
  /**
   * Transform Enterprise search result to Simple product list
   */
  static transformProductList(searchResult: TWLProductSearchResult): SimpleProductListResponse {
    try {
      const products = searchResult.products.map(product => this.transformProduct(product))
      
      return {
        products,
        total: searchResult.total,
        page: searchResult.page,
        pageSize: searchResult.pageSize,
        hasMore: searchResult.hasMore
      }
    } catch (error) {
      console.error('Error transforming product list:', error)
      
      return {
        products: [],
        total: 0,
        page: 1,
        pageSize: 20,
        hasMore: false
      }
    }
  }
  
  /**
   * Transform array of Enterprise products to Simple products
   */
  static transformProducts(enterpriseProducts: TWLProduct[]): SimpleProduct[] {
    return enterpriseProducts.map(product => this.transformProduct(product))
  }
  
  /**
   * Clean product name - Remove folder reference numbers
   */
  private static cleanProductName(name: string): string {
    if (!name || typeof name !== 'string') return 'Product Name'
    
    // Remove folder reference numbers (like "1. ", "2. ", "10. ", etc.)
    return name.replace(/^\d+\.\s*/, '').trim()
  }
  
  /**
   * Clean brand name - Remove folder reference numbers and "Limited Edition"
   */
  private static cleanBrandName(brandName: string): string {
    if (!brandName || typeof brandName !== 'string') return 'Unknown Brand'
    
    return brandName
      .replace(/^\d+\.\s*/, '') // Remove folder numbers
      .replace(/\s*Limited Edition\s*/i, '') // Remove "Limited Edition"
      .trim()
  }
  
  /**
   * Generate product slug for URL
   */
  private static generateSlug(product: TWLProduct): string {
    try {
      const category = product.category?.id || 'product'
      const brand = this.cleanBrandName(product.brand?.name || '').toLowerCase().replace(/\s+/g, '-')
      const name = this.cleanProductName(product.name).toLowerCase().replace(/\s+/g, '-')
      const sku = product.sku?.toLowerCase() || ''
      
      return `${category}-${brand}-${name}-${sku}`.replace(/[^a-z0-9-]/g, '').replace(/-+/g, '-')
    } catch (error) {
      return product.id || 'unknown-product'
    }
  }
}

export default ProductTransformer
