/**
 * TWL Enterprise Product Data Models
 * Type-safe interfaces for the entire product system
 */

// ============================================================================
// CORE PRODUCT INTERFACES
// ============================================================================

/**
 * Main Product Interface - Enterprise Grade
 */
export interface TWLProduct {
  // Core Identifiers
  id: string                    // Unique product ID (e.g., "sneakers-nike-mixte-air-force-bd7700-222")
  sku: string                   // SKU from folder name (e.g., "BD7700-222")
  internalReference: string     // Internal reference code
  
  // Basic Information
  name: string                  // Clean product name
  brand: TWLBrand              // Brand information
  category: TWLCategory        // Product category
  subcategory?: string         // Subcategory if applicable
  
  // Product Hierarchy (from folder structure)
  hierarchy: TWLProductHierarchy
  
  // Media Assets
  media: TWLProductMedia
  
  // Variants & Models
  variants: TWLProductVariant[]
  defaultVariant: string       // ID of default variant
  
  // Pricing
  pricing: TWLPricing
  
  // Inventory
  inventory: TWLInventory
  
  // Product Details
  details: TWLProductDetails
  
  // Metadata
  metadata: TWLProductMetadata
  
  // System Fields
  system: TWLSystemFields
}

/**
 * Product Hierarchy - Maps to folder structure
 */
export interface TWLProductHierarchy {
  category: string             // "1. SNEAKERS"
  brand: string               // "1. NIKE Limited Edition"
  modelFamily: string         // "1. AIR FORCE"
  gender: string              // "1. MIXTE"
  collaboration?: string      // "1. GUCCI"
  productFolder: string       // "BD7700-222 -- Gucci"
  fullPath: string            // Complete path from products root
}

/**
 * Product Media - Images and Videos
 */
export interface TWLProductMedia {
  images: TWLProductImage[]
  videos: TWLProductVideo[]
  thumbnails: TWLThumbnail[]
  primaryImage: string        // URL of primary image
  primaryVideo?: string       // URL of primary video
}

/**
 * Product Image
 */
export interface TWLProductImage {
  id: string
  url: string                 // Full URL path
  filename: string            // Original filename
  alt: string                 // Alt text for accessibility
  width?: number
  height?: number
  size?: number               // File size in bytes
  format: 'webp' | 'jpg' | 'png'
  variantId?: string          // Associated variant if applicable
  sortOrder: number           // Display order
  isHero: boolean            // Is this a hero image?
  metadata?: {
    colorway?: string
    angle?: 'front' | 'back' | 'side' | 'top' | 'bottom' | 'detail'
    tags?: string[]
  }
}

/**
 * Product Video
 */
export interface TWLProductVideo {
  id: string
  url: string                 // Full URL path
  filename: string            // Original filename
  thumbnail: string           // Thumbnail image URL
  duration?: number           // Duration in seconds
  size?: number               // File size in bytes
  format: 'mp4' | 'webm'
  variantId?: string          // Associated variant if applicable
  sortOrder: number           // Display order
  metadata?: {
    type?: 'product' | 'lifestyle' | 'detail' | 'unboxing'
    tags?: string[]
  }
}

/**
 * Thumbnail
 */
export interface TWLThumbnail {
  size: 'small' | 'medium' | 'large'
  url: string
  width: number
  height: number
}

/**
 * Product Variant - Different colorways/models
 */
export interface TWLProductVariant {
  id: string                  // Unique variant ID
  name: string                // Variant name (e.g., "Pink Colorway")
  sku: string                 // Variant SKU
  colorway?: string           // Color description
  images: string[]            // Image URLs for this variant
  videos: string[]            // Video URLs for this variant
  pricing: TWLPricing        // Variant-specific pricing
  inventory: TWLInventory    // Variant-specific inventory
  isDefault: boolean         // Is this the default variant?
  sortOrder: number          // Display order
  metadata?: {
    colorHex?: string        // Primary color hex code
    materials?: string[]     // Specific materials
    features?: string[]      // Variant-specific features
  }
}

/**
 * Brand Information
 */
export interface TWLBrand {
  id: string                  // Brand ID (e.g., "nike")
  name: string                // Display name (e.g., "Nike")
  fullName: string            // Full name (e.g., "Nike Limited Edition")
  logo?: string               // Brand logo URL
  description?: string        // Brand description
  website?: string            // Brand website
  isLuxury: boolean          // Is this a luxury brand?
  tier: 'premium' | 'luxury' | 'ultra-luxury'
}

/**
 * Category Information
 */
export interface TWLCategory {
  id: string                  // Category ID (e.g., "sneakers")
  name: string                // Display name (e.g., "Sneakers")
  fullName: string            // Full name (e.g., "1. SNEAKERS")
  description?: string        // Category description
  parentCategory?: string     // Parent category if applicable
  sortOrder: number          // Display order
  isActive: boolean          // Is category active?
}

/**
 * Pricing Information
 */
export interface TWLPricing {
  // Customer Prices (MXN)
  currentPrice: number        // Current selling price
  originalPrice: number       // Original/MSRP price
  discountPercent: number     // Discount percentage
  
  // Supplier Information (Internal Only)
  supplierCost: {
    rmb: number               // Cost in Chinese Yuan
    usd: number               // Cost in USD
    transport: number         // Transport cost
    total: number             // Total cost
  }
  
  // Pricing Strategy
  margin: number              // Profit margin percentage
  tier: 'suggested' | 'premium' | 'luxury'  // Pricing tier
  
  // Currency & Localization
  currency: 'MXN' | 'USD'     // Display currency
  taxIncluded: boolean        // Is tax included?
  
  // Special Pricing
  isOnSale: boolean          // Is currently on sale?
  saleEndDate?: Date         // Sale end date
  memberPrice?: number       // VIP member price
}

/**
 * Inventory Information
 */
export interface TWLInventory {
  inStock: boolean           // Is in stock?
  stockCount: number         // Available quantity
  lowStockThreshold: number  // Low stock warning threshold
  sizes: TWLSize[]          // Available sizes
  reservedStock: number     // Reserved for orders
  lastRestocked?: Date      // Last restock date
  nextRestock?: Date        // Expected restock date
  isPreorder: boolean       // Is this a preorder item?
  maxQuantityPerOrder: number // Max quantity per order
}

/**
 * Size Information
 */
export interface TWLSize {
  id: string                 // Size ID (e.g., "42")
  display: string            // Display name (e.g., "42 EU")
  system: 'EU' | 'US' | 'UK' | 'CM'  // Size system
  inStock: boolean          // Is this size in stock?
  stockCount: number        // Stock for this size
  isPopular: boolean        // Is this a popular size?
}

/**
 * Product Details
 */
export interface TWLProductDetails {
  description: string        // Main product description
  fullDescription: string   // Detailed description
  features: string[]        // Key features
  materials: string[]       // Materials used
  careInstructions: string[] // Care instructions
  specifications: Record<string, string> // Technical specs
  tags: string[]            // Search tags
  keywords: string[]        // SEO keywords
  
  // Product Classification
  type: 'sneaker' | 'sandal' | 'formal' | 'casual' | 'boot'
  subType: string           // More specific type
  gender: 'men' | 'women' | 'unisex' | 'kids'
  ageGroup: 'adult' | 'youth' | 'child' | 'infant'
  
  // Special Attributes
  isLimitedEdition: boolean
  isCollaboration: boolean
  collaborationPartner?: string
  releaseDate?: Date
  seasonality?: 'spring' | 'summer' | 'fall' | 'winter' | 'all-season'
  
  // Quality & Authenticity
  authenticityGuarantee: boolean
  qualityGrade: 'A+' | 'A' | 'B+' | 'B'
  condition: 'new' | 'like-new' | 'excellent' | 'good'
}

/**
 * Product Metadata
 */
export interface TWLProductMetadata {
  // SEO
  seoTitle?: string
  seoDescription?: string
  seoKeywords?: string[]
  
  // Analytics
  viewCount: number
  purchaseCount: number
  wishlistCount: number
  shareCount: number
  
  // Reviews & Ratings
  rating: number            // Average rating (0-5)
  reviewCount: number       // Number of reviews
  ratingDistribution: {     // Rating breakdown
    5: number
    4: number
    3: number
    2: number
    1: number
  }
  
  // Social Proof
  isPopular: boolean
  isTrending: boolean
  isBestseller: boolean
  isNewArrival: boolean
  
  // Content
  lifestyle: {
    occasions: string[]      // When to wear
    style: string[]         // Style categories
    personality: string[]   // Personality types
  }
}

/**
 * System Fields
 */
export interface TWLSystemFields {
  createdAt: Date
  updatedAt: Date
  version: number           // Data version for cache invalidation
  source: 'filesystem' | 'api' | 'manual'
  lastScanned: Date        // Last time filesystem was scanned
  checksum: string         // Data integrity checksum
  
  // Cache Information
  cacheKey: string         // Cache key
  cacheTTL: number         // Cache TTL in seconds
  cacheHits: number        // Number of cache hits
  
  // Performance
  loadTime: number         // Load time in milliseconds
  fileSize: number         // Total file size in bytes
  imageCount: number       // Number of images
  videoCount: number       // Number of videos
  
  // Status
  status: 'active' | 'inactive' | 'draft' | 'archived'
  visibility: 'public' | 'private' | 'hidden'
  isValid: boolean         // Data validation status
  validationErrors?: string[] // Validation errors if any
}

// ============================================================================
// UTILITY TYPES
// ============================================================================

/**
 * Product Search Result
 */
export interface TWLProductSearchResult {
  products: TWLProduct[]
  total: number
  page: number
  pageSize: number
  hasMore: boolean
  facets: TWLSearchFacets
  searchTime: number       // Search time in milliseconds
}

/**
 * Search Facets
 */
export interface TWLSearchFacets {
  brands: Array<{ name: string; count: number }>
  categories: Array<{ name: string; count: number }>
  priceRanges: Array<{ range: string; count: number }>
  sizes: Array<{ size: string; count: number }>
  colors: Array<{ color: string; count: number }>
  inStock: { available: number; outOfStock: number }
}

/**
 * Product Filter Options
 */
export interface TWLProductFilters {
  brands?: string[]
  categories?: string[]
  priceMin?: number
  priceMax?: number
  sizes?: string[]
  colors?: string[]
  inStockOnly?: boolean
  isLimitedEdition?: boolean
  rating?: number
  sortBy?: 'name' | 'price' | 'rating' | 'popularity' | 'newest'
  sortOrder?: 'asc' | 'desc'
}

/**
 * Bulk Operation Result
 */
export interface TWLBulkOperationResult {
  success: boolean
  processed: number
  failed: number
  errors: Array<{ id: string; error: string }>
  duration: number         // Operation duration in milliseconds
}

export default TWLProduct
