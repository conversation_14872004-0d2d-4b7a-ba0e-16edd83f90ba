'use client'

import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'

export default function NewArrivals() {

  // Placeholder component - will be expanded later
  return (
    <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
      {[1, 2, 3, 4, 5, 6].map((item) => (
        <Card key={item} variant="default" className="group">
          <CardContent className="p-3">
            <div className="relative">
              <div className="aspect-square bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 rounded-lg mb-3 flex items-center justify-center">
                <span className="text-gray-500 text-xs">New {item}</span>
              </div>
              <Badge variant="success" size="sm" className="absolute top-2 left-2">
                Nuevo
              </Badge>
            </div>
            <h4 className="text-sm font-medium mb-1 line-clamp-2">
              New Arrival {item}
            </h4>
            <p className="text-xs text-gray-500 mb-2">Brand Name</p>
            <p className="text-sm font-bold">$2,500 MXN</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
