/**
 * TWL Cart Enterprise Wrapper
 * Simple wrapper to enhance existing cart with enterprise features
 * Drop-in replacement for existing product loading functions
 */

import productAdapter from './ProductAdapter'
import cartMigration from './CartMigration'

class CartEnterpriseWrapper {
  constructor() {
    this.isInitialized = false
    this.fallbackEnabled = true
    this.enhancedFeaturesEnabled = true
    this.metrics = {
      enhancedLoads: 0,
      fallbackLoads: 0,
      validations: 0,
      recommendations: 0
    }
  }

  /**
   * Initialize the enterprise wrapper
   */
  async initialize() {
    try {
      console.log('🚀 Initializing Cart Enterprise Wrapper...')
      
      // Initialize product adapter
      await productAdapter.initialize()
      
      // Check for cart migration
      if (cartMigration.needsMigration()) {
        console.log('🔄 Migrating legacy cart to enterprise format...')
        const migrationResult = await cartMigration.migrateCart()
        
        if (migrationResult.success) {
          console.log(`✅ Cart migration completed: ${migrationResult.migratedItems} items migrated`)
        } else {
          console.warn('⚠️ Cart migration failed, continuing with fallback mode')
        }
      }

      this.isInitialized = true
      console.log('✅ Cart Enterprise Wrapper initialized successfully')
      
      return true
    } catch (error) {
      console.error('❌ Failed to initialize Cart Enterprise Wrapper:', error)
      this.isInitialized = false
      return false
    }
  }

  /**
   * Enhanced product finder - drop-in replacement for existing findProduct
   * @param {string} productId - Product ID to find
   * @returns {Promise<Object>} Enhanced product data
   */
  async findProduct(productId) {
    try {
      if (this.isInitialized && this.enhancedFeaturesEnabled) {
        const enhancedProduct = await productAdapter.getProductForCart(productId)
        
        if (enhancedProduct && enhancedProduct._source === 'enterprise') {
          this.metrics.enhancedLoads++
          console.log(`🚀 Enhanced product loaded: ${enhancedProduct.name}`)
          return enhancedProduct
        }
      }

      // Fallback to existing product loading
      this.metrics.fallbackLoads++
      console.log(`🔄 Using fallback product loading for: ${productId}`)
      
      // Import existing functions dynamically to avoid circular dependencies
      const { getProductById } = await import('@/lib/data/products')
      const { loadRealProduct } = await import('@/lib/real-products-loader')
      
      // Try loadRealProduct first
      try {
        const product = await loadRealProduct(productId)
        if (product) return product
      } catch (error) {
        console.warn('⚠️ loadRealProduct failed, trying getProductById')
      }
      
      // Final fallback
      return await getProductById(productId)

    } catch (error) {
      console.error(`❌ Error finding product ${productId}:`, error)
      throw error
    }
  }

  /**
   * Enhanced add to cart with validation
   * @param {string} productId - Product ID
   * @param {string} size - Product size
   * @param {number} quantity - Quantity to add
   * @returns {Promise<Object>} Add result with enhanced data
   */
  async addToCart(productId, size, quantity = 1) {
    try {
      const product = await this.findProduct(productId)
      
      if (!product) {
        return { success: false, error: 'Product not found' }
      }

      // Enhanced stock validation
      if (this.enhancedFeaturesEnabled && product._source === 'enterprise') {
        if (!product.inStock) {
          return { success: false, error: 'Product is out of stock' }
        }

        if (product.stockLevel && product.stockLevel < quantity) {
          return { 
            success: false, 
            error: `Only ${product.stockLevel} items available`,
            availableStock: product.stockLevel
          }
        }
      }

      return { 
        success: true, 
        product,
        enhanced: product._source === 'enterprise',
        message: `Added ${product.name} to cart`
      }

    } catch (error) {
      console.error('❌ Error adding to cart:', error)
      return { success: false, error: error.message }
    }
  }

  /**
   * Validate cart items for stock and price changes
   * @param {Array} cartItems - Current cart items
   * @returns {Promise<Object>} Validation results
   */
  async validateCartItems(cartItems) {
    if (!this.isInitialized || !this.enhancedFeaturesEnabled) {
      return { validated: false, message: 'Enhanced validation not available' }
    }

    try {
      this.metrics.validations++
      const validationResults = {}
      let hasIssues = false

      for (const item of cartItems) {
        try {
          const validation = await productAdapter.validateCartItem(item)
          validationResults[item.id] = validation

          if (!validation.isValid || validation.hasStockIssue || validation.hasPriceChange) {
            hasIssues = true
          }
        } catch (error) {
          console.error(`❌ Error validating item ${item.id}:`, error)
          validationResults[item.id] = { 
            isValid: false, 
            hasError: true, 
            error: error.message 
          }
          hasIssues = true
        }
      }

      return {
        validated: true,
        hasIssues,
        results: validationResults,
        summary: {
          totalItems: cartItems.length,
          validItems: Object.values(validationResults).filter(v => v.isValid).length,
          stockIssues: Object.values(validationResults).filter(v => v.hasStockIssue).length,
          priceChanges: Object.values(validationResults).filter(v => v.hasPriceChange).length
        }
      }

    } catch (error) {
      console.error('❌ Error validating cart items:', error)
      return { validated: false, error: error.message }
    }
  }

  /**
   * Get product recommendations based on cart
   * @param {Array} cartItems - Current cart items
   * @param {number} limit - Number of recommendations
   * @returns {Promise<Array>} Product recommendations
   */
  async getRecommendations(cartItems, limit = 4) {
    if (!this.isInitialized || !this.enhancedFeaturesEnabled || cartItems.length === 0) {
      return []
    }

    try {
      this.metrics.recommendations++
      const recommendations = await productAdapter.getCartRecommendations(cartItems, limit)
      
      console.log(`🎯 Generated ${recommendations.length} product recommendations`)
      return recommendations

    } catch (error) {
      console.error('❌ Error getting recommendations:', error)
      return []
    }
  }

  /**
   * Get enhanced cart summary with additional metrics
   * @param {Array} cartItems - Current cart items
   * @returns {Object} Enhanced cart summary
   */
  getEnhancedSummary(cartItems) {
    const basicSummary = {
      itemsCount: cartItems.reduce((sum, item) => sum + item.quantity, 0),
      subtotal: cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0)
    }

    if (!this.enhancedFeaturesEnabled) {
      return basicSummary
    }

    // Enhanced calculations
    const originalSubtotal = cartItems.reduce((sum, item) => {
      const originalPrice = item.originalPrice || item.price
      return sum + (originalPrice * item.quantity)
    }, 0)

    const totalSavings = originalSubtotal - basicSummary.subtotal
    const enhancedItems = cartItems.filter(item => item._source === 'enterprise').length
    const limitedEditionItems = cartItems.filter(item => item.isLimitedEdition).length

    return {
      ...basicSummary,
      originalSubtotal,
      totalSavings,
      savingsPercent: originalSubtotal > 0 ? ((totalSavings / originalSubtotal) * 100).toFixed(1) : 0,
      enhancedItems,
      limitedEditionItems,
      hasEnhancedData: enhancedItems > 0
    }
  }

  /**
   * Get wrapper metrics and performance data
   * @returns {Object} Metrics data
   */
  getMetrics() {
    const adapterMetrics = productAdapter.getMetrics()
    
    return {
      wrapper: this.metrics,
      adapter: adapterMetrics,
      isInitialized: this.isInitialized,
      enhancedFeaturesEnabled: this.enhancedFeaturesEnabled,
      migrationStatus: cartMigration.getMigrationStatus()
    }
  }

  /**
   * Enable or disable enhanced features
   * @param {boolean} enabled - Whether to enable enhanced features
   */
  setEnhancedFeatures(enabled) {
    this.enhancedFeaturesEnabled = enabled
    console.log(`${enabled ? '✅' : '❌'} Enhanced cart features ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Reset all metrics
   */
  resetMetrics() {
    this.metrics = {
      enhancedLoads: 0,
      fallbackLoads: 0,
      validations: 0,
      recommendations: 0
    }
    productAdapter.resetMetrics()
  }
}

// Create singleton instance
const cartEnterpriseWrapper = new CartEnterpriseWrapper()

// Auto-initialize
if (typeof window !== 'undefined') {
  // Initialize after a short delay to ensure other systems are ready
  setTimeout(() => {
    cartEnterpriseWrapper.initialize().catch(error => {
      console.warn('⚠️ Cart Enterprise Wrapper initialization failed:', error)
    })
  }, 2000)
}

// Export both the instance and class
export default cartEnterpriseWrapper
export { CartEnterpriseWrapper }

// Export convenience functions for easy integration
export const findProduct = (productId) => cartEnterpriseWrapper.findProduct(productId)
export const addToCart = (productId, size, quantity) => cartEnterpriseWrapper.addToCart(productId, size, quantity)
export const validateCartItems = (cartItems) => cartEnterpriseWrapper.validateCartItems(cartItems)
export const getRecommendations = (cartItems, limit) => cartEnterpriseWrapper.getRecommendations(cartItems, limit)
export const getEnhancedSummary = (cartItems) => cartEnterpriseWrapper.getEnhancedSummary(cartItems)
export const getMetrics = () => cartEnterpriseWrapper.getMetrics()
