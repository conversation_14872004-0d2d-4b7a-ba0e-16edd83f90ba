/**
 * PRODUCT HELPERS
 * Helper functions for product generation and mapping
 */

// Style mapping (Level 1)
function mapStyle(cytteStyle) {
  const styleMap = {
    '1. SNEAKERS': { id: 'sneakers', display: 'Sneakers', type: 'footwear' },
    '2. SANDALS': { id: 'sandals', display: 'Sandalias', type: 'footwear' },
    '3. FORMAL': { id: 'formal', display: 'Formales', type: 'footwear' },
    '4. CASUAL': { id: 'casual', display: 'Casuales', type: 'footwear' },
    '5. KIDS': { id: 'kids', display: 'Niños', type: 'footwear' }
  }
  
  return styleMap[cytteStyle] || { id: 'sneakers', display: 'Sneakers', type: 'footwear' }
}

// Brand mapping (Level 2)
function mapBrand(cytteBrand) {
  const brandMap = {
    '1. NIKE Limited Edition': { id: 'nike', name: 'Nike', type: 'premium' },
    '2. NIKE Standard': { id: 'nike', name: 'Nike', type: 'standard' },
    '1. GUCCI': { id: 'gucci', name: 'Gucci', type: 'luxury' },
    '2. BALENCIAGA': { id: 'balenciaga', name: 'Balenciaga', type: 'luxury' },
    '3. DIOR': { id: 'dior', name: 'Dior', type: 'luxury' },
    '4. LOUIS VUITTON': { id: 'louis-vuitton', name: 'Louis Vuitton', type: 'luxury' },
    '5. PRADA': { id: 'prada', name: 'Prada', type: 'luxury' },
    '6. BOTTEGA VENETA': { id: 'bottega-veneta', name: 'Bottega Veneta', type: 'luxury' },
    '7. SAINT LAURENT': { id: 'saint-laurent', name: 'Saint Laurent', type: 'luxury' },
    '8. GIVENCHY': { id: 'givenchy', name: 'Givenchy', type: 'luxury' },
    '9. VERSACE': { id: 'versace', name: 'Versace', type: 'luxury' },
    '10. DOLCE & GABBANA': { id: 'dolce-gabbana', name: 'Dolce & Gabbana', type: 'luxury' }
  }
  
  return brandMap[cytteBrand] || { id: 'nike', name: 'Nike', type: 'premium' }
}

// Gender mapping (Level 3)
function mapGender(cytteGender) {
  const genderMap = {
    'MIXTE': { id: 'unisex', display: 'Unisex', path: '1. MIXTE' },
    '1. MIXTE': { id: 'unisex', display: 'Unisex', path: '1. MIXTE' },
    'WOMEN': { id: 'women', display: 'Mujer', path: '2. WOMEN' },
    '2. WOMEN': { id: 'women', display: 'Mujer', path: '2. WOMEN' },
    'MEN': { id: 'men', display: 'Hombre', path: '3. MEN' },
    '3. MEN': { id: 'men', display: 'Hombre', path: '3. MEN' },
    '1. MEN': { id: 'men', display: 'Hombre', path: '1. MEN' }
  }
  
  return genderMap[cytteGender] || { id: 'unisex', display: 'Unisex', path: '1. MIXTE' }
}

// Model family mapping (Level 4)
function mapModelFamily(cytteModel) {
  const modelMap = {
    'jordan': { id: 'jordan', display: 'Air Jordan', family: 'basketball' },
    'air-force': { id: 'air-force', display: 'Air Force', family: 'basketball' },
    'dunk': { id: 'dunk', display: 'Dunk', family: 'skateboarding' },
    'blazer': { id: 'blazer', display: 'Blazer', family: 'skateboarding' },
    'cortez': { id: 'cortez', display: 'Cortez', family: 'running' },
    'air-max': { id: 'air-max', display: 'Air Max', family: 'running' },
    'ace': { id: 'ace', display: 'Ace', family: 'luxury-sneaker' },
    'screener': { id: 'screener', display: 'Screener', family: 'luxury-sneaker' },
    'rython': { id: 'rython', display: 'Rython', family: 'luxury-sneaker' },
    'horsebit': { id: 'horsebit', display: 'Horsebit', family: 'luxury-loafer' }
  }
  
  return modelMap[cytteModel] || { id: 'standard', display: 'Standard', family: 'lifestyle' }
}

// Product name generation
function generateProductName(brand, model, sku, collaborator) {
  if (collaborator) {
    return `${brand} ${model} x ${collaborator}`
  }
  return `${brand} ${model} ${sku}`
}

// Product description generation
function generateProductDescription(brand, model, sku, isCollaboration) {
  const baseDesc = `Auténticos ${brand} ${model} ${sku}. Calzado premium de lujo con descuento exclusivo.`
  
  if (isCollaboration) {
    return `${baseDesc} Edición limitada de colaboración. Pieza única para coleccionistas.`
  }
  
  return `${baseDesc} Diseño icónico y calidad excepcional.`
}

// Product type classification
function getProductType(styleId) {
  const typeMap = {
    'sneakers': 'sneaker',
    'sandals': 'sandal',
    'formal': 'dress-shoe',
    'casual': 'casual-shoe',
    'kids': 'kids-shoe'
  }
  
  return typeMap[styleId] || 'sneaker'
}

function getProductSubType(modelDisplay) {
  const subTypeMap = {
    'Air Jordan': 'basketball-sneaker',
    'Air Force': 'basketball-sneaker',
    'Dunk': 'skateboard-sneaker',
    'Blazer': 'skateboard-sneaker',
    'Cortez': 'running-sneaker',
    'Air Max': 'running-sneaker',
    'Ace': 'luxury-sneaker',
    'Screener': 'luxury-sneaker',
    'Rython': 'luxury-sneaker',
    'Horsebit': 'luxury-loafer'
  }
  
  return subTypeMap[modelDisplay] || 'lifestyle-sneaker'
}

// Price generation
function generatePrice(brandType, isCollaboration) {
  let basePrice
  
  switch (brandType) {
    case 'luxury':
      basePrice = Math.floor(Math.random() * 15000) + 25000 // 25,000 - 40,000 MXN
      break
    case 'premium':
      basePrice = Math.floor(Math.random() * 8000) + 12000 // 12,000 - 20,000 MXN
      break
    default:
      basePrice = Math.floor(Math.random() * 5000) + 5000 // 5,000 - 10,000 MXN
  }
  
  if (isCollaboration) {
    basePrice = Math.floor(basePrice * 1.5) // 50% markup for collaborations
  }
  
  return basePrice
}

// Size generation
function generateSizes(genderId) {
  const sizeMap = {
    'men': ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
    'women': ['5', '5.5', '6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10'],
    'unisex': ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11']
  }
  
  return sizeMap[genderId] || sizeMap['unisex']
}

// Materials generation
function generateMaterials(brandType) {
  const materialMap = {
    'luxury': ['Cuero italiano', 'Gamuza premium', 'Lona de lujo', 'Detalles metálicos'],
    'premium': ['Cuero genuino', 'Sintético premium', 'Malla transpirable', 'Suela de goma'],
    'standard': ['Cuero sintético', 'Textil', 'Suela de goma', 'Forro textil']
  }
  
  return materialMap[brandType] || materialMap['standard']
}

// Release date generation
function generateReleaseDate() {
  const now = new Date()
  const pastDate = new Date(now.getTime() - Math.random() * 365 * 24 * 60 * 60 * 1000) // Random date within last year
  return pastDate.toISOString().split('T')[0]
}

// Tags generation
function generateTags(brand, model, collaborator, style) {
  const tags = [
    brand.toLowerCase(),
    model.toLowerCase().replace(/\s+/g, '-'),
    style,
    'autentico',
    'descuento',
    'lujo'
  ]
  
  if (collaborator) {
    tags.push(collaborator.toLowerCase().replace(/\s+/g, '-'))
    tags.push('colaboracion')
    tags.push('edicion-limitada')
  }
  
  return tags
}

// Keywords generation
function generateKeywords(brand, model, sku) {
  return [
    `${brand} ${model}`,
    `${brand} ${sku}`,
    `${model} autentico`,
    `${brand} descuento`,
    `${model} Mexico`,
    `${brand} ${model} precio`,
    `${sku} original`
  ]
}

// Search terms generation
function generateSearchTerms(brand, model, collaborator) {
  const terms = [
    brand,
    model,
    `${brand} ${model}`,
    'tenis',
    'zapatos',
    'calzado',
    'sneakers'
  ]
  
  if (collaborator) {
    terms.push(collaborator)
    terms.push(`${brand} ${collaborator}`)
    terms.push(`${model} ${collaborator}`)
  }
  
  return terms
}

// Product name generation
function generateProductName(brand, model, sku, collaborator) {
  if (collaborator) {
    return `${brand} ${model} x ${collaborator}`
  }
  return `${brand} ${model} ${sku}`
}

// Product description generation
function generateProductDescription(brand, model, sku, isCollaboration) {
  const baseDesc = `Auténticos ${brand} ${model} ${sku}. Calzado premium de lujo con descuento exclusivo.`

  if (isCollaboration) {
    return `${baseDesc} Edición limitada de colaboración. Pieza única para coleccionistas.`
  }

  return `${baseDesc} Diseño icónico y calidad excepcional.`
}

// Product type classification
function getProductType(styleId) {
  const typeMap = {
    'sneakers': 'sneaker',
    'sandals': 'sandal',
    'formal': 'dress-shoe',
    'casual': 'casual-shoe',
    'kids': 'kids-shoe'
  }

  return typeMap[styleId] || 'sneaker'
}

function getProductSubType(modelDisplay) {
  const subTypeMap = {
    'Air Jordan': 'basketball-sneaker',
    'Air Force': 'basketball-sneaker',
    'Dunk': 'skateboard-sneaker',
    'Blazer': 'skateboard-sneaker',
    'Cortez': 'running-sneaker',
    'Air Max': 'running-sneaker',
    'Ace': 'luxury-sneaker',
    'Screener': 'luxury-sneaker',
    'Rython': 'luxury-sneaker',
    'Horsebit': 'luxury-loafer'
  }

  return subTypeMap[modelDisplay] || 'lifestyle-sneaker'
}

// Price generation
function generatePrice(brandType, isCollaboration) {
  let basePrice

  switch (brandType) {
    case 'luxury':
      basePrice = Math.floor(Math.random() * 15000) + 25000 // 25,000 - 40,000 MXN
      break
    case 'premium':
      basePrice = Math.floor(Math.random() * 8000) + 12000 // 12,000 - 20,000 MXN
      break
    default:
      basePrice = Math.floor(Math.random() * 5000) + 5000 // 5,000 - 10,000 MXN
  }

  if (isCollaboration) {
    basePrice = Math.floor(basePrice * 1.5) // 50% markup for collaborations
  }

  return basePrice
}

// Size generation
function generateSizes(genderId) {
  const sizeMap = {
    'men': ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
    'women': ['5', '5.5', '6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10'],
    'unisex': ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11']
  }

  return sizeMap[genderId] || sizeMap['unisex']
}

// Materials generation
function generateMaterials(brandType) {
  const materialMap = {
    'luxury': ['Cuero italiano', 'Gamuza premium', 'Lona de lujo', 'Detalles metálicos'],
    'premium': ['Cuero genuino', 'Sintético premium', 'Malla transpirable', 'Suela de goma'],
    'standard': ['Cuero sintético', 'Textil', 'Suela de goma', 'Forro textil']
  }

  return materialMap[brandType] || materialMap['standard']
}

// Release date generation
function generateReleaseDate() {
  const now = new Date()
  const pastDate = new Date(now.getTime() - Math.random() * 365 * 24 * 60 * 60 * 1000) // Random date within last year
  return pastDate.toISOString().split('T')[0]
}

// Tags generation
function generateTags(brand, model, collaborator, style) {
  const tags = [
    brand.toLowerCase(),
    model.toLowerCase().replace(/\s+/g, '-'),
    style,
    'autentico',
    'descuento',
    'lujo'
  ]

  if (collaborator) {
    tags.push(collaborator.toLowerCase().replace(/\s+/g, '-'))
    tags.push('colaboracion')
    tags.push('edicion-limitada')
  }

  return tags
}

// Keywords generation
function generateKeywords(brand, model, sku) {
  return [
    `${brand} ${model}`,
    `${brand} ${sku}`,
    `${model} autentico`,
    `${brand} descuento`,
    `${model} Mexico`,
    `${brand} ${model} precio`,
    `${sku} original`
  ]
}

// Search terms generation
function generateSearchTerms(brand, model, collaborator) {
  const terms = [
    brand,
    model,
    `${brand} ${model}`,
    'tenis',
    'zapatos',
    'calzado',
    'sneakers'
  ]

  if (collaborator) {
    terms.push(collaborator)
    terms.push(`${brand} ${collaborator}`)
    terms.push(`${model} ${collaborator}`)
  }

  return terms
}

module.exports = {
  mapStyle,
  mapBrand,
  mapGender,
  mapModelFamily,
  generateProductName,
  generateProductDescription,
  getProductType,
  getProductSubType,
  generatePrice,
  generateSizes,
  generateMaterials,
  generateReleaseDate,
  generateTags,
  generateKeywords,
  generateSearchTerms
}
