'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import OptimizedImage from '@/components/ui/OptimizedImage'
import { useCart } from '@/contexts/CartContext'
import { useCartNotification } from '@/contexts/CartNotificationContext'
import { useWishlist } from '@/contexts/WishlistContext'

export default function SimpleProductCard({ product, index = 0 }) {
  const router = useRouter()
  const { addItem } = useCart()
  const { showCartSuccess } = useCartNotification()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()

  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const [isTouched, setIsTouched] = useState(false)
  const [showSecondImage, setShowSecondImage] = useState(false)
  const [isMobile, setIsMobile] = useState(false)

  // Detect mobile device
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024 || 'ontouchstart' in window)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])
  
  const isWishlisted = isInWishlist(product.id)
  const hasDiscount = product.originalPrice && product.originalPrice > product.price

  // Navigate to product page
  const handleProductClick = () => {
    // Use product ID for navigation (fallback to slug if available)
    const productIdentifier = product.id || product.slug
    console.log('Navigating to product:', productIdentifier, product)
    router.push(`/product/${productIdentifier}`)
  }

  // Add to cart
  const handleAddToCart = async (e) => {
    e.stopPropagation()
    setIsAddingToCart(true)

    try {
      await new Promise(resolve => setTimeout(resolve, 600))
      addItem(product.id, 'M', 1)
      showCartSuccess(product, 'M', 1)
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setIsAddingToCart(false)
    }
  }

  // Toggle wishlist
  const handleWishlistToggle = (e) => {
    e.stopPropagation()
    if (isWishlisted) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product.id)
    }
  }

  // Enhanced touch handlers for mobile-first experience
  const handleTouchStart = () => {
    setIsTouched(true)
    // Haptic feedback for mobile
    if (isMobile && navigator.vibrate) {
      navigator.vibrate(10)
    }
    // On mobile, show second image immediately on touch
    if (isMobile && product.images?.[1]) {
      setShowSecondImage(true)
    }
  }

  const handleTouchEnd = () => {
    setTimeout(() => {
      setIsTouched(false)
      // Hide second image after touch ends on mobile
      if (isMobile) {
        setTimeout(() => setShowSecondImage(false), 200)
      }
    }, 300)
  }

  // Mouse handlers for desktop
  const handleMouseEnter = () => {
    if (!isMobile) {
      setIsHovered(true)
      setShowSecondImage(true)
    }
  }

  const handleMouseLeave = () => {
    if (!isMobile) {
      setIsHovered(false)
      setShowSecondImage(false)
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05, duration: 0.4 }}
      className={`group cursor-pointer bg-white dark:bg-neutral-800 rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-1 h-full flex flex-col touch-manipulation ${
        isTouched ? 'scale-[0.98] shadow-md' : ''
      }`}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onTouchStart={handleTouchStart}
      onTouchEnd={handleTouchEnd}
      onClick={handleProductClick}
      whileTap={{ scale: 0.98 }}
    >
      {/* Product Image Container */}
      <div className="relative aspect-[4/3] bg-gray-100 dark:bg-neutral-700 overflow-hidden">
        {/* First Image - Default */}
        <OptimizedImage
          src={product.images?.[0] || product.image || "/images/placeholder.svg"}
          alt={product.name}
          fill
          className={`absolute inset-0 object-cover transition-all duration-500 ease-out z-0 ${
            showSecondImage && product.images?.[1]
              ? 'opacity-0 scale-110'
              : isMobile
                ? 'opacity-100 scale-100'
                : 'opacity-100 group-hover:scale-110'
          }`}
          sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"
          priority={index < 4} // Prioritize first 4 images for LCP

        />

        {/* Second Image - Hover Effect */}
        {product.images?.[1] && (
          <OptimizedImage
            src={product.images[1]}
            alt={`${product.name} - Vista alternativa`}
            fill
            className={`absolute inset-0 object-cover transition-all duration-500 ease-out z-10 ${
              showSecondImage
                ? 'opacity-100 scale-110'
                : 'opacity-0 scale-100'
            }`}
            sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, 25vw"

          />
        )}

        {/* NUEVO Badge - Top Left */}
        {product.isNew && (
          <div className="absolute top-3 left-3 z-20">
            <div className="bg-gray-800 text-white text-xs px-3 py-1 rounded-md font-semibold">
              NUEVO
            </div>
          </div>
        )}

        {/* Wishlist Heart - Top Right - Enhanced for mobile */}
        <button
          onClick={handleWishlistToggle}
          className={`absolute top-3 right-3 flex items-center justify-center transition-all duration-300 transform z-30 touch-manipulation ${
            isMobile
              ? 'w-12 h-12 active:scale-95'
              : 'w-8 h-8 hover:scale-110'
          }`}
          aria-label={isWishlisted ? "Quitar de favoritos" : "Agregar a favoritos"}
        >
          <svg
            className={`w-6 h-6 transition-colors duration-300 ${
              isWishlisted
                ? 'text-lime-green fill-current'
                : 'text-gray-300 hover:text-lime-green'
            }`}
            viewBox="0 0 24 24"
            fill={isWishlisted ? "currentColor" : "none"}
            stroke="currentColor"
            strokeWidth={isWishlisted ? 0 : 1.5}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Add to Cart Button - Bottom Right - Enhanced for mobile */}
        <button
          onClick={handleAddToCart}
          disabled={isAddingToCart}
          className={`absolute bottom-3 right-3 w-12 h-12 bg-lime-green text-black rounded-full flex items-center justify-center transition-all duration-300 shadow-lg z-30 touch-manipulation ${
            isMobile
              ? `${isTouched ? 'opacity-100 translate-y-0 scale-110 bg-dark-gray text-white' : 'opacity-100 translate-y-0 active:scale-95'}`
              : `${isHovered ? 'opacity-100 translate-y-0 scale-110 hover:bg-dark-gray hover:text-white hover:shadow-xl' : 'opacity-0 translate-y-2 group-hover:opacity-100 group-hover:translate-y-0 group-hover:scale-110'}`
          }`}
          aria-label="Agregar al carrito"
        >
          {isAddingToCart ? (
            <div className="w-5 h-5 border-2 border-current border-t-transparent rounded-full animate-spin" />
          ) : (
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9" />
            </svg>
          )}
        </button>
      </div>

      {/* Product Info */}
      <div className="p-4 flex-1 flex flex-col justify-between">
        {/* Product Name - Already cleaned by ProductTransformer */}
        <div className="mb-2">
          <h3 className="font-bold text-black dark:text-white text-base leading-tight">
            {product.name || 'Product Name'}
          </h3>
        </div>

        {/* Brand Name */}
        <p className="text-sm text-gray-600 dark:text-neutral-400 uppercase tracking-wide mb-1">
          {product.brand || 'Brand'}
        </p>

        {/* Price */}
        <div className="flex items-center gap-2 mt-auto">
          {hasDiscount && (
            <span className="text-sm text-gray-500 dark:text-neutral-400 line-through">
              ${product.originalPrice?.toLocaleString()} MXN
            </span>
          )}
          <span className="font-bold text-xl text-black dark:text-white">
            ${product.price?.toLocaleString()} MXN
          </span>
        </div>
      </div>
    </motion.div>
  )
}
