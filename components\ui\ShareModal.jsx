'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { X, Co<PERSON>, Check, MessageCircle, Instagram, Facebook, Mail, Link2, Music } from 'lucide-react'

const ShareModal = ({ isOpen, onClose, product, currentUrl, selectedModel = 0 }) => {
  const [copied, setCopied] = useState(false)
  const [shareData, setShareData] = useState({
    title: '',
    text: '',
    url: ''
  })

  useEffect(() => {
    if (product && currentUrl) {
      setShareData({
        title: `${product.name} - The White Laces`,
        text: `¡Mira estos increíbles ${product.name} en The White Laces! 🔥 ${product.price ? `$${product.price}` : ''}`,
        url: currentUrl
      })
    }
  }, [product, currentUrl])

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(shareData.url)
      setCopied(true)
      setTimeout(() => setCopied(false), 2000)
    } catch (err) {
      console.error('Failed to copy:', err)
    }
  }

  const shareOptions = [
    {
      name: 'WhatsApp',
      icon: MessageCircle,
      color: 'hover:bg-green-500 hover:text-white',
      action: () => {
        const whatsappUrl = `https://wa.me/?text=${encodeURIComponent(`${shareData.text} ${shareData.url}`)}`
        window.open(whatsappUrl, '_blank')
      }
    },
    {
      name: 'Instagram',
      icon: Instagram,
      color: 'hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500 hover:text-white',
      action: () => {
        // Instagram doesn't support direct sharing, so copy to clipboard
        copyToClipboard()
      }
    },
    {
      name: 'TikTok',
      icon: Music,
      color: 'hover:bg-black hover:text-white',
      action: () => {
        // TikTok doesn't support direct sharing, so copy to clipboard with TikTok-friendly message
        const tiktokMessage = `🔥 ${product?.name} en @thewhitelaces 🔥 ${shareData.url} #TWL #Sneakers #Luxury`
        navigator.clipboard.writeText(tiktokMessage)
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      }
    },
    {
      name: 'Facebook',
      icon: Facebook,
      color: 'hover:bg-blue-600 hover:text-white',
      action: () => {
        const facebookUrl = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(shareData.url)}`
        window.open(facebookUrl, '_blank')
      }
    },
    {
      name: 'Email',
      icon: Mail,
      color: 'hover:bg-gray-600 hover:text-white',
      action: () => {
        const emailUrl = `mailto:?subject=${encodeURIComponent(shareData.title)}&body=${encodeURIComponent(`${shareData.text}\n\n${shareData.url}`)}`
        window.open(emailUrl)
      }
    }
  ]

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/60 backdrop-blur-sm z-50 flex items-start justify-center pt-16 md:pt-20 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
          className="bg-white dark:bg-gray-900 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div>
              <h3 className="text-xl font-bold text-gray-900 dark:text-white font-poppins">
                Compartir Producto
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
                Comparte este increíble producto
              </p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-all duration-200"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Product Preview */}
          {product && (
            <div className="p-6 border-b border-gray-200 dark:border-gray-700">
              <div className="flex items-center gap-4">
                <div className="w-16 h-16 bg-gray-100 dark:bg-gray-800 rounded-xl overflow-hidden flex-shrink-0">
                  {(() => {
                    // Get the current model's first image or fallback to product's first image
                    const currentModel = product.models ? product.models[selectedModel] : null
                    const imageToShow = currentModel?.images?.[0] || product.images?.[0]

                    return imageToShow && (
                      <img
                        src={imageToShow}
                        alt={currentModel?.name || product.name}
                        className="w-full h-full object-cover"
                      />
                    )
                  })()}
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-gray-900 dark:text-white truncate font-poppins">
                    {product.name}
                  </h4>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {product.brand}
                  </p>
                  {/* Show selected model name if available */}
                  {product.models && product.models[selectedModel] && (
                    <p className="text-xs text-gray-400 dark:text-gray-500">
                      {product.models[selectedModel].name}
                    </p>
                  )}
                  {(() => {
                    // Get price from current model or fallback to product price
                    const currentModel = product.models ? product.models[selectedModel] : product
                    const price = currentModel?.price || product.price

                    return price && (
                      <p className="text-lg font-bold text-lime-green-dark mt-1">
                        ${price}
                      </p>
                    )
                  })()}
                </div>
              </div>
            </div>
          )}

          {/* Copy Link Section */}
          <div className="p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center gap-3">
              <div className="flex-1 bg-gray-50 dark:bg-gray-800 rounded-xl p-3 flex items-center gap-3">
                <button
                  onClick={copyToClipboard}
                  className="flex-shrink-0 p-1 rounded-md hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors duration-200 group"
                  title="Copiar enlace"
                >
                  <Link2 className="w-4 h-4 text-gray-400 group-hover:text-lime-green transition-colors duration-200" />
                </button>
                <span className="text-sm text-gray-600 dark:text-gray-300 font-mono break-all leading-relaxed">
                  {shareData.url}
                </span>
              </div>
              <button
                onClick={copyToClipboard}
                className={`px-4 py-3 rounded-xl font-medium transition-all duration-200 flex items-center gap-2 ${
                  copied
                    ? 'bg-lime-green text-black'
                    : 'bg-gray-900 dark:bg-white text-white dark:text-gray-900 hover:bg-gray-800 dark:hover:bg-gray-100'
                }`}
              >
                {copied ? (
                  <>
                    <Check className="w-4 h-4" />
                    <span className="hidden sm:inline">Copiado</span>
                  </>
                ) : (
                  <>
                    <Copy className="w-4 h-4" />
                    <span className="hidden sm:inline">Copiar</span>
                  </>
                )}
              </button>
            </div>
          </div>

          {/* Social Share Options */}
          <div className="p-6">
            <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-4 font-poppins">
              Compartir en redes sociales
            </h4>
            <div className="grid grid-cols-5 gap-3">
              {shareOptions.map((option) => (
                <button
                  key={option.name}
                  onClick={option.action}
                  className={`flex flex-col items-center gap-2 p-4 rounded-xl border-2 border-gray-200 dark:border-gray-700 transition-all duration-200 hover:scale-105 hover:border-transparent ${option.color}`}
                >
                  <option.icon className="w-6 h-6" />
                  <span className="text-xs font-medium">{option.name}</span>
                </button>
              ))}
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}

export default ShareModal
