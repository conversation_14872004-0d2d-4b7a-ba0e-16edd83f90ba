#!/usr/bin/env node

/**
 * CYTTE Product Indexing Script
 * Automatically scans CYTTE supplier folder structure and creates TWL product database
 * Hides supplier information and creates clean TWL product catalog
 */

const fs = require('fs').promises;
const path = require('path');

// Configuration
const CYTTE_BASE_PATH = 'C:/2.MY_APP/TWL/V2/--materials/shoes/2. CYTTE';
const PUBLIC_IMAGES_PATH = 'public/products';
const SUPPLIER_ID = 'cytte-supplier-001';

// Supabase will be configured later
let supabase = null;

// Brand mapping (CYTTE folder names → TWL brand names)
const BRAND_MAPPING = {
  '1. NIKE Limited Edition': 'Nike',
  '2. ADIDAS Limited Edition': 'Adidas',
  '3. HERMES': 'Hermès',
  '4. GUCCI': 'Gucci',
  '5. DIOR': 'Dior',
  '6. LV': '<PERSON>',
  '7. BALENCIAGA': '<PERSON><PERSON>ciaga',
  '8. CHANEL': 'Chanel',
  '9. LOUBOUTIN': '<PERSON>',
  '10. OFF WHITE': 'Off-White',
  '11. GIVENCHY': 'Givenchy',
  '12. Maison MARGIELA': 'Maison Margiela',
  '13. VALENTINO': 'Valentino',
  '14. PRADA': 'Prada',
  '15. MIU MIU': 'Miu Miu',
  '16. BOTTEGA VENETA': 'Bottega Veneta',
  '17. BURBERRY': 'Burberry',
  '18. GOLDEN GOOSE': 'Golden Goose',
  '19. GAMA NORMAL': 'Gama Normal',
  'Common Project': 'Common Projects',
  // Sandals specific
  '1. NIKE Collabs': 'Nike',
  '2. GUCCI': 'Gucci',
  '3. DIOR': 'Dior',
  '4. LV': 'Louis Vuitton',
  '5. BALENCIAGA': 'Balenciaga',
  '6. CHANEL': 'Chanel',
  '7. MAISON MARGIELA': 'Maison Margiela',
  '8. GIVENCHY': 'Givenchy',
  '9. UGG': 'UGG',
  '10. MIU MIU': 'Miu Miu',
  '11. PRADA': 'Prada',
  '12. HERMES': 'Hermès',
  '13. CROCS': 'Crocs',
  '14. BOTTEGA VENETA': 'Bottega Veneta',
  '15. BIRKENSTOCK': 'Birkenstock',
  // Formal specific
  '1. CHANEL': 'Chanel',
  '2. PRADA': 'Prada',
  '3. GUCCI': 'Gucci',
  // Casual specific
  '1. UGG': 'UGG',
  '2. LV': 'Louis Vuitton',
  '3. MIU MIU': 'Miu Miu',
  '4. PRADA': 'Prada',
  '5. BOTTEGA VENETA': 'Bottega Veneta',
  '6. GUCCI': 'Gucci',
  '7. Adidas': 'Adidas',
  // Kids specific
  '1. UGG': 'UGG',
  '2. GOLDEN GOOSE': 'Golden Goose'
};

// Category mapping
const CATEGORY_MAPPING = {
  '1. SNEAKERS': 'sneakers',
  '2. SANDALS': 'sandals', 
  '3. FORMAL': 'formal',
  '4. CASUAL': 'casual',
  '5. KIDS': 'kids'
};

// Gender mapping
const GENDER_MAPPING = {
  '1. MIXTE': 'unisex',
  '2. WOMEN': 'women',
  '3. MEN': 'men'
};

class CytteProductIndexer {
  constructor() {
    this.products = [];
    this.processedCount = 0;
    this.errorCount = 0;
  }

  async scanCytteFolder() {
    console.log('🔍 Scanning CYTTE product folder structure...');
    
    try {
      const categories = await fs.readdir(CYTTE_BASE_PATH);
      
      for (const categoryFolder of categories) {
        if (categoryFolder.startsWith('.')) continue;
        
        const categoryPath = path.join(CYTTE_BASE_PATH, categoryFolder);
        const categoryStats = await fs.stat(categoryPath);
        
        if (categoryStats.isDirectory()) {
          await this.processCategoryFolder(categoryPath, categoryFolder);
        }
      }
      
      console.log(`✅ Scan complete! Found ${this.products.length} products`);
      return this.products;
      
    } catch (error) {
      console.error('❌ Error scanning CYTTE folder:', error);
      throw error;
    }
  }

  async processCategoryFolder(categoryPath, categoryFolder) {
    const category = CATEGORY_MAPPING[categoryFolder];
    if (!category) {
      console.log(`⚠️ Unknown category: ${categoryFolder}`);
      return;
    }

    console.log(`📂 Processing category: ${category}`);
    
    const brands = await fs.readdir(categoryPath);
    
    for (const brandFolder of brands) {
      if (brandFolder.startsWith('.') || brandFolder.endsWith('.txt')) continue;
      
      const brandPath = path.join(categoryPath, brandFolder);
      const brandStats = await fs.stat(brandPath);
      
      if (brandStats.isDirectory()) {
        await this.processBrandFolder(brandPath, brandFolder, category);
      }
    }
  }

  async processBrandFolder(brandPath, brandFolder, category) {
    const brand = BRAND_MAPPING[brandFolder];
    if (!brand) {
      console.log(`⚠️ Unknown brand: ${brandFolder}`);
      return;
    }

    console.log(`🏷️ Processing brand: ${brand} in ${category}`);
    
    const genders = await fs.readdir(brandPath);
    
    for (const genderFolder of genders) {
      if (genderFolder.startsWith('.')) continue;
      
      const genderPath = path.join(brandPath, genderFolder);
      const genderStats = await fs.stat(genderPath);
      
      if (genderStats.isDirectory()) {
        await this.processGenderFolder(genderPath, genderFolder, category, brand);
      }
    }
  }

  async processGenderFolder(genderPath, genderFolder, category, brand) {
    const gender = GENDER_MAPPING[genderFolder];
    if (!gender) {
      console.log(`⚠️ Unknown gender: ${genderFolder}`);
      return;
    }

    const products = await fs.readdir(genderPath);
    
    for (const productFolder of products) {
      if (productFolder.startsWith('.')) continue;
      
      const productPath = path.join(genderPath, productFolder);
      const productStats = await fs.stat(productPath);
      
      if (productStats.isDirectory()) {
        await this.processProductFolder(productPath, productFolder, category, brand, gender);
      }
    }
  }

  async processProductFolder(productPath, productFolder, category, brand, gender) {
    try {
      console.log(`👟 Processing product: ${productFolder}`);
      
      // Extract product info from folder name
      const [supplierSku, ...nameParts] = productFolder.split(' -- ');
      const productName = nameParts.join(' ') || this.generateProductName(brand, supplierSku);
      
      // Read product files
      const files = await fs.readdir(productPath);

      // Look for both WebP and JPG images
      let webpImages = files.filter(file => file.endsWith('.webp'));
      let jpgImages = files.filter(file => file.toLowerCase().endsWith('.jpg') || file.toLowerCase().endsWith('.jpeg'));

      let images = [];
      let imageFormat = 'none';

      if (webpImages.length > 0) {
        images = webpImages;
        imageFormat = 'webp';
      } else if (jpgImages.length > 0) {
        images = jpgImages;
        imageFormat = 'jpg';
      }

      const descriptionFile = files.find(file => file.toLowerCase() === 'description.txt');

      // Skip if no images at all
      if (images.length === 0) {
        console.log(`⚠️ No images found for ${productFolder}, skipping...`);
        return;
      }

      // Read description if available
      let description = `${brand} ${productName} - Luxury ${category} from The White Laces collection.`;
      if (descriptionFile) {
        try {
          const descContent = await fs.readFile(path.join(productPath, descriptionFile), 'utf8');
          description = descContent.trim() || description;
        } catch (error) {
          console.log(`⚠️ Could not read description for ${productFolder}`);
        }
      }

      // Generate TWL SKU (hide supplier SKU)
      const twlSku = this.generateTwlSku(brand, category, supplierSku);
      
      // Create product object
      const product = {
        sku: twlSku,
        supplier_sku: supplierSku,
        name: productName,
        brand: brand,
        category: category,
        gender: gender,
        description: description,
        price: this.estimatePrice(brand, category),
        supplier_id: SUPPLIER_ID,
        images: images,
        image_format: imageFormat,
        needs_conversion: imageFormat === 'jpg',
        source_path: productPath,
        featured: this.shouldBeFeatured(brand),
        status: imageFormat === 'webp' ? 'active' : 'needs_conversion'
      };

      this.products.push(product);
      this.processedCount++;
      
    } catch (error) {
      console.error(`❌ Error processing ${productFolder}:`, error);
      this.errorCount++;
    }
  }

  generateTwlSku(brand, category, supplierSku) {
    const brandCode = brand.substring(0, 3).toUpperCase();
    const categoryCode = category.substring(0, 3).toUpperCase();
    const uniqueId = supplierSku.replace(/[^A-Z0-9]/g, '').substring(0, 6);
    return `TWL-${brandCode}-${categoryCode}-${uniqueId}`;
  }

  generateProductName(brand, supplierSku) {
    // Generate a product name based on brand and SKU if no name provided
    const models = {
      'Nike': ['Air Force 1', 'Air Max', 'Dunk', 'Jordan'],
      'Gucci': ['Ace', 'Rhyton', 'Screener', 'Tennis 1977'],
      'Dior': ['B23', 'B22', 'Walk\'n\'Dior'],
      'Louis Vuitton': ['Archlight', 'Run Away', 'Trainer'],
      'Balenciaga': ['Triple S', 'Track', 'Speed', 'Runner']
    };
    
    const brandModels = models[brand] || ['Sneaker', 'Shoe'];
    const randomModel = brandModels[Math.floor(Math.random() * brandModels.length)];
    
    return `${brand} ${randomModel}`;
  }

  estimatePrice(brand, category) {
    // Price estimation based on brand positioning
    const priceRanges = {
      'Nike': { min: 120, max: 500 },
      'Adidas': { min: 100, max: 400 },
      'Gucci': { min: 600, max: 1500 },
      'Dior': { min: 800, max: 2500 },
      'Louis Vuitton': { min: 700, max: 2000 },
      'Balenciaga': { min: 500, max: 1800 },
      'Chanel': { min: 800, max: 2200 },
      'Christian Louboutin': { min: 600, max: 1800 },
      'Off-White': { min: 400, max: 1200 },
      'Hermès': { min: 1000, max: 3000 }
    };

    const range = priceRanges[brand] || { min: 200, max: 800 };
    const categoryMultiplier = category === 'formal' ? 1.3 : 1.0;
    
    const basePrice = Math.floor(Math.random() * (range.max - range.min) + range.min);
    return Math.floor(basePrice * categoryMultiplier);
  }

  shouldBeFeatured(brand) {
    const featuredBrands = ['Nike', 'Gucci', 'Dior', 'Louis Vuitton', 'Balenciaga', 'Off-White'];
    return featuredBrands.includes(brand) && Math.random() > 0.7; // 30% chance for featured brands
  }

  async saveToDatabase() {
    if (!supabase) {
      console.log('⚠️ Supabase not configured. Skipping database save.');
      console.log('💡 To enable database saving, install @supabase/supabase-js and configure environment variables.');
      return;
    }

    console.log('💾 Saving products to database...');

    try {
      // Insert products in batches
      const batchSize = 50;
      for (let i = 0; i < this.products.length; i += batchSize) {
        const batch = this.products.slice(i, i + batchSize);

        const { data, error } = await supabase
          .from('products')
          .insert(batch);

        if (error) {
          console.error('❌ Database error:', error);
          throw error;
        }

        console.log(`✅ Saved batch ${Math.floor(i/batchSize) + 1}/${Math.ceil(this.products.length/batchSize)}`);
      }

      console.log(`🎉 Successfully saved ${this.products.length} products to database!`);

    } catch (error) {
      console.error('❌ Error saving to database:', error);
      throw error;
    }
  }

  async generateReport() {
    console.log('\n📊 INDEXING REPORT');
    console.log('==================');
    console.log(`Total products processed: ${this.processedCount}`);
    console.log(`Errors encountered: ${this.errorCount}`);
    
    // Group by brand
    const brandCounts = {};
    this.products.forEach(product => {
      brandCounts[product.brand] = (brandCounts[product.brand] || 0) + 1;
    });
    
    console.log('\n🏷️ Products by Brand:');
    Object.entries(brandCounts)
      .sort(([,a], [,b]) => b - a)
      .forEach(([brand, count]) => {
        console.log(`  ${brand}: ${count} products`);
      });
    
    // Group by category
    const categoryCounts = {};
    this.products.forEach(product => {
      categoryCounts[product.category] = (categoryCounts[product.category] || 0) + 1;
    });
    
    console.log('\n📂 Products by Category:');
    Object.entries(categoryCounts).forEach(([category, count]) => {
      console.log(`  ${category}: ${count} products`);
    });
  }

  async saveToJSON() {
    try {
      // Create output directory if it doesn't exist
      const outputDir = 'output';
      try {
        await fs.mkdir(outputDir, { recursive: true });
      } catch (error) {
        // Directory might already exist
      }

      const outputPath = path.join(outputDir, 'indexed-products.json');
      const jsonData = {
        metadata: {
          supplier: 'CYTTE',
          indexed_at: new Date().toISOString(),
          total_products: this.products.length,
          processed_count: this.processedCount,
          error_count: this.errorCount
        },
        products: this.products
      };

      await fs.writeFile(outputPath, JSON.stringify(jsonData, null, 2), 'utf8');
      console.log(`💾 Products saved to ${outputPath}`);

    } catch (error) {
      console.error('❌ Error saving JSON:', error);
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting CYTTE Product Indexing...\n');
  
  const indexer = new CytteProductIndexer();
  
  try {
    // Scan folder structure
    await indexer.scanCytteFolder();
    
    // Generate report
    await indexer.generateReport();

    // Save to JSON file
    await indexer.saveToJSON();

    // Save to database (will skip if Supabase not configured)
    await indexer.saveToDatabase();

    console.log('\n✅ Indexing completed successfully!');
    
  } catch (error) {
    console.error('\n❌ Indexing failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { CytteProductIndexer };
