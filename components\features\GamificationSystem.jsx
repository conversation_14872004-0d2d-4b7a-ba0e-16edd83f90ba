'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'

// Mock user progress data
const mockUserProgress = {
  level: 8,
  currentXP: 2450,
  nextLevelXP: 3000,
  totalXP: 12450,
  tier: 'Gold',
  points: 8750,
  streak: 12,
  badges: [
    { id: 'first-purchase', name: 'Primera Compra', icon: '🛍️', earned: true, rarity: 'common' },
    { id: 'sneaker-lover', name: '<PERSON><PERSON> de Sneakers', icon: '👟', earned: true, rarity: 'common' },
    { id: 'social-butterfly', name: 'Mari<PERSON>sa Social', icon: '🦋', earned: true, rarity: 'rare' },
    { id: 'trendsetter', name: 'Trendsetter', icon: '🔥', earned: true, rarity: 'epic' },
    { id: 'collector', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '💎', earned: false, rarity: 'legendary' },
    { id: 'influencer', name: 'Influencer', icon: '⭐', earned: false, rarity: 'mythical' }
  ],
  achievements: [
    { id: 'spend-10k', name: 'Gastador Nivel 1', description: 'Gasta $10,000 MXN', progress: 8500, target: 10000, reward: 500 },
    { id: 'reviews-10', name: 'Crítico', description: 'Escribe 10 reseñas', progress: 7, target: 10, reward: 200 },
    { id: 'referrals-5', name: 'Embajador', description: 'Refiere 5 amigos', progress: 3, target: 5, reward: 1000 }
  ],
  recentActivity: [
    { type: 'purchase', description: 'Compraste Jordan 1 Chicago', xp: 200, timestamp: '2024-01-20T10:30:00Z' },
    { type: 'review', description: 'Escribiste una reseña', xp: 50, timestamp: '2024-01-19T15:45:00Z' },
    { type: 'social', description: 'Compartiste en redes sociales', xp: 25, timestamp: '2024-01-18T12:20:00Z' }
  ]
}

const tierColors = {
  Bronze: 'from-orange-600 to-orange-800',
  Silver: 'from-gray-400 to-gray-600',
  Gold: 'from-yellow-400 to-yellow-600',
  Platinum: 'from-purple-400 to-purple-600',
  Diamond: 'from-blue-400 to-blue-600'
}

const rarityColors = {
  common: 'text-gray-500',
  rare: 'text-blue-500',
  epic: 'text-purple-500',
  legendary: 'text-orange-500',
  mythical: 'text-red-500'
}

export default function GamificationSystem({ 
  className = '',
  showFullDashboard = true,
  compact = false 
}) {
  const [userProgress, setUserProgress] = useState(null)
  const [isLoading, setIsLoading] = useState(true)
  const [selectedBadge, setSelectedBadge] = useState(null)
  const [showRewards, setShowRewards] = useState(false)

  useEffect(() => {
    // Simulate loading user progress
    const loadProgress = async () => {
      setIsLoading(true)
      await new Promise(resolve => setTimeout(resolve, 1000))
      setUserProgress(mockUserProgress)
      setIsLoading(false)
    }
    
    loadProgress()
  }, [])

  const getXPProgress = () => {
    if (!userProgress) return 0
    const levelXP = userProgress.currentXP - (userProgress.nextLevelXP - (userProgress.nextLevelXP - userProgress.currentXP))
    return (userProgress.currentXP / userProgress.nextLevelXP) * 100
  }

  const getAchievementProgress = (achievement) => {
    return (achievement.progress / achievement.target) * 100
  }

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const activityTime = new Date(timestamp)
    const diffInHours = Math.floor((now - activityTime) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Hace menos de 1h'
    if (diffInHours < 24) return `Hace ${diffInHours}h`
    const diffInDays = Math.floor(diffInHours / 24)
    return `Hace ${diffInDays}d`
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="bg-warm-camel/20 rounded h-8 w-48"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="bg-warm-camel/20 rounded-lg h-32"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (compact) {
    return (
      <div className={`${className}`}>
        <Card variant="glass">
          <CardContent className="p-4">
            <div className="flex items-center gap-4">
              <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${tierColors[userProgress.tier]} flex items-center justify-center text-white font-bold`}>
                {userProgress.level}
              </div>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                    Nivel {userProgress.level}
                  </span>
                  <Badge variant="primary" size="sm">{userProgress.tier}</Badge>
                </div>
                <div className="w-full bg-warm-camel/20 rounded-full h-2">
                  <div 
                    className="bg-rich-gold h-2 rounded-full transition-all duration-500"
                    style={{ width: `${getXPProgress()}%` }}
                  />
                </div>
                <div className="flex justify-between text-xs text-warm-camel mt-1">
                  <span>{userProgress.currentXP} XP</span>
                  <span>{userProgress.nextLevelXP} XP</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className={`space-y-8 ${className}`}>
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
            Tu Progreso TWL
          </h2>
          <p className="text-warm-camel text-sm">
            Gana puntos, desbloquea badges y sube de nivel
          </p>
        </div>
        
        <AnimatedButton
          variant="secondary"
          size="sm"
          onClick={() => setShowRewards(!showRewards)}
          icon={
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
            </svg>
          }
        >
          Ver Recompensas
        </AnimatedButton>
      </div>

      {/* Level Progress */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card variant="glass">
          <CardContent className="p-6">
            <div className="flex items-center gap-6">
              
              {/* Level Badge */}
              <div className={`w-20 h-20 rounded-full bg-gradient-to-r ${tierColors[userProgress.tier]} flex items-center justify-center text-white font-bold text-2xl shadow-lg`}>
                {userProgress.level}
              </div>
              
              {/* Progress Info */}
              <div className="flex-1">
                <div className="flex items-center gap-3 mb-2">
                  <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                    Nivel {userProgress.level}
                  </h3>
                  <Badge variant="primary" size="sm">{userProgress.tier}</Badge>
                  <div className="flex items-center gap-1 text-orange-500">
                    <span>🔥</span>
                    <span className="text-sm font-medium">{userProgress.streak} días</span>
                  </div>
                </div>
                
                <div className="w-full bg-warm-camel/20 rounded-full h-3 mb-2">
                  <motion.div 
                    className="bg-gradient-to-r from-rich-gold to-yellow-500 h-3 rounded-full"
                    initial={{ width: 0 }}
                    animate={{ width: `${getXPProgress()}%` }}
                    transition={{ duration: 1, ease: "easeOut" }}
                  />
                </div>
                
                <div className="flex justify-between text-sm">
                  <span className="text-warm-camel">
                    {userProgress.currentXP} / {userProgress.nextLevelXP} XP
                  </span>
                  <span className="text-forest-emerald dark:text-light-cloud-gray font-medium">
                    {userProgress.nextLevelXP - userProgress.currentXP} XP para siguiente nivel
                  </span>
                </div>
              </div>
              
              {/* Points */}
              <div className="text-center">
                <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                  {userProgress.points.toLocaleString()}
                </div>
                <div className="text-sm text-warm-camel">Puntos TWL</div>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Stats Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        
        {/* Badges */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
        >
          <Card variant="default">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                Badges Desbloqueados
              </h3>
              
              <div className="grid grid-cols-3 gap-3 mb-4">
                {userProgress.badges.map((badge) => (
                  <motion.button
                    key={badge.id}
                    onClick={() => setSelectedBadge(badge)}
                    className={`aspect-square rounded-lg border-2 flex items-center justify-center text-2xl transition-all ${
                      badge.earned 
                        ? 'border-rich-gold bg-rich-gold/10 hover:scale-105' 
                        : 'border-warm-camel/30 bg-warm-camel/5 opacity-50'
                    }`}
                    whileHover={{ scale: badge.earned ? 1.05 : 1 }}
                    whileTap={{ scale: badge.earned ? 0.95 : 1 }}
                  >
                    {badge.icon}
                  </motion.button>
                ))}
              </div>
              
              <div className="text-center">
                <span className="text-sm text-warm-camel">
                  {userProgress.badges.filter(b => b.earned).length} / {userProgress.badges.length} desbloqueados
                </span>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Achievements */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          <Card variant="default">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                Logros en Progreso
              </h3>
              
              <div className="space-y-4">
                {userProgress.achievements.map((achievement) => (
                  <div key={achievement.id}>
                    <div className="flex justify-between items-center mb-1">
                      <span className="text-sm font-medium text-forest-emerald dark:text-light-cloud-gray">
                        {achievement.name}
                      </span>
                      <span className="text-xs text-rich-gold">
                        +{achievement.reward} pts
                      </span>
                    </div>
                    <p className="text-xs text-warm-camel mb-2">
                      {achievement.description}
                    </p>
                    <div className="w-full bg-warm-camel/20 rounded-full h-2">
                      <div 
                        className="bg-rich-gold h-2 rounded-full transition-all duration-500"
                        style={{ width: `${getAchievementProgress(achievement)}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-warm-camel mt-1">
                      <span>{achievement.progress}</span>
                      <span>{achievement.target}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Recent Activity */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
        >
          <Card variant="default">
            <CardContent className="p-6">
              <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                Actividad Reciente
              </h3>
              
              <div className="space-y-3">
                {userProgress.recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-rich-gold/20 rounded-full flex items-center justify-center">
                      <span className="text-xs">
                        {activity.type === 'purchase' ? '🛍️' : 
                         activity.type === 'review' ? '⭐' : '📱'}
                      </span>
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-forest-emerald dark:text-light-cloud-gray">
                        {activity.description}
                      </p>
                      <p className="text-xs text-warm-camel">
                        {formatTimeAgo(activity.timestamp)}
                      </p>
                    </div>
                    <span className="text-sm font-medium text-rich-gold">
                      +{activity.xp} XP
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Badge Detail Modal */}
      <AnimatePresence>
        {selectedBadge && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setSelectedBadge(null)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              className="max-w-md w-full"
              onClick={(e) => e.stopPropagation()}
            >
              <Card variant="glass">
                <CardContent className="p-8 text-center">
                  <div className="text-6xl mb-4">{selectedBadge.icon}</div>
                  <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    {selectedBadge.name}
                  </h3>
                  <Badge 
                    variant={selectedBadge.earned ? "success" : "default"} 
                    size="sm" 
                    className={`mb-4 ${rarityColors[selectedBadge.rarity]}`}
                  >
                    {selectedBadge.rarity.charAt(0).toUpperCase() + selectedBadge.rarity.slice(1)}
                  </Badge>
                  <p className="text-warm-camel mb-6">
                    {selectedBadge.earned 
                      ? '¡Felicidades! Has desbloqueado este badge.' 
                      : 'Sigue participando en la comunidad para desbloquear este badge.'}
                  </p>
                  <AnimatedButton
                    variant="primary"
                    onClick={() => setSelectedBadge(null)}
                  >
                    Cerrar
                  </AnimatedButton>
                </CardContent>
              </Card>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
