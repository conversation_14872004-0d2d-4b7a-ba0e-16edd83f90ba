#!/usr/bin/env node

/**
 * COMPREHENSIVE AUDIT & TEST SCRIPT
 * Tests all implemented functionality and reports issues
 */

const fs = require('fs')
const path = require('path')

class TWLAuditor {
  constructor() {
    this.issues = []
    this.successes = []
    this.warnings = []
  }

  log(type, message, details = null) {
    const entry = { type, message, details, timestamp: new Date().toISOString() }
    
    switch (type) {
      case 'success':
        this.successes.push(entry)
        console.log(`✅ ${message}`)
        break
      case 'warning':
        this.warnings.push(entry)
        console.log(`⚠️  ${message}`)
        break
      case 'error':
        this.issues.push(entry)
        console.log(`❌ ${message}`)
        break
      default:
        console.log(`ℹ️  ${message}`)
    }
    
    if (details) {
      console.log(`   ${details}`)
    }
  }

  async runFullAudit() {
    console.log('🔍 STARTING COMPREHENSIVE TWL AUDIT...\n')
    
    await this.auditColorPalette()
    await this.auditImagePaths()
    await this.auditProductData()
    await this.auditComponents()
    await this.auditMobileOptimization()
    await this.auditAutoSliding()
    
    this.generateReport()
  }

  async auditColorPalette() {
    console.log('🎨 AUDITING COLOR PALETTE...')
    
    try {
      // Check Tailwind config
      const tailwindPath = path.join(process.cwd(), 'tailwind.config.js')
      const tailwindContent = fs.readFileSync(tailwindPath, 'utf8')
      
      if (tailwindContent.includes("'lime-green': '#BFFF00'")) {
        this.log('success', 'Lime green color defined in Tailwind config')
      } else {
        this.log('error', 'Lime green color not found in Tailwind config')
      }
      
      // Check SimpleProductCard for correct colors
      const cardPath = path.join(process.cwd(), 'components', 'ui', 'SimpleProductCard.jsx')
      const cardContent = fs.readFileSync(cardPath, 'utf8')
      
      if (cardContent.includes('bg-lime-green')) {
        this.log('success', 'Add to cart button uses correct lime-green color')
      } else if (cardContent.includes('bg-lime-500')) {
        this.log('error', 'Add to cart button still uses lime-500 instead of lime-green')
      }
      
      if (cardContent.includes('text-lime-green')) {
        this.log('success', 'Wishlist heart uses correct lime-green color')
      } else if (cardContent.includes('text-lime-500')) {
        this.log('error', 'Wishlist heart still uses lime-500 instead of lime-green')
      }
      
    } catch (error) {
      this.log('error', 'Failed to audit color palette', error.message)
    }
  }

  async auditImagePaths() {
    console.log('\n🖼️  AUDITING IMAGE PATHS...')
    
    try {
      // Check products data
      const productsPath = path.join(process.cwd(), 'lib', 'data', 'products.js')
      const productsContent = fs.readFileSync(productsPath, 'utf8')
      
      const materialsReferences = (productsContent.match(/\/--materials\//g) || []).length
      const imageReferences = (productsContent.match(/\/images\//g) || []).length
      
      if (materialsReferences === 0) {
        this.log('success', 'All --materials paths have been converted to web-accessible paths')
      } else {
        this.log('error', `${materialsReferences} --materials references still exist`)
      }
      
      if (imageReferences > 0) {
        this.log('success', `${imageReferences} web-accessible image paths found`)
      } else {
        this.log('warning', 'No /images/ paths found in products data')
      }
      
      // Check if public images directory exists
      const publicImagesPath = path.join(process.cwd(), 'public', 'images')
      if (fs.existsSync(publicImagesPath)) {
        this.log('success', 'Public images directory exists')
      } else {
        this.log('error', 'Public images directory missing')
      }
      
      // Check OptimizedImage component
      const optimizedImagePath = path.join(process.cwd(), 'components', 'ui', 'OptimizedImage.jsx')
      if (fs.existsSync(optimizedImagePath)) {
        this.log('success', 'OptimizedImage component created')
      } else {
        this.log('error', 'OptimizedImage component missing')
      }
      
    } catch (error) {
      this.log('error', 'Failed to audit image paths', error.message)
    }
  }

  async auditProductData() {
    console.log('\n📊 AUDITING PRODUCT DATA...')
    
    try {
      const productsPath = path.join(process.cwd(), 'lib', 'data', 'products.js')
      const productsContent = fs.readFileSync(productsPath, 'utf8')
      
      // Check for products with images array
      const productsWithImages = (productsContent.match(/"images":\s*\[/g) || []).length
      if (productsWithImages > 0) {
        this.log('success', `${productsWithImages} products have images arrays`)
      } else {
        this.log('error', 'No products with images arrays found')
      }
      
      // Check for limited edition products
      const limitedProducts = (productsContent.match(/"isLimited":\s*true/g) || []).length
      if (limitedProducts > 0) {
        this.log('success', `${limitedProducts} limited edition products found`)
      } else {
        this.log('warning', 'No limited edition products found for auto-sliding section')
      }
      
    } catch (error) {
      this.log('error', 'Failed to audit product data', error.message)
    }
  }

  async auditComponents() {
    console.log('\n🧩 AUDITING COMPONENTS...')
    
    const components = [
      'components/ui/SimpleProductCard.jsx',
      'components/ui/ProductSlider.jsx',
      'components/features/FeaturedDrops.jsx',
      'components/ui/OptimizedImage.jsx'
    ]
    
    for (const componentPath of components) {
      const fullPath = path.join(process.cwd(), componentPath)
      if (fs.existsSync(fullPath)) {
        this.log('success', `${componentPath} exists`)
        
        // Check for specific functionality
        const content = fs.readFileSync(fullPath, 'utf8')
        
        if (componentPath.includes('ProductSlider')) {
          if (content.includes('autoSlide')) {
            this.log('success', 'ProductSlider has auto-slide functionality')
          } else {
            this.log('error', 'ProductSlider missing auto-slide functionality')
          }
          
          if (content.includes('onTouchStart')) {
            this.log('success', 'ProductSlider has touch support')
          } else {
            this.log('error', 'ProductSlider missing touch support')
          }
        }
        
        if (componentPath.includes('SimpleProductCard')) {
          if (content.includes('OptimizedImage')) {
            this.log('success', 'SimpleProductCard uses OptimizedImage')
          } else {
            this.log('error', 'SimpleProductCard not using OptimizedImage')
          }
          
          if (content.includes('touch-manipulation')) {
            this.log('success', 'SimpleProductCard has touch optimization')
          } else {
            this.log('error', 'SimpleProductCard missing touch optimization')
          }
        }
        
      } else {
        this.log('error', `${componentPath} missing`)
      }
    }
  }

  async auditMobileOptimization() {
    console.log('\n📱 AUDITING MOBILE OPTIMIZATION...')
    
    try {
      // Check Tailwind config for touch utilities
      const tailwindPath = path.join(process.cwd(), 'tailwind.config.js')
      const tailwindContent = fs.readFileSync(tailwindPath, 'utf8')
      
      if (tailwindContent.includes('touch-manipulation')) {
        this.log('success', 'Touch manipulation utility added to Tailwind')
      } else {
        this.log('error', 'Touch manipulation utility missing from Tailwind')
      }
      
      // Check for responsive breakpoints
      const cardPath = path.join(process.cwd(), 'components', 'ui', 'SimpleProductCard.jsx')
      const cardContent = fs.readFileSync(cardPath, 'utf8')
      
      if (cardContent.includes('sm:')) {
        this.log('success', 'Responsive breakpoints used in SimpleProductCard')
      } else {
        this.log('warning', 'Limited responsive breakpoints in SimpleProductCard')
      }
      
    } catch (error) {
      this.log('error', 'Failed to audit mobile optimization', error.message)
    }
  }

  async auditAutoSliding() {
    console.log('\n🎠 AUDITING AUTO-SLIDING FUNCTIONALITY...')
    
    try {
      const featuredDropsPath = path.join(process.cwd(), 'components', 'features', 'FeaturedDrops.jsx')
      const featuredDropsContent = fs.readFileSync(featuredDropsPath, 'utf8')
      
      if (featuredDropsContent.includes('autoSlide={true}')) {
        this.log('success', 'Auto-slide enabled for limited edition section')
      } else {
        this.log('error', 'Auto-slide not enabled for limited edition section')
      }
      
      if (featuredDropsContent.includes('maxProducts={20}')) {
        this.log('success', 'Limited edition section limited to 20 products')
      } else {
        this.log('error', 'Limited edition section not limited to 20 products')
      }
      
    } catch (error) {
      this.log('error', 'Failed to audit auto-sliding functionality', error.message)
    }
  }

  generateReport() {
    console.log('\n📋 AUDIT REPORT')
    console.log('=' * 50)
    
    console.log(`\n✅ SUCCESSES: ${this.successes.length}`)
    this.successes.forEach(item => {
      console.log(`   • ${item.message}`)
    })
    
    console.log(`\n⚠️  WARNINGS: ${this.warnings.length}`)
    this.warnings.forEach(item => {
      console.log(`   • ${item.message}`)
    })
    
    console.log(`\n❌ ISSUES: ${this.issues.length}`)
    this.issues.forEach(item => {
      console.log(`   • ${item.message}`)
      if (item.details) {
        console.log(`     ${item.details}`)
      }
    })
    
    // Overall status
    console.log('\n🎯 OVERALL STATUS')
    if (this.issues.length === 0) {
      console.log('✅ ALL TESTS PASSED - Ready for production!')
    } else if (this.issues.length <= 2) {
      console.log('⚠️  MINOR ISSUES - Mostly ready, fix remaining issues')
    } else {
      console.log('❌ MAJOR ISSUES - Requires attention before deployment')
    }
    
    // Save detailed report
    const report = {
      timestamp: new Date().toISOString(),
      summary: {
        successes: this.successes.length,
        warnings: this.warnings.length,
        issues: this.issues.length
      },
      details: {
        successes: this.successes,
        warnings: this.warnings,
        issues: this.issues
      }
    }
    
    try {
      const outputDir = path.join(process.cwd(), 'output')
      if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true })
      }
      
      fs.writeFileSync(
        path.join(outputDir, 'audit-report.json'),
        JSON.stringify(report, null, 2)
      )
      console.log('\n📄 Detailed report saved to output/audit-report.json')
    } catch (error) {
      console.log('\n❌ Failed to save detailed report:', error.message)
    }
  }
}

// Main execution
async function main() {
  const auditor = new TWLAuditor()
  await auditor.runFullAudit()
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ Audit failed:', error)
    process.exit(1)
  })
}

module.exports = { TWLAuditor }
