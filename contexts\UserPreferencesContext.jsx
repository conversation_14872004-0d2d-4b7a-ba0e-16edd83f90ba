'use client'

import { createContext, useContext, useState, useEffect } from 'react'

const UserPreferencesContext = createContext()

export function useUserPreferences() {
  const context = useContext(UserPreferencesContext)
  if (!context) {
    throw new Error('useUserPreferences must be used within UserPreferencesProvider')
  }
  return context
}

export function UserPreferencesProvider({ children }) {
  // Style Preferences
  const [stylePreferences, setStylePreferences] = useState({
    favoritebrands: [],
    preferredStyles: [],
    preferredColors: [],
    sizePreferences: {}, // { brand: size }
    priceRange: { min: 0, max: 50000 }
  })

  // Recently Viewed Products
  const [recentlyViewed, setRecentlyViewed] = useState([])

  // Address Book
  const [addresses, setAddresses] = useState([])
  const [defaultAddressId, setDefaultAddressId] = useState(null)

  // Payment Methods
  const [paymentMethods, setPaymentMethods] = useState([])
  const [defaultPaymentId, setDefaultPaymentId] = useState(null)

  // Notification Preferences
  const [notificationPreferences, setNotificationPreferences] = useState({
    newArrivals: true,
    limitedEditions: true,
    priceDrops: true,
    communityUpdates: false,
    magazineContent: false,
    orderUpdates: true,
    promotions: true
  })

  // Load preferences from localStorage on mount
  useEffect(() => {
    const savedPreferences = localStorage.getItem('userPreferences')
    if (savedPreferences) {
      const parsed = JSON.parse(savedPreferences)
      setStylePreferences(parsed.stylePreferences || stylePreferences)
      setRecentlyViewed(parsed.recentlyViewed || [])
      setAddresses(parsed.addresses || [])
      setDefaultAddressId(parsed.defaultAddressId || null)
      setPaymentMethods(parsed.paymentMethods || [])
      setDefaultPaymentId(parsed.defaultPaymentId || null)
      setNotificationPreferences(parsed.notificationPreferences || notificationPreferences)
    }
  }, [])

  // Save preferences to localStorage whenever they change
  useEffect(() => {
    const preferences = {
      stylePreferences,
      recentlyViewed,
      addresses,
      defaultAddressId,
      paymentMethods,
      defaultPaymentId,
      notificationPreferences
    }
    localStorage.setItem('userPreferences', JSON.stringify(preferences))
  }, [stylePreferences, recentlyViewed, addresses, defaultAddressId, paymentMethods, defaultPaymentId, notificationPreferences])

  // Style Preferences Functions
  const updateStylePreferences = (newPreferences) => {
    setStylePreferences(prev => ({ ...prev, ...newPreferences }))
  }

  const addFavoriteBrand = (brand) => {
    setStylePreferences(prev => ({
      ...prev,
      favoritebrands: [...new Set([...prev.favoritebrands, brand])]
    }))
  }

  const removeFavoriteBrand = (brand) => {
    setStylePreferences(prev => ({
      ...prev,
      favoritebrands: prev.favoritebrands.filter(b => b !== brand)
    }))
  }

  const setSizeForBrand = (brand, size) => {
    setStylePreferences(prev => ({
      ...prev,
      sizePreferences: { ...prev.sizePreferences, [brand]: size }
    }))
  }

  // Recently Viewed Functions
  const addToRecentlyViewed = (product) => {
    setRecentlyViewed(prev => {
      const filtered = prev.filter(p => p.id !== product.id)
      const updated = [product, ...filtered].slice(0, 20) // Keep last 20 items
      return updated
    })
  }

  const clearRecentlyViewed = () => {
    setRecentlyViewed([])
  }

  // Address Book Functions
  const addAddress = (address) => {
    const newAddress = {
      id: Date.now().toString(),
      ...address,
      createdAt: new Date().toISOString()
    }
    setAddresses(prev => [...prev, newAddress])
    
    // Set as default if it's the first address
    if (addresses.length === 0) {
      setDefaultAddressId(newAddress.id)
    }
    
    return newAddress.id
  }

  const updateAddress = (id, updates) => {
    setAddresses(prev => prev.map(addr => 
      addr.id === id ? { ...addr, ...updates } : addr
    ))
  }

  const deleteAddress = (id) => {
    setAddresses(prev => prev.filter(addr => addr.id !== id))
    if (defaultAddressId === id) {
      const remaining = addresses.filter(addr => addr.id !== id)
      setDefaultAddressId(remaining.length > 0 ? remaining[0].id : null)
    }
  }

  const setDefaultAddress = (id) => {
    setDefaultAddressId(id)
  }

  const getDefaultAddress = () => {
    return addresses.find(addr => addr.id === defaultAddressId)
  }

  // Payment Methods Functions
  const addPaymentMethod = (paymentMethod) => {
    const newPayment = {
      id: Date.now().toString(),
      ...paymentMethod,
      createdAt: new Date().toISOString()
    }
    setPaymentMethods(prev => [...prev, newPayment])
    
    // Set as default if it's the first payment method
    if (paymentMethods.length === 0) {
      setDefaultPaymentId(newPayment.id)
    }
    
    return newPayment.id
  }

  const updatePaymentMethod = (id, updates) => {
    setPaymentMethods(prev => prev.map(payment => 
      payment.id === id ? { ...payment, ...updates } : payment
    ))
  }

  const deletePaymentMethod = (id) => {
    setPaymentMethods(prev => prev.filter(payment => payment.id !== id))
    if (defaultPaymentId === id) {
      const remaining = paymentMethods.filter(payment => payment.id !== id)
      setDefaultPaymentId(remaining.length > 0 ? remaining[0].id : null)
    }
  }

  const setDefaultPayment = (id) => {
    setDefaultPaymentId(id)
  }

  const getDefaultPayment = () => {
    return paymentMethods.find(payment => payment.id === defaultPaymentId)
  }

  // Notification Preferences Functions
  const updateNotificationPreferences = (newPreferences) => {
    setNotificationPreferences(prev => ({ ...prev, ...newPreferences }))
  }

  // Get size recommendation for brand
  const getSizeForBrand = (brand) => {
    return stylePreferences.sizePreferences[brand] || null
  }

  // Check if product matches user preferences
  const matchesPreferences = (product) => {
    const { favoritebrands, preferredStyles, preferredColors, priceRange } = stylePreferences
    
    let score = 0
    
    if (favoritebrands.includes(product.brand)) score += 3
    if (preferredStyles.some(style => product.category?.toLowerCase().includes(style.toLowerCase()))) score += 2
    if (preferredColors.some(color => product.name?.toLowerCase().includes(color.toLowerCase()))) score += 1
    if (product.price >= priceRange.min && product.price <= priceRange.max) score += 1
    
    return score
  }

  const value = {
    // Style Preferences
    stylePreferences,
    updateStylePreferences,
    addFavoriteBrand,
    removeFavoriteBrand,
    setSizeForBrand,
    getSizeForBrand,
    matchesPreferences,
    
    // Recently Viewed
    recentlyViewed,
    addToRecentlyViewed,
    clearRecentlyViewed,
    
    // Address Book
    addresses,
    defaultAddressId,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress,
    getDefaultAddress,
    
    // Payment Methods
    paymentMethods,
    defaultPaymentId,
    addPaymentMethod,
    updatePaymentMethod,
    deletePaymentMethod,
    setDefaultPayment,
    getDefaultPayment,
    
    // Notifications
    notificationPreferences,
    updateNotificationPreferences
  }

  return (
    <UserPreferencesContext.Provider value={value}>
      {children}
    </UserPreferencesContext.Provider>
  )
}
