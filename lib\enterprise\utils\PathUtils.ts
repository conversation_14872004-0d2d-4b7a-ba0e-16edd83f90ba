/**
 * TWL Enterprise Path Utilities
 * Handles path conversions and validations for the product system
 */

import path from 'path'

export class PathUtils {
  /**
   * Convert filesystem path to web-accessible path
   */
  static toWebPath(filePath: string): string {
    // Convert Windows backslashes to forward slashes
    let webPath = filePath.replace(/\\/g, '/')

    // Remove the absolute path prefix and keep only the relative path from public
    const publicIndex = webPath.indexOf('/public/')
    if (publicIndex !== -1) {
      webPath = webPath.substring(publicIndex + 7) // Remove '/public'
    } else if (webPath.startsWith('public/')) {
      // Handle relative paths that start with 'public/'
      webPath = webPath.substring(6) // Remove 'public'
    }

    // Ensure path starts with /
    if (!webPath.startsWith('/')) {
      webPath = '/' + webPath
    }

    return webPath
  }

  /**
   * Convert web path to filesystem path
   */
  static toFilePath(webPath: string, basePath: string = process.cwd()): string {
    // Remove leading slash
    const relativePath = webPath.startsWith('/') ? webPath.slice(1) : webPath
    
    // Join with base path and public directory
    return path.join(basePath, 'public', relativePath)
  }

  /**
   * Validate that a path is safe (no directory traversal)
   */
  static isSafePath(inputPath: string): boolean {
    const normalizedPath = path.normalize(inputPath)
    return !normalizedPath.includes('..')
  }

  /**
   * Extract filename without extension
   */
  static getFilenameWithoutExtension(filePath: string): string {
    const basename = path.basename(filePath)
    const extension = path.extname(basename)
    return basename.slice(0, -extension.length)
  }

  /**
   * Get file extension
   */
  static getFileExtension(filePath: string): string {
    return path.extname(filePath).toLowerCase().slice(1)
  }

  /**
   * Check if path is an image file
   */
  static isImageFile(filePath: string): boolean {
    const imageExtensions = ['jpg', 'jpeg', 'png', 'webp', 'gif', 'svg']
    const extension = this.getFileExtension(filePath)
    return imageExtensions.includes(extension)
  }

  /**
   * Check if path is a video file
   */
  static isVideoFile(filePath: string): boolean {
    const videoExtensions = ['mp4', 'webm', 'mov', 'avi', 'mkv']
    const extension = this.getFileExtension(filePath)
    return videoExtensions.includes(extension)
  }

  /**
   * Generate a clean URL slug from text
   */
  static slugify(text: string): string {
    return text
      .toLowerCase()
      .replace(/[^\w\s-]/g, '') // Remove special characters
      .replace(/[\s_-]+/g, '-') // Replace spaces and underscores with hyphens
      .replace(/^-+|-+$/g, '') // Remove leading/trailing hyphens
  }

  /**
   * Join URL paths safely
   */
  static joinUrlPaths(...paths: string[]): string {
    return paths
      .map(path => path.replace(/^\/+|\/+$/g, '')) // Remove leading/trailing slashes
      .filter(path => path.length > 0) // Remove empty paths
      .join('/')
  }

  /**
   * Ensure path has leading slash
   */
  static ensureLeadingSlash(path: string): string {
    return path.startsWith('/') ? path : '/' + path
  }

  /**
   * Remove leading slash
   */
  static removeLeadingSlash(path: string): string {
    return path.startsWith('/') ? path.slice(1) : path
  }

  /**
   * Get relative path between two paths
   */
  static getRelativePath(from: string, to: string): string {
    return path.relative(from, to).replace(/\\/g, '/')
  }

  /**
   * Check if a path exists and is accessible
   */
  static async pathExists(filePath: string): Promise<boolean> {
    try {
      const fs = await import('fs')
      await fs.promises.access(filePath)
      return true
    } catch {
      return false
    }
  }

  /**
   * Get directory name from path
   */
  static getDirname(filePath: string): string {
    return path.dirname(filePath)
  }

  /**
   * Get basename from path
   */
  static getBasename(filePath: string): string {
    return path.basename(filePath)
  }

  /**
   * Resolve path relative to base
   */
  static resolve(basePath: string, ...paths: string[]): string {
    return path.resolve(basePath, ...paths)
  }
}

export default PathUtils
