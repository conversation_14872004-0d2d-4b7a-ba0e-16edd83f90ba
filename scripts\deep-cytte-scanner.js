#!/usr/bin/env node

/**
 * DEEP CYTTE SCANNER
 * Recursively scans all folders to find every single product
 * Targets 5,480+ products from complete CYTTE structure
 */

const fs = require('fs')
const path = require('path')

function deepScanCytteStructure() {
  const cytteBasePath = path.join(process.cwd(), '--materials', 'shoes', '2. CYTTE')
  
  if (!fs.existsSync(cytteBasePath)) {
    console.log('❌ CYTTE folder not found at:', cytteBasePath)
    return []
  }
  
  console.log('🔍 DEEP SCANNING CYTTE structure for ALL products...')
  console.log('Base path:', cytteBasePath)
  
  const allProducts = []
  let foldersScanned = 0
  let productsFound = 0
  
  // Recursive function to scan all folders
  function scanFolderRecursively(folderPath, pathComponents = []) {
    foldersScanned++
    
    if (foldersScanned % 100 === 0) {
      console.log(`📁 Scanned ${foldersScanned} folders, found ${productsFound} products...`)
    }
    
    try {
      const items = fs.readdirSync(folderPath, { withFileTypes: true })
      
      // Check if this folder contains product images
      const imageFiles = items.filter(item => 
        item.isFile() && /\.(webp|jpg|jpeg|png)$/i.test(item.name)
      ).map(item => item.name)
      
      if (imageFiles.length > 0) {
        // This is a product folder!
        const product = createProductFromDeepPath(folderPath, pathComponents, imageFiles)
        if (product) {
          allProducts.push(product)
          productsFound++
        }
        return // Don't scan deeper if we found images
      }
      
      // Check for description files (another indicator of product folders)
      const hasDescription = items.some(item => 
        item.isFile() && item.name.toLowerCase().includes('description')
      )
      
      if (hasDescription && imageFiles.length === 0) {
        // Might be a product folder without images yet
        const product = createProductFromDeepPath(folderPath, pathComponents, [])
        if (product) {
          allProducts.push(product)
          productsFound++
        }
      }
      
      // Continue scanning subdirectories
      const subFolders = items.filter(item => item.isDirectory())
      
      subFolders.forEach(subFolder => {
        const subFolderPath = path.join(folderPath, subFolder.name)
        const newPathComponents = [...pathComponents, subFolder.name]
        scanFolderRecursively(subFolderPath, newPathComponents)
      })
      
    } catch (error) {
      console.log(`⚠️  Error scanning ${folderPath}:`, error.message)
    }
  }
  
  // Start recursive scan
  scanFolderRecursively(cytteBasePath, [])
  
  console.log(`\n📈 DEEP SCAN COMPLETE`)
  console.log(`Total folders scanned: ${foldersScanned}`)
  console.log(`Total products found: ${productsFound}`)
  console.log(`Products with data: ${allProducts.length}`)
  
  return allProducts
}

function createProductFromDeepPath(folderPath, pathComponents, imageFiles) {
  try {
    // Parse path components to extract 6-level hierarchy
    const relativePath = folderPath.replace(process.cwd(), '').replace(/\\/g, '/')
    const pathParts = pathComponents.filter(part => part.trim() !== '')
    
    if (pathParts.length < 2) {
      return null // Not enough path information
    }
    
    // Extract information from path
    const style = pathParts[0] || '1. SNEAKERS'
    const brand = pathParts[1] || 'Unknown'
    const lastFolder = pathParts[pathParts.length - 1]
    
    // Try to extract SKU from folder name
    const skuMatch = lastFolder.match(/^([A-Z0-9\-]+)(?:\s*--\s*(.+))?$/i)
    const skuCode = skuMatch ? skuMatch[1] : generateSKU()
    const brandReference = skuMatch ? (skuMatch[2] || extractBrandReference(pathParts)) : extractBrandReference(pathParts)
    
    // Determine gender from path
    const gender = determineGender(pathParts)
    
    // Determine model family
    const modelFamily = determineModelFamily(pathParts)
    
    // Detect collaboration
    const collaborator = detectCollaborationFromPath(pathParts, brandReference)
    const isCollaboration = !!collaborator
    
    // Map style
    const styleInfo = mapStyle(style)
    const brandInfo = mapBrand(brand)
    const genderInfo = mapGender(gender)
    const modelInfo = mapModelFamily(modelFamily)
    
    // Generate product ID
    const productId = `${styleInfo.id}-${brandInfo.id}-${genderInfo.id}-${modelInfo.id}-${skuCode.toLowerCase()}`
    
    // Generate image URLs
    const imageUrls = imageFiles.map(img => `${relativePath}/${img}`)
    
    return {
      id: productId,
      sku: skuCode,
      internalReference: brandReference,
      name: generateProductName(brandInfo.name, modelInfo.display, brandReference, collaborator),
      description: generateProductDescription(brandInfo.name, modelInfo.display, brandReference, isCollaboration),
      
      // Level 1: Style
      style: styleInfo.id,
      styleDisplay: styleInfo.display,
      styleCytteId: style,
      
      // Level 2: Brand
      brand: brandInfo.name,
      brandId: brandInfo.id,
      brandType: brandInfo.type,
      brandCytteId: brand,
      
      // Level 3: Gender
      gender: genderInfo.id.toUpperCase(),
      genderDisplay: genderInfo.display,
      genderPath: genderInfo.path,
      
      // Level 4: Model Family
      modelFamily: modelInfo.id,
      modelFamilyDisplay: modelInfo.display,
      modelVariant: modelInfo.id,
      modelCytteId: modelFamily,
      
      // Level 5: Collaboration
      isCollaboration,
      collaborationType: isCollaboration ? 'brand-x-brand' : null,
      collaborator,
      collaboratorDisplay: collaborator ? `${collaborator} x ${brandInfo.name}` : null,
      collabCytteId: collaborator || null,
      
      // Level 6: Product Folder
      productFolder: lastFolder,
      skuCode,
      brandReference,
      
      // Generated data
      imagePath: relativePath + '/',
      images: imageUrls,
      type: getProductType(styleInfo.id),
      subType: getProductSubType(modelInfo.display),
      
      // Default values
      price: generatePrice(brandInfo.type, isCollaboration),
      originalPrice: null,
      currency: 'MXN',
      colors: ['Disponible'],
      sizes: generateSizes(genderInfo.id),
      materials: generateMaterials(brandInfo.type),
      
      isLimited: isCollaboration || brandInfo.type === 'luxury',
      isExclusive: brandInfo.type === 'luxury',
      isVip: brandInfo.type === 'luxury' || isCollaboration,
      isNew: Math.random() > 0.7,
      
      stock: Math.floor(Math.random() * 20) + 1,
      availability: imageFiles.length > 0 ? 'in-stock' : 'pre-order',
      releaseDate: generateReleaseDate(),
      rating: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10,
      reviews: Math.floor(Math.random() * 500) + 10,
      
      tags: generateTags(brandInfo.name, modelInfo.display, collaborator, styleInfo.id),
      keywords: generateKeywords(brandInfo.name, modelInfo.display, brandReference),
      searchTerms: generateSearchTerms(brandInfo.name, modelInfo.display, collaborator),
      
      // Metadata
      pathComponents,
      folderDepth: pathParts.length,
      hasImages: imageFiles.length > 0,
      imageCount: imageFiles.length,
      
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }
    
  } catch (error) {
    console.log(`⚠️  Error creating product from path ${folderPath}:`, error.message)
    return null
  }
}

// Helper functions for path analysis
function determineGender(pathParts) {
  for (const part of pathParts) {
    if (part.includes('WOMEN') || part.includes('2. WOMEN')) return 'WOMEN'
    if (part.includes('MEN') || part.includes('3. MEN') || part.includes('1. MEN')) return 'MEN'
    if (part.includes('MIXTE') || part.includes('1. MIXTE')) return 'MIXTE'
  }
  return 'MIXTE' // Default to unisex
}

function determineModelFamily(pathParts) {
  const modelKeywords = {
    'JORDAN': 'jordan',
    'AIR FORCE': 'air-force',
    'DUNK': 'dunk',
    'BLAZER': 'blazer',
    'CORTEZ': 'cortez',
    'AIR MAX': 'air-max',
    'ACE': 'ace',
    'SCREENER': 'screener',
    'RYTHON': 'rython',
    'HORSEBIT': 'horsebit',
    'TENNIS': 'tennis'
  }
  
  for (const part of pathParts) {
    const upperPart = part.toUpperCase()
    for (const [keyword, family] of Object.entries(modelKeywords)) {
      if (upperPart.includes(keyword)) {
        return family
      }
    }
  }
  
  return 'standard'
}

function detectCollaborationFromPath(pathParts, brandReference) {
  const collabKeywords = {
    'OFF': 'Off-White',
    'WHITE': 'Off-White',
    'SACAI': 'Sacai',
    'TRAVIS': 'Travis Scott',
    'VIRGIL': 'Virgil Abloh',
    'UNION': 'Union LA',
    'BODEGA': 'Bodega',
    'CLOT': 'CLOT',
    'DIOR': 'Dior',
    'LV': 'Louis Vuitton',
    'GORETEX': 'Gore-Tex'
  }
  
  const searchText = (pathParts.join(' ') + ' ' + brandReference).toUpperCase()
  
  for (const [keyword, collaborator] of Object.entries(collabKeywords)) {
    if (searchText.includes(keyword)) {
      return collaborator
    }
  }
  
  return null
}

function extractBrandReference(pathParts) {
  const lastPart = pathParts[pathParts.length - 1]
  
  // Try to extract from last folder name
  const match = lastPart.match(/--\s*(.+)$/i)
  if (match) {
    return match[1].trim()
  }
  
  // Look for recognizable brand references in path
  const brandKeywords = ['Nike', 'Gucci', 'Dior', 'LV', 'Chanel', 'Balenciaga', 'Off-White', 'Sacai']
  
  for (const part of pathParts.reverse()) {
    for (const brand of brandKeywords) {
      if (part.toLowerCase().includes(brand.toLowerCase())) {
        return brand
      }
    }
  }
  
  return 'Classic'
}

function generateSKU() {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const numbers = '0123456789'
  
  let sku = ''
  // 3 letters
  for (let i = 0; i < 3; i++) {
    sku += letters[Math.floor(Math.random() * letters.length)]
  }
  // 3 numbers
  for (let i = 0; i < 3; i++) {
    sku += numbers[Math.floor(Math.random() * numbers.length)]
  }
  sku += '-'
  // 3 letters
  for (let i = 0; i < 3; i++) {
    sku += letters[Math.floor(Math.random() * letters.length)]
  }
  
  return sku
}

// Import helper functions from the main expander
const {
  mapStyle, mapBrand, mapGender, mapModelFamily,
  getProductType, getProductSubType, generatePrice,
  generateSizes, generateMaterials, generateReleaseDate,
  generateTags, generateKeywords, generateSearchTerms,
  generateProductName, generateProductDescription
} = require('./cytte-database-expander-helpers.js')

// Main execution function
async function runDeepScan() {
  console.log('🚀 DEEP CYTTE SCANNER STARTING...')
  console.log('=' .repeat(60))

  try {
    const products = deepScanCytteStructure()

    if (products.length === 0) {
      console.log('❌ No products found. Check CYTTE folder structure.')
      return
    }

    console.log(`\n💾 Saving ${products.length} products to database...`)

    // Save to JSON file
    const outputPath = path.join(process.cwd(), 'lib', 'data', 'cytte-deep-scan-products.json')
    fs.writeFileSync(outputPath, JSON.stringify(products, null, 2))

    console.log(`✅ Database saved to: ${outputPath}`)

    // Generate detailed summary
    const summary = generateDetailedSummary(products)
    console.log('\n📊 DEEP SCAN SUMMARY:')
    console.log('=' .repeat(60))
    console.log(summary)

    return products

  } catch (error) {
    console.error('❌ Error in deep scan:', error)
    throw error
  }
}

function generateDetailedSummary(products) {
  const stats = {
    total: products.length,
    withImages: products.filter(p => p.hasImages).length,
    withoutImages: products.filter(p => !p.hasImages).length,
    byStyle: {},
    byBrand: {},
    byGender: {},
    byDepth: {},
    collaborations: 0,
    limited: 0,
    luxury: 0,
    avgImagesPerProduct: 0
  }

  let totalImages = 0

  products.forEach(product => {
    // By style
    stats.byStyle[product.style] = (stats.byStyle[product.style] || 0) + 1

    // By brand
    stats.byBrand[product.brand] = (stats.byBrand[product.brand] || 0) + 1

    // By gender
    stats.byGender[product.gender] = (stats.byGender[product.gender] || 0) + 1

    // By folder depth
    stats.byDepth[product.folderDepth] = (stats.byDepth[product.folderDepth] || 0) + 1

    // Special counts
    if (product.isCollaboration) stats.collaborations++
    if (product.isLimited) stats.limited++
    if (product.brandType === 'luxury') stats.luxury++

    totalImages += product.imageCount
  })

  stats.avgImagesPerProduct = Math.round((totalImages / products.length) * 10) / 10

  return `
📈 TOTAL PRODUCTS FOUND: ${stats.total}
📸 Products with images: ${stats.withImages}
📁 Products without images: ${stats.withoutImages}
🖼️  Average images per product: ${stats.avgImagesPerProduct}

📊 BY STYLE:
${Object.entries(stats.byStyle)
  .sort(([,a], [,b]) => b - a)
  .map(([style, count]) => `  ${style}: ${count}`)
  .join('\n')}

👥 BY GENDER:
${Object.entries(stats.byGender)
  .map(([gender, count]) => `  ${gender}: ${count}`)
  .join('\n')}

🏢 TOP 15 BRANDS:
${Object.entries(stats.byBrand)
  .sort(([,a], [,b]) => b - a)
  .slice(0, 15)
  .map(([brand, count]) => `  ${brand}: ${count}`)
  .join('\n')}

📁 BY FOLDER DEPTH:
${Object.entries(stats.byDepth)
  .sort(([a], [b]) => parseInt(a) - parseInt(b))
  .map(([depth, count]) => `  Level ${depth}: ${count} products`)
  .join('\n')}

⭐ SPECIAL CATEGORIES:
  🤝 Collaborations: ${stats.collaborations}
  🌟 Limited Editions: ${stats.limited}
  💎 Luxury Items: ${stats.luxury}
  📊 Total Images: ${totalImages}
`
}

// Run if called directly
if (require.main === module) {
  runDeepScan().catch(console.error)
}

module.exports = {
  deepScanCytteStructure,
  createProductFromDeepPath,
  runDeepScan,
  generateDetailedSummary
}
