#!/usr/bin/env node

/**
 * CYTTE DATABASE EXPANDER
 * Automatically imports all 5,480+ products from CYTTE supplier structure
 * Following exact 6-level hierarchy
 */

const fs = require('fs')
const path = require('path')

// CYTTE folder structure mapping
const CYTTE_STRUCTURE_MAP = {
  // Level 1: Style mapping
  styles: {
    '1. SNEAKERS': 'sneakers',
    '2. SANDALS': 'sandals', 
    '3. FORMAL': 'formal',
    '4. CASUAL': 'casual',
    '5. KIDS': 'kids'
  },
  
  // Level 2: Brand mapping
  brands: {
    '1. NIKE Limited Edition': { id: 'nike', name: 'Nike', type: 'streetwear' },
    '2. ADIDAS Limited Edition': { id: 'adidas', name: 'Adidas', type: 'streetwear' },
    '3. HERMES': { id: 'hermes', name: 'Herm<PERSON>', type: 'luxury' },
    '4. GUCCI': { id: 'gucci', name: '<PERSON><PERSON>', type: 'luxury' },
    '5. DIOR': { id: 'dior', name: 'Dior', type: 'luxury' },
    '6. LV': { id: 'lv', name: '<PERSON>', type: 'luxury' },
    '7. BALENCIAGA': { id: 'balenciaga', name: 'Balenciaga', type: 'luxury' },
    '8. CHANEL': { id: 'chanel', name: 'Chanel', type: 'luxury' },
    '9. LOUBOUTIN': { id: 'louboutin', name: 'Christian Louboutin', type: 'luxury' },
    '10. OFF WHITE': { id: 'off-white', name: 'Off-White', type: 'streetwear' },
    '11. GIVENCHY': { id: 'givenchy', name: 'Givenchy', type: 'luxury' },
    '12. Maison MARGIELA': { id: 'maison-margiela', name: 'Maison Margiela', type: 'luxury' },
    '13. VALENTINO': { id: 'valentino', name: 'Valentino', type: 'luxury' },
    '14. PRADA': { id: 'prada', name: 'Prada', type: 'luxury' },
    '15. MIU MIU': { id: 'miu-miu', name: 'Miu Miu', type: 'luxury' },
    '16. BOTTEGA VENETA': { id: 'bottega-veneta', name: 'Bottega Veneta', type: 'luxury' },
    '17. BURBERRY': { id: 'burberry', name: 'Burberry', type: 'luxury' },
    '18. GOLDEN GOOSE': { id: 'golden-goose', name: 'Golden Goose', type: 'luxury' },
    '19. GAMA NORMAL': { id: 'gama-normal', name: 'Gama Normal', type: 'casual' },
    'Common Project': { id: 'common-project', name: 'Common Projects', type: 'minimalist' },
    '1. NIKE Collabs': { id: 'nike-collabs', name: 'Nike Collaborations', type: 'streetwear' },
    '9. UGG': { id: 'ugg', name: 'UGG', type: 'casual' },
    '13. CROCS': { id: 'crocs', name: 'Crocs', type: 'casual' },
    '15. BIRKENSTOCK': { id: 'birkenstock', name: 'Birkenstock', type: 'casual' }
  },
  
  // Level 3: Gender mapping
  genders: {
    '1. MIXTE': { id: 'mixte', display: 'Unisex', path: 'mixte' },
    '2. WOMEN': { id: 'women', display: 'Mujer', path: 'women' },
    '3. MEN': { id: 'men', display: 'Hombre', path: 'men' },
    'MIXTE': { id: 'mixte', display: 'Unisex', path: 'mixte' },
    'WOMEN': { id: 'women', display: 'Mujer', path: 'women' },
    'MEN': { id: 'men', display: 'Hombre', path: 'men' }
  },
  
  // Level 4: Model Family mapping
  modelFamilies: {
    '1. AIR FORCE': { id: 'air-force', display: 'Air Force' },
    '2. AIR JORDAN': { id: 'air-jordan', display: 'Air Jordan' },
    '3. CORTEZ': { id: 'cortez', display: 'Cortez' },
    '4. DUNK LOW': { id: 'dunk-low', display: 'Dunk Low' },
    '5. AIR MAX 1': { id: 'air-max-1', display: 'Air Max 1' },
    '6. AIR MAX 97': { id: 'air-max-97', display: 'Air Max 97' },
    '7. FOG': { id: 'fog', display: 'Fear of God' },
    '8. OFF WHITE': { id: 'off-white-collection', display: 'Off-White Collection' },
    '9. JACQUEMUS': { id: 'jacquemus', display: 'Jacquemus' },
    '10. BLAZER': { id: 'blazer', display: 'Blazer' },
    '11. VAPOR WAFFLE': { id: 'vapor-waffle', display: 'Vapor Waffle' },
    '12. LEBRON JAMES': { id: 'lebron', display: 'LeBron' },
    '1. JORDAN 1 LOW': { id: 'jordan-1-low', display: 'Jordan 1 Low' },
    '2. JORDAN 1 HIGH': { id: 'jordan-1-high', display: 'Jordan 1 High' },
    'ACE': { id: 'ace', display: 'Ace' },
    'Screener': { id: 'screener', display: 'Screener' },
    'Rython': { id: 'rython', display: 'Rython' },
    'Tennis 1977': { id: 'tennis-1977', display: 'Tennis 1977' },
    'Horsebit': { id: 'horsebit', display: 'Horsebit' }
  }
}

// Scan CYTTE folder structure
function scanCytteStructure() {
  const cytteBasePath = path.join(process.cwd(), '--materials', 'shoes', '2. CYTTE')
  
  if (!fs.existsSync(cytteBasePath)) {
    console.log('❌ CYTTE folder not found at:', cytteBasePath)
    return []
  }
  
  console.log('🔍 Scanning CYTTE structure...')
  console.log('Base path:', cytteBasePath)
  
  const products = []
  let totalProducts = 0
  
  // Level 1: Styles (1. SNEAKERS, 2. SANDALS, etc.)
  const styles = fs.readdirSync(cytteBasePath, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name)
  
  console.log(`\n📊 Found ${styles.length} main styles:`)
  styles.forEach(style => console.log(`   ${style}`))
  
  styles.forEach(styleName => {
    const stylePath = path.join(cytteBasePath, styleName)
    const styleId = CYTTE_STRUCTURE_MAP.styles[styleName] || styleName.toLowerCase()
    
    console.log(`\n🔍 Scanning style: ${styleName}`)
    
    // Level 2: Brands
    const brands = fs.readdirSync(stylePath, { withFileTypes: true })
      .filter(dirent => dirent.isDirectory())
      .map(dirent => dirent.name)
    
    console.log(`   Found ${brands.length} brands`)
    
    brands.forEach(brandName => {
      const brandPath = path.join(stylePath, brandName)
      const brandInfo = CYTTE_STRUCTURE_MAP.brands[brandName] || {
        id: brandName.toLowerCase().replace(/\s+/g, '-'),
        name: brandName,
        type: 'unknown'
      }
      
      // Check if brand has direct products or gender subfolders
      const brandContents = fs.readdirSync(brandPath, { withFileTypes: true })
        .filter(dirent => dirent.isDirectory())
        .map(dirent => dirent.name)
      
      // Check if contents are gender folders or direct products
      const hasGenderFolders = brandContents.some(item => 
        item.includes('MIXTE') || item.includes('WOMEN') || item.includes('MEN') ||
        item.startsWith('1.') || item.startsWith('2.') || item.startsWith('3.')
      )
      
      if (hasGenderFolders) {
        // Level 3: Gender folders
        brandContents.forEach(genderName => {
          const genderPath = path.join(brandPath, genderName)
          const genderInfo = CYTTE_STRUCTURE_MAP.genders[genderName] || {
            id: 'mixte',
            display: 'Unisex',
            path: 'mixte'
          }
          
          // Level 4: Model families or direct products
          const genderContents = fs.readdirSync(genderPath, { withFileTypes: true })
            .filter(dirent => dirent.isDirectory())
            .map(dirent => dirent.name)
          
          genderContents.forEach(modelName => {
            const modelPath = path.join(genderPath, modelName)
            const modelInfo = CYTTE_STRUCTURE_MAP.modelFamilies[modelName] || {
              id: modelName.toLowerCase().replace(/\s+/g, '-'),
              display: modelName
            }
            
            // Level 5 & 6: Check for collaborations and final products
            const modelContents = fs.readdirSync(modelPath, { withFileTypes: true })
              .filter(dirent => dirent.isDirectory())
              .map(dirent => dirent.name)
            
            modelContents.forEach(productFolder => {
              const productPath = path.join(modelPath, productFolder)
              
              // Check if this folder contains images (final product) or more subfolders
              const productContents = fs.readdirSync(productPath, { withFileTypes: true })
              const hasImages = productContents.some(item => 
                item.isFile() && /\.(webp|jpg|jpeg|png)$/i.test(item.name)
              )
              
              if (hasImages) {
                // This is a final product folder
                const product = createProductFromPath({
                  styleName,
                  styleId,
                  brandName,
                  brandInfo,
                  genderName,
                  genderInfo,
                  modelName,
                  modelInfo,
                  productFolder,
                  productPath,
                  images: productContents.filter(item => 
                    item.isFile() && /\.(webp|jpg|jpeg|png)$/i.test(item.name)
                  ).map(item => item.name)
                })
                
                products.push(product)
                totalProducts++
              } else {
                // This might be a collaboration folder, scan deeper
                productContents.filter(item => item.isDirectory()).forEach(subFolder => {
                  const subFolderPath = path.join(productPath, subFolder.name)
                  const subFolderContents = fs.readdirSync(subFolderPath, { withFileTypes: true })
                  const hasSubImages = subFolderContents.some(item => 
                    item.isFile() && /\.(webp|jpg|jpeg|png)$/i.test(item.name)
                  )
                  
                  if (hasSubImages) {
                    const product = createProductFromPath({
                      styleName,
                      styleId,
                      brandName,
                      brandInfo,
                      genderName,
                      genderInfo,
                      modelName,
                      modelInfo,
                      productFolder: subFolder.name,
                      productPath: subFolderPath,
                      collaborationFolder: productFolder,
                      images: subFolderContents.filter(item => 
                        item.isFile() && /\.(webp|jpg|jpeg|png)$/i.test(item.name)
                      ).map(item => item.name)
                    })
                    
                    products.push(product)
                    totalProducts++
                  }
                })
              }
            })
          })
        })
      } else {
        // Direct products under brand (no gender subfolders)
        brandContents.forEach(productFolder => {
          const productPath = path.join(brandPath, productFolder)
          const productContents = fs.readdirSync(productPath, { withFileTypes: true })
          const hasImages = productContents.some(item => 
            item.isFile() && /\.(webp|jpg|jpeg|png)$/i.test(item.name)
          )
          
          if (hasImages) {
            const product = createProductFromPath({
              styleName,
              styleId,
              brandName,
              brandInfo,
              genderName: 'MIXTE',
              genderInfo: { id: 'mixte', display: 'Unisex', path: 'mixte' },
              modelName: 'standard',
              modelInfo: { id: 'standard', display: 'Standard' },
              productFolder,
              productPath,
              images: productContents.filter(item => 
                item.isFile() && /\.(webp|jpg|jpeg|png)$/i.test(item.name)
              ).map(item => item.name)
            })
            
            products.push(product)
            totalProducts++
          }
        })
      }
    })
  })
  
  console.log(`\n📈 SCAN COMPLETE`)
  console.log(`Total products found: ${totalProducts}`)
  console.log(`Products with complete data: ${products.length}`)
  
  return products
}

// Create product object from folder path
function createProductFromPath(pathInfo) {
  const {
    styleName, styleId, brandName, brandInfo, genderName, genderInfo,
    modelName, modelInfo, productFolder, productPath, collaborationFolder, images
  } = pathInfo
  
  // Parse SKU and brand reference from product folder
  const skuMatch = productFolder.match(/^([A-Z0-9\-]+)(?:\s*--\s*(.+))?$/i)
  const skuCode = skuMatch ? skuMatch[1] : productFolder
  const brandReference = skuMatch ? (skuMatch[2] || brandInfo.name) : brandInfo.name
  
  // Detect collaboration
  const isCollaboration = !!(collaborationFolder || 
    brandReference.includes('x ') || 
    brandReference.includes(' x ') ||
    brandReference.includes('collab') ||
    productFolder.toLowerCase().includes('off') ||
    productFolder.toLowerCase().includes('sacai') ||
    productFolder.toLowerCase().includes('travis'))
  
  const collaborator = detectCollaborator(brandReference, collaborationFolder)
  
  // Generate product ID
  const productId = `${styleId}-${brandInfo.id}-${genderInfo.id}-${modelInfo.id}-${skuCode.toLowerCase()}`
  
  // Generate relative image paths
  const relativePath = productPath.replace(process.cwd(), '').replace(/\\/g, '/')
  const imageUrls = images.map(img => `${relativePath}/${img}`)
  
  return {
    id: productId,
    sku: skuCode,
    internalReference: brandReference,
    name: generateProductName(brandInfo.name, modelInfo.display, brandReference, collaborator),
    description: generateProductDescription(brandInfo.name, modelInfo.display, brandReference, isCollaboration),
    
    // Level 1: Style
    style: styleId,
    styleDisplay: capitalizeFirst(styleId),
    styleCytteId: styleName,
    
    // Level 2: Brand
    brand: brandInfo.name,
    brandId: brandInfo.id,
    brandType: brandInfo.type,
    brandCytteId: brandName,
    
    // Level 3: Gender
    gender: genderInfo.id.toUpperCase(),
    genderDisplay: genderInfo.display,
    genderPath: genderInfo.path,
    
    // Level 4: Model Family
    modelFamily: modelInfo.id,
    modelFamilyDisplay: modelInfo.display,
    modelVariant: modelInfo.id,
    modelCytteId: modelName,
    
    // Level 5: Collaboration
    isCollaboration,
    collaborationType: isCollaboration ? 'brand-x-brand' : null,
    collaborator,
    collaboratorDisplay: collaborator ? `${collaborator} x ${brandInfo.name}` : null,
    collabCytteId: collaborationFolder || null,
    
    // Level 6: Product Folder
    productFolder,
    skuCode,
    brandReference,
    
    // Generated data
    imagePath: relativePath + '/',
    images: imageUrls,
    type: getProductType(styleId),
    subType: getProductSubType(modelInfo.display),
    
    // Default values
    price: generatePrice(brandInfo.type, isCollaboration),
    originalPrice: null,
    currency: 'MXN',
    colors: ['Disponible'],
    sizes: generateSizes(genderInfo.id),
    materials: generateMaterials(brandInfo.type),
    
    isLimited: isCollaboration || brandInfo.type === 'luxury',
    isExclusive: brandInfo.type === 'luxury',
    isVip: brandInfo.type === 'luxury' || isCollaboration,
    isNew: Math.random() > 0.7,
    
    stock: Math.floor(Math.random() * 20) + 1,
    availability: 'in-stock',
    releaseDate: generateReleaseDate(),
    rating: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10,
    reviews: Math.floor(Math.random() * 500) + 10,
    
    tags: generateTags(brandInfo.name, modelInfo.display, collaborator, styleId),
    keywords: generateKeywords(brandInfo.name, modelInfo.display, brandReference),
    searchTerms: generateSearchTerms(brandInfo.name, modelInfo.display, collaborator),
    
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

// Helper functions
function detectCollaborator(brandReference, collaborationFolder) {
  const collabKeywords = {
    'off': 'Off-White',
    'white': 'Off-White',
    'sacai': 'Sacai',
    'travis': 'Travis Scott',
    'scott': 'Travis Scott',
    'virgil': 'Virgil Abloh',
    'abloh': 'Virgil Abloh',
    'union': 'Union LA',
    'bodega': 'Bodega',
    'clot': 'CLOT',
    'dior': 'Dior',
    'lv': 'Louis Vuitton',
    'goretex': 'Gore-Tex',
    'nike': 'Nike',
    'adidas': 'Adidas'
  }

  const searchText = (brandReference + ' ' + (collaborationFolder || '')).toLowerCase()

  for (const [keyword, collaborator] of Object.entries(collabKeywords)) {
    if (searchText.includes(keyword)) {
      return collaborator
    }
  }

  return null
}

function generateProductName(brand, model, reference, collaborator) {
  if (collaborator && collaborator !== brand) {
    return `${collaborator} x ${brand} ${model} "${reference}"`
  }
  return `${brand} ${model} "${reference}"`
}

function generateProductDescription(brand, model, reference, isCollaboration) {
  if (isCollaboration) {
    return `Colaboración exclusiva ${brand} ${model}. Edición limitada con detalles únicos.`
  }
  return `${brand} ${model} de alta calidad. Diseño elegante y materiales premium.`
}

function capitalizeFirst(str) {
  return str.charAt(0).toUpperCase() + str.slice(1)
}

function getProductType(style) {
  const typeMap = {
    'sneakers': 'sneaker',
    'sandals': 'sandal',
    'formal': 'formal',
    'casual': 'casual',
    'kids': 'kids'
  }
  return typeMap[style] || 'shoe'
}

function getProductSubType(model) {
  const subTypeMap = {
    'Jordan 1 High': 'high-top',
    'Jordan 1 Low': 'low-top',
    'Air Force': 'low-top',
    'Dunk Low': 'low-top',
    'Blazer': 'high-top',
    'Ace': 'low-top',
    'Horsebit': 'loafer',
    'Tennis 1977': 'low-top'
  }

  if (model.toLowerCase().includes('high')) return 'high-top'
  if (model.toLowerCase().includes('low')) return 'low-top'
  if (model.toLowerCase().includes('slide')) return 'slide'
  if (model.toLowerCase().includes('loafer')) return 'loafer'

  return subTypeMap[model] || 'sneaker'
}

function generatePrice(brandType, isCollaboration) {
  const basePrices = {
    'luxury': [15000, 45000],
    'streetwear': [3000, 25000],
    'casual': [1500, 8000],
    'athletic': [2000, 12000]
  }

  const [min, max] = basePrices[brandType] || [2000, 10000]
  let price = Math.floor(Math.random() * (max - min) + min)

  if (isCollaboration) {
    price = Math.floor(price * 1.5) // Collaborations cost more
  }

  // Round to nearest 100
  return Math.round(price / 100) * 100
}

function generateSizes(gender) {
  const sizeMaps = {
    'women': ['5', '5.5', '6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10'],
    'men': ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12', '13'],
    'mixte': ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12']
  }

  return sizeMaps[gender] || sizeMaps['mixte']
}

function generateMaterials(brandType) {
  const materialSets = {
    'luxury': ['Cuero Italiano Premium', 'Gamuza', 'Seda', 'Detalles Dorados'],
    'streetwear': ['Cuero Premium', 'Canvas', 'Mesh Transpirable', 'Suela de Goma'],
    'casual': ['Cuero', 'Textil', 'Suela Cómoda'],
    'athletic': ['Mesh Deportivo', 'Cuero Sintético', 'Suela Air', 'Tecnología Avanzada']
  }

  return materialSets[brandType] || ['Materiales de Calidad']
}

function generateReleaseDate() {
  const start = new Date('2023-01-01')
  const end = new Date('2024-12-31')
  const randomTime = start.getTime() + Math.random() * (end.getTime() - start.getTime())
  return new Date(randomTime).toISOString().split('T')[0]
}

function generateTags(brand, model, collaborator, style) {
  const tags = [
    brand.toLowerCase().replace(/\s+/g, '-'),
    model.toLowerCase().replace(/\s+/g, '-'),
    style
  ]

  if (collaborator) {
    tags.push(collaborator.toLowerCase().replace(/\s+/g, '-'))
    tags.push('collaboration')
  }

  return tags
}

function generateKeywords(brand, model, reference) {
  return [
    brand.toLowerCase(),
    model.toLowerCase(),
    reference.toLowerCase(),
    'zapatos',
    'calzado',
    'moda'
  ]
}

function generateSearchTerms(brand, model, collaborator) {
  const terms = [
    `${brand} ${model}`,
    brand,
    model
  ]

  if (collaborator) {
    terms.push(`${collaborator} ${brand}`)
    terms.push(collaborator)
  }

  return terms
}

// Main execution function
async function expandDatabase() {
  console.log('🚀 CYTTE DATABASE EXPANSION STARTING...')
  console.log('=' .repeat(50))

  try {
    const products = scanCytteStructure()

    if (products.length === 0) {
      console.log('❌ No products found. Check CYTTE folder structure.')
      return
    }

    console.log(`\n💾 Saving ${products.length} products to database...`)

    // Save to JSON file
    const outputPath = path.join(process.cwd(), 'lib', 'data', 'cytte-expanded-products.json')
    fs.writeFileSync(outputPath, JSON.stringify(products, null, 2))

    console.log(`✅ Database saved to: ${outputPath}`)

    // Generate summary
    const summary = generateSummary(products)
    console.log('\n📊 DATABASE EXPANSION SUMMARY:')
    console.log('=' .repeat(50))
    console.log(summary)

    return products

  } catch (error) {
    console.error('❌ Error expanding database:', error)
    throw error
  }
}

function generateSummary(products) {
  const stats = {
    total: products.length,
    byStyle: {},
    byBrand: {},
    byGender: {},
    collaborations: 0,
    limited: 0,
    luxury: 0
  }

  products.forEach(product => {
    // By style
    stats.byStyle[product.style] = (stats.byStyle[product.style] || 0) + 1

    // By brand
    stats.byBrand[product.brand] = (stats.byBrand[product.brand] || 0) + 1

    // By gender
    stats.byGender[product.gender] = (stats.byGender[product.gender] || 0) + 1

    // Special counts
    if (product.isCollaboration) stats.collaborations++
    if (product.isLimited) stats.limited++
    if (product.brandType === 'luxury') stats.luxury++
  })

  return `
Total Products: ${stats.total}

By Style:
${Object.entries(stats.byStyle).map(([style, count]) => `  ${style}: ${count}`).join('\n')}

By Gender:
${Object.entries(stats.byGender).map(([gender, count]) => `  ${gender}: ${count}`).join('\n')}

Top Brands:
${Object.entries(stats.byBrand)
  .sort(([,a], [,b]) => b - a)
  .slice(0, 10)
  .map(([brand, count]) => `  ${brand}: ${count}`)
  .join('\n')}

Special Categories:
  Collaborations: ${stats.collaborations}
  Limited Editions: ${stats.limited}
  Luxury Items: ${stats.luxury}
`
}

// Run if called directly
if (require.main === module) {
  expandDatabase().catch(console.error)
}

module.exports = {
  scanCytteStructure,
  createProductFromPath,
  expandDatabase,
  generateSummary
}
