'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import EnhancedVoiceSearch from './EnhancedVoiceSearch'
import VisualSearch from './VisualSearch'
import SmartWishlist from './SmartWishlist'
import SocialShopping from './SocialShopping'
import StyleMatchAI from './StyleMatchAI'

// Icons
const VoiceIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
  </svg>
)

const CameraIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
  </svg>
)

const SparkleIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
  </svg>
)

export default function AIFloatingButtons() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [activeModal, setActiveModal] = useState(null)
  const router = useRouter()

  // Handle search results from AI modals
  const handleSearch = (searchTerm) => {
    setActiveModal(null)
    setIsExpanded(false)
    router.push(`/shop?search=${encodeURIComponent(searchTerm)}`)
  }

  // Close modal handler
  const closeModal = () => {
    setActiveModal(null)
  }

  const aiFeatures = [
    {
      id: 'voice',
      name: 'Voice Search',
      icon: <VoiceIcon />,
      // TWL Brand: Neon Volt Lime
      bgColor: 'bg-[#BFFF00]',
      hoverColor: 'hover:bg-[#1F2937] hover:text-white',
      accentColor: 'border-[#BFFF00]/30',
      textColor: 'text-black',
      action: () => {
        setActiveModal('voice')
        setIsExpanded(false)
      }
    },
    {
      id: 'visual',
      name: 'Visual Search',
      icon: <CameraIcon />,
      // TWL Brand: Electric Orange
      bgColor: 'bg-orange-500',
      hoverColor: 'hover:bg-orange-600',
      accentColor: 'border-orange-400/30',
      textColor: 'text-white',
      action: () => {
        setActiveModal('visual')
        setIsExpanded(false)
      }
    },
    {
      id: 'style-match',
      name: 'Style Match AI',
      icon: <SparkleIcon />,
      // TWL Brand: Dark Grey
      bgColor: 'bg-gray-800',
      hoverColor: 'hover:bg-gray-700',
      accentColor: 'border-gray-600/30',
      textColor: 'text-white',
      action: () => {
        setActiveModal('style-match')
        setIsExpanded(false)
      }
    },
    {
      id: 'wishlist',
      name: 'Smart Wishlist',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      ),
      // TWL Brand: Neon Volt Lime (secondary)
      bgColor: 'bg-[#A6E600]',
      hoverColor: 'hover:bg-[#8FCC00]',
      accentColor: 'border-[#A6E600]/30',
      textColor: 'text-black',
      action: () => {
        setActiveModal('wishlist')
        setIsExpanded(false)
      }
    },
    {
      id: 'social',
      name: 'Social Shopping',
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      ),
      // TWL Brand: Electric Orange (secondary)
      bgColor: 'bg-orange-400',
      hoverColor: 'hover:bg-orange-500',
      accentColor: 'border-orange-300/30',
      textColor: 'text-white',
      action: () => {
        setActiveModal('social')
        setIsExpanded(false)
      }
    }
  ]

  return (
    <div className="fixed bottom-20 right-4 z-40 lg:bottom-6 lg:right-6 lg:z-50">
      {/* AI Feature Buttons */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.8 }}
            className="flex flex-col gap-3 mb-4"
          >
            {aiFeatures.map((feature, index) => (
              <motion.button
                key={feature.id}
                initial={{ opacity: 0, x: 50, scale: 0.8 }}
                animate={{ opacity: 1, x: 0, scale: 1 }}
                exit={{ opacity: 0, x: 50, scale: 0.8 }}
                transition={{
                  delay: index * 0.15,
                  type: "spring",
                  stiffness: 300,
                  damping: 25
                }}
                onClick={feature.action}
                className={`
                  group flex items-center gap-3 lg:gap-4 px-3 lg:px-5 py-2.5 lg:py-3.5 rounded-xl lg:rounded-2xl
                  ${feature.bgColor} ${feature.hoverColor}
                  ${feature.textColor} font-medium
                  shadow-md lg:shadow-lg hover:shadow-lg lg:hover:shadow-2xl
                  transition-all duration-400 hover:scale-105
                  backdrop-blur-md border ${feature.accentColor}
                  min-w-[140px] lg:min-w-[180px] relative overflow-hidden
                  before:absolute before:inset-0
                  before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300
                  ${(feature.id === 'voice' || feature.id === 'wishlist')
                    ? 'before:bg-black/5'
                    : 'before:bg-white/5'
                  }
                `}
                title={feature.name}
                whileHover={{
                  scale: 1.05,
                  transition: { duration: 0.2 }
                }}
                whileTap={{
                  scale: 0.98,
                  transition: { duration: 0.1 }
                }}
              >
                <div className="flex-shrink-0 relative">
                  <div className={`w-6 h-6 lg:w-8 lg:h-8 rounded-full flex items-center justify-center transition-colors duration-300 ${
                    (feature.id === 'voice' || feature.id === 'wishlist')
                      ? 'bg-black/10 group-hover:bg-black/20'
                      : 'bg-white/10 group-hover:bg-white/20'
                  }`}>
                    {feature.icon}
                  </div>
                  {/* Subtle glow effect */}
                  <div className={`absolute inset-0 rounded-full blur-sm transition-all duration-300 ${
                    (feature.id === 'voice' || feature.id === 'wishlist')
                      ? 'bg-black/5 group-hover:bg-black/10'
                      : 'bg-white/5 group-hover:bg-white/10'
                  }`}></div>
                </div>
                <div className="flex flex-col items-start">
                  <span className="text-xs lg:text-sm font-semibold whitespace-nowrap tracking-wide">
                    {feature.name}
                  </span>
                  <span className="text-xs opacity-75 font-normal hidden lg:block">
                    {feature.id === 'voice' && 'Speak naturally'}
                    {feature.id === 'visual' && 'Upload & find'}
                    {feature.id === 'style-match' && 'AI recommendations'}
                    {feature.id === 'wishlist' && 'Smart insights'}
                    {feature.id === 'social' && 'Community feed'}
                  </span>
                </div>
              </motion.button>
            ))}
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main Toggle Button - Mobile Responsive */}
      <motion.button
        onClick={() => setIsExpanded(!isExpanded)}
        className={`
          w-12 h-12 lg:w-16 lg:h-16 rounded-xl lg:rounded-2xl
          bg-gradient-to-br from-gray-900 to-black
          hover:from-gray-800 hover:to-gray-900
          text-[#BFFF00] shadow-lg lg:shadow-2xl hover:shadow-xl lg:hover:shadow-3xl
          flex items-center justify-center
          transition-all duration-400
          border border-gray-700/50 hover:border-[#BFFF00]/30
          backdrop-blur-md relative overflow-hidden
          before:absolute before:inset-0 before:bg-gradient-to-br
          before:from-[#BFFF00]/5 before:to-transparent
          before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300
        `}
        whileHover={{
          scale: 1.05,
          transition: { duration: 0.2 }
        }}
        whileTap={{
          scale: 0.95,
          transition: { duration: 0.1 }
        }}
        animate={{
          rotate: isExpanded ? 45 : 0,
          scale: isExpanded ? 1.1 : 1,
        }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 25
        }}
        title="AI Features"
      >
        <motion.div
          animate={{
            rotate: isExpanded ? -45 : 0,
            scale: isExpanded ? 1.1 : 1
          }}
          transition={{
            type: "spring",
            stiffness: 400,
            damping: 25
          }}
          className="relative z-10"
        >
          <AnimatePresence mode="wait">
            {isExpanded ? (
              <motion.svg
                key="close"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.5 }}
                className="w-5 h-5 lg:w-7 lg:h-7"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                strokeWidth={2.5}
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M6 18L18 6M6 6l12 12" />
              </motion.svg>
            ) : (
              <motion.svg
                key="ai"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                exit={{ opacity: 0, scale: 0.5 }}
                className="w-5 h-5 lg:w-7 lg:h-7"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                strokeWidth={2}
              >
                <path strokeLinecap="round" strokeLinejoin="round" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
              </motion.svg>
            )}
          </AnimatePresence>
        </motion.div>
      </motion.button>

      {/* Sophisticated Multi-Layer Pulse Animation */}
      {!isExpanded && (
        <>
          {/* Primary pulse */}
          <motion.div
            className="absolute inset-0 rounded-xl lg:rounded-2xl bg-[#BFFF00] opacity-20 pointer-events-none"
            animate={{
              scale: [1, 1.3, 1],
              opacity: [0.2, 0, 0.2]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          {/* Secondary pulse */}
          <motion.div
            className="absolute inset-0 rounded-xl lg:rounded-2xl bg-[#BFFF00] opacity-10 pointer-events-none"
            animate={{
              scale: [1, 1.6, 1],
              opacity: [0.1, 0, 0.1]
            }}
            transition={{
              duration: 3,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 0.5
            }}
          />
          {/* Tertiary pulse */}
          <motion.div
            className="absolute inset-0 rounded-xl lg:rounded-2xl bg-gradient-to-br from-[#BFFF00] to-yellow-300 opacity-5 pointer-events-none"
            animate={{
              scale: [1, 1.9, 1],
              opacity: [0.05, 0, 0.05]
            }}
            transition={{
              duration: 4,
              repeat: Infinity,
              ease: "easeInOut",
              delay: 1
            }}
          />
        </>
      )}

      {/* Sophisticated Backdrop */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ opacity: 0, backdropFilter: "blur(0px)" }}
            animate={{ opacity: 1, backdropFilter: "blur(8px)" }}
            exit={{ opacity: 0, backdropFilter: "blur(0px)" }}
            transition={{ duration: 0.3, ease: "easeInOut" }}
            className="fixed inset-0 bg-gradient-to-br from-black/10 via-gray-900/20 to-black/30 -z-10"
            onClick={() => setIsExpanded(false)}
          />
        )}
      </AnimatePresence>

      {/* AI Feature Modals */}
      <AnimatePresence>
        {activeModal === 'voice' && (
          <EnhancedVoiceSearch
            isOpen={true}
            onResults={(results, query) => handleSearch(query)}
            onClose={closeModal}
          />
        )}

        {activeModal === 'visual' && (
          <VisualSearch
            onSearch={handleSearch}
            onClose={closeModal}
          />
        )}

        {activeModal === 'wishlist' && (
          <SmartWishlist
            onClose={closeModal}
          />
        )}

        {activeModal === 'social' && (
          <SocialShopping
            onClose={closeModal}
          />
        )}

        {activeModal === 'style-match' && (
          <StyleMatchAI
            onClose={closeModal}
            onRecommendations={(recommendations) => {
              // Handle recommendations if needed
              console.log('Style recommendations:', recommendations)
            }}
          />
        )}
      </AnimatePresence>
    </div>
  )
}
