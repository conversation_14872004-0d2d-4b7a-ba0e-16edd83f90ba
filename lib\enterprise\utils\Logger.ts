/**
 * TWL Enterprise Logger
 * Comprehensive logging system for the enterprise product system
 */

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

export interface LogEntry {
  timestamp: Date
  level: LogLevel
  module: string
  message: string
  data?: any
  error?: Error
  requestId?: string
  userId?: string
}

export class Logger {
  private module: string
  private logLevel: LogLevel
  private enableConsole: boolean
  private enableFile: boolean
  private logBuffer: LogEntry[]

  constructor(module: string, options: {
    logLevel?: LogLevel
    enableConsole?: boolean
    enableFile?: boolean
  } = {}) {
    this.module = module
    this.logLevel = options.logLevel ?? LogLevel.INFO
    this.enableConsole = options.enableConsole ?? true
    this.enableFile = options.enableFile ?? false
    this.logBuffer = []
  }

  debug(message: string, data?: any): void {
    this.log(LogLevel.DEBUG, message, data)
  }

  info(message: string, data?: any): void {
    this.log(LogLevel.INFO, message, data)
  }

  warn(message: string, data?: any): void {
    this.log(LogLevel.WARN, message, data)
  }

  error(message: string, error?: Error | any): void {
    this.log(LogLevel.ERROR, message, undefined, error)
  }

  fatal(message: string, error?: Error | any): void {
    this.log(LogLevel.FATAL, message, undefined, error)
  }

  private log(level: LogLevel, message: string, data?: any, error?: Error | any): void {
    if (level < this.logLevel) return

    const entry: LogEntry = {
      timestamp: new Date(),
      level,
      module: this.module,
      message,
      data,
      error: error instanceof Error ? error : undefined
    }

    this.logBuffer.push(entry)

    if (this.enableConsole) {
      this.logToConsole(entry)
    }

    if (this.enableFile) {
      this.logToFile(entry)
    }
  }

  private logToConsole(entry: LogEntry): void {
    const timestamp = entry.timestamp.toISOString()
    const levelStr = LogLevel[entry.level].padEnd(5)
    const prefix = `[${timestamp}] ${levelStr} [${entry.module}]`
    
    const message = `${prefix} ${entry.message}`
    
    switch (entry.level) {
      case LogLevel.DEBUG:
        console.debug(message, entry.data)
        break
      case LogLevel.INFO:
        console.info(message, entry.data)
        break
      case LogLevel.WARN:
        console.warn(message, entry.data)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(message, entry.error || entry.data)
        break
    }
  }

  private logToFile(entry: LogEntry): void {
    // File logging implementation would go here
    // For now, just store in buffer
  }

  getLogBuffer(): LogEntry[] {
    return [...this.logBuffer]
  }

  clearLogBuffer(): void {
    this.logBuffer = []
  }
}

export default Logger
