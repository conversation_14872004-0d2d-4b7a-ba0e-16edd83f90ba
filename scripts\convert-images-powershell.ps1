# PowerShell Image Conversion Script for CYTTE Products
# Converts JPG images to WebP format using Windows built-in capabilities
# Alternative to Sharp library for immediate conversion

param(
    [string]$SourcePath = "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE",
    [string]$OutputPath = "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE-WEBP",
    [string]$PublicPath = "C:\2.MY_APP\TWL\V2\public\products",
    [int]$Quality = 85,
    [int]$MaxWidth = 800,
    [int]$MaxHeight = 800
)

Write-Host "🚀 Starting CYTTE Image Conversion (PowerShell Version)" -ForegroundColor Green
Write-Host "📂 Source: $SourcePath" -ForegroundColor Cyan
Write-Host "📂 Output: $OutputPath" -ForegroundColor Cyan
Write-Host "📂 Public: $PublicPath" -ForegroundColor Cyan
Write-Host ""

# Counters
$convertedCount = 0
$errorCount = 0
$skippedCount = 0

# Create output directories
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
    Write-Host "✅ Created output directory: $OutputPath" -ForegroundColor Green
}

if (!(Test-Path $PublicPath)) {
    New-Item -ItemType Directory -Path $PublicPath -Force | Out-Null
    Write-Host "✅ Created public directory: $PublicPath" -ForegroundColor Green
}

# Function to sanitize folder names for web
function Sanitize-WebName {
    param([string]$name)
    return $name.ToLower() -replace '[^a-z0-9]', '-' -replace '-+', '-' -replace '^-|-$', ''
}

# Function to convert image using .NET System.Drawing (if available)
function Convert-ImageToWebP {
    param(
        [string]$InputPath,
        [string]$OutputPath,
        [int]$Quality = 85,
        [int]$MaxWidth = 800,
        [int]$MaxHeight = 800
    )
    
    try {
        # For now, we'll copy the JPG files and rename them
        # This is a placeholder until we can install proper WebP conversion tools
        Copy-Item -Path $InputPath -Destination ($OutputPath -replace '\.webp$', '.jpg') -Force
        
        # Create a placeholder WebP file (actually JPG renamed)
        Copy-Item -Path $InputPath -Destination $OutputPath -Force
        
        return $true
    }
    catch {
        Write-Host "❌ Error converting $InputPath : $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Function to process a product folder
function Process-ProductFolder {
    param(
        [string]$ProductPath,
        [string]$ProductName,
        [string]$RelativePath
    )
    
    Write-Host "👟 Processing: $ProductName" -ForegroundColor Yellow
    
    # Create output directory structure
    $outputProductPath = Join-Path $OutputPath $RelativePath
    $publicProductPath = Join-Path $PublicPath (Sanitize-WebName $ProductName)
    
    if (!(Test-Path $outputProductPath)) {
        New-Item -ItemType Directory -Path $outputProductPath -Force | Out-Null
    }
    
    if (!(Test-Path $publicProductPath)) {
        New-Item -ItemType Directory -Path $publicProductPath -Force | Out-Null
    }
    
    # Get all JPG files
    $jpgFiles = Get-ChildItem -Path $ProductPath -Filter "*.jpg" -File
    $jpegFiles = Get-ChildItem -Path $ProductPath -Filter "*.jpeg" -File
    $allImages = $jpgFiles + $jpegFiles
    
    if ($allImages.Count -eq 0) {
        Write-Host "⚠️ No JPG images found in $ProductName" -ForegroundColor Yellow
        $script:skippedCount++
        return
    }
    
    $imageIndex = 1
    foreach ($image in $allImages) {
        $webpName = [System.IO.Path]::GetFileNameWithoutExtension($image.Name) + ".webp"
        $publicWebpName = "image-$imageIndex.webp"
        
        $outputImagePath = Join-Path $outputProductPath $webpName
        $publicImagePath = Join-Path $publicProductPath $publicWebpName
        
        # Convert image (placeholder conversion for now)
        if (Convert-ImageToWebP -InputPath $image.FullName -OutputPath $outputImagePath -Quality $Quality -MaxWidth $MaxWidth -MaxHeight $MaxHeight) {
            # Also copy to public directory with simplified naming
            Copy-Item -Path $image.FullName -Destination $publicImagePath -Force
            $script:convertedCount++
        } else {
            $script:errorCount++
        }
        
        $imageIndex++
    }
    
    # Copy description file if exists
    $descFile = Get-ChildItem -Path $ProductPath -Filter "description.txt" -File
    if ($descFile) {
        $outputDescPath = Join-Path $outputProductPath "description.txt"
        Copy-Item -Path $descFile.FullName -Destination $outputDescPath -Force
    }
}

# Main processing function
function Process-CytteFolder {
    Write-Host "🔍 Scanning CYTTE folder structure..." -ForegroundColor Cyan
    
    # Get all categories
    $categories = Get-ChildItem -Path $SourcePath -Directory
    
    foreach ($category in $categories) {
        if ($category.Name.StartsWith('.')) { continue }
        
        Write-Host "📂 Processing category: $($category.Name)" -ForegroundColor Magenta
        
        # Get all brands in category
        $brands = Get-ChildItem -Path $category.FullName -Directory
        
        foreach ($brand in $brands) {
            if ($brand.Name.StartsWith('.') -or $brand.Name.EndsWith('.txt')) { continue }
            
            Write-Host "🏷️ Processing brand: $($brand.Name)" -ForegroundColor Blue
            
            # Get all gender folders in brand
            $genders = Get-ChildItem -Path $brand.FullName -Directory
            
            foreach ($gender in $genders) {
                if ($gender.Name.StartsWith('.')) { continue }
                
                # Get all products in gender folder
                $products = Get-ChildItem -Path $gender.FullName -Directory
                
                foreach ($product in $products) {
                    if ($product.Name.StartsWith('.')) { continue }
                    
                    $relativePath = Join-Path $category.Name (Join-Path $brand.Name (Join-Path $gender.Name $product.Name))
                    Process-ProductFolder -ProductPath $product.FullName -ProductName $product.Name -RelativePath $relativePath
                }
            }
        }
    }
}

# Execute main processing
try {
    Process-CytteFolder
    
    Write-Host ""
    Write-Host "✅ Image conversion completed!" -ForegroundColor Green
    Write-Host "📊 Conversion Summary:" -ForegroundColor Cyan
    Write-Host "   Converted: $convertedCount images" -ForegroundColor Green
    Write-Host "   Errors: $errorCount images" -ForegroundColor Red
    Write-Host "   Skipped: $skippedCount products" -ForegroundColor Yellow
    
    # Generate report
    $report = @{
        conversion_completed_at = (Get-Date).ToString("yyyy-MM-ddTHH:mm:ssZ")
        total_converted = $convertedCount
        total_errors = $errorCount
        total_skipped = $skippedCount
        source_path = $SourcePath
        output_path = $OutputPath
        public_path = $PublicPath
        note = "Placeholder conversion - JPG files copied as WebP. Install proper WebP converter for actual format conversion."
    }
    
    $reportPath = "output\image-conversion-report.json"
    if (!(Test-Path "output")) {
        New-Item -ItemType Directory -Path "output" -Force | Out-Null
    }
    
    $report | ConvertTo-Json -Depth 3 | Out-File -FilePath $reportPath -Encoding UTF8
    Write-Host "📊 Report saved to: $reportPath" -ForegroundColor Cyan
    
    Write-Host ""
    Write-Host "🎯 Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Install proper WebP conversion tool (cwebp or Sharp)" -ForegroundColor White
    Write-Host "2. Re-run conversion with actual WebP encoding" -ForegroundColor White
    Write-Host "3. Update product indexing script to use converted images" -ForegroundColor White
    Write-Host "4. Test image loading on website" -ForegroundColor White
    
} catch {
    Write-Host "❌ Conversion failed: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "🎉 PowerShell conversion process completed!" -ForegroundColor Green
