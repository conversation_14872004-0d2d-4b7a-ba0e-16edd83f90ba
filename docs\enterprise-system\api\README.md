# TWL Enterprise API Documentation

**🌐 Complete API Reference for The White Laces Enterprise Product System**

This document provides comprehensive API documentation for the TWL Enterprise Product System, including all endpoints, request/response formats, authentication, and integration examples.

## 📋 Table of Contents

- [API Overview](#-api-overview)
- [Authentication](#-authentication)
- [Rate Limiting](#-rate-limiting)
- [Product Endpoints](#-product-endpoints)
- [Search Endpoints](#-search-endpoints)
- [System Endpoints](#-system-endpoints)
- [Error Handling](#-error-handling)
- [Response Formats](#-response-formats)
- [Integration Examples](#-integration-examples)

## 🎯 API Overview

The TWL Enterprise API provides RESTful endpoints for product management, search, and system operations with enterprise-grade performance and reliability.

### **🚀 API Specifications**

| Specification | Value |
|---------------|-------|
| **Base URL** | `https://api.twl.com/enterprise` |
| **Version** | `v1` |
| **Protocol** | HTTPS only |
| **Format** | JSON |
| **Rate Limit** | 100 requests/minute |
| **Response Time** | < 100ms average |

### **📊 Performance Guarantees**

- **Response Time**: < 100ms for 95% of requests
- **Availability**: 99.9% uptime SLA
- **Throughput**: 10,000+ concurrent requests
- **Cache Hit Rate**: > 95% for product requests

## 🔐 Authentication

### **API Key Authentication**

All API requests require authentication using an API key in the request header:

```http
Authorization: Bearer YOUR_API_KEY
```

### **Request Example**

```bash
curl -H "Authorization: Bearer twl_api_key_123456789" \
     -H "Content-Type: application/json" \
     https://api.twl.com/enterprise/products/sneakers-nike-mixte-air-force-bd7700-222
```

### **Authentication Errors**

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| `401` | `UNAUTHORIZED` | Missing or invalid API key |
| `403` | `FORBIDDEN` | API key lacks required permissions |
| `429` | `RATE_LIMIT_EXCEEDED` | Rate limit exceeded |

## ⚡ Rate Limiting

### **Rate Limit Rules**

| Endpoint Type | Limit | Window |
|---------------|-------|--------|
| **Product Retrieval** | 100 requests | 1 minute |
| **Search Operations** | 50 requests | 1 minute |
| **Batch Operations** | 10 requests | 1 minute |
| **System Operations** | 5 requests | 1 minute |

### **Rate Limit Headers**

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
X-RateLimit-Window: 60
```

### **Rate Limit Response**

```json
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Rate limit exceeded. Try again in 60 seconds.",
    "details": {
      "limit": 100,
      "window": 60,
      "retryAfter": 60
    }
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "requestId": "req_123456789"
  }
}
```

## 🛍️ Product Endpoints

### **Get Single Product**

Retrieve a single product by ID with complete details.

```http
GET /api/enterprise/products/{productId}
```

#### **Parameters**

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `productId` | string | Yes | Unique product identifier |

#### **Example Request**

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     https://api.twl.com/enterprise/products/sneakers-nike-mixte-air-force-bd7700-222
```

#### **Example Response**

```json
{
  "success": true,
  "data": {
    "id": "sneakers-nike-mixte-air-force-bd7700-222",
    "name": "Nike Air Force 1 x Gucci",
    "brand": {
      "id": "nike",
      "name": "Nike",
      "isLuxury": true,
      "tier": "luxury"
    },
    "category": {
      "id": "sneakers",
      "name": "Sneakers"
    },
    "pricing": {
      "currentPrice": 175,
      "originalPrice": 280,
      "discountPercent": 37,
      "currency": "MXN"
    },
    "inventory": {
      "inStock": true,
      "stockCount": 5,
      "sizes": [
        {
          "id": "42",
          "display": "42 EU",
          "inStock": true,
          "isPopular": true
        }
      ]
    },
    "media": {
      "images": [
        {
          "id": "img_0",
          "url": "/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- Gucci/o_1hfi0lgi514331ru41hu4km31qsp47.webp",
          "alt": "Nike Air Force 1 x Gucci - Main View",
          "isHero": true
        }
      ],
      "videos": [
        {
          "id": "vid_0",
          "url": "/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- Gucci/Video-nike-gucci-1.mp4",
          "thumbnail": "/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- Gucci/o_1hfi0lgi514331ru41hu4km31qsp47.webp"
        }
      ]
    },
    "details": {
      "description": "Premium luxury footwear with authentic materials and craftsmanship.",
      "features": ["Authentic materials", "Premium quality", "Limited edition"],
      "isLimitedEdition": true,
      "isCollaboration": true,
      "collaborationPartner": "Gucci"
    },
    "metadata": {
      "rating": 4.8,
      "reviewCount": 127,
      "isPopular": true,
      "isTrending": false
    }
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 45,
    "requestId": "req_123456789",
    "fromCache": true,
    "cacheLayer": "memory"
  }
}
```

### **Get Multiple Products (Batch)**

Retrieve multiple products by their IDs in a single request.

```http
POST /api/enterprise/products/batch
```

#### **Request Body**

```json
{
  "productIds": [
    "sneakers-nike-mixte-air-force-bd7700-222",
    "sneakers-adidas-men-yeezy-boost-350",
    "sandals-hermes-women-oran-h"
  ]
}
```

#### **Example Response**

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "sneakers-nike-mixte-air-force-bd7700-222",
        "name": "Nike Air Force 1 x Gucci",
        // ... complete product data
      }
    ],
    "requested": 3,
    "found": 1,
    "notFound": 2
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 78,
    "requestId": "req_123456790"
  }
}
```

## 🔍 Search Endpoints

### **Search Products**

Search products with advanced filtering, sorting, and pagination.

```http
GET /api/enterprise/products?q={query}&{filters}
```

#### **Query Parameters**

| Parameter | Type | Description | Example |
|-----------|------|-------------|---------|
| `q` | string | Search query | `Nike Air Force` |
| `brands` | string | Comma-separated brand IDs | `nike,adidas,gucci` |
| `categories` | string | Comma-separated category IDs | `sneakers,sandals` |
| `priceMin` | number | Minimum price filter | `100` |
| `priceMax` | number | Maximum price filter | `500` |
| `inStockOnly` | boolean | Only in-stock products | `true` |
| `isLimitedEdition` | boolean | Only limited edition products | `true` |
| `rating` | number | Minimum rating filter | `4.0` |
| `sortBy` | string | Sort field | `price`, `rating`, `popularity`, `newest` |
| `sortOrder` | string | Sort direction | `asc`, `desc` |
| `page` | number | Page number (1-based) | `1` |
| `pageSize` | number | Items per page (max 100) | `20` |

#### **Example Request**

```bash
curl -H "Authorization: Bearer YOUR_API_KEY" \
     "https://api.twl.com/enterprise/products?q=Nike&brands=nike&inStockOnly=true&sortBy=price&sortOrder=asc&page=1&pageSize=10"
```

#### **Example Response**

```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "sneakers-nike-mixte-air-force-bd7700-222",
        "name": "Nike Air Force 1 x Gucci",
        "pricing": {
          "currentPrice": 175,
          "originalPrice": 280
        },
        // ... other product fields
      }
    ],
    "total": 47,
    "page": 1,
    "pageSize": 10,
    "hasMore": true,
    "facets": {
      "brands": [
        { "name": "Nike", "count": 25 },
        { "name": "Adidas", "count": 15 }
      ],
      "categories": [
        { "name": "Sneakers", "count": 35 },
        { "name": "Sandals", "count": 12 }
      ],
      "priceRanges": [
        { "range": "100-200", "count": 20 },
        { "range": "200-300", "count": 15 }
      ],
      "inStock": {
        "available": 42,
        "outOfStock": 5
      }
    },
    "searchTime": 23
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 67,
    "requestId": "req_123456791"
  }
}
```

### **Get Categories**

Retrieve all available product categories with product counts.

```http
GET /api/enterprise/products/categories
```

#### **Example Response**

```json
{
  "success": true,
  "data": [
    {
      "id": "sneakers",
      "name": "Sneakers",
      "productCount": 245
    },
    {
      "id": "sandals",
      "name": "Sandals",
      "productCount": 156
    },
    {
      "id": "formal",
      "name": "Formal",
      "productCount": 96
    }
  ],
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 12,
    "requestId": "req_123456792"
  }
}
```

### **Get Brands**

Retrieve all available brands with product counts and metadata.

```http
GET /api/enterprise/products/brands
```

#### **Example Response**

```json
{
  "success": true,
  "data": [
    {
      "id": "nike",
      "name": "Nike",
      "isLuxury": true,
      "tier": "luxury",
      "productCount": 89
    },
    {
      "id": "gucci",
      "name": "Gucci",
      "isLuxury": true,
      "tier": "ultra-luxury",
      "productCount": 67
    }
  ],
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 15,
    "requestId": "req_123456793"
  }
}
```

## ⚙️ System Endpoints

### **System Status**

Get comprehensive system health and status information.

```http
GET /api/enterprise/system/status
```

#### **Example Response**

```json
{
  "success": true,
  "data": {
    "status": "healthy",
    "uptime": 86400000,
    "version": "1.0.0",
    "environment": "production",
    "components": {
      "loader": "healthy",
      "cache": "healthy",
      "api": "healthy",
      "scanner": "healthy"
    },
    "metrics": {
      "productsLoaded": 497,
      "cacheHitRate": 96.7,
      "averageResponseTime": 45,
      "errorRate": 0.02,
      "memoryUsage": 256,
      "cpuUsage": 15
    },
    "lastChecked": "2024-01-01T12:00:00Z"
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 8,
    "requestId": "req_123456794"
  }
}
```

### **Trigger System Scan**

Manually trigger a full product scan (admin only).

```http
POST /api/enterprise/system/scan
```

#### **Example Response**

```json
{
  "success": true,
  "data": {
    "message": "Scan triggered successfully",
    "scanId": "scan_123456789",
    "estimatedDuration": "2-5 minutes"
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 5,
    "requestId": "req_123456795"
  }
}
```

## ❌ Error Handling

### **Error Response Format**

All errors follow a consistent format:

```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human-readable error message",
    "details": {
      // Additional error context
    }
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 12,
    "requestId": "req_123456796"
  }
}
```

### **Error Codes**

| HTTP Status | Error Code | Description |
|-------------|------------|-------------|
| `400` | `INVALID_REQUEST` | Malformed request or invalid parameters |
| `400` | `VALIDATION_ERROR` | Request validation failed |
| `401` | `UNAUTHORIZED` | Missing or invalid authentication |
| `403` | `FORBIDDEN` | Insufficient permissions |
| `404` | `PRODUCT_NOT_FOUND` | Requested product does not exist |
| `429` | `RATE_LIMIT_EXCEEDED` | Rate limit exceeded |
| `500` | `INTERNAL_ERROR` | Internal server error |
| `503` | `SERVICE_UNAVAILABLE` | Service temporarily unavailable |

### **Error Examples**

#### **Product Not Found**

```json
{
  "success": false,
  "error": {
    "code": "PRODUCT_NOT_FOUND",
    "message": "Product not found: invalid-product-id",
    "details": {
      "productId": "invalid-product-id"
    }
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 15,
    "requestId": "req_123456797"
  }
}
```

#### **Validation Error**

```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid pagination parameters",
    "details": {
      "field": "pageSize",
      "value": 150,
      "constraint": "Maximum value is 100"
    }
  },
  "meta": {
    "timestamp": "2024-01-01T12:00:00Z",
    "duration": 3,
    "requestId": "req_123456798"
  }
}
```

## 📊 Response Formats

### **Standard Response Structure**

All successful API responses follow this structure:

```typescript
interface APIResponse<T> {
  success: true
  data: T
  meta: {
    timestamp: string      // ISO 8601 timestamp
    duration: number       // Response time in milliseconds
    requestId: string      // Unique request identifier
    version?: string       // API version
    fromCache?: boolean    // Whether response came from cache
    cacheLayer?: string    // Which cache layer was used
  }
}
```

### **Pagination Response**

Search and list endpoints include pagination metadata:

```typescript
interface PaginatedResponse<T> {
  success: true
  data: {
    items: T[]
    total: number
    page: number
    pageSize: number
    hasMore: boolean
    // ... other response data
  }
  meta: ResponseMeta
}
```

## 🔧 Integration Examples

### **JavaScript/TypeScript Integration**

```typescript
class TWLAPIClient {
  private apiKey: string
  private baseURL: string

  constructor(apiKey: string, baseURL = 'https://api.twl.com/enterprise') {
    this.apiKey = apiKey
    this.baseURL = baseURL
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const response = await fetch(`${this.baseURL}${endpoint}`, {
      ...options,
      headers: {
        'Authorization': `Bearer ${this.apiKey}`,
        'Content-Type': 'application/json',
        ...options.headers
      }
    })

    if (!response.ok) {
      throw new Error(`API Error: ${response.status} ${response.statusText}`)
    }

    return response.json()
  }

  async getProduct(productId: string) {
    return this.request(`/products/${productId}`)
  }

  async searchProducts(query: string, filters: any = {}, page = 1, pageSize = 20) {
    const params = new URLSearchParams({
      q: query,
      page: page.toString(),
      pageSize: pageSize.toString(),
      ...filters
    })
    
    return this.request(`/products?${params}`)
  }

  async getSystemStatus() {
    return this.request('/system/status')
  }
}

// Usage
const client = new TWLAPIClient('your-api-key')

// Get a product
const product = await client.getProduct('sneakers-nike-mixte-air-force-bd7700-222')

// Search products
const results = await client.searchProducts('Nike', {
  brands: 'nike',
  inStockOnly: 'true'
})
```

### **React Hook Integration**

```typescript
import { useState, useEffect } from 'react'

function useProduct(productId: string) {
  const [product, setProduct] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  useEffect(() => {
    const fetchProduct = async () => {
      try {
        setLoading(true)
        const response = await fetch(`/api/enterprise/products/${productId}`, {
          headers: {
            'Authorization': `Bearer ${process.env.NEXT_PUBLIC_API_KEY}`
          }
        })
        
        if (!response.ok) {
          throw new Error('Failed to fetch product')
        }
        
        const data = await response.json()
        setProduct(data.data)
      } catch (err) {
        setError(err.message)
      } finally {
        setLoading(false)
      }
    }

    if (productId) {
      fetchProduct()
    }
  }, [productId])

  return { product, loading, error }
}

// Usage in component
function ProductPage({ productId }: { productId: string }) {
  const { product, loading, error } = useProduct(productId)

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error: {error}</div>
  if (!product) return <div>Product not found</div>

  return (
    <div>
      <h1>{product.name}</h1>
      <p>Price: ${product.pricing.currentPrice}</p>
      <p>Brand: {product.brand.name}</p>
    </div>
  )
}
```

---

**🌐 This API documentation provides complete coverage of the TWL Enterprise Product System API with enterprise-grade standards for reliability, performance, and developer experience.**

**🚀 Ready to integrate with your applications and build amazing e-commerce experiences!**
