-- MULTI-VARIANT PRODUCT DATABASE SCHEMA
-- Designed for CYTTE supplier structure with video integration

-- Main products table (base product information)
CREATE TABLE products (
    id VARCHAR(255) PRIMARY KEY,
    base_sku VARCHAR(100) NOT NULL,
    name VARCHAR(500) NOT NULL,
    description TEXT,
    slug VARCHAR(255) UNIQUE NOT NULL,
    
    -- Hierarchy (6-level CYTTE structure)
    style VARCHAR(50) NOT NULL,
    style_display VARCHAR(100) NOT NULL,
    style_cytte_id VARCHAR(100),
    
    brand VARCHAR(100) NOT NULL,
    brand_id VARCHAR(50) NOT NULL,
    brand_type ENUM('luxury', 'premium', 'standard') DEFAULT 'standard',
    brand_cytte_id VARCHAR(100),
    
    gender ENUM('MEN', 'WOMEN', 'UNISEX') NOT NULL,
    gender_display VARCHAR(50) NOT NULL,
    gender_path VARCHAR(100),
    
    model_family VARCHAR(100) NOT NULL,
    model_family_display VARCHAR(100) NOT NULL,
    model_variant VARCHAR(100),
    model_cytte_id VARCHAR(100),
    
    -- Collaboration info
    is_collaboration BOOLEAN DEFAULT FALSE,
    collaboration_type ENUM('brand-x-brand', 'brand-x-artist', 'multiple-brand') NULL,
    collaborator VARCHAR(200) NULL,
    collaborator_display VARCHAR(300) NULL,
    
    -- Multi-variant info
    is_multi_variant BOOLEAN DEFAULT FALSE,
    variant_count INT DEFAULT 1,
    
    -- Product classification
    type VARCHAR(50) NOT NULL,
    sub_type VARCHAR(100),
    
    -- Pricing (base price)
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2) NULL,
    currency VARCHAR(3) DEFAULT 'MXN',
    
    -- Product flags
    is_limited BOOLEAN DEFAULT FALSE,
    is_exclusive BOOLEAN DEFAULT FALSE,
    is_vip BOOLEAN DEFAULT FALSE,
    is_new BOOLEAN DEFAULT FALSE,
    
    -- Inventory
    total_stock INT DEFAULT 0,
    availability ENUM('in-stock', 'out-of-stock', 'pre-order', 'discontinued') DEFAULT 'in-stock',
    release_date DATE,
    
    -- Ratings
    rating DECIMAL(3,2) DEFAULT 0.00,
    review_count INT DEFAULT 0,
    
    -- Media counts (aggregated from variants)
    total_images INT DEFAULT 0,
    total_videos INT DEFAULT 0,
    has_videos BOOLEAN DEFAULT FALSE,
    
    -- CYTTE source tracking
    cytte_base_path TEXT,
    last_scanned TIMESTAMP NULL,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_base_sku (base_sku),
    INDEX idx_brand (brand_id),
    INDEX idx_style (style),
    INDEX idx_gender (gender),
    INDEX idx_collaboration (is_collaboration),
    INDEX idx_multi_variant (is_multi_variant),
    INDEX idx_price (price),
    INDEX idx_availability (availability),
    INDEX idx_rating (rating),
    FULLTEXT idx_search (name, description)
);

-- Product variants table (individual variants of products)
CREATE TABLE product_variants (
    id VARCHAR(255) PRIMARY KEY,
    product_id VARCHAR(255) NOT NULL,
    full_sku VARCHAR(150) NOT NULL,
    base_sku VARCHAR(100) NOT NULL,
    variant_name VARCHAR(200) NOT NULL,
    variant_id VARCHAR(255) UNIQUE NOT NULL,
    slug VARCHAR(255) NOT NULL,
    
    description TEXT,
    
    -- Variant-specific pricing (can override product price)
    price DECIMAL(10,2) NULL,
    
    -- Variant availability
    stock INT DEFAULT 0,
    is_available BOOLEAN DEFAULT TRUE,
    
    -- Media counts
    image_count INT DEFAULT 0,
    video_count INT DEFAULT 0,
    has_description BOOLEAN DEFAULT FALSE,
    
    -- Collaboration info (variant level)
    collaborator VARCHAR(200) NULL,
    is_collaboration BOOLEAN DEFAULT FALSE,
    
    -- CYTTE source info
    folder_path TEXT,
    cytte_path_components JSON,
    
    -- Timestamps
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_full_sku (full_sku),
    INDEX idx_base_sku (base_sku),
    INDEX idx_variant_name (variant_name),
    INDEX idx_availability (is_available),
    INDEX idx_stock (stock)
);

-- Product variant colors table (many-to-many relationship)
CREATE TABLE variant_colors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    variant_id VARCHAR(255) NOT NULL,
    color_name VARCHAR(100) NOT NULL,
    color_code VARCHAR(7) NULL, -- Hex color code
    display_order INT DEFAULT 0,
    
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE,
    INDEX idx_variant_id (variant_id),
    INDEX idx_color_name (color_name),
    UNIQUE KEY unique_variant_color (variant_id, color_name)
);

-- Product images table
CREATE TABLE product_images (
    id INT AUTO_INCREMENT PRIMARY KEY,
    variant_id VARCHAR(255) NOT NULL,
    image_url VARCHAR(500) NOT NULL,
    original_filename VARCHAR(255),
    alt_text VARCHAR(300),
    display_order INT DEFAULT 0,
    is_primary BOOLEAN DEFAULT FALSE,
    
    -- Image metadata
    width INT NULL,
    height INT NULL,
    file_size INT NULL,
    format VARCHAR(10) NULL,
    
    -- CYTTE source
    cytte_original_path TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE,
    INDEX idx_variant_id (variant_id),
    INDEX idx_display_order (display_order),
    INDEX idx_primary (is_primary)
);

-- Product videos table
CREATE TABLE product_videos (
    id INT AUTO_INCREMENT PRIMARY KEY,
    variant_id VARCHAR(255) NOT NULL,
    video_url VARCHAR(500) NOT NULL,
    original_filename VARCHAR(255),
    title VARCHAR(300),
    description TEXT,
    display_order INT DEFAULT 0,
    
    -- Video metadata
    duration INT NULL, -- in seconds
    width INT NULL,
    height INT NULL,
    file_size BIGINT NULL,
    format VARCHAR(10) NULL,
    thumbnail_url VARCHAR(500) NULL,
    
    -- CYTTE source
    cytte_original_path TEXT,
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    FOREIGN KEY (variant_id) REFERENCES product_variants(id) ON DELETE CASCADE,
    INDEX idx_variant_id (variant_id),
    INDEX idx_display_order (display_order)
);

-- Product sizes table
CREATE TABLE product_sizes (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id VARCHAR(255) NOT NULL,
    size_value VARCHAR(20) NOT NULL,
    size_type ENUM('US', 'EU', 'UK', 'CM') DEFAULT 'US',
    display_order INT DEFAULT 0,
    is_available BOOLEAN DEFAULT TRUE,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_size_value (size_value),
    INDEX idx_availability (is_available),
    UNIQUE KEY unique_product_size (product_id, size_value, size_type)
);

-- Product materials table
CREATE TABLE product_materials (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id VARCHAR(255) NOT NULL,
    material_name VARCHAR(100) NOT NULL,
    material_type ENUM('upper', 'sole', 'lining', 'accent', 'other') DEFAULT 'other',
    display_order INT DEFAULT 0,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_material_type (material_type),
    UNIQUE KEY unique_product_material (product_id, material_name)
);

-- Product tags table (for SEO and filtering)
CREATE TABLE product_tags (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id VARCHAR(255) NOT NULL,
    tag_name VARCHAR(100) NOT NULL,
    tag_type ENUM('brand', 'style', 'color', 'collaboration', 'feature', 'seo') DEFAULT 'seo',
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_tag_name (tag_name),
    INDEX idx_tag_type (tag_type),
    UNIQUE KEY unique_product_tag (product_id, tag_name)
);

-- Product search terms table (for search optimization)
CREATE TABLE product_search_terms (
    id INT AUTO_INCREMENT PRIMARY KEY,
    product_id VARCHAR(255) NOT NULL,
    search_term VARCHAR(200) NOT NULL,
    relevance_score DECIMAL(3,2) DEFAULT 1.00,
    
    FOREIGN KEY (product_id) REFERENCES products(id) ON DELETE CASCADE,
    INDEX idx_product_id (product_id),
    INDEX idx_search_term (search_term),
    INDEX idx_relevance (relevance_score),
    UNIQUE KEY unique_product_search_term (product_id, search_term)
);

-- CYTTE scan log table (for tracking import processes)
CREATE TABLE cytte_scan_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    scan_type ENUM('full', 'incremental', 'single-product') NOT NULL,
    status ENUM('running', 'completed', 'failed') NOT NULL,
    
    -- Scan statistics
    folders_scanned INT DEFAULT 0,
    variants_found INT DEFAULT 0,
    products_created INT DEFAULT 0,
    products_updated INT DEFAULT 0,
    errors_count INT DEFAULT 0,
    
    -- Scan details
    base_path TEXT,
    scan_filters JSON NULL,
    error_details JSON NULL,
    
    -- Timing
    started_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP NULL,
    duration_seconds INT NULL,
    
    INDEX idx_scan_type (scan_type),
    INDEX idx_status (status),
    INDEX idx_started_at (started_at)
);

-- Create views for common queries

-- View for products with their primary variant info
CREATE VIEW products_with_primary_variant AS
SELECT 
    p.*,
    pv.variant_name as primary_variant_name,
    pv.variant_id as primary_variant_id,
    pv.price as variant_price,
    pv.stock as variant_stock,
    GROUP_CONCAT(DISTINCT vc.color_name ORDER BY vc.display_order) as colors
FROM products p
LEFT JOIN product_variants pv ON p.id = pv.product_id
LEFT JOIN variant_colors vc ON pv.id = vc.variant_id
WHERE pv.id = (
    SELECT id FROM product_variants pv2 
    WHERE pv2.product_id = p.id 
    ORDER BY pv2.created_at ASC 
    LIMIT 1
)
GROUP BY p.id, pv.id;

-- View for variant details with media counts
CREATE VIEW variant_details AS
SELECT 
    pv.*,
    p.name as product_name,
    p.brand,
    p.style,
    COUNT(DISTINCT pi.id) as actual_image_count,
    COUNT(DISTINCT pvi.id) as actual_video_count,
    GROUP_CONCAT(DISTINCT vc.color_name ORDER BY vc.display_order) as colors,
    (SELECT pi2.image_url FROM product_images pi2 WHERE pi2.variant_id = pv.id AND pi2.is_primary = TRUE LIMIT 1) as primary_image
FROM product_variants pv
JOIN products p ON pv.product_id = p.id
LEFT JOIN product_images pi ON pv.id = pi.variant_id
LEFT JOIN product_videos pvi ON pv.id = pvi.variant_id
LEFT JOIN variant_colors vc ON pv.id = vc.variant_id
GROUP BY pv.id;
