'use client'

import { useState } from 'react'
import Image from 'next/image'
import { motion } from 'framer-motion'
import SimpleProductCard from '@/components/ui/SimpleProductCard'
import Badge from '@/components/ui/Badge'
import Button from '@/components/ui/Button'

// Curated looks with lifestyle photos and matching products - Reference Design Inspired
const shopTheLookData = [
  {
    id: 'look-1',
    title: 'Gucci Street Luxury',
    lifestyleImage: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=800&h=600&fit=crop&crop=center',
    description: 'Luxury streetwear meets urban sophistication',
    products: [
      {
        id: 'prod-1',
        name: 'Punto G',
        brand: 'GUCCI',
        category: 'BUFANDA',
        price: 1490,
        originalPrice: 2300,
        image: 'https://images.unsplash.com/photo-1584464491033-06628f3a6b7b?w=400&h=400&fit=crop&crop=center',
        badge: 'BUFANDA',
        badgeColor: 'bg-amber-500'
      },
      {
        id: 'prod-2',
        name: '<PERSON> <PERSON><PERSON>',
        brand: 'GUCCI',
        category: 'TENIS',
        price: 3490,
        originalPrice: 6800,
        image: 'https://images.unsplash.com/photo-1560769629-975ec94e6a86?w=400&h=400&fit=crop&crop=center',
        badge: 'TENIS',
        badgeColor: 'bg-blue-500'
      }
    ]
  },
  {
    id: 'look-2',
    title: 'Urban Minimalist',
    lifestyleImage: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=800&h=600&fit=crop&crop=center',
    description: 'Clean lines and modern street style',
    products: [
      {
        id: 'prod-3',
        name: 'Air Jordan 1 High',
        brand: 'JORDAN',
        category: 'SNEAKERS',
        price: 2890,
        originalPrice: 3200,
        image: 'https://images.unsplash.com/photo-1511556532299-8f662fc26c06?w=400&h=400&fit=crop&crop=center',
        badge: 'SNEAKERS',
        badgeColor: 'bg-red-500'
      },
      {
        id: 'prod-4',
        name: 'Dunk Low Panda',
        brand: 'NIKE',
        category: 'SNEAKERS',
        price: 1890,
        originalPrice: 2100,
        image: 'https://images.unsplash.com/photo-1606107557195-0e29a4b5b4aa?w=400&h=400&fit=crop&crop=center',
        badge: 'SNEAKERS',
        badgeColor: 'bg-green-500'
      }
    ]
  }
]

export default function ShopTheLook() {
  const [currentLook, setCurrentLook] = useState(0)
  const [hoveredProduct, setHoveredProduct] = useState(null)

  const look = shopTheLookData[currentLook]

  const handleAddToCart = (product) => {
    console.log('Adding to cart:', product)
    // Add to cart logic here
  }

  const nextLook = () => {
    setCurrentLook((prev) => (prev + 1) % shopTheLookData.length)
  }

  const prevLook = () => {
    setCurrentLook((prev) => (prev - 1 + shopTheLookData.length) % shopTheLookData.length)
  }

  return (
    <div className="w-full">
      {/* 4 Equal Vertical Rectangle Cards - Reference Design Layout */}
      <motion.div
        key={currentLook}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6"
      >
        {/* Card 1 - First Lifestyle Image */}
        <div className="relative aspect-[3/4] rounded-2xl overflow-hidden bg-light-gray dark:bg-neutral-800 group cursor-pointer">
          <Image
            src={look.lifestyleImage}
            alt={`${look.title} - Lifestyle 1`}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 1024px) 50vw, 25vw"
            priority
          />

          {/* Overlay with look info */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />
          <div className="absolute bottom-4 left-4 right-4 text-white">
            <h3 className="text-sm lg:text-base font-godber font-bold mb-1">
              {look.title}
            </h3>
            <p className="text-xs font-poppins opacity-90">
              {look.description}
            </p>
          </div>
        </div>

        {/* Card 2 - Second Lifestyle Image */}
        <div className="relative aspect-[3/4] rounded-2xl overflow-hidden bg-light-gray dark:bg-neutral-800 group cursor-pointer">
          <Image
            src={shopTheLookData[(currentLook + 1) % shopTheLookData.length].lifestyleImage}
            alt={`${look.title} - Lifestyle 2`}
            fill
            className="object-cover group-hover:scale-105 transition-transform duration-300"
            sizes="(max-width: 1024px) 50vw, 25vw"
          />

          {/* Subtle overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
        </div>

        {/* Card 3 - First Product */}
        <SimpleProductCard
          product={{
            ...look.products[0],
            gender: 'MIXTE'
          }}
          index={2}
        />

        {/* Card 4 - Second Product */}
        <SimpleProductCard
          product={{
            ...look.products[1],
            gender: 'MIXTE'
          }}
          index={3}
        />
      </motion.div>

      {/* Navigation Controls */}
      <div className="flex justify-center items-center mt-8 gap-4">
        <button
          onClick={prevLook}
          className="w-10 h-10 bg-border-gray dark:bg-neutral-700 hover:bg-lime-green hover:text-pure-black rounded-full flex items-center justify-center transition-colors duration-200"
        >
          ←
        </button>

        {/* Look Indicators */}
        <div className="flex gap-2">
          {shopTheLookData.map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentLook(index)}
              className={`w-3 h-3 rounded-full transition-colors duration-200 ${
                index === currentLook
                  ? 'bg-lime-green'
                  : 'bg-border-gray dark:bg-neutral-600'
              }`}
            />
          ))}
        </div>

        <button
          onClick={nextLook}
          className="w-10 h-10 bg-border-gray dark:bg-neutral-700 hover:bg-lime-green hover:text-pure-black rounded-full flex items-center justify-center transition-colors duration-200"
        >
          →
        </button>
      </div>
    </div>
  )
}
