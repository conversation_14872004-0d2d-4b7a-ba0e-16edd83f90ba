Below is your AI Prompt Library for Developers — a comprehensive, categorized collection of ready-to-use prompts tailored to building The White Laces (TWL) e-commerce platform using Next.js , Tailwind CSS , and hosted on Vercel , with a focus on glassmorphism , mobile-first UX , and Mexico-first strategy .

This library will help you and your team use AI tools like Cursor , Claude 4 , or GPT-4 to generate precise, high-quality code snippets that align with your brand identity, technical stack, and user experience goals.

🧠 The White Laces – AI Prompt Library
Next.js | Tailwind CSS | Glassmorphic UI | Mexico-First Strategy
👟 Luxury Footwear Meets Gen Z Culture | E-commerce Platform 

📚 Table of Contents
🔹 Atomic Components
🎨 Styling & Theme Logic
🧩 Feature-Based Components
🌐 Localization & Multi-Language
🔍 Search & AI Features
🧠 State Management & Utilities
🛠️ APIs & Backend Integration
🧪 Testing & QA Prompts
📦 Component Documentation
🧱 Performance Optimization


1. 🔹 Atomic Components
Use these prompts to generate small but essential UI elements.

🧾 Button Component

Generate a reusable Button component for TWL using React and Tailwind CSS.
Support variants: primary, secondary, ghost.
Use the following colors:
- Primary: #FF1C53 (Neon Pulse)
- Secondary: Mist Gray + Cyber Blue border
Include hover animation, active state, disabled styling.
Make it accessible with ARIA labels.


📝 Input Field

Create a floating label input field in Tailwind CSS for TWL.
Style: Mist Gray background, frosted overlay, light text.
Include placeholder behavior and focus indicator.
Add error message slot for validation.


🎁 Badge System

Design a badge system for TWL with types:
- Limited Edition (Neon Pulse glow)
- VIP Exclusive (Gold Dust background)
- On Sale (Velvet Red + strikethrough)
- New Arrival (Cyber Blue border)

Use Tailwind animations for neon pulse and scale effects.
Ensure accessibility via screen reader support.


📦 Card Layout

Build a glassmorphic ProductCard component for TWL.
Include image, title, price, and "Add to Cart" button.
Use backdrop blur, soft shadows, and Frosted Overlay style.
Support dark/light mode.


🧾 Modal Dialog

Write a modal component for TWL using Tailwind CSS.
Include:
- Frosted overlay background
- Fade-in animation
- Scrollable content area
- Dismiss on click outside/escape key
- Reusable with children props


2. 🎨 Styling & Theme Logic
Use these prompts to build theme-aware components and utilities.

🌗 Theme Provider Hook

Implement a theme context provider for TWL using React Context API.
Allow switching between dark and light themes.
Persist preference in localStorage.
Apply theme class to <html> tag.
Include hook: useTheme()

🌙 Dark Mode Toggle

Create a dark mode toggle component for TWL.
Use a sun/moon icon.
Animate scale-up on press.
Save preference in localStorage.

🌍 Language Switcher

Generate a language switcher component for TWL.
Support es-MX (default), en-US, pt-BR.
Show flags as icons.
Update URL prefix (e.g., /es-MX/home).

🎨 Color Utility File

Create a color utility file for TWL.
Define all brand colors:
- Fog Black (#14161A)
- Mist Gray (#1E2127)
- Neon Pulse (#FF1C53)
- Cyber Blue (#00F9FF)
- Gold Dust (#FFD166)

Export them as constants for reuse across components.


3. 🧩 Feature-Based Components
These prompts help generate larger UI components used throughout the site.

📦 Product Card

Generate a ProductCard component for TWL using Tailwind CSS.
Include:
- Image with lazy loading
- Title and price
- Add to Cart button
- Hover elevation increase
- Neon glow on limited edition tags
Make it responsive from mobile to desktop.

📸 UGC Post Card

Create a UGCPostCard component for TWL.
Show:
- User avatar
- Shoe photo
- Caption
- Like/share buttons
Style with Soft Cloud background and subtle shadow.
Make share buttons link to Instagram/TikTok.

🧮 Product Grid

Implement a ProductGrid component for TWL.
Support:
- 2-column layout on mobile
- 3–4 columns on desktop
- Infinite scroll
- Skeleton loader before images load
Use Tailwind’s grid and animation utilities.

🔍 Search Bar with Voice Support

Build a search bar for TWL with voice input option.
Features:
- Text input with placeholder
- Microphone icon
- Click opens browser speech recognition
- Returns shoe suggestions
Style: Mist Gray background with frosted look.

🧠 Wishlist Section

Create a WishlistSection for TWL.
Allow users to create multiple lists.
Each list shows product cards with edit/delete/share options.
Support push notifications when items go on sale.
Use localStorage or Firebase backend.


4. 🌐 Localization & Multi-Language
Prompts for generating internationalization-ready components.

🇲🇽 Mexican Spanish Translation File

Generate a JSON translation file for TWL in Mexican Spanish.
Keys: welcome, add_to_cart, wishlist, limited_edition, etc.
Values: Localized strings appropriate for LATAM culture.

🌍 i18n Helper Functions

Write helper functions for TWL's i18n system.
Functions:
- t(key, locale) → returns translated string
- getLocale() → detects browser language
- setLocale(locale) → saves to localStorage
Use next-i18next structure.

🏷️ Language Switcher Dropdown

Implement a dropdown language switcher for TWL.
Show flags for es-MX, en-US, pt-BR.
Change language and reload page.
Update HTML lang attribute.


5. 🔍 Search & AI Features
Prompts for integrating smart features into TWL.

🎙️ Voice Search Handler

Create a voice search handler for TWL.
On microphone button click, activate browser speech recognition.
Return transcribed query and pass to search results.
Use Web Speech API.

🖼️ Visual Search Upload

Generate a visual search upload component for TWL.
User can drag/drop an image or select from camera.
Upload image → call similarity search API.
Display matching shoes below.

🧠 Smart Recommendation Engine

Write a recommendation engine function for TWL.
Input: current product viewed
Output: list of similar products
Use metadata like brand, type, gender
Include TypeScript types if applicable


6. 🧠 State Management & Utilities
Prompts for managing app logic and data flow.

🧠 UseWishlist Hook

Implement a custom hook `useWishlist()` for TWL.
Support:
- Adding/removing items
- Multiple wishlists
- Sync with localStorage
- Shareable URLs

💰 Format Price Function

Write a function called `formatPrice(amount, locale)` for TWL.
Support:
- MXN (es-MX)
- COP (Colombia)
- CLP (Chile)
- USD (fallback)
Format currency symbol based on region.

🔗 Slugify Function

Create a slugify function for TWL.
Convert product names to URL-friendly slugs.
Example: "Nike Air Max 97" → "nike-air-max-97"
Remove special characters and make lowercase.


7. 🛠️ APIs & Backend Integration
Use these prompts to generate backend integrations.

🛒 Stripe Checkout API

Write a Stripe checkout API route for TWL.
Use Next.js API Routes.
Support guest checkout.
Accept cart data and return session ID.

📦 Cloudinary Image Loader

Generate a function for TWL that uploads and optimizes product images via Cloudinary.
Support:
- Drag-and-drop upload
- Image preview
- Return optimized URL
Use fetch + FormData.

🧭 Algolia Search Setup

Set up a search integration for TWL using Algolia.
Index fields: name, brand, category, gender, size
Generate:
- SearchBar component
- Results display
- Debounced input


8. 🧪 Testing & QA Prompts
Prompts for writing unit tests, end-to-end tests, and linting rules.

✅ Jest Unit Test for Button

Write a Jest unit test for the TWL Button component.
Test:
- Renders correctly
- Fires onClick event
- Applies correct variant classes
Use React Testing Library.

🧪 Cypress E2E Test for Cart

Generate a Cypress E2E test for the TWL cart flow.
Test:
- Add item to cart
- View cart
- Update quantity
- Proceed to checkout
Use fixtures for mock product data.

🧹 ESLint Rules for TWL

Create ESLint config for TWL project.
Rules:
- Enforce Tailwind order
- No console.log
- Require alt attributes on images
- Enforce TypeScript usage
- Ensure no unused variables


9. 📦 Component Documentation
Prompts for auto-documentation and internal wiki entries.

📄 Storybook Entry for ProductCard

Generate a Storybook entry for TWL’s ProductCard component.
Include:
- Default card
- With limited edition badge
- In dark mode
- With quick view modal
Use MDX format for documentation.

📝 Notion Page Template for Components

Create a Notion page template for documenting each TWL component.
Sections:
- Purpose
- Props
- Code Sample
- Usage Notes
- Accessibility Info
- Localization Ready?

📋 JSDoc for Button.jsx

Add JSDoc comments to the TWL Button component.
Document:
- All props
- Supported variants
- Example usage
- Accessibility notes


10. 🧱 Performance Optimization
Prompts for optimizing performance, SEO, and accessibility.

⚡ Optimize ProductCard for LCP

Optimize the ProductCard component for fast Largest Contentful Paint (LCP).
Use:
- Lazy loading
- Preload critical resources
- Reduce JS payload
- Compressed image formats

🧢 Improve TTI with Code Splitting

Suggest ways to improve Time to Interactive (TTI) in TWL.
Recommend:
- Dynamic imports
- Code splitting by route
- Remove unnecessary polyfills
- Use SWR for data fetching

🧠 Add Skeleton Loaders

Generate skeleton loaders for TWL product grids.
Use Tailwind CSS with shimmer effect.
Fade out when real images load.

📊 Run Lighthouse Audit

Write a script to run Lighthouse audits on all TWL pages.
Check:
- Performance score
- SEO
- Accessibility
- Best Practices
Output results to CSV or JSON.


🧰 Bonus: Prompt Engineering Tips
✅ Best Practices for Writing AI Prompts

Tip,                    Description
📌 Be Specific,         “Glassmorphic button with Neon Pulse border” vs “a button”
🧠 Ask for Explanation, “Also explain how this works.”
🧩 Include Constraints, “Don’t use inline styles.”
🧪 Request Refinement,  “Can you fix this for mobile?”
🧠 Use Role Prompting,  “Act as a senior frontend dev at Apple designing a luxury e-commerce site.”

