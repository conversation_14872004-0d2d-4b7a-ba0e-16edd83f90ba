# 🎨 TWL REFERENCE DESIGN MASTER COLOR PALETTE (2025)

**Based on homepage reference design - Clean, Minimal, Luxury**

This is the **MASTER COLOR PALETTE** for The White Laces e-commerce website, extracted from the reference design and optimized for all pages and components.

## 🎯 **PRIMARY BRAND COLORS**

### Lime Green (Brand Accent)
- **Primary**: `#BFFF00` - Main brand color for buttons, highlights, CTAs
- **Primary Dark**: `#A6E600` - Hover states, pressed buttons
- **Primary Light**: `#CCFF33` - Light accents, subtle highlights

**Usage**: CTA buttons, active states, brand elements, accent highlights

---

## 🤍 **REFERENCE DESIGN NEUTRALS**

### Background Colors
- **Pure White**: `#FFFFFF` - Main background, card backgrounds
- **Light Gray**: `#F8F9FA` - Section backgrounds, subtle areas
- **Card Gray**: `#F3F4F6` - Card backgrounds (alternative)

### Text Colors
- **Pure Black**: `#000000` - Headings, high contrast text
- **Dark Gray**: `#1F2937` - Primary body text
- **Text Gray**: `#6B7280` - Secondary text, descriptions
- **Light Gray**: `#9CA3AF` - Placeholder text, captions

### Border Colors
- **Border Gray**: `#E5E7EB` - Default borders, dividers
- **Light Border**: `#F3F4F6` - Subtle borders
- **Medium Border**: `#D1D5DB` - Visible borders

---

## 🌙 **DARK MODE COLORS**

### Backgrounds
- **Dark Gray**: `#1F2937` - Main dark background
- **Neutral 800**: `#1F2937` - Section backgrounds
- **Neutral 900**: `#111827` - Deep backgrounds

### Text (Dark Mode)
- **Pure White**: `#FFFFFF` - Primary text on dark
- **Neutral 300**: `#D1D5DB` - Secondary text on dark
- **Neutral 400**: `#9CA3AF` - Tertiary text on dark

---

## 🎨 **STATUS COLORS**

### Success (Emerald Green)
- **Success**: `#10B981` - Success messages, confirmations
- **Success Dark**: `#059669` - Hover states

### Error (Red)
- **Error**: `#EF4444` - Error messages, warnings
- **Error Dark**: `#DC2626` - Hover states

### Warning (Amber)
- **Warning**: `#F59E0B` - Warning messages, alerts
- **Warning Dark**: `#D97706` - Hover states

### Info (Blue)
- **Info**: `#3B82F6` - Information messages, tips
- **Info Dark**: `#2563EB` - Hover states

---

## 🧊 **GLASSMORPHISM OVERLAYS**

- **Frosted Light**: `rgba(255,255,255,0.8)` - White frosted overlay
- **Frosted Dark**: `rgba(31,41,55,0.8)` - Dark frosted overlay
- **Frosted Border**: `rgba(229,231,235,0.5)` - Border frosted overlay

---

## 📱 **TAILWIND CSS CLASSES**

### Background Classes
```css
bg-pure-white       /* #FFFFFF */
bg-light-gray       /* #F8F9FA */
bg-dark-gray        /* #1F2937 */
bg-lime-green       /* #BFFF00 */
```

### Text Classes
```css
text-pure-black     /* #000000 */
text-dark-gray      /* #1F2937 */
text-text-gray      /* #6B7280 */
text-pure-white     /* #FFFFFF */
```

### Border Classes
```css
border-border-gray  /* #E5E7EB */
border-light-gray   /* #F3F4F6 */
border-medium-gray  /* #D1D5DB */
```

---

## 🎯 **USAGE GUIDELINES**

### ✅ **DO:**
- Use **Pure White** (`#FFFFFF`) for main backgrounds
- Use **Pure Black** (`#000000`) for headings
- Use **Lime Green** (`#BFFF00`) for brand accents
- Use **Text Gray** (`#6B7280`) for secondary text
- Use **Light Gray** (`#F8F9FA`) for section backgrounds

### ❌ **DON'T:**
- Mix old color variables with new ones
- Use gradients (per user preference)
- Use colors outside this palette
- Override brand lime green color

---

## 🔄 **MIGRATION FROM OLD PALETTE**

### Old → New Mappings
```css
/* OLD COLORS → NEW COLORS */
ice-white → pure-white
jet-black → pure-black
graphite-gray → dark-gray
chrome-metallic → text-gray
neon-volt-lime → lime-green
```

---

## 📋 **IMPLEMENTATION CHECKLIST**

### ✅ **CORE SYSTEM FILES:**
- [x] Updated `tailwind.config.js` with new color system
- [x] Updated `app/globals.css` CSS variables
- [x] Updated `app/layout.jsx` main background
- [x] Updated CSS component styles (.btn-primary, .btn-secondary, etc.)
- [x] Updated selection styles

### ✅ **PAGES:**
- [x] Updated homepage (`app/page.jsx`)
- [x] Updated Hero component (`components/features/Hero.jsx`)
- [x] Verified other pages (shop, tienda, etc.) - no old colors found

### ✅ **UI COMPONENTS:**
- [x] Updated Card component (`components/ui/Card.jsx`)
- [x] Updated Button component (`components/ui/Button.jsx`)
- [x] Updated Header component (`components/layout/Header.jsx`)

### ✅ **TESTING:**
- [x] Server running successfully with no compilation errors
- [x] Dark mode compatibility verified
- [x] Reference design color palette fully implemented

### 🔄 **REMAINING TASKS:**
- [ ] Verify accessibility contrast ratios (WCAG compliance)
- [ ] Test all interactive states (hover, focus, active)
- [ ] Cross-browser compatibility testing

---

**This master palette ensures consistency across the entire TWL e-commerce platform while maintaining the clean, minimal aesthetic of the reference design.**
