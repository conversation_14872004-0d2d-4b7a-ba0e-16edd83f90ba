#!/usr/bin/env node

/**
 * ULTIMATE PERFECTIONIST DATABASE SCHEMA
 * 
 * ENTERPRISE-GRADE COMPLETE CLASSIFICATION SYSTEM for TWL
 * 
 * Features:
 * - Complete size & fit classification
 * - Condition & authenticity verification
 * - Rarity & market intelligence
 * - Technical specifications
 * - Cultural & historical context
 * - Sustainability & ethics scoring
 * - Investment & collectibility analysis
 * - Regional & localization data
 * - Social media & virality tracking
 * - Competitive intelligence
 */

const ULTIMATE_SCHEMA = {
  // Size & Fit Classification
  sizing: {
    ranges: {
      'US_MEN': { min: 4, max: 18, step: 0.5 },
      'US_WOMEN': { min: 4, max: 16, step: 0.5 },
      'EU': { min: 36, max: 52, step: 0.5 },
      'UK': { min: 3.5, max: 17, step: 0.5 },
      'CM': { min: 22, max: 32, step: 0.5 }
    },
    fitTypes: {
      'TRUE_TO_SIZE': { id: 1, name_es: 'Talla Real', name_en: 'True to Size' },
      'RUNS_SMALL': { id: 2, name_es: 'Talla Pequeña', name_en: 'Runs Small' },
      'RUNS_LARGE': { id: 3, name_es: 'Talla Grande', name_en: 'Runs Large' },
      'HALF_SIZE_UP': { id: 4, name_es: 'Media Talla Más', name_en: 'Half Size Up' },
      'HALF_SIZE_DOWN': { id: 5, name_es: 'Media Talla Menos', name_en: 'Half Size Down' }
    },
    widthOptions: {
      'NARROW': { id: 1, code: 'B', name_es: 'Estrecho', name_en: 'Narrow' },
      'REGULAR': { id: 2, code: 'D', name_es: 'Regular', name_en: 'Regular' },
      'WIDE': { id: 3, code: 'E', name_es: 'Ancho', name_en: 'Wide' },
      'EXTRA_WIDE': { id: 4, code: 'EE', name_es: 'Extra Ancho', name_en: 'Extra Wide' }
    }
  },

  // Condition & Authenticity
  condition: {
    grades: {
      'DEADSTOCK': { id: 1, score: 10, name_es: 'Nuevo', name_en: 'Deadstock', description: 'Brand new, never worn' },
      'VNDS': { id: 2, score: 9, name_es: 'Casi Nuevo', name_en: 'VNDS', description: 'Very near deadstock' },
      'EXCELLENT': { id: 3, score: 8, name_es: 'Excelente', name_en: 'Excellent', description: 'Minimal wear' },
      'VERY_GOOD': { id: 4, score: 7, name_es: 'Muy Bueno', name_en: 'Very Good', description: 'Light wear' },
      'GOOD': { id: 5, score: 6, name_es: 'Bueno', name_en: 'Good', description: 'Moderate wear' },
      'FAIR': { id: 6, score: 5, name_es: 'Regular', name_en: 'Fair', description: 'Heavy wear' }
    },
    authenticity: {
      'TWL_AUTHENTICATED': { id: 1, name: 'TWL Authenticated', trust_score: 10 },
      'STOCKX_VERIFIED': { id: 2, name: 'StockX Verified', trust_score: 9 },
      'GOAT_VERIFIED': { id: 3, name: 'GOAT Verified', trust_score: 9 },
      'LEGIT_CHECK_PASSED': { id: 4, name: 'Legit Check Passed', trust_score: 8 },
      'RETAIL_RECEIPT': { id: 5, name: 'Retail Receipt', trust_score: 7 }
    },
    boxCondition: {
      'ORIGINAL_BOX': { id: 1, name_es: 'Caja Original', name_en: 'Original Box' },
      'REPLACEMENT_BOX': { id: 2, name_es: 'Caja Reemplazo', name_en: 'Replacement Box' },
      'DAMAGED_BOX': { id: 3, name_es: 'Caja Dañada', name_en: 'Damaged Box' },
      'NO_BOX': { id: 4, name_es: 'Sin Caja', name_en: 'No Box' }
    }
  },

  // Rarity & Market Classification
  rarity: {
    scores: {
      'ULTRA_RARE': { id: 1, score: 10, production: '<1000', name: 'Ultra Rare' },
      'EXTREMELY_RARE': { id: 2, score: 9, production: '1000-5000', name: 'Extremely Rare' },
      'VERY_RARE': { id: 3, score: 8, production: '5000-10000', name: 'Very Rare' },
      'RARE': { id: 4, score: 7, production: '10000-25000', name: 'Rare' },
      'LIMITED': { id: 5, score: 6, production: '25000-50000', name: 'Limited' },
      'SCARCE': { id: 6, score: 5, production: '50000-100000', name: 'Scarce' },
      'UNCOMMON': { id: 7, score: 4, production: '100000-250000', name: 'Uncommon' },
      'COMMON': { id: 8, score: 3, production: '250000+', name: 'Common' }
    },
    hypeLevel: {
      'GRAIL': { id: 1, score: 10, name: 'Grail Status' },
      'ULTRA_HYPE': { id: 2, score: 9, name: 'Ultra Hype' },
      'HIGH_HYPE': { id: 3, score: 8, name: 'High Hype' },
      'MODERATE_HYPE': { id: 4, score: 6, name: 'Moderate Hype' },
      'LOW_HYPE': { id: 5, score: 4, name: 'Low Hype' },
      'NO_HYPE': { id: 6, score: 2, name: 'No Hype' }
    }
  },

  // Technical Specifications
  technical: {
    technologies: {
      'AIR_MAX': { id: 1, brand: 'Nike', type: 'Cushioning', description: 'Visible air cushioning' },
      'ZOOM_AIR': { id: 2, brand: 'Nike', type: 'Cushioning', description: 'Responsive air cushioning' },
      'REACT': { id: 3, brand: 'Nike', type: 'Cushioning', description: 'Lightweight foam' },
      'BOOST': { id: 4, brand: 'Adidas', type: 'Cushioning', description: 'Energy return foam' },
      'PRIMEKNIT': { id: 5, brand: 'Adidas', type: 'Upper', description: 'Seamless knit upper' },
      'FLYKNIT': { id: 6, brand: 'Nike', type: 'Upper', description: 'Lightweight knit upper' }
    },
    soleTypes: {
      'RUBBER': { id: 1, durability: 8, grip: 9, name: 'Rubber Outsole' },
      'EVA': { id: 2, durability: 6, grip: 6, name: 'EVA Midsole' },
      'PU': { id: 3, durability: 7, grip: 7, name: 'Polyurethane' },
      'CARBON_FIBER': { id: 4, durability: 9, grip: 8, name: 'Carbon Fiber' }
    },
    closureTypes: {
      'LACES': { id: 1, name_es: 'Cordones', name_en: 'Laces' },
      'VELCRO': { id: 2, name_es: 'Velcro', name_en: 'Velcro' },
      'SLIP_ON': { id: 3, name_es: 'Sin Cordones', name_en: 'Slip-on' },
      'ZIPPER': { id: 4, name_es: 'Cremallera', name_en: 'Zipper' },
      'BUCKLE': { id: 5, name_es: 'Hebilla', name_en: 'Buckle' }
    }
  },

  // Cultural & Historical Context
  cultural: {
    significance: {
      'HIP_HOP_CULTURE': { id: 1, name: 'Hip-Hop Culture', influence: 9 },
      'BASKETBALL': { id: 2, name: 'Basketball', influence: 10 },
      'SKATEBOARDING': { id: 3, name: 'Skateboarding', influence: 8 },
      'RUNNING': { id: 4, name: 'Running', influence: 7 },
      'FASHION_WEEK': { id: 5, name: 'Fashion Week', influence: 9 },
      'STREET_STYLE': { id: 6, name: 'Street Style', influence: 8 }
    },
    celebrities: {
      'MICHAEL_JORDAN': { id: 1, name: 'Michael Jordan', influence: 10, category: 'Sports' },
      'KANYE_WEST': { id: 2, name: 'Kanye West', influence: 9, category: 'Music' },
      'TRAVIS_SCOTT': { id: 3, name: 'Travis Scott', influence: 9, category: 'Music' },
      'VIRGIL_ABLOH': { id: 4, name: 'Virgil Abloh', influence: 9, category: 'Design' },
      'RIHANNA': { id: 5, name: 'Rihanna', influence: 8, category: 'Music/Fashion' }
    }
  },

  // Seasonal & Trend Classification
  seasonal: {
    suitability: {
      'SPRING_SUMMER': { id: 1, name_es: 'Primavera/Verano', name_en: 'Spring/Summer', months: [3,4,5,6,7,8] },
      'FALL_WINTER': { id: 2, name_es: 'Otoño/Invierno', name_en: 'Fall/Winter', months: [9,10,11,12,1,2] },
      'ALL_SEASON': { id: 3, name_es: 'Todo el Año', name_en: 'All Season', months: [1,2,3,4,5,6,7,8,9,10,11,12] }
    },
    trendCategories: {
      'RETRO': { id: 1, name: 'Retro', cycle: 'Recurring', popularity: 8 },
      'FUTURISTIC': { id: 2, name: 'Futuristic', cycle: 'Emerging', popularity: 7 },
      'MINIMALIST': { id: 3, name: 'Minimalist', cycle: 'Stable', popularity: 9 },
      'MAXIMALIST': { id: 4, name: 'Maximalist', cycle: 'Trending', popularity: 6 },
      'VINTAGE': { id: 5, name: 'Vintage', cycle: 'Evergreen', popularity: 8 }
    }
  },

  // Sustainability & Ethics
  sustainability: {
    materials: {
      'RECYCLED_POLYESTER': { id: 1, eco_score: 8, name: 'Recycled Polyester' },
      'ORGANIC_COTTON': { id: 2, eco_score: 9, name: 'Organic Cotton' },
      'VEGAN_LEATHER': { id: 3, eco_score: 7, name: 'Vegan Leather' },
      'HEMP': { id: 4, eco_score: 10, name: 'Hemp' },
      'BAMBOO': { id: 5, eco_score: 9, name: 'Bamboo' }
    },
    certifications: {
      'CRADLE_TO_CRADLE': { id: 1, name: 'Cradle to Cradle', score: 10 },
      'BLUESIGN': { id: 2, name: 'Bluesign', score: 9 },
      'OEKO_TEX': { id: 3, name: 'Oeko-Tex', score: 8 },
      'GOTS': { id: 4, name: 'GOTS', score: 9 },
      'FAIR_TRADE': { id: 5, name: 'Fair Trade', score: 8 }
    }
  },

  // Investment & Collectibility
  investment: {
    grades: {
      'BLUE_CHIP': { id: 1, name: 'Blue Chip', risk: 'Low', return_potential: 'Stable' },
      'GROWTH': { id: 2, name: 'Growth', risk: 'Medium', return_potential: 'High' },
      'SPECULATIVE': { id: 3, name: 'Speculative', risk: 'High', return_potential: 'Very High' },
      'DEFENSIVE': { id: 4, name: 'Defensive', risk: 'Very Low', return_potential: 'Low' }
    },
    collectibility: {
      'MUSEUM_QUALITY': { id: 1, score: 10, name: 'Museum Quality' },
      'COLLECTORS_ITEM': { id: 2, score: 8, name: "Collector's Item" },
      'ENTHUSIAST': { id: 3, score: 6, name: 'Enthusiast Level' },
      'STANDARD': { id: 4, score: 4, name: 'Standard' }
    }
  },

  // Regional & Localization (Mexico Focus)
  regional: {
    mexicanPreferences: {
      'SOCCER_CULTURE': { id: 1, relevance: 10, description: 'Football/Soccer cultural connection' },
      'LUCHA_LIBRE': { id: 2, relevance: 8, description: 'Wrestling culture influence' },
      'DIA_DE_MUERTOS': { id: 3, relevance: 9, description: 'Day of the Dead themes' },
      'AZTEC_HERITAGE': { id: 4, relevance: 7, description: 'Aztec cultural elements' },
      'REGGAETON_CULTURE': { id: 5, relevance: 9, description: 'Reggaeton music influence' }
    },
    sizing: {
      'MEXICAN_PREFERENCE': { 
        men_popular: ['8.5', '9', '9.5', '10', '10.5'],
        women_popular: ['6', '6.5', '7', '7.5', '8'],
        conversion_note: 'Mexican customers prefer US sizing'
      }
    }
  },

  // Social Media & Virality
  socialMedia: {
    platforms: {
      'INSTAGRAM': { id: 1, weight: 0.4, hashtag_prefix: '#' },
      'TIKTOK': { id: 2, weight: 0.3, hashtag_prefix: '#' },
      'TWITTER': { id: 3, weight: 0.2, hashtag_prefix: '#' },
      'YOUTUBE': { id: 4, weight: 0.1, hashtag_prefix: '#' }
    },
    viralityFactors: {
      'CELEBRITY_ENDORSEMENT': { multiplier: 3.0, name: 'Celebrity Endorsement' },
      'INFLUENCER_POST': { multiplier: 2.0, name: 'Influencer Post' },
      'TIKTOK_TREND': { multiplier: 2.5, name: 'TikTok Trend' },
      'MEME_STATUS': { multiplier: 1.8, name: 'Meme Status' },
      'FASHION_WEEK': { multiplier: 2.2, name: 'Fashion Week Feature' }
    }
  },

  // Advanced Search Dimensions
  occasions: {
    'CASUAL': { id: 1, name_es: 'Casual', name_en: 'Casual', formality: 2 },
    'SMART_CASUAL': { id: 2, name_es: 'Semi-Formal', name_en: 'Smart Casual', formality: 4 },
    'BUSINESS': { id: 3, name_es: 'Negocios', name_en: 'Business', formality: 6 },
    'FORMAL': { id: 4, name_es: 'Formal', name_en: 'Formal', formality: 8 },
    'PARTY': { id: 5, name_es: 'Fiesta', name_en: 'Party', formality: 5 },
    'SPORT': { id: 6, name_es: 'Deporte', name_en: 'Sport', formality: 1 },
    'TRAVEL': { id: 7, name_es: 'Viaje', name_en: 'Travel', formality: 3 }
  },

  // Competitive Intelligence
  competitive: {
    platforms: {
      'STOCKX': { id: 1, name: 'StockX', market_share: 0.4 },
      'GOAT': { id: 2, name: 'GOAT', market_share: 0.3 },
      'FLIGHT_CLUB': { id: 3, name: 'Flight Club', market_share: 0.1 },
      'GRAILED': { id: 4, name: 'Grailed', market_share: 0.1 },
      'EBAY': { id: 5, name: 'eBay', market_share: 0.1 }
    },
    priceComparison: {
      'BELOW_MARKET': { id: 1, name: 'Below Market', advantage: 'High' },
      'AT_MARKET': { id: 2, name: 'At Market', advantage: 'Medium' },
      'ABOVE_MARKET': { id: 3, name: 'Above Market', advantage: 'Low' },
      'PREMIUM_PRICING': { id: 4, name: 'Premium Pricing', advantage: 'Luxury' }
    }
  }
};

module.exports = ULTIMATE_SCHEMA;
