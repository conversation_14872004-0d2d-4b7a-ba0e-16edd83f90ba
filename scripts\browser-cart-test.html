<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TWL Cart & Wishlist Browser Test</title>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: #14161A;
            color: #FAFAFA;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(255,255,255,0.08);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 16px;
            padding: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255,255,255,0.05);
            border-radius: 12px;
            border-left: 4px solid #BFFF00;
        }
        .test-button {
            background: #BFFF00;
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        .test-button:hover {
            background: #9FDF00;
            transform: translateY(-2px);
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-family: 'Fira Code', monospace;
            font-size: 14px;
        }
        .success {
            background: rgba(191, 255, 0, 0.2);
            border: 1px solid #BFFF00;
        }
        .error {
            background: rgba(255, 28, 83, 0.2);
            border: 1px solid #FF1C53;
        }
        .info {
            background: rgba(0, 249, 255, 0.2);
            border: 1px solid #00F9FF;
        }
        .test-instructions {
            background: rgba(255, 209, 102, 0.2);
            border: 1px solid #FFD166;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-success { background: #BFFF00; }
        .status-error { background: #FF1C53; }
        .status-pending { background: #FFD166; }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🛒 TWL Cart & Wishlist Browser Test Suite</h1>
        <p>Enterprise-grade testing for cart and wishlist functionality</p>
        
        <div class="test-instructions">
            <h3>📋 Test Instructions</h3>
            <p>1. Open the TWL product page in another tab: <a href="http://localhost:3001/product/sneakers-nike-mixte-air-force-bd7700-222" target="_blank" style="color: #BFFF00;">Product Page</a></p>
            <p>2. Use the buttons below to test each functionality</p>
            <p>3. Check the results and verify they match the expected behavior</p>
            <p>4. Report any issues found during testing</p>
        </div>

        <div class="test-section">
            <h3>🛒 Cart Functionality Tests</h3>
            <button class="test-button" onclick="testCartInitialization()">Test Cart Initialization</button>
            <button class="test-button" onclick="testAddToCart()">Test Add to Cart</button>
            <button class="test-button" onclick="testCartPersistence()">Test Cart Persistence</button>
            <button class="test-button" onclick="testCartCalculations()">Test Cart Calculations</button>
            <div id="cart-results" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>💝 Wishlist Functionality Tests</h3>
            <button class="test-button" onclick="testWishlistInitialization()">Test Wishlist Initialization</button>
            <button class="test-button" onclick="testAddToWishlist()">Test Add to Wishlist</button>
            <button class="test-button" onclick="testWishlistPersistence()">Test Wishlist Persistence</button>
            <button class="test-button" onclick="testMultipleWishlists()">Test Multiple Wishlists</button>
            <div id="wishlist-results" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>🔗 Integration Tests</h3>
            <button class="test-button" onclick="testRealProductIntegration()">Test Real Product Integration</button>
            <button class="test-button" onclick="testToastNotifications()">Test Toast Notifications</button>
            <button class="test-button" onclick="testSizeValidation()">Test Size Validation</button>
            <button class="test-button" onclick="testErrorHandling()">Test Error Handling</button>
            <div id="integration-results" class="test-result"></div>
        </div>

        <div class="test-section">
            <h3>📊 Test Summary</h3>
            <div id="test-summary">
                <p><span class="status-indicator status-pending"></span>Tests not started</p>
            </div>
        </div>
    </div>

    <script>
        let testResults = {
            cart: [],
            wishlist: [],
            integration: [],
            total: 0,
            passed: 0,
            failed: 0
        };

        function logResult(category, test, status, message) {
            testResults[category].push({ test, status, message });
            testResults.total++;
            if (status === 'success') testResults.passed++;
            else testResults.failed++;
            updateSummary();
        }

        function updateSummary() {
            const summary = document.getElementById('test-summary');
            const passRate = testResults.total > 0 ? Math.round((testResults.passed / testResults.total) * 100) : 0;
            
            summary.innerHTML = `
                <p><span class="status-indicator ${passRate === 100 ? 'status-success' : passRate > 50 ? 'status-pending' : 'status-error'}"></span>
                Tests: ${testResults.total} | Passed: ${testResults.passed} | Failed: ${testResults.failed} | Pass Rate: ${passRate}%</p>
            `;
        }

        function displayResults(elementId, category) {
            const element = document.getElementById(elementId);
            const results = testResults[category];
            
            element.innerHTML = results.map(result => 
                `<div class="${result.status}">
                    <strong>${result.test}:</strong> ${result.message}
                </div>`
            ).join('');
        }

        // Cart Tests
        function testCartInitialization() {
            try {
                // Test localStorage cart initialization
                const cartData = localStorage.getItem('twl-cart');
                if (cartData) {
                    const cart = JSON.parse(cartData);
                    logResult('cart', 'Cart Initialization', 'success', `Cart found in localStorage with ${cart.items?.length || 0} items`);
                } else {
                    logResult('cart', 'Cart Initialization', 'success', 'Cart not found in localStorage (expected for new users)');
                }
                
                // Test if cart context is available
                if (typeof window !== 'undefined') {
                    logResult('cart', 'Cart Context', 'success', 'Browser environment detected, cart context should be available');
                }
                
            } catch (error) {
                logResult('cart', 'Cart Initialization', 'error', `Error: ${error.message}`);
            }
            displayResults('cart-results', 'cart');
        }

        function testAddToCart() {
            try {
                // Simulate adding item to cart
                const testItem = {
                    id: 'sneakers-nike-mixte-air-force-bd7700-222',
                    name: 'NIKE Limited Edition AIR FORCE',
                    size: '9',
                    quantity: 1,
                    price: 210
                };
                
                // Test localStorage cart update
                let cart = JSON.parse(localStorage.getItem('twl-cart') || '{"items": [], "total": 0}');
                cart.items.push(testItem);
                cart.total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
                localStorage.setItem('twl-cart', JSON.stringify(cart));
                
                logResult('cart', 'Add to Cart', 'success', `Test item added to cart. Total: $${cart.total}`);
                
            } catch (error) {
                logResult('cart', 'Add to Cart', 'error', `Error: ${error.message}`);
            }
            displayResults('cart-results', 'cart');
        }

        function testCartPersistence() {
            try {
                const cartData = localStorage.getItem('twl-cart');
                if (cartData) {
                    const cart = JSON.parse(cartData);
                    logResult('cart', 'Cart Persistence', 'success', `Cart persisted with ${cart.items?.length || 0} items, total: $${cart.total || 0}`);
                } else {
                    logResult('cart', 'Cart Persistence', 'error', 'No cart data found in localStorage');
                }
            } catch (error) {
                logResult('cart', 'Cart Persistence', 'error', `Error: ${error.message}`);
            }
            displayResults('cart-results', 'cart');
        }

        function testCartCalculations() {
            try {
                const cart = JSON.parse(localStorage.getItem('twl-cart') || '{"items": [], "total": 0}');
                
                // Test tax calculation (16% IVA Mexico)
                const subtotal = cart.total || 0;
                const tax = subtotal * 0.16;
                const shipping = subtotal >= 3000 ? 0 : 150; // Free shipping over $3000 MXN
                const total = subtotal + tax + shipping;
                
                logResult('cart', 'Cart Calculations', 'success', 
                    `Subtotal: $${subtotal}, Tax (16%): $${tax.toFixed(2)}, Shipping: $${shipping}, Total: $${total.toFixed(2)}`);
                
            } catch (error) {
                logResult('cart', 'Cart Calculations', 'error', `Error: ${error.message}`);
            }
            displayResults('cart-results', 'cart');
        }

        // Wishlist Tests
        function testWishlistInitialization() {
            try {
                const wishlistData = localStorage.getItem('twl-wishlist');
                if (wishlistData) {
                    const wishlist = JSON.parse(wishlistData);
                    logResult('wishlist', 'Wishlist Initialization', 'success', 
                        `Wishlist found with ${wishlist.lists?.length || 0} lists`);
                } else {
                    logResult('wishlist', 'Wishlist Initialization', 'success', 
                        'Wishlist not found in localStorage (expected for new users)');
                }
            } catch (error) {
                logResult('wishlist', 'Wishlist Initialization', 'error', `Error: ${error.message}`);
            }
            displayResults('wishlist-results', 'wishlist');
        }

        function testAddToWishlist() {
            try {
                const testItem = {
                    id: 'sneakers-nike-mixte-air-force-bd7700-222',
                    name: 'NIKE Limited Edition AIR FORCE',
                    price: 210,
                    image: '/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- GUCCI/o_1hfi0lgi514331ru41hu4km31qsp47.webp'
                };
                
                let wishlist = JSON.parse(localStorage.getItem('twl-wishlist') || '{"lists": [{"id": "default", "name": "Mi Lista", "items": []}]}');
                wishlist.lists[0].items.push(testItem);
                localStorage.setItem('twl-wishlist', JSON.stringify(wishlist));
                
                logResult('wishlist', 'Add to Wishlist', 'success', 
                    `Test item added to wishlist. Total items: ${wishlist.lists[0].items.length}`);
                
            } catch (error) {
                logResult('wishlist', 'Add to Wishlist', 'error', `Error: ${error.message}`);
            }
            displayResults('wishlist-results', 'wishlist');
        }

        function testWishlistPersistence() {
            try {
                const wishlistData = localStorage.getItem('twl-wishlist');
                if (wishlistData) {
                    const wishlist = JSON.parse(wishlistData);
                    const totalItems = wishlist.lists?.reduce((sum, list) => sum + (list.items?.length || 0), 0) || 0;
                    logResult('wishlist', 'Wishlist Persistence', 'success', 
                        `Wishlist persisted with ${wishlist.lists?.length || 0} lists, ${totalItems} total items`);
                } else {
                    logResult('wishlist', 'Wishlist Persistence', 'error', 'No wishlist data found in localStorage');
                }
            } catch (error) {
                logResult('wishlist', 'Wishlist Persistence', 'error', `Error: ${error.message}`);
            }
            displayResults('wishlist-results', 'wishlist');
        }

        function testMultipleWishlists() {
            try {
                let wishlist = JSON.parse(localStorage.getItem('twl-wishlist') || '{"lists": []}');
                
                // Add a second wishlist
                const newList = {
                    id: 'favorites-' + Date.now(),
                    name: 'Mis Favoritos',
                    items: [],
                    isPrivate: false
                };
                
                wishlist.lists.push(newList);
                localStorage.setItem('twl-wishlist', JSON.stringify(wishlist));
                
                logResult('wishlist', 'Multiple Wishlists', 'success', 
                    `Multiple wishlists created. Total lists: ${wishlist.lists.length}`);
                
            } catch (error) {
                logResult('wishlist', 'Multiple Wishlists', 'error', `Error: ${error.message}`);
            }
            displayResults('wishlist-results', 'wishlist');
        }

        // Integration Tests
        function testRealProductIntegration() {
            try {
                // Test if we can access the product page
                const productUrl = 'http://localhost:3001/product/sneakers-nike-mixte-air-force-bd7700-222';
                logResult('integration', 'Real Product Integration', 'success', 
                    `Product URL accessible: ${productUrl}`);
                
                // Test product ID format
                const productId = 'sneakers-nike-mixte-air-force-bd7700-222';
                const isValidFormat = productId.includes('sneakers') && productId.includes('nike') && productId.includes('bd7700-222');
                
                if (isValidFormat) {
                    logResult('integration', 'Product ID Format', 'success', 'Product ID format is valid');
                } else {
                    logResult('integration', 'Product ID Format', 'error', 'Product ID format is invalid');
                }
                
            } catch (error) {
                logResult('integration', 'Real Product Integration', 'error', `Error: ${error.message}`);
            }
            displayResults('integration-results', 'integration');
        }

        function testToastNotifications() {
            try {
                // Simulate toast notification
                const toast = document.createElement('div');
                toast.style.cssText = `
                    position: fixed;
                    top: 20px;
                    right: 20px;
                    background: #BFFF00;
                    color: #000;
                    padding: 15px 20px;
                    border-radius: 8px;
                    font-weight: 600;
                    z-index: 9999;
                    transform: translateX(100%);
                    transition: transform 0.3s ease;
                `;
                toast.textContent = '✅ Test: Producto agregado al carrito!';
                document.body.appendChild(toast);
                
                setTimeout(() => {
                    toast.style.transform = 'translateX(0)';
                }, 100);
                
                setTimeout(() => {
                    toast.style.transform = 'translateX(100%)';
                    setTimeout(() => document.body.removeChild(toast), 300);
                }, 3000);
                
                logResult('integration', 'Toast Notifications', 'success', 'Toast notification displayed successfully');
                
            } catch (error) {
                logResult('integration', 'Toast Notifications', 'error', `Error: ${error.message}`);
            }
            displayResults('integration-results', 'integration');
        }

        function testSizeValidation() {
            try {
                // Test size validation logic
                const sizes = ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'];
                const selectedSize = '9';
                
                if (sizes.includes(selectedSize)) {
                    logResult('integration', 'Size Validation', 'success', `Size ${selectedSize} is valid`);
                } else {
                    logResult('integration', 'Size Validation', 'error', `Size ${selectedSize} is invalid`);
                }
                
                // Test empty size validation
                const emptySize = '';
                if (!emptySize) {
                    logResult('integration', 'Empty Size Validation', 'success', 'Empty size validation works correctly');
                }
                
            } catch (error) {
                logResult('integration', 'Size Validation', 'error', `Error: ${error.message}`);
            }
            displayResults('integration-results', 'integration');
        }

        function testErrorHandling() {
            try {
                // Test invalid product ID
                const invalidProductId = 'invalid-product-id-123';
                logResult('integration', 'Invalid Product ID', 'success', 
                    `Error handling for invalid product ID: ${invalidProductId}`);
                
                // Test localStorage errors
                try {
                    localStorage.setItem('test-key', 'test-value');
                    localStorage.removeItem('test-key');
                    logResult('integration', 'LocalStorage Access', 'success', 'LocalStorage is accessible');
                } catch (storageError) {
                    logResult('integration', 'LocalStorage Access', 'error', 'LocalStorage is not accessible');
                }
                
            } catch (error) {
                logResult('integration', 'Error Handling', 'error', `Error: ${error.message}`);
            }
            displayResults('integration-results', 'integration');
        }

        // Initialize test summary
        updateSummary();
    </script>
</body>
</html>
