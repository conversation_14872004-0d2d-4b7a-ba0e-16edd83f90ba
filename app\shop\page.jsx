'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { mockProducts } from '../../lib/data/products'
import AnimatedProductCard from '../../components/ui/AnimatedProductCard'

export default function ShopPage() {
  console.log('🛍️🛍️🛍️ SHOP PAGE COMPONENT LOADING!')

  const [products, setProducts] = useState([])
  const [isLoadingProducts, setIsLoadingProducts] = useState(true)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)

  // Load mock products for testing wishlist functionality
  useEffect(() => {
    const loadProducts = () => {
      try {
        console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: Loading mock products for wishlist testing')

        // Convert mock products to use /products/ paths
        const productsWithCorrectPaths = mockProducts.map(product => ({
          ...product,
          images: product.images?.map(img =>
            img.replace('/products-organized/', '/products/')
          ) || [],
          videos: product.videos?.map(video =>
            video.replace('/products-organized/', '/products/')
          ) || []
        }))

        console.log('🛍️🛍️🛍️ SHOP PAGE CLIENT: Loaded', productsWithCorrectPaths.length, 'products')
        setProducts(productsWithCorrectPaths)
      } catch (error) {
        console.error('🛍️🛍️🛍️ SHOP PAGE CLIENT: Error loading products:', error)
      } finally {
        setIsLoadingProducts(false)
      }
    }

    loadProducts()
  }, [])

  const handleAuthRequired = () => {
    setIsAuthModalOpen(true)
  }
  if (isLoadingProducts) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-dark-gray pt-36 flex items-center justify-center">
        <div className="text-center">
          <div className="text-6xl mb-4">👟</div>
          <h2 className="text-2xl font-godber font-bold text-pure-black dark:text-pure-white mb-4">
            Cargando productos...
          </h2>
          <p className="text-text-gray dark:text-neutral-400">
            Preparando nuestra colección exclusiva
          </p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-pure-white dark:bg-dark-gray pt-36">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-godber font-bold text-pure-black dark:text-pure-white mb-4 tracking-godber-sm">
            Tienda
          </h1>
          <p className="text-text-gray dark:text-neutral-400 text-lg font-poppins">
            Descubre nuestra colección exclusiva de sneakers de lujo ({products.length} productos)
          </p>
        </motion.div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6">
          <AnimatePresence mode="popLayout">
            {products.slice(0, 20).map((product, index) => (
              <AnimatedProductCard
                key={product.id}
                product={product}
                index={index}
                onAuthRequired={handleAuthRequired}
              />
            ))}
          </AnimatePresence>
        </div>

        {/* Show more products message */}
        {products.length > 20 && (
          <div className="text-center mt-12">
            <p className="text-text-gray dark:text-neutral-400 font-poppins">
              Mostrando 20 de {products.length} productos
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
