// Application constants for The White Laces

// App Configuration
export const APP_CONFIG = {
  name: 'The White Laces',
  shortName: 'TWL',
  description: 'Luxury streetwear e-commerce platform',
  version: '1.0.0',
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
  email: '<EMAIL>',
  phone: '+52 55 1234 5678'
}

// Supported Locales
export const LOCALES = {
  'es-MX': {
    name: '<PERSON>spa<PERSON><PERSON> (México)',
    flag: '🇲🇽',
    currency: 'MXN',
    currencySymbol: '$'
  },
  'en': {
    name: 'English',
    flag: '🇺🇸',
    currency: 'USD',
    currencySymbol: '$'
  },
  'pt-BR': {
    name: 'Portugu<PERSON><PERSON> (Brasil)',
    flag: '🇧🇷',
    currency: 'BRL',
    currencySymbol: 'R$'
  }
}

export const DEFAULT_LOCALE = 'es-MX'

// Product Categories
export const CATEGORIES = {
  SNEAKERS: 'sneakers',
  LUXURY: 'luxury',
  CASUAL: 'casual',
  FORMAL: 'formal',
  SANDALS: 'sandals',
  BOOTS: 'boots'
}

// Product Subcategories
export const SUBCATEGORIES = {
  LIFESTYLE: 'lifestyle',
  BASKETBALL: 'basketball',
  RUNNING: 'running',
  SKATE: 'skate',
  DESIGNER: 'designer',
  COLLABORATION: 'collaboration'
}

// Gender Categories
export const GENDERS = {
  MEN: 'men',
  WOMEN: 'women',
  UNISEX: 'unisex',
  KIDS: 'kids'
}

// Brands
export const BRANDS = {
  NIKE: 'Nike',
  ADIDAS: 'Adidas',
  JORDAN: 'Jordan',
  GUCCI: 'Gucci',
  BALENCIAGA: 'Balenciaga',
  OFF_WHITE: 'Off-White',
  YEEZY: 'Yeezy',
  CONVERSE: 'Converse',
  VANS: 'Vans',
  DIOR: 'Dior'
}

// Size Systems
export const SIZE_SYSTEMS = {
  US: 'US',
  EU: 'EU',
  UK: 'UK',
  CM: 'CM'
}

// Common Sizes (US)
export const SIZES = [
  '5', '5.5', '6', '6.5', '7', '7.5', '8', '8.5', 
  '9', '9.5', '10', '10.5', '11', '11.5', '12', '12.5', '13'
]

// Colors
export const COLORS = {
  WHITE: { name: 'Blanco', hex: '#FFFFFF', id: 'white' },
  BLACK: { name: 'Negro', hex: '#000000', id: 'black' },
  RED: { name: 'Rojo', hex: '#FF0000', id: 'red' },
  BLUE: { name: 'Azul', hex: '#0000FF', id: 'blue' },
  GREEN: { name: 'Verde', hex: '#00FF00', id: 'green' },
  YELLOW: { name: 'Amarillo', hex: '#FFFF00', id: 'yellow' },
  ORANGE: { name: 'Naranja', hex: '#FFA500', id: 'orange' },
  PURPLE: { name: 'Morado', hex: '#800080', id: 'purple' },
  PINK: { name: 'Rosa', hex: '#FFC0CB', id: 'pink' },
  BROWN: { name: 'Café', hex: '#A52A2A', id: 'brown' },
  GRAY: { name: 'Gris', hex: '#808080', id: 'gray' },
  CREAM: { name: 'Crema', hex: '#F5F5DC', id: 'cream' }
}

// Price Ranges (MXN)
export const PRICE_RANGES = {
  UNDER_3000: { min: 0, max: 3000, label: 'Menos de $3,000' },
  RANGE_3000_10000: { min: 3000, max: 10000, label: '$3,000 - $10,000' },
  RANGE_10000_20000: { min: 10000, max: 20000, label: '$10,000 - $20,000' },
  RANGE_20000_50000: { min: 20000, max: 50000, label: '$20,000 - $50,000' },
  OVER_50000: { min: 50000, max: Infinity, label: 'Más de $50,000' }
}

// Sort Options
export const SORT_OPTIONS = {
  RELEVANCE: { value: 'relevance', label: 'Relevancia' },
  PRICE_LOW: { value: 'price-low', label: 'Precio: Menor a Mayor' },
  PRICE_HIGH: { value: 'price-high', label: 'Precio: Mayor a Menor' },
  NAME: { value: 'name', label: 'Nombre A-Z' },
  RATING: { value: 'rating', label: 'Mejor Calificados' },
  NEWEST: { value: 'newest', label: 'Más Nuevos' },
  POPULAR: { value: 'popular', label: 'Más Populares' }
}

// Order Status
export const ORDER_STATUS = {
  PENDING: 'pending',
  CONFIRMED: 'confirmed',
  PROCESSING: 'processing',
  SHIPPED: 'shipped',
  DELIVERED: 'delivered',
  CANCELLED: 'cancelled',
  REFUNDED: 'refunded'
}

// Payment Methods
export const PAYMENT_METHODS = {
  STRIPE: 'stripe',
  MERCADO_PAGO: 'mercado_pago',
  PAYPAL: 'paypal',
  OXXO: 'oxxo',
  SPEI: 'spei'
}

// Shipping Methods
export const SHIPPING_METHODS = {
  STANDARD: { id: 'standard', name: 'Envío Estándar', days: '3-5', price: 150 },
  EXPRESS: { id: 'express', name: 'Envío Express', days: '1-2', price: 300 },
  OVERNIGHT: { id: 'overnight', name: 'Envío Nocturno', days: '1', price: 500 },
  FREE: { id: 'free', name: 'Envío Gratis', days: '5-7', price: 0, minAmount: 2500 }
}

// User Roles
export const USER_ROLES = {
  CUSTOMER: 'customer',
  VIP: 'vip',
  ADMIN: 'admin',
  MODERATOR: 'moderator'
}

// Wishlist Types
export const WISHLIST_TYPES = {
  DEFAULT: 'default',
  FAVORITES: 'favorites',
  CUSTOM: 'custom'
}

// Social Platforms
export const SOCIAL_PLATFORMS = {
  INSTAGRAM: 'instagram',
  TIKTOK: 'tiktok',
  TWITTER: 'twitter',
  FACEBOOK: 'facebook',
  YOUTUBE: 'youtube'
}

// Feature Flags
export const FEATURES = {
  VOICE_SEARCH: process.env.NEXT_PUBLIC_ENABLE_VOICE_SEARCH === 'true',
  VISUAL_SEARCH: process.env.NEXT_PUBLIC_ENABLE_VISUAL_SEARCH === 'true',
  AR_TRYONS: process.env.NEXT_PUBLIC_ENABLE_AR_TRYONS === 'true',
  SOCIAL_SHOPPING: process.env.NEXT_PUBLIC_ENABLE_SOCIAL_SHOPPING === 'true',
  BIOMETRIC_AUTH: process.env.NEXT_PUBLIC_ENABLE_BIOMETRIC_AUTH === 'true'
}

// API Endpoints
export const API_ENDPOINTS = {
  PRODUCTS: '/api/products',
  SEARCH: '/api/search',
  CART: '/api/cart',
  WISHLIST: '/api/wishlist',
  ORDERS: '/api/orders',
  AUTH: '/api/auth',
  USER: '/api/user',
  REVIEWS: '/api/reviews',
  RECOMMENDATIONS: '/api/recommendations'
}

// Cache Keys
export const CACHE_KEYS = {
  PRODUCTS: 'products',
  CATEGORIES: 'categories',
  BRANDS: 'brands',
  USER_PREFERENCES: 'user_preferences',
  CART: 'cart',
  WISHLIST: 'wishlist'
}

// Local Storage Keys
export const STORAGE_KEYS = {
  THEME: 'twl_theme',
  LOCALE: 'twl_locale',
  CART: 'twl_cart',
  RECENTLY_VIEWED: 'twl_recently_viewed',
  SEARCH_HISTORY: 'twl_search_history',
  USER_PREFERENCES: 'twl_user_preferences'
}

// Animation Durations (ms)
export const ANIMATION_DURATION = {
  FAST: 150,
  NORMAL: 300,
  SLOW: 500,
  EXTRA_SLOW: 1000
}

// Breakpoints (matches Tailwind CSS)
export const BREAKPOINTS = {
  SM: 640,
  MD: 768,
  LG: 1024,
  XL: 1280,
  '2XL': 1536
}

// Image Sizes
export const IMAGE_SIZES = {
  THUMBNAIL: { width: 150, height: 150 },
  SMALL: { width: 300, height: 300 },
  MEDIUM: { width: 600, height: 600 },
  LARGE: { width: 1200, height: 1200 },
  HERO: { width: 1920, height: 1080 }
}

// Error Messages
export const ERROR_MESSAGES = {
  NETWORK_ERROR: 'Error de conexión. Por favor, intenta de nuevo.',
  INVALID_CREDENTIALS: 'Credenciales inválidas.',
  PRODUCT_NOT_FOUND: 'Producto no encontrado.',
  OUT_OF_STOCK: 'Producto agotado.',
  PAYMENT_FAILED: 'Error en el pago. Por favor, intenta de nuevo.',
  GENERIC_ERROR: 'Algo salió mal. Por favor, intenta de nuevo.'
}

// Success Messages
export const SUCCESS_MESSAGES = {
  PRODUCT_ADDED_TO_CART: 'Producto agregado al carrito.',
  PRODUCT_ADDED_TO_WISHLIST: 'Producto agregado a la lista de deseos.',
  ORDER_PLACED: 'Pedido realizado exitosamente.',
  PROFILE_UPDATED: 'Perfil actualizado exitosamente.',
  PASSWORD_CHANGED: 'Contraseña cambiada exitosamente.'
}

// Regex Patterns
export const REGEX_PATTERNS = {
  EMAIL: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  PHONE: /^(\+52|52)?[\s\-]?(\d{2})[\s\-]?(\d{4})[\s\-]?(\d{4})$/,
  POSTAL_CODE: /^\d{5}$/,
  CREDIT_CARD: /^\d{4}[\s\-]?\d{4}[\s\-]?\d{4}[\s\-]?\d{4}$/
}
