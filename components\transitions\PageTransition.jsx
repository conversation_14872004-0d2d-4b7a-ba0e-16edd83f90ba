'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { usePathname } from 'next/navigation'
import { useEffect, useState } from 'react'

const pageVariants = {
  // Slide transitions
  slideLeft: {
    initial: { x: '100%', opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: '-100%', opacity: 0 }
  },
  slideRight: {
    initial: { x: '-100%', opacity: 0 },
    animate: { x: 0, opacity: 1 },
    exit: { x: '100%', opacity: 0 }
  },
  slideUp: {
    initial: { y: '100%', opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: '-100%', opacity: 0 }
  },
  slideDown: {
    initial: { y: '-100%', opacity: 0 },
    animate: { y: 0, opacity: 1 },
    exit: { y: '100%', opacity: 0 }
  },
  
  // Fade transitions
  fade: {
    initial: { opacity: 0 },
    animate: { opacity: 1 },
    exit: { opacity: 0 }
  },
  fadeScale: {
    initial: { opacity: 0, scale: 0.95 },
    animate: { opacity: 1, scale: 1 },
    exit: { opacity: 0, scale: 1.05 }
  },
  
  // Organic transitions
  breathe: {
    initial: { opacity: 0, scale: 0.98, filter: 'blur(4px)' },
    animate: { 
      opacity: 1, 
      scale: 1, 
      filter: 'blur(0px)',
      transition: {
        duration: 0.8,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: { 
      opacity: 0, 
      scale: 1.02, 
      filter: 'blur(4px)',
      transition: {
        duration: 0.4,
        ease: [0.55, 0.06, 0.68, 0.19]
      }
    }
  },
  
  // Morphing transition
  morph: {
    initial: { 
      opacity: 0, 
      scale: 0.9, 
      rotateX: 15,
      transformPerspective: 1000
    },
    animate: { 
      opacity: 1, 
      scale: 1, 
      rotateX: 0,
      transition: {
        duration: 0.7,
        ease: [0.25, 0.46, 0.45, 0.94]
      }
    },
    exit: { 
      opacity: 0, 
      scale: 1.1, 
      rotateX: -15,
      transition: {
        duration: 0.4,
        ease: [0.55, 0.06, 0.68, 0.19]
      }
    }
  },
  
  // Liquid transition
  liquid: {
    initial: { 
      opacity: 0, 
      scale: 0.95,
      borderRadius: '50%',
      filter: 'blur(8px)'
    },
    animate: { 
      opacity: 1, 
      scale: 1,
      borderRadius: '0%',
      filter: 'blur(0px)',
      transition: {
        duration: 0.9,
        ease: [0.25, 0.46, 0.45, 0.94],
        borderRadius: { duration: 1.2 }
      }
    },
    exit: { 
      opacity: 0, 
      scale: 1.05,
      borderRadius: '50%',
      filter: 'blur(8px)',
      transition: {
        duration: 0.5,
        ease: [0.55, 0.06, 0.68, 0.19]
      }
    }
  }
}

// Route-specific transition mapping
const routeTransitions = {
  '/': 'breathe',
  '/shop': 'slideLeft',
  '/forms-demo': 'morph',
  '/brands': 'fadeScale',
  '/limited-editions': 'liquid',
  '/community': 'slideUp',
  '/magazine': 'fade',
  '/product': 'slideLeft', // For product detail pages
  '/checkout': 'slideUp',
  '/account': 'slideRight'
}

// Determine transition based on route navigation
const getTransitionType = (currentPath, previousPath) => {
  // Special cases for specific route combinations
  if (previousPath === '/' && currentPath === '/shop') return 'slideLeft'
  if (previousPath === '/shop' && currentPath === '/') return 'slideRight'
  if (currentPath.startsWith('/product/')) return 'slideLeft'
  if (previousPath?.startsWith('/product/') && currentPath === '/shop') return 'slideRight'
  
  // Default to route-specific transition
  return routeTransitions[currentPath] || 'breathe'
}

export default function PageTransition({ children }) {
  const pathname = usePathname()
  const [previousPath, setPreviousPath] = useState(null)
  const [transitionType, setTransitionType] = useState('breathe')

  useEffect(() => {
    const newTransitionType = getTransitionType(pathname, previousPath)
    setTransitionType(newTransitionType)
    setPreviousPath(pathname)
  }, [pathname, previousPath])

  const currentVariant = pageVariants[transitionType]

  return (
    <AnimatePresence mode="wait" initial={false}>
      <motion.div
        key={pathname}
        initial="initial"
        animate="animate"
        exit="exit"
        variants={currentVariant}
        className="min-h-screen"
        style={{ transformStyle: 'preserve-3d' }}
      >
        {children}
      </motion.div>
    </AnimatePresence>
  )
}

// Loading overlay component for smoother transitions
export function TransitionOverlay({ isVisible }) {
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 z-50 pointer-events-none"
        >
          {/* Organic loading pattern */}
          <div className="absolute inset-0 bg-gradient-to-br from-light-cloud-gray/80 via-soft-steel-gray/60 to-warm-camel/40 dark:from-deep-pine/80 dark:via-forest-emerald/60 dark:to-rich-gold/40" />
          
          {/* Animated particles */}
          <div className="absolute inset-0 overflow-hidden">
            {[...Array(12)].map((_, i) => (
              <motion.div
                key={i}
                className="absolute w-2 h-2 bg-rich-gold/30 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                }}
                animate={{
                  scale: [0, 1, 0],
                  opacity: [0, 0.6, 0],
                  x: [0, (Math.random() - 0.5) * 100],
                  y: [0, (Math.random() - 0.5) * 100],
                }}
                transition={{
                  duration: 2,
                  repeat: Infinity,
                  delay: i * 0.1,
                  ease: "easeInOut"
                }}
              />
            ))}
          </div>
          
          {/* Central loading indicator */}
          <div className="absolute inset-0 flex items-center justify-center">
            <motion.div
              className="w-16 h-16 border-4 border-rich-gold/20 border-t-rich-gold rounded-full"
              animate={{ rotate: 360 }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Hook for programmatic transitions
export function usePageTransition() {
  const [isTransitioning, setIsTransitioning] = useState(false)
  
  const startTransition = () => setIsTransitioning(true)
  const endTransition = () => setIsTransitioning(false)
  
  return {
    isTransitioning,
    startTransition,
    endTransition
  }
}
