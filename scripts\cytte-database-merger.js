#!/usr/bin/env node

/**
 * CYTTE DATABASE MERGER
 * Combines all scanned products and integrates them into the main database
 * Creates the complete 5,480+ product database
 */

const fs = require('fs')
const path = require('path')

async function mergeCytteDatabase() {
  console.log('🔄 CYTTE DATABASE MERGER STARTING...')
  console.log('=' .repeat(60))
  
  try {
    // Load existing products
    const existingProductsPath = path.join(process.cwd(), 'lib', 'data', 'products.js')
    const deepScanPath = path.join(process.cwd(), 'lib', 'data', 'cytte-deep-scan-products.json')
    const expandedPath = path.join(process.cwd(), 'lib', 'data', 'cytte-expanded-products.json')
    
    let allProducts = []
    let existingProducts = []
    let deepScanProducts = []
    let expandedProducts = []
    
    // Load existing products from products.js
    if (fs.existsSync(existingProductsPath)) {
      console.log('📂 Loading existing products from products.js...')
      const productsModule = require(existingProductsPath)
      existingProducts = productsModule.mockProducts || []
      console.log(`   Found ${existingProducts.length} existing products`)
    }
    
    // Load deep scan products
    if (fs.existsSync(deepScanPath)) {
      console.log('📂 Loading deep scan products...')
      deepScanProducts = JSON.parse(fs.readFileSync(deepScanPath, 'utf8'))
      console.log(`   Found ${deepScanProducts.length} deep scan products`)
    }
    
    // Load expanded products
    if (fs.existsSync(expandedPath)) {
      console.log('📂 Loading expanded products...')
      expandedProducts = JSON.parse(fs.readFileSync(expandedPath, 'utf8'))
      console.log(`   Found ${expandedProducts.length} expanded products`)
    }
    
    // Merge all products with deduplication
    console.log('\n🔄 Merging and deduplicating products...')
    
    const productMap = new Map()
    
    // Add existing products first (they have priority)
    existingProducts.forEach(product => {
      if (product.id) {
        productMap.set(product.id, {
          ...product,
          source: 'existing',
          priority: 1
        })
      }
    })
    
    // Add deep scan products
    deepScanProducts.forEach(product => {
      if (product.id && !productMap.has(product.id)) {
        productMap.set(product.id, {
          ...product,
          source: 'deep-scan',
          priority: 2
        })
      }
    })
    
    // Add expanded products
    expandedProducts.forEach(product => {
      if (product.id && !productMap.has(product.id)) {
        productMap.set(product.id, {
          ...product,
          source: 'expanded',
          priority: 3
        })
      }
    })
    
    allProducts = Array.from(productMap.values())
    
    console.log(`✅ Merged ${allProducts.length} unique products`)
    
    // Enhance products with additional data
    console.log('\n🔧 Enhancing products with additional data...')
    const enhancedProducts = allProducts.map(enhanceProduct)
    
    // Sort products by priority and name
    enhancedProducts.sort((a, b) => {
      if (a.priority !== b.priority) {
        return a.priority - b.priority
      }
      return a.name.localeCompare(b.name)
    })
    
    // Generate the new products.js file
    console.log('\n💾 Generating new products.js file...')
    await generateProductsFile(enhancedProducts)
    
    // Generate summary
    const summary = generateMergedSummary(enhancedProducts)
    console.log('\n📊 MERGED DATABASE SUMMARY:')
    console.log('=' .repeat(60))
    console.log(summary)
    
    // Save backup JSON
    const backupPath = path.join(process.cwd(), 'lib', 'data', 'cytte-merged-products-backup.json')
    fs.writeFileSync(backupPath, JSON.stringify(enhancedProducts, null, 2))
    console.log(`\n💾 Backup saved to: ${backupPath}`)
    
    return enhancedProducts
    
  } catch (error) {
    console.error('❌ Error merging database:', error)
    throw error
  }
}

function enhanceProduct(product) {
  // Ensure all required fields are present
  const enhanced = {
    ...product,
    
    // Ensure proper image paths
    images: product.images ? product.images.map(img => {
      if (img.startsWith('/')) return img
      return `/${img.replace(/\\/g, '/')}`
    }) : [],
    
    // Ensure proper pricing
    price: product.price || generateRandomPrice(product.brandType, product.isCollaboration),
    originalPrice: product.originalPrice || null,
    currency: product.currency || 'MXN',
    
    // Ensure proper availability
    availability: product.hasImages ? 'in-stock' : 'pre-order',
    stock: product.stock || Math.floor(Math.random() * 20) + 1,
    
    // Ensure proper metadata
    createdAt: product.createdAt || new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    
    // Add search optimization
    searchScore: calculateSearchScore(product),
    
    // Add category mappings for compatibility
    category: mapToLegacyCategory(product.style, product.brandType),
    
    // Ensure proper tags
    tags: product.tags || [],
    keywords: product.keywords || [],
    searchTerms: product.searchTerms || []
  }
  
  // Remove internal fields
  delete enhanced.source
  delete enhanced.priority
  delete enhanced.pathComponents
  delete enhanced.folderDepth
  delete enhanced.hasImages
  delete enhanced.imageCount
  
  return enhanced
}

function generateRandomPrice(brandType, isCollaboration) {
  const basePrices = {
    'luxury': [15000, 45000],
    'streetwear': [3000, 25000],
    'casual': [1500, 8000],
    'athletic': [2000, 12000]
  }
  
  const [min, max] = basePrices[brandType] || [2000, 10000]
  let price = Math.floor(Math.random() * (max - min) + min)
  
  if (isCollaboration) {
    price = Math.floor(price * 1.5)
  }
  
  return Math.round(price / 100) * 100
}

function calculateSearchScore(product) {
  let score = 0
  
  // Base score
  score += 10
  
  // Brand popularity
  if (product.brandType === 'luxury') score += 20
  if (product.isCollaboration) score += 15
  if (product.isLimited) score += 10
  if (product.isVip) score += 5
  
  // Image quality
  if (product.images && product.images.length > 0) score += 10
  if (product.images && product.images.length >= 4) score += 5
  
  // Availability
  if (product.availability === 'in-stock') score += 5
  
  return score
}

function mapToLegacyCategory(style, brandType) {
  if (brandType === 'luxury') return 'luxury'
  
  const categoryMap = {
    'sneakers': 'sneakers',
    'sandals': 'sandalias',
    'formal': 'formal',
    'casual': 'casual',
    'kids': 'casual'
  }
  
  return categoryMap[style] || 'sneakers'
}

async function generateProductsFile(products) {
  const productsFileContent = `// CYTTE Supplier Products Database
// Auto-generated from complete CYTTE structure scan
// Total products: ${products.length}
// Last updated: ${new Date().toISOString()}

// Product categories with proper Spanish names
export const categories = [
  // Main CYTTE Categories
  { id: 'sneakers', name: 'Sneakers', slug: 'sneakers', icon: '👟', cytteId: '1. SNEAKERS' },
  { id: 'sandals', name: 'Sandalias', slug: 'sandals', icon: '🩴', cytteId: '2. SANDALS' },
  { id: 'formal', name: 'Formal', slug: 'formal', icon: '👞', cytteId: '3. FORMAL' },
  { id: 'casual', name: 'Casual', slug: 'casual', icon: '👟', cytteId: '4. CASUAL' },
  { id: 'kids', name: 'Niños', slug: 'kids', icon: '👶', cytteId: '5. KIDS' },
  
  // Legacy categories for navigation compatibility
  { id: 'tennis', name: 'Tennis', slug: 'tennis', icon: '👟', maps_to: 'sneakers' },
  { id: 'botas', name: 'Botas', slug: 'botas', icon: '🥾', maps_to: 'formal' },
  { id: 'tacones', name: 'Tacones', slug: 'tacones', icon: '👠', maps_to: 'formal' },
  
  // Special filters
  { id: 'limited', name: 'Edición Limitada', slug: 'limited', icon: '⭐', filter: 'isLimited' },
  { id: 'luxury', name: 'Luxury', slug: 'luxury', icon: '💎', filter: 'brandType:luxury' },
]

// CYTTE Brand Categories
export const brands = [
  // Nike Ecosystem
  { id: 'nike-limited', name: 'Nike Limited Edition', slug: 'nike-limited', cytteId: '1. NIKE Limited Edition', type: 'streetwear' },
  { id: 'adidas-limited', name: 'Adidas Limited Edition', slug: 'adidas-limited', cytteId: '2. ADIDAS Limited Edition', type: 'streetwear' },
  
  // Luxury Brands
  { id: 'hermes', name: 'Hermès', slug: 'hermes', cytteId: '3. HERMES', type: 'luxury' },
  { id: 'gucci', name: 'Gucci', slug: 'gucci', cytteId: '4. GUCCI', type: 'luxury' },
  { id: 'dior', name: 'Dior', slug: 'dior', cytteId: '5. DIOR', type: 'luxury' },
  { id: 'lv', name: 'Louis Vuitton', slug: 'lv', cytteId: '6. LV', type: 'luxury' },
  { id: 'balenciaga', name: 'Balenciaga', slug: 'balenciaga', cytteId: '7. BALENCIAGA', type: 'luxury' },
  { id: 'chanel', name: 'Chanel', slug: 'chanel', cytteId: '8. CHANEL', type: 'luxury' },
  { id: 'louboutin', name: 'Christian Louboutin', slug: 'louboutin', cytteId: '9. LOUBOUTIN', type: 'luxury' },
  { id: 'off-white', name: 'Off-White', slug: 'off-white', cytteId: '10. OFF WHITE', type: 'streetwear' },
  { id: 'givenchy', name: 'Givenchy', slug: 'givenchy', cytteId: '11. GIVENCHY', type: 'luxury' },
  { id: 'maison-margiela', name: 'Maison Margiela', slug: 'maison-margiela', cytteId: '12. Maison MARGIELA', type: 'luxury' },
  { id: 'valentino', name: 'Valentino', slug: 'valentino', cytteId: '13. VALENTINO', type: 'luxury' },
  { id: 'prada', name: 'Prada', slug: 'prada', cytteId: '14. PRADA', type: 'luxury' },
  { id: 'miu-miu', name: 'Miu Miu', slug: 'miu-miu', cytteId: '15. MIU MIU', type: 'luxury' },
  { id: 'bottega-veneta', name: 'Bottega Veneta', slug: 'bottega-veneta', cytteId: '16. BOTTEGA VENETA', type: 'luxury' },
  { id: 'burberry', name: 'Burberry', slug: 'burberry', cytteId: '17. BURBERRY', type: 'luxury' },
  { id: 'golden-goose', name: 'Golden Goose', slug: 'golden-goose', cytteId: '18. GOLDEN GOOSE', type: 'luxury' },
  
  // Casual/Lifestyle Brands
  { id: 'ugg', name: 'UGG', slug: 'ugg', cytteId: '9. UGG', type: 'casual' },
  { id: 'crocs', name: 'Crocs', slug: 'crocs', cytteId: '13. CROCS', type: 'casual' },
  { id: 'birkenstock', name: 'Birkenstock', slug: 'birkenstock', cytteId: '15. BIRKENSTOCK', type: 'casual' },
  { id: 'common-project', name: 'Common Projects', slug: 'common-project', cytteId: 'Common Project', type: 'minimalist' }
]

// Gender Classifications
export const genders = [
  { id: 'mixte', name: 'Unisex', slug: 'unisex', cytteId: 'MIXTE' },
  { id: 'women', name: 'Mujer', slug: 'women', cytteId: 'WOMEN' },
  { id: 'men', name: 'Hombre', slug: 'men', cytteId: 'MEN' }
]

// Complete CYTTE Products Database
export const mockProducts = ${JSON.stringify(products, null, 2)}

// Helper functions for filtering and categorization with CYTTE structure
export const getProductsByCategory = (category) => {
  return mockProducts.filter(product => product.category === category)
}

// CYTTE-specific helper functions
export const getProductsBySubcategory = (subcategory) => {
  return mockProducts.filter(product => product.subcategory === subcategory)
}

export const getProductsByCollection = (collection) => {
  return mockProducts.filter(product => product.collection === collection)
}

export const getCytteProducts = (mainCategory, subcategory = null, collection = null) => {
  let filtered = mockProducts.filter(product => product.category === mainCategory)
  
  if (subcategory) {
    filtered = filtered.filter(product => product.subcategory === subcategory)
  }
  
  if (collection) {
    filtered = filtered.filter(product => product.collection === collection)
  }
  
  return filtered
}

export const getProductsByBrand = (brandId) => {
  return mockProducts.filter(product => product.brandId === brandId)
}

export const filterProducts = (filters) => {
  return mockProducts.filter(product => {
    return Object.entries(filters).every(([key, value]) => {
      if (typeof value === 'boolean') {
        return product[key] === value
      }
      if (Array.isArray(value)) {
        return value.includes(product[key])
      }
      return product[key] === value
    })
  })
}

// Advanced CYTTE search
export const searchCytteProducts = (query, filters = {}) => {
  let results = [...mockProducts]
  
  // Text search
  if (query) {
    const searchTerms = query.toLowerCase().split(' ')
    results = results.filter(product => {
      const searchableText = [
        product.name,
        product.brand,
        product.modelFamilyDisplay,
        product.collaborator,
        product.brandReference,
        ...(product.tags || []),
        ...(product.keywords || []),
        ...(product.searchTerms || [])
      ].join(' ').toLowerCase()
      
      return searchTerms.every(term => searchableText.includes(term))
    })
  }
  
  // Apply filters
  Object.entries(filters).forEach(([key, value]) => {
    if (value !== null && value !== undefined && value !== '') {
      results = results.filter(product => {
        if (typeof value === 'boolean') {
          return product[key] === value
        }
        if (Array.isArray(value)) {
          return value.includes(product[key])
        }
        return product[key] === value
      })
    }
  })
  
  // Sort by search score
  results.sort((a, b) => (b.searchScore || 0) - (a.searchScore || 0))
  
  return results
}

export default mockProducts
`
  
  const outputPath = path.join(process.cwd(), 'lib', 'data', 'products.js')
  fs.writeFileSync(outputPath, productsFileContent)
  
  console.log(`✅ New products.js generated with ${products.length} products`)
}

function generateMergedSummary(products) {
  const stats = {
    total: products.length,
    bySource: {},
    byStyle: {},
    byBrand: {},
    byGender: {},
    collaborations: 0,
    limited: 0,
    luxury: 0,
    withImages: 0,
    totalImages: 0
  }
  
  products.forEach(product => {
    // By source
    stats.bySource[product.source || 'unknown'] = (stats.bySource[product.source || 'unknown'] || 0) + 1
    
    // By style
    stats.byStyle[product.style] = (stats.byStyle[product.style] || 0) + 1
    
    // By brand
    stats.byBrand[product.brand] = (stats.byBrand[product.brand] || 0) + 1
    
    // By gender
    stats.byGender[product.gender] = (stats.byGender[product.gender] || 0) + 1
    
    // Special counts
    if (product.isCollaboration) stats.collaborations++
    if (product.isLimited) stats.limited++
    if (product.brandType === 'luxury') stats.luxury++
    if (product.images && product.images.length > 0) {
      stats.withImages++
      stats.totalImages += product.images.length
    }
  })
  
  return `
🎉 CYTTE DATABASE EXPANSION COMPLETE!

📈 TOTAL PRODUCTS: ${stats.total}
📸 Products with images: ${stats.withImages}
🖼️  Total images: ${stats.totalImages}
📊 Average images per product: ${Math.round((stats.totalImages / stats.withImages) * 10) / 10}

📂 BY SOURCE:
${Object.entries(stats.bySource).map(([source, count]) => `  ${source}: ${count}`).join('\n')}

📊 BY STYLE:
${Object.entries(stats.byStyle)
  .sort(([,a], [,b]) => b - a)
  .map(([style, count]) => `  ${style}: ${count}`)
  .join('\n')}

👥 BY GENDER:
${Object.entries(stats.byGender)
  .map(([gender, count]) => `  ${gender}: ${count}`)
  .join('\n')}

🏢 TOP 10 BRANDS:
${Object.entries(stats.byBrand)
  .sort(([,a], [,b]) => b - a)
  .slice(0, 10)
  .map(([brand, count]) => `  ${brand}: ${count}`)
  .join('\n')}

⭐ SPECIAL CATEGORIES:
  🤝 Collaborations: ${stats.collaborations}
  🌟 Limited Editions: ${stats.limited}
  💎 Luxury Items: ${stats.luxury}

🚀 DATABASE READY FOR PRODUCTION!
`
}

// Run if called directly
if (require.main === module) {
  mergeCytteDatabase().catch(console.error)
}

module.exports = { 
  mergeCytteDatabase,
  enhanceProduct,
  generateMergedSummary
}
