'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { useRouter, usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
import { useTheme } from '@/components/theme/ThemeProvider'
import AuthModal from '@/components/ui/AuthModal'
import SneakerAvatar from '@/components/ui/SneakerAvatar'

// Icons
const SearchIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
  </svg>
)

const ShareIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
  </svg>
)

const HeartIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
  </svg>
)

const ShoppingBagIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
  </svg>
)

const UserIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
  </svg>
)

const MenuIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
  </svg>
)

const CloseIcon = () => (
  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
  </svg>
)

const SunIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
  </svg>
)

const MoonIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
  </svg>
)

export default function NewHeader() {
  const router = useRouter()
  const pathname = usePathname()
  const { user, isAuthenticated } = useAuth()
  const { getItemsCount } = useCart()
  const { getTotalItemsCount } = useWishlist()
  const { theme, toggleTheme } = useTheme()

  const [isScrolled, setIsScrolled] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)

  // Handle scroll effect - COMPLETELY STABLE (NO FLASHING)
  useEffect(() => {
    let ticking = false

    const handleScroll = () => {
      if (!ticking) {
        requestAnimationFrame(() => {
          // Simple, stable threshold - no complex logic
          setIsScrolled(window.scrollY > 100)
          ticking = false
        })
        ticking = true
      }
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  // Handle navigation
  const handleNavigation = (path) => {
    router.push(path)
  }

  // Handle search
  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
      setSearchQuery('')
    }
  }

  const cartItemsCount = getItemsCount()
  const wishlistItemsCount = getTotalItemsCount()

  // Function to check if a route is active
  const isActiveRoute = (route) => {
    return pathname === route
  }

  // Navigation item functions removed - categories now in hamburger menu only

  return (
    <>
      {/* Two-Level Navigation Structure */}
      <header className="relative">
        {/* Top Level - SOLID NAVIGATION (NO GLASSMORPHIC EFFECTS) */}
        <div
          className={`${
            isScrolled
              ? 'fixed top-0 left-0 right-0 z-50 bg-white border-b border-gray-200 shadow-sm'
              : 'relative bg-white border-b border-gray-100'
          }`}
          style={{
            height: '72px',
            minHeight: '72px'
          }}
        >
          <div className="w-full mx-auto px-4 sm:px-6 lg:px-8 lg:max-w-7xl">
            <div className="flex items-center justify-between h-18" style={{ height: '72px' }}>
              {/* Logo Section - Enterprise Mobile Optimized */}
              <div className="flex-shrink-0 min-w-0">
                <a
                  href="/"
                  className="flex items-center space-x-3 cursor-pointer group hover:opacity-90 transition-opacity duration-200"
                  onClick={() => console.log('Logo clicked!')}
                >
                  <div className="flex items-center justify-center group-hover:scale-110 transition-all duration-300 w-10 h-10">
                    <Image
                      src="/twl.svg"
                      alt="TWL Logo"
                      width={40}
                      height={40}
                      className="w-full h-full filter group-hover:brightness-110 transition-all duration-200"
                    />
                  </div>

                  {/* Desktop: Full logo text */}
                  <div className="hidden md:block group-hover:scale-105 transition-transform duration-200">
                    <Image
                      src="/logotwl.svg"
                      alt="The White Laces"
                      width={150}
                      height={40}
                      className="w-auto h-8 filter group-hover:brightness-110 transition-all duration-300"
                      priority
                    />
                  </div>

                  {/* Mobile: Logo only - no text */}
                </a>
              </div>

              {/* Search Bar - Center - Enterprise Mobile Optimized */}
              <div className="hidden md:flex flex-1 max-w-md mx-4 lg:mx-8 min-w-0">
                <form onSubmit={handleSearch} className="w-full">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search here..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className={`w-full px-4 py-3 pl-10 pr-4 text-gray-700 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 font-poppins ${
                        isScrolled
                          ? 'bg-white/70 backdrop-blur-md border-2 border-white/40 shadow-sm hover:shadow-md'
                          : 'bg-gray-50/90 backdrop-blur-sm border-2 border-gray-200/60 hover:border-gray-300/80'
                      }`}
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                      <SearchIcon />
                    </div>
                  </div>
                </form>
              </div>

              {/* Right Side Icons - Enterprise Mobile Optimized */}
              <div className="flex items-center space-x-2 pr-2 flex-shrink-0 pt-1">
                {/* Mobile Menu Button - Enterprise Touch Optimized */}
                <button
                  className={`lg:hidden p-3 text-gray-600 hover:text-black transition-all duration-300 rounded-full min-w-[44px] min-h-[44px] touch-manipulation ${
                    isScrolled ? 'hover:bg-white/40 backdrop-blur-sm' : 'hover:bg-gray-100/50'
                  }`}
                  onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                  aria-label="Toggle mobile menu"
                >
                  {isMobileMenuOpen ? <CloseIcon /> : <MenuIcon />}
                </button>

                {/* Theme Toggle Button - Enterprise Touch Optimized */}
                <button
                  onClick={toggleTheme}
                  className="p-3 text-gray-600 hover:text-black transition-all duration-300 rounded-full transform hover:scale-110 min-w-[44px] min-h-[44px] touch-manipulation"
                  aria-label="Toggle theme"
                >
                  <motion.div
                    animate={{ rotate: theme === 'dark' ? 0 : 180 }}
                    transition={{ duration: 0.5, type: "spring", stiffness: 200 }}
                  >
                    {theme === 'dark' ? <SunIcon /> : <MoonIcon />}
                  </motion.div>
                </button>

                <button className="hidden sm:block p-3 text-gray-600 hover:text-black transition-all duration-300 rounded-full transform hover:scale-110 min-w-[44px] min-h-[44px] touch-manipulation" aria-label="Share">
                  <ShareIcon />
                </button>

                <a
                  href="/wishlist"
                  className="twl-button p-3 text-gray-600 hover:text-black relative rounded-full cursor-pointer transition-all duration-300 transform hover:scale-110 min-w-[44px] min-h-[44px] touch-manipulation"
                  onClick={() => console.log('Wishlist clicked!')}
                  aria-label="View wishlist"
                >
                  <HeartIcon />
                  {wishlistItemsCount > 0 && (
                    <span className="absolute top-0 right-0 bg-lime-green text-black text-xs rounded-full h-5 w-5 flex items-center justify-center font-semibold shadow-lg transform translate-x-1 -translate-y-1">
                      {wishlistItemsCount}
                    </span>
                  )}
                </a>

                <a
                  href="/cart"
                  className="twl-button p-3 text-gray-600 hover:text-black relative rounded-full cursor-pointer transition-all duration-300 transform hover:scale-110 min-w-[44px] min-h-[44px] touch-manipulation"
                  onClick={() => console.log('Cart clicked!')}
                  aria-label="View shopping cart"
                >
                  <ShoppingBagIcon />
                  {cartItemsCount > 0 && (
                    <span className="absolute top-0 right-0 bg-lime-green text-black text-xs rounded-full h-5 w-5 flex items-center justify-center font-semibold shadow-lg transform translate-x-1 -translate-y-1">
                      {cartItemsCount}
                    </span>
                  )}
                </a>

                {!isAuthenticated ? (
                  // Show user icon when not logged in - Enterprise Touch Optimized
                  <button
                    className="twl-button p-3 text-gray-600 hover:text-black rounded-full cursor-pointer transition-all duration-300 transform hover:scale-110 min-w-[44px] min-h-[44px] touch-manipulation"
                    onClick={() => setIsAuthModalOpen(true)}
                    aria-label="Sign in to account"
                  >
                    <UserIcon />
                  </button>
                ) : (
                  // Show username + avatar when logged in - Enterprise Touch Optimized
                  <div className="flex items-center gap-3">
                    <div className="hidden lg:block text-sm text-gray-600 dark:text-gray-300 font-medium">
                      {user?.firstName || 'Usuario'}
                    </div>

                    <button
                      className="twl-button cursor-pointer transition-all duration-300 transform hover:scale-110 min-w-[44px] min-h-[44px] touch-manipulation"
                      onClick={() => router.push('/account')}
                      aria-label="Go to account dashboard"
                    >
                      <SneakerAvatar
                        type={user?.avatar || 'classic'}
                        size="md"
                        className="border-2 border-lime-green/30 hover:border-lime-green"
                      />
                    </button>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Level Navigation Removed - Categories available in hamburger menu */}

        {/* Mobile Menu Dropdown - CRITICAL FEATURE RESTORED */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
              className="lg:hidden fixed left-0 right-0 z-40 bg-white/98 backdrop-blur-xl border-b border-gray-200 shadow-lg"
              style={{ top: '72px' }}
            >
            <div className="w-full mx-auto px-4 py-6 lg:max-w-7xl">
              {/* Mobile Navigation Links - Optimized Spacing */}
              <nav className="space-y-1">
                <a
                  href="/brands"
                  className="block text-gray-700 hover:text-black font-medium py-4 px-5 rounded-lg hover:bg-gray-100/70 transition-all duration-300 transform hover:scale-[1.01] hover:translate-x-1 min-h-[48px] touch-manipulation"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  BRANDS
                </a>
                <a
                  href="/limited-editions"
                  className="block text-gray-700 hover:text-black font-medium py-4 px-5 rounded-lg hover:bg-gray-100/70 transition-all duration-300 transform hover:scale-[1.01] hover:translate-x-1 min-h-[48px] touch-manipulation"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  LIMITED EDITIONS
                </a>
                <a
                  href="/women"
                  className="block text-gray-700 hover:text-black font-medium py-4 px-5 rounded-lg hover:bg-gray-100/70 transition-all duration-300 transform hover:scale-[1.01] hover:translate-x-1 min-h-[48px] touch-manipulation"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  WOMEN
                </a>
                <a
                  href="/men"
                  className="block text-gray-700 hover:text-black font-medium py-4 px-5 rounded-lg hover:bg-gray-100/70 transition-all duration-300 transform hover:scale-[1.01] hover:translate-x-1 min-h-[48px] touch-manipulation"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  MEN
                </a>
                <a
                  href="/kids"
                  className="block text-gray-700 hover:text-black font-medium py-4 px-5 rounded-lg hover:bg-gray-100/70 transition-all duration-300 transform hover:scale-[1.01] hover:translate-x-1 min-h-[48px] touch-manipulation"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  KIDS
                </a>
                <a
                  href="/tienda"
                  className="block text-gray-700 hover:text-black font-medium py-4 px-5 rounded-lg hover:bg-gray-100/70 transition-all duration-300 transform hover:scale-[1.01] hover:translate-x-1 min-h-[48px] touch-manipulation"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  TIENDA
                </a>
                <a
                  href="/ia"
                  className="block text-gray-700 hover:text-black font-medium py-4 px-5 rounded-lg hover:bg-gray-100/70 transition-all duration-300 transform hover:scale-[1.01] hover:translate-x-1 min-h-[48px] touch-manipulation"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  IA
                </a>
                <a
                  href="/social"
                  className="block text-gray-700 hover:text-black font-medium py-4 px-5 rounded-lg hover:bg-gray-100/70 transition-all duration-300 transform hover:scale-[1.01] hover:translate-x-1 min-h-[48px] touch-manipulation"
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  SOCIAL
                </a>
              </nav>

              {/* Mobile Search Bar */}
              <div className="mt-6 pt-6 border-t border-gray-200/50">
                <form onSubmit={handleSearch} className="w-full">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search products..."
                      value={searchQuery}
                      onChange={(e) => setSearchQuery(e.target.value)}
                      className="w-full px-4 py-3 pl-10 pr-4 text-gray-700 bg-gray-50/90 border-2 border-gray-200/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent transition-all duration-300 font-poppins hover:border-gray-300/80"
                    />
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3">
                      <SearchIcon />
                    </div>
                  </div>
                </form>
              </div>

              {/* Mobile Theme Toggle */}
              <div className="mt-4 pt-4 border-t border-gray-200/50">
                <button
                  onClick={toggleTheme}
                  className="flex items-center justify-between w-full text-gray-700 hover:text-black font-medium py-3 px-4 rounded-lg hover:bg-gray-100/50 transition-all duration-200"
                >
                  <span>Theme</span>
                  <motion.div
                    animate={{ rotate: theme === 'dark' ? 0 : 180 }}
                    transition={{ duration: 0.5, type: "spring", stiffness: 200 }}
                  >
                    {theme === 'dark' ? <SunIcon /> : <MoonIcon />}
                  </motion.div>
                </button>
              </div>
            </div>
          </motion.div>
          )}
        </AnimatePresence>
      </header>

      {/* ENTERPRISE-GRADE MOBILE BOTTOM NAVIGATION */}
      <nav
        className="fixed bottom-0 left-0 right-0 z-50 lg:hidden"
        role="navigation"
        aria-label="Mobile navigation"
      >
        <div className="bg-white/95 dark:bg-black/95 backdrop-blur-xl border-t border-black/10 dark:border-white/10 shadow-2xl">
          {/* Safe area padding for iOS */}
          <div className="px-2 pt-2 pb-4">
            <div className="flex items-center justify-around">
              {[
                {
                  name: 'Inicio',
                  href: '/',
                  ariaLabel: 'Ir a página de inicio',
                  icon: (isActive) => (
                    <svg
                      className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`}
                      fill={isActive ? 'currentColor' : 'none'}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                    </svg>
                  )
                },
                {
                  name: 'Tienda',
                  href: '/tienda',
                  ariaLabel: 'Ir a la tienda',
                  icon: (isActive) => (
                    <svg
                      className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`}
                      fill={isActive ? 'currentColor' : 'none'}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                  )
                },
                {
                  name: 'IA',
                  href: '/ia',
                  ariaLabel: 'Funciones de inteligencia artificial',
                  icon: (isActive) => (
                    <svg
                      className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`}
                      fill={isActive ? 'currentColor' : 'none'}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                    </svg>
                  )
                },
                {
                  name: 'Social',
                  href: '/social',
                  ariaLabel: 'Red social y comunidad',
                  icon: (isActive) => (
                    <svg
                      className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`}
                      fill={isActive ? 'currentColor' : 'none'}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  )
                },
                {
                  name: 'Perfil',
                  href: isAuthenticated ? '/account' : '#',
                  ariaLabel: isAuthenticated ? 'Ir a mi perfil' : 'Iniciar sesión',
                  onClick: () => {
                    if (isAuthenticated) {
                      router.push('/account')
                    } else {
                      setIsAuthModalOpen(true)
                    }
                  },
                  icon: (isActive) => isAuthenticated ? (
                    <SneakerAvatar
                      type={user?.avatar || 'classic'}
                      size="sm"
                      className="border border-lime-green/50"
                    />
                  ) : (
                    <svg
                      className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`}
                      fill={isActive ? 'currentColor' : 'none'}
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                      aria-hidden="true"
                    >
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                  )
                }
              ].map((item, index) => {
                const active = isActiveRoute(item.href)

                return (
                  <motion.button
                    key={item.name}
                    onClick={item.onClick || (() => handleNavigation(item.href))}
                    className={`
                      flex flex-col items-center justify-center p-3 rounded-2xl
                      transition-all duration-300 min-w-[60px] min-h-[60px] relative
                      touch-manipulation
                      ${active
                        ? 'bg-lime-green/10 text-lime-green'
                        : 'text-black/60 dark:text-white/60 hover:text-black dark:hover:text-white hover:bg-black/5 dark:hover:bg-white/5'
                      }
                    `}
                    whileTap={{ scale: 0.9 }}
                    initial={{ y: 20, opacity: 0 }}
                    animate={{ y: 0, opacity: 1 }}
                    transition={{ delay: index * 0.1, duration: 0.3 }}
                    aria-label={item.ariaLabel}
                    role="button"
                    tabIndex={0}
                  >
                    {/* Active indicator */}
                    {active && (
                      <motion.div
                        layoutId="activeTab"
                        className="absolute inset-0 bg-lime-green/10 rounded-2xl"
                        transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
                      />
                    )}

                    {/* Icon */}
                    <motion.div
                      className="relative z-10 mb-1"
                      animate={{
                        scale: active ? 1.1 : 1,
                        y: active ? -2 : 0
                      }}
                      transition={{ duration: 0.2 }}
                    >
                      {item.icon(active)}
                    </motion.div>

                    {/* Label */}
                    <span className={`
                      text-xs font-medium relative z-10 transition-all duration-300
                      ${active ? 'text-lime-green font-semibold' : ''}
                    `}>
                      {item.name}
                    </span>

                    {/* Active dot */}
                    {active && (
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        className="absolute -top-1 w-1 h-1 bg-lime-green rounded-full"
                      />
                    )}
                  </motion.button>
                )
              })}
            </div>
          </div>
        </div>
      </nav>

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialMode="login"
      />
    </>
  )
}
