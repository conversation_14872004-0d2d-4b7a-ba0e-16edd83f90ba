#!/usr/bin/env node

/**
 * 🏢 ENTERPRISE-GRADE MOBILE AUDIT SYSTEM
 * 
 * Comprehensive mobile optimization audit with enterprise standards
 * Built for production-ready luxury e-commerce validation
 * 
 * Features:
 * - Navigation functionality audit
 * - Touch interaction validation
 * - Performance benchmarking
 * - Accessibility compliance
 * - Cross-device compatibility
 * - Security assessment
 * - UX/UI standards verification
 */

const puppeteer = require('puppeteer')
const fs = require('fs').promises
const path = require('path')

class EnterpriseMobileAuditor {
  constructor() {
    this.results = {
      timestamp: new Date().toISOString(),
      overall: 'PENDING',
      categories: {},
      recommendations: [],
      criticalIssues: [],
      summary: {}
    }
  }

  async runAudit() {
    console.log('🏢 ENTERPRISE-GRADE MOBILE AUDIT')
    console.log('================================')
    console.log('🎯 Comprehensive mobile optimization validation...')
    console.log('')

    try {
      const browser = await puppeteer.launch({
        headless: 'new',
        args: [
          '--no-sandbox',
          '--disable-setuid-sandbox',
          '--disable-dev-shm-usage',
          '--disable-web-security',
          '--disable-features=VizDisplayCompositor'
        ]
      })

      const page = await browser.newPage()

      // Enterprise mobile device emulation
      await page.emulate({
        name: 'Enterprise Mobile Test',
        userAgent: 'Mozilla/5.0 (iPhone; CPU iPhone OS 15_0 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/15.0 Mobile/15E148 Safari/604.1',
        viewport: {
          width: 390,
          height: 844,
          deviceScaleFactor: 3,
          isMobile: true,
          hasTouch: true,
          isLandscape: false
        }
      })

      // Audit categories
      await this.auditNavigation(page)
      await this.auditTouchInteractions(page)
      await this.auditPerformance(page)
      await this.auditAccessibility(page)
      await this.auditResponsiveDesign(page)
      await this.auditSecurity(page)
      await this.auditUXStandards(page)

      // Generate comprehensive results
      this.calculateOverallScore()
      await this.saveResults()
      this.generateReport()

      await browser.close()
      return this.results

    } catch (error) {
      console.error('❌ Enterprise mobile audit failed:', error)
      throw error
    }
  }

  async auditNavigation(page) {
    console.log('🧭 Auditing navigation functionality...')
    
    try {
      await page.goto('http://localhost:3000', { waitUntil: 'networkidle0' })

      const navigationAudit = await page.evaluate(() => {
        const issues = []
        const recommendations = []
        
        // Check for mobile navigation
        const mobileNav = document.querySelector('.mobile-nav, [class*="mobile"], nav')
        if (!mobileNav) {
          issues.push('No mobile navigation detected')
        }

        // Check navigation visibility
        const header = document.querySelector('header, [role="banner"]')
        if (header) {
          const headerStyles = window.getComputedStyle(header)
          if (headerStyles.position !== 'fixed' && headerStyles.position !== 'sticky') {
            recommendations.push('Consider sticky navigation for better mobile UX')
          }
        }

        // Check for hamburger menu or mobile menu toggle
        const menuToggle = document.querySelector('[aria-label*="menu"], .menu-toggle, .hamburger')
        if (!menuToggle && window.innerWidth < 768) {
          issues.push('No mobile menu toggle found for small screens')
        }

        // Check navigation links
        const navLinks = document.querySelectorAll('nav a, [role="navigation"] a')
        let touchFriendlyNavLinks = 0
        navLinks.forEach(link => {
          const rect = link.getBoundingClientRect()
          if (rect.width >= 44 && rect.height >= 44) {
            touchFriendlyNavLinks++
          }
        })

        const navScore = Math.max(0, 100 - (issues.length * 25) - (recommendations.length * 10))

        return {
          score: navScore,
          issues,
          recommendations,
          metrics: {
            totalNavLinks: navLinks.length,
            touchFriendlyNavLinks,
            touchFriendlyPercentage: navLinks.length > 0 ? (touchFriendlyNavLinks / navLinks.length) * 100 : 100
          }
        }
      })

      this.results.categories.navigation = navigationAudit
      console.log(`   ✅ Navigation audit complete: ${navigationAudit.score}/100`)

    } catch (error) {
      this.results.categories.navigation = {
        score: 0,
        error: error.message,
        issues: ['Navigation audit failed'],
        recommendations: ['Fix navigation implementation']
      }
      console.log(`   ❌ Navigation audit failed: ${error.message}`)
    }
  }

  async auditTouchInteractions(page) {
    console.log('👆 Auditing touch interactions...')
    
    try {
      const touchAudit = await page.evaluate(() => {
        const issues = []
        const recommendations = []
        
        // Check touch targets
        const interactiveElements = document.querySelectorAll('button, a, input, [role="button"], [onclick]')
        let touchFriendlyCount = 0
        let tooSmallCount = 0

        interactiveElements.forEach(element => {
          const rect = element.getBoundingClientRect()
          const styles = window.getComputedStyle(element)
          
          if (rect.width >= 44 && rect.height >= 44) {
            touchFriendlyCount++
          } else if (rect.width < 44 || rect.height < 44) {
            tooSmallCount++
          }

          // Check for touch-action optimization
          if (styles.touchAction === 'auto') {
            // Could be optimized
          }
        })

        // Calculate scores
        const touchFriendlyPercentage = interactiveElements.length > 0 ? 
          (touchFriendlyCount / interactiveElements.length) * 100 : 100

        if (touchFriendlyPercentage < 80) {
          issues.push(`${tooSmallCount} touch targets are too small (< 44px)`)
        }

        if (touchFriendlyPercentage < 90) {
          recommendations.push('Increase touch target sizes for better accessibility')
        }

        // Check for touch optimizations
        const htmlElement = document.documentElement
        const htmlStyles = window.getComputedStyle(htmlElement)
        
        if (!htmlStyles.touchAction.includes('manipulation')) {
          recommendations.push('Add touch-action: manipulation for better touch response')
        }

        const touchScore = Math.round(touchFriendlyPercentage)

        return {
          score: touchScore,
          issues,
          recommendations,
          metrics: {
            totalInteractiveElements: interactiveElements.length,
            touchFriendlyElements: touchFriendlyCount,
            tooSmallElements: tooSmallCount,
            touchFriendlyPercentage
          }
        }
      })

      this.results.categories.touchInteractions = touchAudit
      console.log(`   ✅ Touch interactions audit complete: ${touchAudit.score}/100`)

    } catch (error) {
      this.results.categories.touchInteractions = {
        score: 0,
        error: error.message,
        issues: ['Touch interactions audit failed'],
        recommendations: ['Implement proper touch targets']
      }
      console.log(`   ❌ Touch interactions audit failed: ${error.message}`)
    }
  }

  async auditPerformance(page) {
    console.log('⚡ Auditing performance...')
    
    try {
      const performanceAudit = await page.evaluate(() => {
        const issues = []
        const recommendations = []
        
        const navigation = performance.getEntriesByType('navigation')[0]
        const paint = performance.getEntriesByType('paint')
        const resources = performance.getEntriesByType('resource')

        const metrics = {
          loadTime: navigation ? navigation.loadEventEnd - navigation.loadEventStart : 0,
          domContentLoaded: navigation ? navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart : 0,
          firstPaint: paint.find(entry => entry.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: paint.find(entry => entry.name === 'first-contentful-paint')?.startTime || 0,
          resourceCount: resources.length,
          totalResourceSize: resources.reduce((sum, r) => sum + (r.transferSize || 0), 0)
        }

        // Performance scoring
        let score = 100

        if (metrics.firstContentfulPaint > 3000) {
          score -= 30
          issues.push('First Contentful Paint is too slow (>3s)')
        } else if (metrics.firstContentfulPaint > 2000) {
          score -= 15
          recommendations.push('Optimize First Contentful Paint (<2s)')
        }

        if (metrics.resourceCount > 100) {
          score -= 20
          issues.push('Too many resources loaded')
        } else if (metrics.resourceCount > 50) {
          score -= 10
          recommendations.push('Consider reducing resource count')
        }

        if (metrics.totalResourceSize > 5 * 1024 * 1024) { // 5MB
          score -= 25
          issues.push('Total resource size is too large (>5MB)')
        } else if (metrics.totalResourceSize > 2 * 1024 * 1024) { // 2MB
          score -= 10
          recommendations.push('Optimize resource sizes')
        }

        return {
          score: Math.max(0, score),
          issues,
          recommendations,
          metrics
        }
      })

      this.results.categories.performance = performanceAudit
      console.log(`   ✅ Performance audit complete: ${performanceAudit.score}/100`)

    } catch (error) {
      this.results.categories.performance = {
        score: 0,
        error: error.message,
        issues: ['Performance audit failed'],
        recommendations: ['Implement performance optimizations']
      }
      console.log(`   ❌ Performance audit failed: ${error.message}`)
    }
  }

  async auditAccessibility(page) {
    console.log('♿ Auditing accessibility...')
    
    try {
      const accessibilityAudit = await page.evaluate(() => {
        const issues = []
        const recommendations = []
        
        // Check for alt text on images
        const images = document.querySelectorAll('img')
        let imagesWithoutAlt = 0
        images.forEach(img => {
          if (!img.alt || img.alt.trim() === '') {
            imagesWithoutAlt++
          }
        })

        if (imagesWithoutAlt > 0) {
          issues.push(`${imagesWithoutAlt} images missing alt text`)
        }

        // Check for proper heading structure
        const headings = document.querySelectorAll('h1, h2, h3, h4, h5, h6')
        if (headings.length === 0) {
          issues.push('No heading structure found')
        }

        // Check for focus indicators
        const focusableElements = document.querySelectorAll('button, a, input, select, textarea, [tabindex]')
        // This would need more sophisticated testing in a real audit

        // Check for ARIA labels
        const interactiveElements = document.querySelectorAll('button, [role="button"]')
        let elementsWithoutLabels = 0
        interactiveElements.forEach(element => {
          if (!element.getAttribute('aria-label') && !element.textContent.trim()) {
            elementsWithoutLabels++
          }
        })

        if (elementsWithoutLabels > 0) {
          recommendations.push(`${elementsWithoutLabels} interactive elements could use better labeling`)
        }

        const accessibilityScore = Math.max(0, 100 - (issues.length * 20) - (recommendations.length * 10))

        return {
          score: accessibilityScore,
          issues,
          recommendations,
          metrics: {
            totalImages: images.length,
            imagesWithoutAlt,
            totalHeadings: headings.length,
            interactiveElements: interactiveElements.length,
            elementsWithoutLabels
          }
        }
      })

      this.results.categories.accessibility = accessibilityAudit
      console.log(`   ✅ Accessibility audit complete: ${accessibilityAudit.score}/100`)

    } catch (error) {
      this.results.categories.accessibility = {
        score: 0,
        error: error.message,
        issues: ['Accessibility audit failed'],
        recommendations: ['Implement accessibility standards']
      }
      console.log(`   ❌ Accessibility audit failed: ${error.message}`)
    }
  }

  async auditResponsiveDesign(page) {
    console.log('📱 Auditing responsive design...')
    
    try {
      const viewports = [
        { width: 320, height: 568, name: 'iPhone SE' },
        { width: 375, height: 667, name: 'iPhone 8' },
        { width: 390, height: 844, name: 'iPhone 13 Pro' },
        { width: 414, height: 896, name: 'iPhone 11 Pro Max' },
        { width: 768, height: 1024, name: 'iPad' }
      ]

      let responsiveScore = 0
      const issues = []
      const recommendations = []

      for (const viewport of viewports) {
        await page.setViewport(viewport)
        await new Promise(resolve => setTimeout(resolve, 500))

        const viewportTest = await page.evaluate((vp) => {
          const hasHorizontalScroll = document.body.scrollWidth > window.innerWidth
          const hasVerticalOverflow = document.body.scrollHeight > window.innerHeight * 3 // Reasonable limit
          
          return {
            viewport: vp.name,
            hasHorizontalScroll,
            hasVerticalOverflow,
            actualWidth: window.innerWidth,
            actualHeight: window.innerHeight
          }
        }, viewport)

        if (!viewportTest.hasHorizontalScroll) {
          responsiveScore += 20
        } else {
          issues.push(`Horizontal scroll detected on ${viewport.name}`)
        }
      }

      if (responsiveScore < 80) {
        recommendations.push('Improve responsive design for better mobile compatibility')
      }

      this.results.categories.responsiveDesign = {
        score: responsiveScore,
        issues,
        recommendations,
        metrics: { viewportsTested: viewports.length }
      }

      console.log(`   ✅ Responsive design audit complete: ${responsiveScore}/100`)

    } catch (error) {
      this.results.categories.responsiveDesign = {
        score: 0,
        error: error.message,
        issues: ['Responsive design audit failed'],
        recommendations: ['Implement responsive design']
      }
      console.log(`   ❌ Responsive design audit failed: ${error.message}`)
    }
  }

  async auditSecurity(page) {
    console.log('🔒 Auditing security...')
    
    try {
      const securityAudit = await page.evaluate(() => {
        const issues = []
        const recommendations = []
        
        // Check for HTTPS
        if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
          issues.push('Site not served over HTTPS')
        }

        // Check for mixed content
        const insecureResources = Array.from(document.querySelectorAll('img, script, link')).filter(el => {
          const src = el.src || el.href
          return src && src.startsWith('http:') && location.protocol === 'https:'
        })

        if (insecureResources.length > 0) {
          issues.push(`${insecureResources.length} insecure resources detected`)
        }

        // Check for CSP headers (would need server-side check)
        // Check for proper form handling
        const forms = document.querySelectorAll('form')
        forms.forEach(form => {
          if (form.method.toLowerCase() === 'get' && form.querySelector('input[type="password"]')) {
            issues.push('Password form using GET method')
          }
        })

        const securityScore = Math.max(0, 100 - (issues.length * 25) - (recommendations.length * 10))

        return {
          score: securityScore,
          issues,
          recommendations,
          metrics: {
            protocol: location.protocol,
            insecureResources: insecureResources.length,
            totalForms: forms.length
          }
        }
      })

      this.results.categories.security = securityAudit
      console.log(`   ✅ Security audit complete: ${securityAudit.score}/100`)

    } catch (error) {
      this.results.categories.security = {
        score: 0,
        error: error.message,
        issues: ['Security audit failed'],
        recommendations: ['Implement security best practices']
      }
      console.log(`   ❌ Security audit failed: ${error.message}`)
    }
  }

  async auditUXStandards(page) {
    console.log('🎨 Auditing UX standards...')
    
    try {
      const uxAudit = await page.evaluate(() => {
        const issues = []
        const recommendations = []
        
        // Check for loading states
        const loadingElements = document.querySelectorAll('[class*="loading"], [class*="spinner"], [class*="skeleton"]')
        if (loadingElements.length === 0) {
          recommendations.push('Consider adding loading states for better UX')
        }

        // Check for error states
        const errorElements = document.querySelectorAll('[class*="error"], [role="alert"]')
        
        // Check for empty states
        const emptyStateElements = document.querySelectorAll('[class*="empty"], [class*="no-results"]')
        
        // Check for feedback mechanisms
        const feedbackElements = document.querySelectorAll('[class*="toast"], [class*="notification"], [class*="alert"]')
        
        // Check for consistent spacing
        const elements = document.querySelectorAll('*')
        // This would need more sophisticated analysis

        // Check for proper contrast (simplified)
        const textElements = document.querySelectorAll('p, h1, h2, h3, h4, h5, h6, span, a')
        // This would need color contrast analysis

        const uxScore = Math.max(0, 100 - (issues.length * 15) - (recommendations.length * 5))

        return {
          score: uxScore,
          issues,
          recommendations,
          metrics: {
            loadingElements: loadingElements.length,
            errorElements: errorElements.length,
            emptyStateElements: emptyStateElements.length,
            feedbackElements: feedbackElements.length,
            textElements: textElements.length
          }
        }
      })

      this.results.categories.uxStandards = uxAudit
      console.log(`   ✅ UX standards audit complete: ${uxAudit.score}/100`)

    } catch (error) {
      this.results.categories.uxStandards = {
        score: 0,
        error: error.message,
        issues: ['UX standards audit failed'],
        recommendations: ['Implement UX best practices']
      }
      console.log(`   ❌ UX standards audit failed: ${error.message}`)
    }
  }

  calculateOverallScore() {
    const categories = Object.values(this.results.categories)
    const totalScore = categories.reduce((sum, category) => sum + (category.score || 0), 0)
    const averageScore = Math.round(totalScore / categories.length)
    
    // Collect all issues and recommendations
    categories.forEach(category => {
      if (category.issues) {
        this.results.criticalIssues.push(...category.issues)
      }
      if (category.recommendations) {
        this.results.recommendations.push(...category.recommendations)
      }
    })

    this.results.summary = {
      totalCategories: categories.length,
      averageScore,
      criticalIssues: this.results.criticalIssues.length,
      recommendations: this.results.recommendations.length
    }

    if (averageScore >= 90) this.results.overall = 'EXCELLENT'
    else if (averageScore >= 80) this.results.overall = 'GOOD'
    else if (averageScore >= 70) this.results.overall = 'FAIR'
    else this.results.overall = 'NEEDS_IMPROVEMENT'
  }

  async saveResults() {
    const reportsDir = path.join(process.cwd(), 'reports')
    
    try {
      await fs.mkdir(reportsDir, { recursive: true })
    } catch (error) {
      // Directory already exists
    }
    
    const resultsPath = path.join(reportsDir, 'enterprise-mobile-audit.json')
    await fs.writeFile(resultsPath, JSON.stringify(this.results, null, 2))
    
    console.log(`📊 Enterprise audit results saved to: ${resultsPath}`)
  }

  generateReport() {
    console.log('')
    console.log('🏢 ENTERPRISE MOBILE AUDIT RESULTS')
    console.log('==================================')
    console.log(`🎯 Overall Status: ${this.results.overall}`)
    console.log(`📈 Average Score: ${this.results.summary.averageScore}/100`)
    console.log(`🚨 Critical Issues: ${this.results.summary.criticalIssues}`)
    console.log(`💡 Recommendations: ${this.results.summary.recommendations}`)
    console.log('')
    
    console.log('📊 Category Breakdown:')
    Object.entries(this.results.categories).forEach(([category, result]) => {
      const status = result.score >= 80 ? '✅' : result.score >= 60 ? '⚠️' : '❌'
      console.log(`   ${status} ${category}: ${result.score}/100`)
    })
    
    if (this.results.criticalIssues.length > 0) {
      console.log('')
      console.log('🚨 Critical Issues:')
      this.results.criticalIssues.slice(0, 5).forEach(issue => {
        console.log(`   • ${issue}`)
      })
    }
    
    if (this.results.recommendations.length > 0) {
      console.log('')
      console.log('💡 Top Recommendations:')
      this.results.recommendations.slice(0, 5).forEach(rec => {
        console.log(`   • ${rec}`)
      })
    }
    
    console.log('')
    console.log('🎉 ENTERPRISE MOBILE AUDIT COMPLETE!')
    
    if (this.results.summary.averageScore >= 85) {
      console.log('🏆 Mobile implementation meets enterprise standards!')
    } else if (this.results.summary.averageScore >= 75) {
      console.log('👍 Mobile implementation is good with room for improvement!')
    } else {
      console.log('⚠️  Mobile implementation needs significant improvements.')
    }
  }
}

// Main execution
async function main() {
  const auditor = new EnterpriseMobileAuditor()
  
  try {
    const results = await auditor.runAudit()
    
    if (results.summary.averageScore < 75) {
      console.log('\n⚠️  Enterprise mobile audit below target.')
      process.exit(1)
    }
    
    console.log('\n🎉 Enterprise mobile audit successful!')
    process.exit(0)
    
  } catch (error) {
    console.error('❌ Enterprise mobile audit failed:', error)
    process.exit(1)
  }
}

// Run the audit
if (require.main === module) {
  main()
}

module.exports = EnterpriseMobileAuditor
