// Real Products Loader for TWL - Connects to actual CYTTE product database
import { products } from '@/data/products'
import cytteProducts from '@/lib/data/cytte-deep-scan-products.json'

console.log('🔧🔧🔧 REAL PRODUCTS LOADER MODULE LOADED!')
console.log('🔧🔧🔧 MODULE IS BEING IMPORTED!')

// Convert database image paths to actual file paths
const convertDatabasePathToActualPath = (imagePath) => {
  if (!imagePath) return imagePath

  let convertedPath = imagePath

  // Convert from CYTTE database format to new organized structure
  // Database: /--materials/shoes/2. CYTTE/1. SNEAKERS/1. NIKE Limited Edition/...
  // Old:      /products/1. SNEAKERS/1. NIKE Limited Edition/...
  // New:      /products-organized/1-sneakers/1-nike-limited/...

  if (convertedPath.startsWith('/--materials/shoes/2. CYTTE/')) {
    convertedPath = convertedPath.replace('/--materials/shoes/2. CYTTE/', '/products-organized/')
  }

  // Also handle other potential formats
  if (convertedPath.startsWith('/images/products/')) {
    convertedPath = convertedPath.replace('/images/products/', '/products-organized/')
  }

  // Handle old products path format
  if (convertedPath.startsWith('/products/')) {
    convertedPath = convertedPath.replace('/products/', '/products-organized/')
  }

  // Convert file extensions from .jpg to .webp (CYTTE images are stored as WebP)
  if (convertedPath.endsWith('.jpg')) {
    convertedPath = convertedPath.replace('.jpg', '.webp')
  }

  // Log the conversion for debugging (disabled for production)
  // if (imagePath.includes('--materials') || imagePath.endsWith('.jpg')) {
  //   console.log('Converting image path:', imagePath, '→', convertedPath)
  // }

  return convertedPath
}

// Group products by model family to create variants
const groupProductsByModelFamily = (products) => {
  const grouped = {}

  products.forEach(product => {
    // Create a grouping key based on brand, model family, and gender
    const groupKey = `${product.brand}-${product.modelFamily}-${product.gender}`.toLowerCase()

    if (!grouped[groupKey]) {
      grouped[groupKey] = {
        baseProduct: product,
        variants: []
      }
    }

    grouped[groupKey].variants.push(product)
  })

  return grouped
}

// Create a unified product with all variants
const createUnifiedProduct = (groupedData) => {
  const { baseProduct, variants } = groupedData

  // Use the first variant as the base, but include all images from all variants
  const allImages = []
  const models = []

  variants.forEach((variant, index) => {
    // Convert all image paths for this variant
    const convertedImages = variant.images?.map(img => convertDatabasePathToActualPath(img)) || []

    // Add to models array for variant selection
    models.push({
      id: variant.id,
      name: `${variant.brandReference || variant.internalReference || `Modelo ${index + 1}`}`,
      sku: variant.sku,
      price: variant.price,
      originalPrice: variant.originalPrice,
      images: convertedImages,
      inStock: variant.stock > 0,
      stock: variant.stock || 0,
      colors: variant.colors || ['Disponible'],
      description: variant.description
    })

    // Add images to the main collection (use first variant's images as primary)
    if (index === 0) {
      allImages.push(...convertedImages)
    }
  })

  return {
    ...baseProduct,
    id: baseProduct.id, // Use the first variant's ID as primary
    images: allImages,
    models: models,
    // Enhanced product information
    rating: baseProduct.rating || 4.5,
    reviewCount: baseProduct.reviews || 0,
    sizes: baseProduct.sizes || ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
    features: baseProduct.materials || ['Materiales Premium', 'Diseño Exclusivo', 'Calidad Superior'],
    fullDescription: baseProduct.description || 'Descripción completa del producto.',
    careInstructions: [
      'Limpiar con paño húmedo',
      'No sumergir en agua',
      'Usar protector de materiales',
      'Almacenar en lugar seco'
    ],
    type: baseProduct.type || 'sneaker',
    subType: baseProduct.subType || 'lifestyle',
    gender: baseProduct.gender || 'unisex',
    inStock: variants.some(v => v.stock > 0),
    stockCount: variants.reduce((total, v) => total + (v.stock || 0), 0),
    brand: baseProduct.brand,
    modelFamily: baseProduct.modelFamily,
    modelFamilyDisplay: baseProduct.modelFamilyDisplay
  }
}

// Helper function to load description from .txt file
const loadProductDescription = async (productPath) => {
  try {
    const response = await fetch(`${productPath}/Description.txt`)
    if (response.ok) {
      const text = await response.text()
      return text.trim()
    }
  } catch (error) {
    console.log('No description file found for:', productPath)
  }
  return null
}

// 🔍 UNIVERSAL PRODUCT MEDIA LOADER - Dynamically loads all images and videos from any product directory
const loadProductMediaFromDirectory = async (productPath, sku) => {
  console.log('🔍 LOADING MEDIA FROM DIRECTORY:', productPath)

  // Define known image and video patterns for different products
  const mediaPatterns = {
    'BD7700-222': {
      images: [
        'o_1hfi0lgi514331ru41hu4km31qsp47.webp',
        'o_1hfi0lgi61ad617f41o9k1peh1uq548.webp',
        'o_1hfi0lgi6apo15rbmvq2eco3f49.webp',
        'o_1hfi0lgi71ic1jnt1b09fo61cjn4a.webp',
        'o_1hfi0lgi81mmvp4e1ru65dbqk4c.webp',
        'o_1hfi0lgi8lta26dngkj9ns084b.webp',
        'o_1hfi0lgi91qrd1s7u19bpbfp1lqc4e.webp',
        'o_1hfi0lgi91uti1iq78tphq7a3b4f.webp',
        'o_1hfi0lgi962u1l1nnj11m0r167o4d.webp',
        'o_1hjg0hb8f1car11sv1vm3sj51o1fi.webp',
        'o_1hjg0hb8gg271iu61n8v1rus855j.webp',
        'o_1hjg0hb8gjptplevau157o1jb6k.webp',
        'o_1hjg0hb8h13pr1b171o17lfi1ec4l.webp',
        'o_1hjg0hb8hjcrvjtlji1cgn1qjum.webp',
        'o_1hjg0hb8i12p42vvh0i1n878p3n.webp',
        'o_1hjg0hb8i1ebk154d7bd4m016u3o.webp',
        'o_1hjg0hb8itnt1380trf1p0e5nfp.webp',
        'o_1hjg0hb8jspr9c21dmjm2r1a3vq.webp'
      ],
      videos: [
        'Video-nike-gucci-1.mp4',
        'Video-nike-gucci-2.mp4'
      ]
    },
    'AO4606-001': {
      images: [
        'o_1h8d0aqnd5fh1n84r631ml472128.webp',
        'o_1h8d0aqndkg1el829o1glv1g6v29.webp',
        'o_1h8d0aqne1eseeq71nl85r81ldc2b.webp',
        'o_1h8d0aqne1h2p1qhi1khe1pmjncu2d.webp',
        'o_1h8d0aqneai15r5fn6pb11jdq2c.webp',
        'o_1h8d0aqnep58nlv1lvdldu1a502a.webp',
        'o_1h8d0aqnf893m8u1v5l1r449b2f.webp',
        'o_1h8d0aqnfkgs1dqsjgh4k56tb2g.webp',
        'o_1h8d0aqnftjcop3uvk176s1rb32e.webp',
        'o_1h8d0c8dl14s8bhh181bdutnnn2i.webp',
        'o_1h8d0c8dl2hqunh17to42v128t2h.webp',
        'o_1h8d0c8dm15rd7g41ing18akke42k.webp',
        'o_1h8d0c8dmra31fbf18b917u0uun2l.webp',
        'o_1h8d0c8dms8s12oe156d1jh51gha2j.webp',
        'o_1h8d0c8dn17osp1311661rhfcjo2m.webp',
        'o_1h8d0c8dn1823k3m1l37l1k1pet2p.webp',
        'o_1h8d0c8dn9kk14ac7gr1hhs1hui2o.webp',
        'o_1h8d0c8dnnf07th9l0vqtctu2n.webp',
        'o_1h8d0d27barud2jm1c15m21atn2r.webp',
        'o_1h8d0d27bf6j17s9rnh1n0c1jam2q.webp',
        'o_1h8d0d27c196cuup17n553cv62t.webp',
        'o_1h8d0d27c1qms1nfc1ek08g82p2s.webp',
        'o_1h8d0d27cer813b6189c1b30qad30.webp',
        'o_1h8d0d27cnspvro1hj01ru7l92v.webp',
        'o_1h8d0d27ctkh1qq6h2h11fj1kn92u.webp',
        'o_1h8d0d27d1gl71atvm44gmq1ss31.webp',
        'o_1h8d0d27dpfl1dij1il21m1t14432.webp'
      ],
      videos: [
        'Video-OW-AF1-1.mp4',
        'Video-OW-AF1-2.mp4',
        'Video-OW-AF1-3.mp4'
      ]
    },
    'JGD212-EJD': {
      images: [
        'i1741540275819_3689_0_0.webp',
        'i1741540277076_4173_0_1.webp',
        'i1741540275820_3047_0_2.webp',
        'i1741540277076_1056_0_3.webp',
        'i1741540275819_5584_0_4.webp',
        'i1741540275818_3439_0_5.webp',
        'i1741540275818_4193_0_7.webp',
        'i1741540277077_1281_0_8.webp'
      ],
      videos: []
    }
  }

  // Get media for this specific SKU
  const productMedia = mediaPatterns[sku]

  if (productMedia) {
    const images = productMedia.images.map(filename => `${productPath}/${filename}`)
    const videos = productMedia.videos.map(filename => `${productPath}/${filename}`)

    console.log(`🖼️ LOADED ${images.length} IMAGES FOR ${sku}`)
    console.log(`🎬 LOADED ${videos.length} VIDEOS FOR ${sku}`)

    return { images, videos }
  }

  // Fallback: return empty arrays if no specific media found
  console.log(`⚠️ NO SPECIFIC MEDIA PATTERNS FOUND FOR ${sku}, USING FALLBACK`)
  return {
    images: [`${productPath}/placeholder.webp`],
    videos: []
  }
}
