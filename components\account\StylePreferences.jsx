'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'

const availableBrands = [
  { id: 'nike', name: '<PERSON>', icon: '✓' },
  { id: 'adidas', name: 'Adidas', icon: '⚡' },
  { id: 'gucci', name: '<PERSON><PERSON>', icon: '👑' },
  { id: 'dior', name: '<PERSON><PERSON>', icon: '💎' },
  { id: 'balenciaga', name: '<PERSON><PERSON><PERSON><PERSON>', icon: '🔥' },
  { id: 'off-white', name: 'Off-White', icon: '⚪' },
  { id: 'jordan', name: '<PERSON>', icon: '🏀' },
  { id: 'yeezy', name: 'Yeezy', icon: '🌊' },
  { id: 'louis-vuitton', name: '<PERSON>', icon: '👜' },
  { id: 'chanel', name: '<PERSON><PERSON>', icon: '💫' },
  { id: 'prada', name: '<PERSON><PERSON>', icon: '🖤' },
  { id: 'valentino', name: '<PERSON><PERSON>', icon: '❤️' }
]

const styleCategories = [
  { id: 'sneakers', name: 'Sneakers', icon: '👟' },
  { id: 'luxury', name: 'Luxury', icon: '✨' },
  { id: 'casual', name: 'Casual', icon: '👕' },
  { id: 'formal', name: 'Formal', icon: '👔' },
  { id: 'athletic', name: 'Athletic', icon: '🏃' },
  { id: 'streetwear', name: 'Streetwear', icon: '🛹' },
  { id: 'minimalist', name: 'Minimalist', icon: '⚪' },
  { id: 'bold', name: 'Bold', icon: '🔥' }
]

const colorPreferences = [
  { id: 'black', name: 'Negro', color: '#000000' },
  { id: 'white', name: 'Blanco', color: '#FFFFFF' },
  { id: 'gray', name: 'Gris', color: '#808080' },
  { id: 'brown', name: 'Café', color: '#8B4513' },
  { id: 'red', name: 'Rojo', color: '#FF0000' },
  { id: 'blue', name: 'Azul', color: '#0000FF' },
  { id: 'green', name: 'Verde', color: '#008000' },
  { id: 'yellow', name: 'Amarillo', color: '#FFFF00' },
  { id: 'pink', name: 'Rosa', color: '#FFC0CB' },
  { id: 'purple', name: 'Morado', color: '#800080' }
]

const shoeSizes = [
  { brand: 'nike', sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'] },
  { brand: 'adidas', sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'] },
  { brand: 'gucci', sizes: ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45'] },
  { brand: 'dior', sizes: ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45'] },
  { brand: 'balenciaga', sizes: ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45'] }
]

export default function StylePreferences() {
  const {
    stylePreferences,
    updateStylePreferences,
    addFavoriteBrand,
    removeFavoriteBrand,
    setSizeForBrand,
    getSizeForBrand
  } = useUserPreferences()

  const [activeTab, setActiveTab] = useState('brands')

  const handleBrandToggle = (brandId) => {
    if (stylePreferences.favoritebrands.includes(brandId)) {
      removeFavoriteBrand(brandId)
    } else {
      addFavoriteBrand(brandId)
    }
  }

  const handleStyleToggle = (styleId) => {
    const currentStyles = stylePreferences.preferredStyles || []
    const newStyles = currentStyles.includes(styleId)
      ? currentStyles.filter(s => s !== styleId)
      : [...currentStyles, styleId]
    
    updateStylePreferences({ preferredStyles: newStyles })
  }

  const handleColorToggle = (colorId) => {
    const currentColors = stylePreferences.preferredColors || []
    const newColors = currentColors.includes(colorId)
      ? currentColors.filter(c => c !== colorId)
      : [...currentColors, colorId]
    
    updateStylePreferences({ preferredColors: newColors })
  }

  const handlePriceRangeChange = (field, value) => {
    updateStylePreferences({
      priceRange: {
        ...stylePreferences.priceRange,
        [field]: parseInt(value)
      }
    })
  }

  const tabs = [
    { id: 'brands', name: 'Marcas Favoritas', icon: '🏷️' },
    { id: 'styles', name: 'Estilos', icon: '👕' },
    { id: 'colors', name: 'Colores', icon: '🎨' },
    { id: 'sizes', name: 'Tallas', icon: '📏' },
    { id: 'budget', name: 'Presupuesto', icon: '💰' }
  ]

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
          Preferencias de Estilo
        </h2>
        <p className="text-warm-camel mt-1">
          Personaliza tu experiencia para recibir recomendaciones perfectas
        </p>
      </div>

      {/* Tabs */}
      <div className="flex flex-wrap gap-2">
        {tabs.map((tab) => (
          <AnimatedButton
            key={tab.id}
            variant={activeTab === tab.id ? 'primary' : 'ghost'}
            size="sm"
            onClick={() => setActiveTab(tab.id)}
            icon={<span>{tab.icon}</span>}
          >
            {tab.name}
          </AnimatedButton>
        ))}
      </div>

      {/* Tab Content */}
      <Card variant="glass">
        <CardContent className="p-6">
          
          {/* Favorite Brands */}
          {activeTab === 'brands' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                Marcas Favoritas
              </h3>
              <p className="text-warm-camel">
                Selecciona las marcas que más te gustan para recibir notificaciones de nuevos lanzamientos
              </p>
              
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3">
                {availableBrands.map((brand) => (
                  <motion.button
                    key={brand.id}
                    onClick={() => handleBrandToggle(brand.id)}
                    className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                      stylePreferences.favoritebrands.includes(brand.id)
                        ? 'border-rich-gold bg-rich-gold/10 text-forest-emerald'
                        : 'border-warm-camel/30 hover:border-warm-camel text-warm-camel'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="text-2xl mb-2">{brand.icon}</div>
                    <div className="font-medium">{brand.name}</div>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}

          {/* Style Categories */}
          {activeTab === 'styles' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                Estilos Preferidos
              </h3>
              <p className="text-warm-camel">
                Elige los estilos que mejor describen tu personalidad
              </p>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
                {styleCategories.map((style) => (
                  <motion.button
                    key={style.id}
                    onClick={() => handleStyleToggle(style.id)}
                    className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                      stylePreferences.preferredStyles?.includes(style.id)
                        ? 'border-rich-gold bg-rich-gold/10 text-forest-emerald'
                        : 'border-warm-camel/30 hover:border-warm-camel text-warm-camel'
                    }`}
                    whileHover={{ scale: 1.02 }}
                    whileTap={{ scale: 0.98 }}
                  >
                    <div className="text-2xl mb-2">{style.icon}</div>
                    <div className="font-medium">{style.name}</div>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}

          {/* Color Preferences */}
          {activeTab === 'colors' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                Colores Favoritos
              </h3>
              <p className="text-warm-camel">
                Selecciona los colores que más te gustan
              </p>
              
              <div className="grid grid-cols-5 md:grid-cols-10 gap-3">
                {colorPreferences.map((color) => (
                  <motion.button
                    key={color.id}
                    onClick={() => handleColorToggle(color.id)}
                    className={`relative p-3 rounded-lg border-2 transition-all duration-200 ${
                      stylePreferences.preferredColors?.includes(color.id)
                        ? 'border-rich-gold'
                        : 'border-warm-camel/30 hover:border-warm-camel'
                    }`}
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                  >
                    <div
                      className="w-8 h-8 rounded-full mx-auto"
                      style={{ backgroundColor: color.color }}
                    />
                    {stylePreferences.preferredColors?.includes(color.id) && (
                      <div className="absolute inset-0 flex items-center justify-center">
                        <svg className="w-4 h-4 text-rich-gold" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      </div>
                    )}
                    <div className="text-xs mt-1 text-warm-camel">{color.name}</div>
                  </motion.button>
                ))}
              </div>
            </motion.div>
          )}

          {/* Size Preferences */}
          {activeTab === 'sizes' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-6"
            >
              <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                Tallas por Marca
              </h3>
              <p className="text-warm-camel">
                Guarda tu talla para cada marca para acelerar el proceso de compra
              </p>
              
              {shoeSizes.map((brandSizes) => (
                <div key={brandSizes.brand} className="space-y-3">
                  <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray capitalize">
                    {brandSizes.brand}
                  </h4>
                  
                  <div className="flex flex-wrap gap-2">
                    {brandSizes.sizes.map((size) => (
                      <motion.button
                        key={size}
                        onClick={() => setSizeForBrand(brandSizes.brand, size)}
                        className={`px-3 py-2 rounded-lg border transition-all duration-200 ${
                          getSizeForBrand(brandSizes.brand) === size
                            ? 'border-rich-gold bg-rich-gold text-forest-emerald'
                            : 'border-warm-camel/30 hover:border-warm-camel text-warm-camel'
                        }`}
                        whileHover={{ scale: 1.05 }}
                        whileTap={{ scale: 0.95 }}
                      >
                        {size}
                      </motion.button>
                    ))}
                  </div>
                </div>
              ))}
            </motion.div>
          )}

          {/* Budget Range */}
          {activeTab === 'budget' && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-4"
            >
              <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                Rango de Presupuesto
              </h3>
              <p className="text-warm-camel">
                Define tu rango de precios preferido para recibir recomendaciones relevantes
              </p>
              
              <div className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-forest-emerald dark:text-light-cloud-gray mb-2">
                      Precio Mínimo
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="500"
                      value={stylePreferences.priceRange.min}
                      onChange={(e) => handlePriceRangeChange('min', e.target.value)}
                      className="w-full px-4 py-2 rounded-lg border border-warm-camel/30 bg-transparent text-forest-emerald dark:text-light-cloud-gray focus:border-rich-gold focus:outline-none"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-forest-emerald dark:text-light-cloud-gray mb-2">
                      Precio Máximo
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="500"
                      value={stylePreferences.priceRange.max}
                      onChange={(e) => handlePriceRangeChange('max', e.target.value)}
                      className="w-full px-4 py-2 rounded-lg border border-warm-camel/30 bg-transparent text-forest-emerald dark:text-light-cloud-gray focus:border-rich-gold focus:outline-none"
                    />
                  </div>
                </div>
                
                <div className="text-center p-4 bg-warm-camel/10 rounded-lg">
                  <p className="text-forest-emerald dark:text-light-cloud-gray">
                    Rango seleccionado: <span className="font-bold">
                      ${stylePreferences.priceRange.min.toLocaleString()} - ${stylePreferences.priceRange.max.toLocaleString()} MXN
                    </span>
                  </p>
                </div>
                
                {/* Quick Budget Presets */}
                <div className="space-y-2">
                  <p className="text-sm font-medium text-forest-emerald dark:text-light-cloud-gray">
                    Presets rápidos:
                  </p>
                  <div className="flex flex-wrap gap-2">
                    {[
                      { label: 'Económico', min: 0, max: 5000 },
                      { label: 'Medio', min: 5000, max: 15000 },
                      { label: 'Premium', min: 15000, max: 30000 },
                      { label: 'Luxury', min: 30000, max: 100000 }
                    ].map((preset) => (
                      <AnimatedButton
                        key={preset.label}
                        variant="ghost"
                        size="sm"
                        onClick={() => updateStylePreferences({
                          priceRange: { min: preset.min, max: preset.max }
                        })}
                      >
                        {preset.label}
                      </AnimatedButton>
                    ))}
                  </div>
                </div>
              </div>
            </motion.div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
