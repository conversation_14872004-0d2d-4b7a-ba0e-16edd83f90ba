#!/usr/bin/env node

/**
 * TWL Category Filtering Test Script
 * Tests the category filtering functionality
 */

// Import the products data
const { mockProducts } = require('../lib/products-data.js');

console.log('🧪 Testing Category Filtering\n');

// Category mapping (same as in FeaturedDrops component)
const categoryMapping = {
  'tennis': 'sneakers',
  'sandalias': 'sandals',
  'botas': 'boots',
  'tacones': 'heels',
  'casual': 'casual',
  'formal': 'formal'
};

// Get all available categories
const availableCategories = [...new Set(mockProducts.map(p => p.category))];
console.log('📋 Available product categories:', availableCategories);
console.log(`📊 Total products: ${mockProducts.length}\n`);

// Test each category filter
Object.entries(categoryMapping).forEach(([navCategory, productCategory]) => {
  const filtered = mockProducts.filter(product => product.category === productCategory);
  
  console.log(`🔍 Category: "${navCategory}" -> "${productCategory}"`);
  console.log(`   Found ${filtered.length} products:`);
  
  if (filtered.length > 0) {
    filtered.forEach(product => {
      console.log(`   ✅ ${product.name} (${product.brand})`);
    });
  } else {
    console.log(`   ❌ No products found for category "${productCategory}"`);
  }
  console.log('');
});

// Test products by category breakdown
console.log('📊 Products by Category:');
availableCategories.forEach(category => {
  const count = mockProducts.filter(p => p.category === category).length;
  console.log(`   ${category}: ${count} products`);
});

console.log('\n✅ Category filtering test completed!');
console.log('\n💡 To test in browser:');
console.log('   1. Run: npm run dev');
console.log('   2. Open: http://localhost:3002');
console.log('   3. Go to "Nuestra Colección" section');
console.log('   4. Click different category tabs');
console.log('   5. Check browser console for filtering logs');
