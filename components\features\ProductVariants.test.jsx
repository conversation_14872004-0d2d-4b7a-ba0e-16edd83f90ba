import { render, screen } from '@testing-library/react'
import ProductVariants from './ProductVariants'

describe('ProductVariants', () => {
  it('renders without crashing', () => {
    render(<ProductVariants />)
    expect(screen.getByText('ProductVariants')).toBeInTheDocument()
  })

  it('displays the component title', () => {
    render(<ProductVariants />)
    expect(screen.getByRole('heading', { level: 2 })).toHaveTextContent('ProductVariants')
  })
})
