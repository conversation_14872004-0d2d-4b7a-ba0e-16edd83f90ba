'use client'

import { useState, useEffect, useRef } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'

// Mock voice search results
const mockVoiceResults = {
  'jordan 1 chicago': [
    { id: 4, name: 'Jordan 1 Retro High Chicago', brand: 'Jordan', price: 4200, confidence: 0.95 },
    { id: 5, name: 'Jordan 1 Low Chicago', brand: 'Jordan', price: 3200, confidence: 0.87 }
  ],
  'yeezy boost': [
    { id: 2, name: 'Yeezy Boost 350 V2 Zebra', brand: 'Adidas', price: 8500, confidence: 0.92 },
    { id: 6, name: 'Yeezy Boost 700 Wave Runner', brand: 'Adidas', price: 7800, confidence: 0.89 }
  ],
  'air force white': [
    { id: 1, name: 'Nike Air Force 1 Low White', brand: 'Nike', price: 2500, confidence: 0.98 }
  ],
  'sneakers rojos': [
    { id: 4, name: 'Jordan 1 Retro High Chicago', brand: 'Jordan', price: 4200, confidence: 0.85 },
    { id: 7, name: 'Nike Dunk Low University Red', brand: 'Nike', price: 3800, confidence: 0.82 }
  ]
}

export default function EnhancedVoiceSearch({ 
  onResults, 
  onClose, 
  isOpen = false,
  className = '' 
}) {
  const [isListening, setIsListening] = useState(false)
  const [transcript, setTranscript] = useState('')
  const [isProcessing, setIsProcessing] = useState(false)
  const [results, setResults] = useState([])
  const [error, setError] = useState(null)
  const [confidence, setConfidence] = useState(0)
  const [language, setLanguage] = useState('es-MX')
  const recognitionRef = useRef(null)
  const timeoutRef = useRef(null)

  useEffect(() => {
    if (typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)) {
      const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
      recognitionRef.current = new SpeechRecognition()
      
      recognitionRef.current.continuous = false
      recognitionRef.current.interimResults = true
      recognitionRef.current.lang = language
      recognitionRef.current.maxAlternatives = 3

      recognitionRef.current.onstart = () => {
        setIsListening(true)
        setError(null)
        setTranscript('')
      }

      recognitionRef.current.onresult = (event) => {
        let finalTranscript = ''
        let interimTranscript = ''

        for (let i = event.resultIndex; i < event.results.length; i++) {
          const transcript = event.results[i][0].transcript
          const confidence = event.results[i][0].confidence

          if (event.results[i].isFinal) {
            finalTranscript += transcript
            setConfidence(confidence)
          } else {
            interimTranscript += transcript
          }
        }

        setTranscript(finalTranscript || interimTranscript)

        if (finalTranscript) {
          processVoiceCommand(finalTranscript, confidence)
        }
      }

      recognitionRef.current.onerror = (event) => {
        setError(getErrorMessage(event.error))
        setIsListening(false)
      }

      recognitionRef.current.onend = () => {
        setIsListening(false)
      }
    }

    return () => {
      if (recognitionRef.current) {
        recognitionRef.current.stop()
      }
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [language])

  const getErrorMessage = (error) => {
    switch (error) {
      case 'no-speech':
        return 'No se detectó voz. Intenta hablar más claro.'
      case 'audio-capture':
        return 'No se pudo acceder al micrófono.'
      case 'not-allowed':
        return 'Permiso de micrófono denegado.'
      case 'network':
        return 'Error de conexión. Verifica tu internet.'
      default:
        return 'Error de reconocimiento de voz.'
    }
  }

  const processVoiceCommand = async (text, confidence) => {
    setIsProcessing(true)
    
    try {
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      const cleanText = text.toLowerCase().trim()
      const searchResults = findProducts(cleanText)
      
      setResults(searchResults)
      
      if (onResults) {
        onResults(searchResults, cleanText)
      }
      
    } catch (error) {
      setError('Error procesando la búsqueda por voz')
    } finally {
      setIsProcessing(false)
    }
  }

  const findProducts = (searchTerms) => {
    const results = []
    
    Object.entries(mockVoiceResults).forEach(([key, products]) => {
      if (searchTerms.includes(key) || key.includes(searchTerms)) {
        results.push(...products)
      }
    })
    
    if (results.length === 0) {
      Object.entries(mockVoiceResults).forEach(([key, products]) => {
        const keywords = searchTerms.split(' ')
        const keyWords = key.split(' ')
        
        const hasMatch = keywords.some(keyword => 
          keyWords.some(keyWord => 
            keyWord.includes(keyword) || keyword.includes(keyWord)
          )
        )
        
        if (hasMatch) {
          results.push(...products.map(p => ({ ...p, confidence: p.confidence * 0.8 })))
        }
      })
    }
    
    const uniqueResults = results.filter((product, index, self) => 
      index === self.findIndex(p => p.id === product.id)
    )
    
    return uniqueResults.sort((a, b) => b.confidence - a.confidence).slice(0, 5)
  }

  const startListening = () => {
    if (recognitionRef.current && !isListening) {
      setResults([])
      setError(null)
      recognitionRef.current.start()
      
      timeoutRef.current = setTimeout(() => {
        if (recognitionRef.current && isListening) {
          recognitionRef.current.stop()
        }
      }, 10000)
    }
  }

  const stopListening = () => {
    if (recognitionRef.current && isListening) {
      recognitionRef.current.stop()
    }
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }

  const toggleLanguage = () => {
    const newLang = language === 'es-MX' ? 'en-US' : 'es-MX'
    setLanguage(newLang)
    if (recognitionRef.current) {
      recognitionRef.current.lang = newLang
    }
  }

  if (!isOpen) return null

  const isSupported = typeof window !== 'undefined' && ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window)

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className={`max-w-2xl w-full max-h-[90vh] overflow-y-auto ${className}`}
          onClick={(e) => e.stopPropagation()}
        >
          <Card variant="glass">
            <CardContent className="p-8">
              
              {/* Header */}
              <div className="flex items-center justify-between mb-6">
                <div>
                  <h2 className="text-2xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    🎤 Búsqueda por Voz AI
                  </h2>
                  <p className="text-warm-camel text-sm">
                    Di el nombre del producto que buscas
                  </p>
                </div>
                
                <div className="flex items-center gap-2">
                  <button
                    onClick={toggleLanguage}
                    className="px-3 py-1 rounded-lg bg-warm-camel/10 text-warm-camel hover:bg-warm-camel/20 transition-colors text-sm"
                  >
                    {language === 'es-MX' ? '🇲🇽 ES' : '🇺🇸 EN'}
                  </button>
                  
                  <button
                    onClick={onClose}
                    className="text-warm-camel hover:text-rich-gold transition-colors"
                  >
                    <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </div>
              </div>

              {!isSupported ? (
                <div className="text-center py-8">
                  <div className="text-4xl mb-4">🚫</div>
                  <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    Navegador no compatible
                  </h3>
                  <p className="text-warm-camel">
                    Tu navegador no soporta reconocimiento de voz. 
                    Prueba con Chrome, Safari o Edge.
                  </p>
                </div>
              ) : (
                <>
                  {/* Voice Interface */}
                  <div className="text-center mb-8">
                    <motion.button
                      onClick={isListening ? stopListening : startListening}
                      disabled={isProcessing}
                      className={`w-24 h-24 rounded-full flex items-center justify-center text-3xl transition-all duration-300 ${
                        isListening 
                          ? 'bg-red-500 text-white shadow-lg shadow-red-500/30' 
                          : 'bg-rich-gold text-forest-emerald hover:shadow-lg hover:shadow-rich-gold/30'
                      } ${isProcessing ? 'opacity-50 cursor-not-allowed' : ''}`}
                      whileHover={{ scale: isProcessing ? 1 : 1.05 }}
                      whileTap={{ scale: isProcessing ? 1 : 0.95 }}
                      animate={isListening ? {
                        scale: [1, 1.1, 1],
                        boxShadow: [
                          '0 0 0 0 rgba(239, 68, 68, 0.4)',
                          '0 0 0 20px rgba(239, 68, 68, 0)',
                          '0 0 0 0 rgba(239, 68, 68, 0)'
                        ]
                      } : {}}
                      transition={{ duration: 1.5, repeat: isListening ? Infinity : 0 }}
                    >
                      {isProcessing ? '⏳' : isListening ? '🎤' : '🎙️'}
                    </motion.button>
                    
                    <div className="mt-4">
                      <p className="text-forest-emerald dark:text-light-cloud-gray font-medium">
                        {isProcessing ? 'Procesando con IA...' : 
                         isListening ? 'Escuchando...' : 
                         'Toca para hablar'}
                      </p>
                      
                      {confidence > 0 && (
                        <div className="mt-2">
                          <Badge variant="success" size="sm">
                            Confianza: {Math.round(confidence * 100)}%
                          </Badge>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Transcript */}
                  {transcript && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-6"
                    >
                      <Card variant="default">
                        <CardContent className="p-4">
                          <h4 className="text-sm font-medium text-warm-camel mb-2">
                            🧠 Texto reconocido por IA:
                          </h4>
                          <p className="text-forest-emerald dark:text-light-cloud-gray">
                            "{transcript}"
                          </p>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )}

                  {/* Error */}
                  {error && (
                    <motion.div
                      initial={{ opacity: 0, y: 10 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="mb-6"
                    >
                      <Card variant="default" className="border-red-500/30">
                        <CardContent className="p-4">
                          <div className="flex items-center gap-2 text-red-500">
                            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            <span className="text-sm">{error}</span>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  )}

                  {/* Results */}
                  {results.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      className="space-y-4"
                    >
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray">
                        🎯 Resultados encontrados ({results.length})
                      </h3>
                      
                      <div className="space-y-3">
                        {results.map((product, index) => (
                          <motion.div
                            key={product.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <Card variant="default" className="hover:shadow-lg transition-all duration-300 cursor-pointer">
                              <CardContent className="p-4">
                                <div className="flex items-center gap-4">
                                  <div className="w-16 h-16 bg-warm-camel/10 rounded-lg flex items-center justify-center">
                                    <span className="text-2xl">👟</span>
                                  </div>
                                  
                                  <div className="flex-1">
                                    <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray line-clamp-1">
                                      {product.name}
                                    </h4>
                                    <p className="text-warm-camel text-sm">{product.brand}</p>
                                    <div className="flex items-center gap-2 mt-1">
                                      <span className="font-bold text-forest-emerald dark:text-light-cloud-gray">
                                        ${product.price.toLocaleString()} MXN
                                      </span>
                                      <Badge 
                                        variant={product.confidence > 0.9 ? "success" : product.confidence > 0.8 ? "warning" : "default"} 
                                        size="sm"
                                      >
                                        {Math.round(product.confidence * 100)}% match
                                      </Badge>
                                    </div>
                                  </div>
                                  
                                  <AnimatedButton
                                    variant="primary"
                                    size="sm"
                                    icon={
                                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                      </svg>
                                    }
                                  >
                                    Ver
                                  </AnimatedButton>
                                </div>
                              </CardContent>
                            </Card>
                          </motion.div>
                        ))}
                      </div>
                    </motion.div>
                  )}

                  {/* Voice Commands Help */}
                  <div className="mt-8 pt-6 border-t border-warm-camel/20">
                    <h4 className="text-sm font-medium text-warm-camel mb-3">
                      💡 Ejemplos de comandos de voz:
                    </h4>
                    <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 text-sm text-warm-camel">
                      <div>"Jordan 1 Chicago"</div>
                      <div>"Yeezy Boost 350"</div>
                      <div>"Air Force blancos"</div>
                      <div>"Sneakers rojos"</div>
                      <div>"Buscar Dunk Low"</div>
                      <div>"Mostrar Gucci"</div>
                    </div>
                  </div>
                </>
              )}
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
