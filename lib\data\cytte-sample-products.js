/**
 * CYTTE SUPPLIER SAMPLE PRODUCTS
 * Following exact 6-level hierarchy structure
 */

import { generateProductPath } from './cytte-6-level-schema.js'

export const cytteProducts = [
  // EXAMPLE 1: Nike x Off-White Jordan 1 High
  {
    id: 'nike-off-white-jordan-1-high-air-dior',
    sku: 'JKD474-EJR',
    internalReference: 'Air Dior',
    name: 'Air Jordan 1 High x Off-White "Air Dior"',
    description: 'Colaboración exclusiva entre Jordan, Off-White y Dior. Edición ultra limitada.',
    
    // LEVEL 1: STYLE
    style: 'sneakers',
    styleDisplay: 'Sneakers',
    styleCytteId: '1. SNEAKERS',
    
    // LEVEL 2: BRAND
    brand: 'Nike',
    brandId: 'nike',
    brandType: 'streetwear',
    brandCytteId: '1. NIKE Limited Edition',
    
    // LEVEL 3: GENDER
    gender: 'MIXTE',
    genderDisplay: 'Unisex',
    genderPath: 'mixte',
    
    // LEVEL 4: SHOE MODEL TYPE FAMILY
    modelFamily: 'jordan',
    modelFamilyDisplay: 'Jordan',
    modelVariant: 'jordan-1-high',
    modelCytteId: '2. JORDAN 1 HIGH',
    
    // LEVEL 5: COLLAB
    isCollaboration: true,
    collaborationType: 'brand-x-brand',
    collaborator: 'Off-White',
    collaboratorDisplay: 'Off-White x Nike x Dior',
    collabCytteId: '8. OFF WHITE',
    
    // LEVEL 6: PRODUCT FOLDER
    productFolder: 'JKD474-EJR -- Air Dior',
    skuCode: 'JKD474-EJR',
    brandReference: 'Air Dior',
    
    // Generated path: /products/sneakers/nike/mixte/jordan/off-white/jkd474-ejr-air-dior/
    imagePath: '/products/sneakers/nike/mixte/jordan/off-white/jkd474-ejr-air-dior/',
    
    type: 'sneaker',
    subType: 'high-top',
    price: 45000,
    originalPrice: 50000,
    currency: 'MXN',
    colors: ['Gris/Azul', 'Blanco/Gris'],
    sizes: ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
    materials: ['Cuero Premium', 'Gamuza', 'Detalles Dior'],
    
    isLimited: true,
    isExclusive: true,
    isVip: true,
    isNew: false,
    
    images: [
      '/products/sneakers/nike/mixte/jordan/off-white/jkd474-ejr-air-dior/image-1.webp',
      '/products/sneakers/nike/mixte/jordan/off-white/jkd474-ejr-air-dior/image-2.webp',
      '/products/sneakers/nike/mixte/jordan/off-white/jkd474-ejr-air-dior/image-3.webp',
      '/products/sneakers/nike/mixte/jordan/off-white/jkd474-ejr-air-dior/image-4.webp'
    ],
    
    stock: 2,
    availability: 'low-stock',
    releaseDate: '2023-12-15',
    rating: 5.0,
    reviews: 23,
    
    tags: ['jordan', 'off-white', 'dior', 'collaboration', 'limited', 'high-top', 'luxury'],
    keywords: ['jordan 1', 'off white', 'dior', 'virgil abloh', 'collaboration', 'sneakers'],
    searchTerms: ['jordan 1 high', 'off-white dior', 'luxury sneakers', 'limited edition']
  },

  // EXAMPLE 2: Gucci Ace Sneaker (No Collaboration)
  {
    id: 'gucci-ace-classic-white-mixte',
    sku: 'JFD538-JJJ',
    internalReference: 'ACE',
    name: 'Gucci Ace Sneaker Classic White',
    description: 'Sneaker icónico de Gucci con rayas Web verdes y rojas.',
    
    // LEVEL 1: STYLE
    style: 'sneakers',
    styleDisplay: 'Sneakers',
    styleCytteId: '1. SNEAKERS',
    
    // LEVEL 2: BRAND
    brand: 'Gucci',
    brandId: 'gucci',
    brandType: 'luxury',
    brandCytteId: '4. GUCCI',
    
    // LEVEL 3: GENDER
    gender: 'MIXTE',
    genderDisplay: 'Unisex',
    genderPath: 'mixte',
    
    // LEVEL 4: SHOE MODEL TYPE FAMILY
    modelFamily: 'ace',
    modelFamilyDisplay: 'Ace',
    modelVariant: 'ace-classic',
    modelCytteId: 'ACE',
    
    // LEVEL 5: COLLAB (No collaboration)
    isCollaboration: false,
    collaborationType: null,
    collaborator: null,
    collaboratorDisplay: null,
    collabCytteId: null,
    
    // LEVEL 6: PRODUCT FOLDER
    productFolder: 'JFD538-JJJ -- ACE',
    skuCode: 'JFD538-JJJ',
    brandReference: 'ACE',
    
    // Generated path: /products/sneakers/gucci/mixte/ace/standard/jfd538-jjj-ace/
    imagePath: '/products/sneakers/gucci/mixte/ace/standard/jfd538-jjj-ace/',
    
    type: 'sneaker',
    subType: 'low-top',
    price: 18500,
    originalPrice: null,
    currency: 'MXN',
    colors: ['Blanco/Verde/Rojo', 'Negro/Verde/Rojo'],
    sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
    materials: ['Cuero Italiano', 'Suela de Goma'],
    
    isLimited: false,
    isExclusive: true,
    isVip: true,
    isNew: true,
    
    images: [
      '/products/sneakers/gucci/mixte/ace/standard/jfd538-jjj-ace/image-1.webp',
      '/products/sneakers/gucci/mixte/ace/standard/jfd538-jjj-ace/image-2.webp',
      '/products/sneakers/gucci/mixte/ace/standard/jfd538-jjj-ace/image-3.webp',
      '/products/sneakers/gucci/mixte/ace/standard/jfd538-jjj-ace/image-4.webp'
    ],
    
    stock: 12,
    availability: 'in-stock',
    releaseDate: '2024-01-20',
    rating: 4.7,
    reviews: 89,
    
    tags: ['gucci', 'ace', 'luxury', 'italian', 'web-stripes', 'classic'],
    keywords: ['gucci ace', 'luxury sneakers', 'italian shoes', 'web stripes'],
    searchTerms: ['ace sneaker', 'gucci white', 'luxury footwear', 'designer sneakers']
  },

  // EXAMPLE 3: LV x Nike Collaboration
  {
    id: 'lv-nike-air-force-1-collab',
    sku: 'FC1688-133',
    internalReference: 'LV',
    name: 'Louis Vuitton x Nike Air Force 1',
    description: 'Colaboración histórica entre Louis Vuitton y Nike.',
    
    // LEVEL 1: STYLE
    style: 'sneakers',
    styleDisplay: 'Sneakers',
    styleCytteId: '1. SNEAKERS',
    
    // LEVEL 2: BRAND
    brand: 'Nike',
    brandId: 'nike',
    brandType: 'streetwear',
    brandCytteId: '1. NIKE Limited Edition',
    
    // LEVEL 3: GENDER
    gender: 'MIXTE',
    genderDisplay: 'Unisex',
    genderPath: 'mixte',
    
    // LEVEL 4: SHOE MODEL TYPE FAMILY
    modelFamily: 'air-force',
    modelFamilyDisplay: 'Air Force',
    modelVariant: 'air-force-1',
    modelCytteId: '1. AIR FORCE',
    
    // LEVEL 5: COLLAB
    isCollaboration: true,
    collaborationType: 'brand-x-brand',
    collaborator: 'Louis Vuitton',
    collaboratorDisplay: 'Louis Vuitton x Nike',
    collabCytteId: 'LV collab',
    
    // LEVEL 6: PRODUCT FOLDER
    productFolder: 'FC1688-133 -- LV',
    skuCode: 'FC1688-133',
    brandReference: 'LV',
    
    // Generated path: /products/sneakers/nike/mixte/air-force/louis-vuitton/fc1688-133-lv/
    imagePath: '/products/sneakers/nike/mixte/air-force/louis-vuitton/fc1688-133-lv/',
    
    type: 'sneaker',
    subType: 'low-top',
    price: 35000,
    originalPrice: 40000,
    currency: 'MXN',
    colors: ['Marrón LV', 'Negro LV', 'Blanco LV'],
    sizes: ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
    materials: ['Cuero LV', 'Monogram Canvas', 'Suela Nike'],
    
    isLimited: true,
    isExclusive: true,
    isVip: true,
    isNew: true,
    
    images: [
      '/products/sneakers/nike/mixte/air-force/louis-vuitton/fc1688-133-lv/image-1.webp',
      '/products/sneakers/nike/mixte/air-force/louis-vuitton/fc1688-133-lv/image-2.webp',
      '/products/sneakers/nike/mixte/air-force/louis-vuitton/fc1688-133-lv/image-3.webp',
      '/products/sneakers/nike/mixte/air-force/louis-vuitton/fc1688-133-lv/image-4.webp'
    ],
    
    stock: 5,
    availability: 'low-stock',
    releaseDate: '2024-02-01',
    rating: 4.9,
    reviews: 156,
    
    tags: ['louis-vuitton', 'nike', 'air-force-1', 'collaboration', 'luxury', 'limited'],
    keywords: ['lv nike', 'louis vuitton air force', 'luxury collaboration', 'monogram'],
    searchTerms: ['lv x nike', 'louis vuitton sneakers', 'luxury air force', 'designer collaboration']
  }
]

// Helper function to generate all product paths
export const generateAllProductPaths = () => {
  return cytteProducts.map(product => ({
    ...product,
    imagePath: generateProductPath(product)
  }))
}

// Search functions for 6-level structure
export const searchByLevel = (level, value) => {
  switch (level) {
    case 1: // Style
      return cytteProducts.filter(p => p.style === value)
    case 2: // Brand
      return cytteProducts.filter(p => p.brandId === value)
    case 3: // Gender
      return cytteProducts.filter(p => p.gender === value)
    case 4: // Model Family
      return cytteProducts.filter(p => p.modelFamily === value)
    case 5: // Collaboration
      return cytteProducts.filter(p => p.collaborator === value)
    case 6: // SKU
      return cytteProducts.filter(p => p.skuCode === value)
    default:
      return cytteProducts
  }
}

export const searchMultiLevel = (filters) => {
  let results = [...cytteProducts]
  
  if (filters.style) results = results.filter(p => p.style === filters.style)
  if (filters.brand) results = results.filter(p => p.brandId === filters.brand)
  if (filters.gender) results = results.filter(p => p.gender === filters.gender)
  if (filters.modelFamily) results = results.filter(p => p.modelFamily === filters.modelFamily)
  if (filters.collaborator) results = results.filter(p => p.collaborator === filters.collaborator)
  if (filters.skuCode) results = results.filter(p => p.skuCode === filters.skuCode)
  
  return results
}

export default cytteProducts
