// Mock products data for The White Laces
export const products = [
  {
    id: 1,
    name: 'Nike Air Force 1 Low White',
    brand: 'Nike',
    price: 2500,
    originalPrice: 2800,
    category: 'sneakers',
    subcategory: 'lifestyle',
    gender: 'unisex',
    description: 'El icónico Air Force 1 en su versión más clásica. Un diseño atemporal que nunca pasa de moda.',
    features: ['Cuero premium', 'Suela de goma', 'Amortiguación Air', 'Diseño clásico'],
    images: ['/placeholder-shoe-1.jpg', '/placeholder-shoe-2.jpg'],
    rating: 4.8,
    reviews: 1250,
    tags: ['classic', 'lifestyle', 'basketball', 'street'],
    releaseDate: '2023-01-15',
    isLimited: false,
    isFeatured: true,
    isNew: false,
    variants: [
      {
        id: 'af1-white-1',
        color: { id: 'white', name: '<PERSON>', hex: '#FFFFFF' },
        sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
        price: 2500,
        originalPrice: 2800,
        stock: 45,
        sku: 'AF1-LOW-WHT-001',
        images: ['/placeholder-shoe-1.jpg', '/placeholder-shoe-2.jpg']
      },
      {
        id: 'af1-black-1',
        color: { id: 'black', name: 'Negro', hex: '#000000' },
        sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
        price: 2500,
        originalPrice: 2800,
        stock: 32,
        sku: 'AF1-LOW-BLK-001',
        images: ['/placeholder-shoe-black-1.jpg', '/placeholder-shoe-black-2.jpg']
      }
    ]
  },
  {
    id: 2,
    name: 'Adidas Yeezy Boost 350 V2 Zebra',
    brand: 'Adidas',
    price: 8500,
    originalPrice: 4500,
    category: 'sneakers',
    subcategory: 'lifestyle',
    gender: 'unisex',
    description: 'El icónico Yeezy 350 V2 en el colorway Zebra más codiciado.',
    features: ['Primeknit upper', 'Boost midsole', 'Rubber outsole', 'Zebra pattern'],
    images: ['/placeholder-yeezy-1.jpg', '/placeholder-yeezy-2.jpg'],
    rating: 4.9,
    reviews: 890,
    tags: ['yeezy', 'kanye', 'boost', 'limited'],
    releaseDate: '2023-03-20',
    isLimited: true,
    isFeatured: true,
    isNew: false,
    variants: [
      {
        id: 'yeezy-zebra-1',
        color: { id: 'zebra', name: 'Zebra (Blanco/Negro)', hex: '#F5F5F5' },
        sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
        price: 8500,
        originalPrice: 4500,
        stock: 12,
        sku: 'YZY-350-ZBR-001',
        images: ['/placeholder-yeezy-1.jpg', '/placeholder-yeezy-2.jpg']
      }
    ]
  },
  {
    id: 3,
    name: 'Gucci Ace Sneakers White',
    brand: 'Gucci',
    price: 15000,
    originalPrice: 18000,
    category: 'luxury',
    subcategory: 'designer',
    gender: 'unisex',
    description: 'Sneakers de lujo Gucci Ace con detalles icónicos de la marca italiana.',
    features: ['Piel italiana', 'Detalles bordados', 'Suela de goma', 'Made in Italy'],
    images: ['/placeholder-gucci-1.jpg', '/placeholder-gucci-2.jpg'],
    rating: 4.7,
    reviews: 340,
    tags: ['luxury', 'designer', 'italian', 'premium'],
    releaseDate: '2023-02-10',
    isLimited: false,
    isFeatured: true,
    isNew: false,
    variants: [
      {
        id: 'gucci-ace-white-1',
        color: { id: 'white', name: 'Blanco', hex: '#FFFFFF' },
        sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
        price: 15000,
        originalPrice: 18000,
        stock: 8,
        sku: 'GCC-ACE-WHT-001',
        images: ['/placeholder-gucci-1.jpg', '/placeholder-gucci-2.jpg']
      }
    ]
  },
  {
    id: 4,
    name: 'Jordan 1 Retro High Chicago',
    brand: 'Jordan',
    price: 4200,
    originalPrice: 3800,
    category: 'sneakers',
    subcategory: 'basketball',
    gender: 'unisex',
    description: 'El legendario Air Jordan 1 en el colorway Chicago original.',
    features: ['Cuero premium', 'Air cushioning', 'Rubber outsole', 'Wings logo'],
    images: ['/placeholder-jordan-1.jpg', '/placeholder-jordan-2.jpg'],
    rating: 4.9,
    reviews: 2100,
    tags: ['jordan', 'basketball', 'retro', 'chicago'],
    releaseDate: '2023-04-01',
    isLimited: false,
    isFeatured: true,
    isNew: true,
    variants: [
      {
        id: 'jordan-1-chicago-1',
        color: { id: 'red', name: 'Chicago (Rojo/Blanco/Negro)', hex: '#FF0000' },
        sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
        price: 4200,
        originalPrice: 3800,
        stock: 25,
        sku: 'JRD-1-CHI-001',
        images: ['/placeholder-jordan-1.jpg', '/placeholder-jordan-2.jpg']
      }
    ]
  },
  {
    id: 5,
    name: 'Converse Chuck Taylor All Star',
    brand: 'Converse',
    price: 1800,
    originalPrice: 2000,
    category: 'sneakers',
    subcategory: 'casual',
    gender: 'unisex',
    description: 'El clásico Chuck Taylor que ha definido generaciones.',
    features: ['Canvas upper', 'Rubber toe cap', 'Vulcanized sole', 'Classic design'],
    images: ['/placeholder-converse-1.jpg', '/placeholder-converse-2.jpg'],
    rating: 4.5,
    reviews: 1800,
    tags: ['classic', 'canvas', 'casual', 'vintage'],
    releaseDate: '2023-01-01',
    isLimited: false,
    isFeatured: false,
    isNew: false,
    variants: [
      {
        id: 'converse-chuck-black-1',
        color: { id: 'black', name: 'Negro', hex: '#000000' },
        sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
        price: 1800,
        originalPrice: 2000,
        stock: 60,
        sku: 'CNV-CHK-BLK-001',
        images: ['/placeholder-converse-1.jpg', '/placeholder-converse-2.jpg']
      }
    ]
  },
  {
    id: 6,
    name: 'Vans Old Skool Black/White',
    brand: 'Vans',
    price: 1900,
    originalPrice: 2100,
    category: 'sneakers',
    subcategory: 'skate',
    gender: 'unisex',
    description: 'El icónico Old Skool de Vans con la clásica raya lateral.',
    features: ['Suede and canvas', 'Waffle outsole', 'Padded collar', 'Side stripe'],
    images: ['/placeholder-vans-1.jpg', '/placeholder-vans-2.jpg'],
    rating: 4.6,
    reviews: 950,
    tags: ['skate', 'classic', 'street', 'california'],
    releaseDate: '2023-02-15',
    isLimited: false,
    isFeatured: false,
    isNew: false,
    variants: [
      {
        id: 'vans-oldskool-bw-1',
        color: { id: 'black', name: 'Negro/Blanco', hex: '#000000' },
        sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
        price: 1900,
        originalPrice: 2100,
        stock: 40,
        sku: 'VNS-OLD-BW-001',
        images: ['/placeholder-vans-1.jpg', '/placeholder-vans-2.jpg']
      }
    ]
  },
  {
    id: 7,
    name: 'Off-White x Nike Air Force 1 MCA',
    brand: 'Off-White',
    price: 67000,
    originalPrice: 4200,
    category: 'sneakers',
    subcategory: 'collaboration',
    gender: 'unisex',
    description: 'Colaboración exclusiva entre Off-White y Nike para el MCA Chicago.',
    features: ['Premium leather', 'Zip-tie detail', 'Deconstructed design', 'Museum branding'],
    images: ['/placeholder-offwhite-1.jpg', '/placeholder-offwhite-2.jpg'],
    rating: 4.8,
    reviews: 120,
    tags: ['collaboration', 'limited', 'virgil', 'museum'],
    releaseDate: '2023-05-15',
    isLimited: true,
    isFeatured: true,
    isNew: true,
    variants: [
      {
        id: 'offwhite-af1-mca-1',
        color: { id: 'blue', name: 'Azul MCA', hex: '#0066CC' },
        sizes: ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
        price: 67000,
        originalPrice: 4200,
        stock: 3,
        sku: 'OW-AF1-MCA-001',
        images: ['/placeholder-offwhite-1.jpg', '/placeholder-offwhite-2.jpg']
      }
    ]
  },
  {
    id: 8,
    name: 'Balenciaga Triple S White',
    brand: 'Balenciaga',
    price: 22000,
    originalPrice: 25000,
    category: 'luxury',
    subcategory: 'designer',
    gender: 'unisex',
    description: 'El chunky sneaker que redefinió la moda contemporánea.',
    features: ['Mixed materials', 'Triple sole', 'Oversized silhouette', 'Luxury construction'],
    images: ['/placeholder-balenciaga-1.jpg', '/placeholder-balenciaga-2.jpg'],
    rating: 4.4,
    reviews: 280,
    tags: ['luxury', 'chunky', 'designer', 'fashion'],
    releaseDate: '2023-03-01',
    isLimited: false,
    isFeatured: true,
    isNew: false,
    variants: [
      {
        id: 'balenciaga-triples-white-1',
        color: { id: 'white', name: 'Blanco', hex: '#FFFFFF' },
        sizes: ['6', '7', '8', '9', '10', '11', '12'],
        price: 22000,
        originalPrice: 25000,
        stock: 15,
        sku: 'BAL-TRP-WHT-001',
        images: ['/placeholder-balenciaga-1.jpg', '/placeholder-balenciaga-2.jpg']
      }
    ]
  }
]

// Helper functions
export const getProductById = (id) => {
  return products.find(product => product.id === parseInt(id))
}

export const getProductsByCategory = (category) => {
  return products.filter(product => product.category === category)
}

export const getProductsByBrand = (brand) => {
  return products.filter(product => product.brand.toLowerCase() === brand.toLowerCase())
}

export const getFeaturedProducts = () => {
  return products.filter(product => product.isFeatured)
}

export const getNewProducts = () => {
  return products.filter(product => product.isNew)
}

export const getLimitedProducts = () => {
  return products.filter(product => product.isLimited)
}

export const searchProducts = (query) => {
  const searchTerm = query.toLowerCase()
  return products.filter(product => 
    product.name.toLowerCase().includes(searchTerm) ||
    product.brand.toLowerCase().includes(searchTerm) ||
    product.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
    product.category.toLowerCase().includes(searchTerm)
  )
}

export const filterProducts = (filters) => {
  let filteredProducts = [...products]

  if (filters.category && filters.category !== 'all') {
    filteredProducts = filteredProducts.filter(product => product.category === filters.category)
  }

  if (filters.brand && filters.brand !== 'all') {
    filteredProducts = filteredProducts.filter(product => product.brand.toLowerCase() === filters.brand.toLowerCase())
  }

  if (filters.priceRange) {
    filteredProducts = filteredProducts.filter(product => 
      product.price >= filters.priceRange.min && product.price <= filters.priceRange.max
    )
  }

  if (filters.size) {
    filteredProducts = filteredProducts.filter(product =>
      product.variants && product.variants.some(variant =>
        variant.sizes && variant.sizes.includes(filters.size)
      )
    )
  }

  if (filters.color) {
    filteredProducts = filteredProducts.filter(product =>
      product.variants && product.variants.some(variant =>
        variant.color && variant.color.id === filters.color
      )
    )
  }

  if (filters.search) {
    const searchTerm = filters.search.toLowerCase()
    filteredProducts = filteredProducts.filter(product => 
      product.name.toLowerCase().includes(searchTerm) ||
      product.brand.toLowerCase().includes(searchTerm) ||
      product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  return filteredProducts
}

export const sortProducts = (products, sortBy) => {
  const sortedProducts = [...products]

  switch (sortBy) {
    case 'price-low':
      return sortedProducts.sort((a, b) => a.price - b.price)
    case 'price-high':
      return sortedProducts.sort((a, b) => b.price - a.price)
    case 'name':
      return sortedProducts.sort((a, b) => a.name.localeCompare(b.name))
    case 'rating':
      return sortedProducts.sort((a, b) => b.rating - a.rating)
    case 'newest':
      return sortedProducts.sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate))
    case 'popular':
      return sortedProducts.sort((a, b) => b.reviews - a.reviews)
    default:
      return sortedProducts
  }
}

// Categories and brands for filters
export const categories = [
  { id: 'all', name: 'Todos', icon: '👟', count: products.length },
  { id: 'sneakers', name: 'Sneakers', icon: '👟', count: products.filter(p => p.category === 'sneakers').length },
  { id: 'luxury', name: 'Lujo', icon: '✨', count: products.filter(p => p.category === 'luxury').length }
]

export const brands = [
  { id: 'all', name: 'Todas las marcas', count: products.length },
  ...Array.from(new Set(products.map(p => p.brand))).map(brand => ({
    id: brand.toLowerCase(),
    name: brand,
    count: products.filter(p => p.brand === brand).length
  }))
]

export const priceRanges = [
  { id: 'all', name: 'Todos los precios', min: 0, max: Infinity },
  { id: 'under-3000', name: 'Menos de $3,000', min: 0, max: 3000 },
  { id: '3000-10000', name: '$3,000 - $10,000', min: 3000, max: 10000 },
  { id: '10000-20000', name: '$10,000 - $20,000', min: 10000, max: 20000 },
  { id: 'over-20000', name: 'Más de $20,000', min: 20000, max: Infinity }
]

export const sizes = ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12']

export const colors = [
  { id: 'white', name: 'Blanco', hex: '#FFFFFF' },
  { id: 'black', name: 'Negro', hex: '#000000' },
  { id: 'red', name: 'Rojo', hex: '#FF0000' },
  { id: 'blue', name: 'Azul', hex: '#0000FF' },
  { id: 'green', name: 'Verde', hex: '#00FF00' },
  { id: 'cream', name: 'Crema', hex: '#F5F5DC' }
]
