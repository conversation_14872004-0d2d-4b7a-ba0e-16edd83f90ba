// CYTTE Products Data for The White Laces - 493 Products from Ultra Deep Scan
let cytteProductsData
try {
  cytteProductsData = require('../lib/data/cytte-deep-scan-products.json')
} catch (error) {
  console.error('Error loading CYTTE products:', error)
  cytteProductsData = []
}

// Convert CYTTE image paths to actual public paths
const convertImagePath = (imagePath) => {
  if (!imagePath) return imagePath

  let convertedPath = imagePath

  // ALWAYS STAY IN /products/ - Convert all paths to use the real /products/ directory structure
  // Database: /--materials/shoes/2. CYTTE/1. SNEAKERS/1. NIKE Limited Edition/...
  // Target:   /products/1. SNEAKERS/1. NIKE Limited Edition/...

  if (convertedPath.startsWith('/--materials/shoes/2. CYTTE/')) {
    convertedPath = convertedPath.replace('/--materials/shoes/2. CYTTE/', '/products/')
  }

  // Also handle other potential formats - all should go to /products/
  if (convertedPath.startsWith('/images/products/')) {
    convertedPath = convertedPath.replace('/images/products/', '/products/')
  }

  // Keep /products/ paths as they are (no conversion to organized structure)
  // This ensures we always use the real product directory structure

  // Convert file extensions from .jpg to .webp (CYTTE images are stored as WebP)
  if (convertedPath.endsWith('.jpg')) {
    convertedPath = convertedPath.replace('.jpg', '.webp')
  }

  // Log the conversion for debugging (disabled for production)
  // if (imagePath.includes('--materials') || imagePath.endsWith('.jpg')) {
  //   console.log('Converting image path:', imagePath, '→', convertedPath)
  // }

  return convertedPath
}

// Convert CYTTE products to TWL format
const convertCytteToTwlFormat = (cytteProduct, index) => {
  try {
    return {
      id: cytteProduct.id, // Use the actual product ID from the database
      name: cytteProduct.name || 'Producto sin nombre',
      brand: cytteProduct.brand || 'Marca desconocida',
      price: cytteProduct.price || 1000,
      originalPrice: cytteProduct.originalPrice || Math.round((cytteProduct.price || 1000) * 1.2),
      category: cytteProduct.style || 'sneakers',
      subcategory: cytteProduct.subType || 'lifestyle',
      gender: (cytteProduct.gender || 'MIXTE').toLowerCase() === 'mixte' ? 'unisex' : (cytteProduct.gender || 'unisex').toLowerCase(),
      description: cytteProduct.description || 'Descripción no disponible',
      features: cytteProduct.materials || ['Materiales premium', 'Diseño exclusivo', 'Calidad superior'],
      images: (cytteProduct.images || []).slice(0, 4).map(convertImagePath), // Take first 4 images with corrected paths
      rating: cytteProduct.rating || 4.0,
      reviews: cytteProduct.reviews || 0,
      tags: cytteProduct.tags || [(cytteProduct.brand || 'brand').toLowerCase(), cytteProduct.style || 'sneakers'],
      releaseDate: cytteProduct.releaseDate || '2024-01-01',
      isLimited: cytteProduct.isLimited || cytteProduct.isExclusive || false,
      isFeatured: index < 20, // First 20 products are featured
      isNew: cytteProduct.isNew || false,
      sku: cytteProduct.sku || `TWL-${index + 1}`,
      stock: cytteProduct.stock || 10,
      availability: cytteProduct.availability || 'in-stock',
      keywords: cytteProduct.keywords || [],
      searchTerms: cytteProduct.searchTerms || [],
      variants: [
        {
          id: `${cytteProduct.sku || `TWL-${index + 1}`}-variant-1`,
          color: { id: 'default', name: 'Disponible', hex: '#000000' },
          sizes: cytteProduct.sizes || ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
          price: cytteProduct.price || 1000,
          originalPrice: cytteProduct.originalPrice || Math.round((cytteProduct.price || 1000) * 1.2),
          stock: cytteProduct.stock || 10,
          sku: cytteProduct.sku || `TWL-${index + 1}`,
          images: (cytteProduct.images || []).slice(0, 4).map(convertImagePath)
        }
      ]
    }
  } catch (error) {
    console.error('Error converting product:', error)
    return null
  }
}

// Convert all CYTTE products to TWL format
const convertedProducts = cytteProductsData.map(convertCytteToTwlFormat).filter(Boolean)
export const products = convertedProducts


// Helper functions
export const getProductById = (id) => {
  return products.find(product => product.id === id)
}

export const getProductsByCategory = (category) => {
  return products.filter(product => product.category === category)
}

export const getProductsByBrand = (brand) => {
  return products.filter(product => product.brand.toLowerCase() === brand.toLowerCase())
}

export const getFeaturedProducts = () => {
  return products.filter(product => product.isFeatured)
}

export const getNewProducts = () => {
  return products.filter(product => product.isNew)
}

export const getLimitedProducts = () => {
  return products.filter(product => product.isLimited)
}

export const searchProducts = (query) => {
  const searchTerm = query.toLowerCase()
  return products.filter(product => 
    product.name.toLowerCase().includes(searchTerm) ||
    product.brand.toLowerCase().includes(searchTerm) ||
    product.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
    product.category.toLowerCase().includes(searchTerm)
  )
}

export const filterProducts = (filters) => {
  let filteredProducts = [...products]

  if (filters.category && filters.category !== 'all') {
    filteredProducts = filteredProducts.filter(product => product.category === filters.category)
  }

  if (filters.brand && filters.brand !== 'all') {
    filteredProducts = filteredProducts.filter(product => product.brand.toLowerCase() === filters.brand.toLowerCase())
  }

  if (filters.priceRange) {
    filteredProducts = filteredProducts.filter(product => 
      product.price >= filters.priceRange.min && product.price <= filters.priceRange.max
    )
  }

  if (filters.size) {
    filteredProducts = filteredProducts.filter(product =>
      product.variants && product.variants.some(variant =>
        variant.sizes && variant.sizes.includes(filters.size)
      )
    )
  }

  if (filters.color) {
    filteredProducts = filteredProducts.filter(product =>
      product.variants && product.variants.some(variant =>
        variant.color && variant.color.id === filters.color
      )
    )
  }

  if (filters.search) {
    const searchTerm = filters.search.toLowerCase()
    filteredProducts = filteredProducts.filter(product => 
      product.name.toLowerCase().includes(searchTerm) ||
      product.brand.toLowerCase().includes(searchTerm) ||
      product.tags.some(tag => tag.toLowerCase().includes(searchTerm))
    )
  }

  return filteredProducts
}

export const sortProducts = (products, sortBy) => {
  const sortedProducts = [...products]

  switch (sortBy) {
    case 'price-low':
      return sortedProducts.sort((a, b) => a.price - b.price)
    case 'price-high':
      return sortedProducts.sort((a, b) => b.price - a.price)
    case 'name':
      return sortedProducts.sort((a, b) => a.name.localeCompare(b.name))
    case 'rating':
      return sortedProducts.sort((a, b) => b.rating - a.rating)
    case 'newest':
      return sortedProducts.sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate))
    case 'popular':
      return sortedProducts.sort((a, b) => b.reviews - a.reviews)
    default:
      return sortedProducts
  }
}

// Categories and brands for filters - Updated for CYTTE data
export const categories = [
  { id: 'all', name: 'Todos', icon: '👟', count: products.length },
  { id: 'sneakers', name: 'Sneakers', icon: '👟', count: products.filter(p => p.category === 'sneakers').length },
  { id: 'sandals', name: 'Sandalias', icon: '🩴', count: products.filter(p => p.category === 'sandals').length },
  { id: 'casual', name: 'Casual', icon: '👞', count: products.filter(p => p.category === 'casual').length },
  { id: 'formal', name: 'Formal', icon: '👔', count: products.filter(p => p.category === 'formal').length },
  { id: 'kids', name: 'Niños', icon: '👶', count: products.filter(p => p.category === 'kids').length }
]

export const brands = [
  { id: 'all', name: 'Todas las marcas', count: products.length },
  ...Array.from(new Set(products.map(p => p.brand))).map(brand => ({
    id: brand.toLowerCase().replace(/\s+/g, '-'),
    name: brand,
    count: products.filter(p => p.brand === brand).length
  }))
]

export const priceRanges = [
  { id: 'all', name: 'Todos los precios', min: 0, max: Infinity },
  { id: 'under-3000', name: 'Menos de $3,000', min: 0, max: 3000 },
  { id: '3000-10000', name: '$3,000 - $10,000', min: 3000, max: 10000 },
  { id: '10000-20000', name: '$10,000 - $20,000', min: 10000, max: 20000 },
  { id: 'over-20000', name: 'Más de $20,000', min: 20000, max: Infinity }
]

export const sizes = ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12']

export const colors = [
  { id: 'white', name: 'Blanco', hex: '#FFFFFF' },
  { id: 'black', name: 'Negro', hex: '#000000' },
  { id: 'red', name: 'Rojo', hex: '#FF0000' },
  { id: 'blue', name: 'Azul', hex: '#0000FF' },
  { id: 'green', name: 'Verde', hex: '#00FF00' },
  { id: 'cream', name: 'Crema', hex: '#F5F5DC' }
]
