'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useAuth } from '@/contexts/AuthContext'
import Button from '@/components/ui/Button'
import AnimatedInput from '@/components/ui/AnimatedInput'
import AnimatedCheckbox from '@/components/ui/AnimatedCheckbox'
import AnimatedSelect from '@/components/ui/AnimatedSelect'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedLoader from '@/components/ui/AnimatedLoader'

export default function AuthModal({ isOpen, onClose, defaultMode = 'login' }) {
  const { login, register, loading, error, clearError } = useAuth()
  const [mode, setMode] = useState(defaultMode) // 'login' or 'register'
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    confirmPassword: '',
    firstName: '',
    lastName: '',
    phone: '',
    dateOfBirth: '',
    gender: '',
    acceptTerms: false,
    newsletter: true
  })

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }))
    
    // Clear error when user starts typing
    if (error) clearError()
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (mode === 'login') {
      const result = await login(formData.email, formData.password)
      if (result.success) {
        onClose()
        setFormData({
          email: '',
          password: '',
          confirmPassword: '',
          firstName: '',
          lastName: '',
          phone: '',
          dateOfBirth: '',
          gender: '',
          acceptTerms: false,
          newsletter: true
        })
      }
    } else {
      // Validation for register
      if (formData.password !== formData.confirmPassword) {
        return
      }
      
      if (!formData.acceptTerms) {
        return
      }
      
      const result = await register({
        email: formData.email,
        password: formData.password,
        firstName: formData.firstName,
        lastName: formData.lastName,
        phone: formData.phone,
        dateOfBirth: formData.dateOfBirth,
        gender: formData.gender,
        preferences: {
          newsletter: formData.newsletter,
          smsNotifications: false
        }
      })
      
      if (result.success) {
        onClose()
        setFormData({
          email: '',
          password: '',
          confirmPassword: '',
          firstName: '',
          lastName: '',
          phone: '',
          dateOfBirth: '',
          gender: '',
          acceptTerms: false,
          newsletter: true
        })
      }
    }
  }

  const switchMode = () => {
    setMode(mode === 'login' ? 'register' : 'login')
    clearError()
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className="glass-card rounded-2xl max-w-md w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-warm-camel/20">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-natural rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-forest-emerald" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                  {mode === 'login' ? 'Iniciar Sesión' : 'Crear Cuenta'}
                </h2>
                <p className="text-sm text-warm-camel">
                  {mode === 'login' ? 'Accede a tu cuenta' : 'Únete a The White Laces'}
                </p>
              </div>
            </div>
            
            <Button variant="ghost" size="icon" onClick={onClose}>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </Button>
          </div>

          {/* Content */}
          <div className="p-6 overflow-y-auto">
            <form onSubmit={handleSubmit} className="space-y-4">
              
              {/* Register Fields */}
              {mode === 'register' && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4"
                >
                  <div className="grid grid-cols-2 gap-3">
                    <AnimatedInput
                      type="text"
                      name="firstName"
                      label="Nombre"
                      placeholder="Nombre"
                      value={formData.firstName}
                      onChange={handleInputChange}
                      required
                    />
                    <AnimatedInput
                      type="text"
                      name="lastName"
                      label="Apellido"
                      placeholder="Apellido"
                      value={formData.lastName}
                      onChange={handleInputChange}
                      required
                    />
                  </div>
                  
                  <AnimatedInput
                    type="tel"
                    name="phone"
                    label="Teléfono"
                    placeholder="Teléfono"
                    value={formData.phone}
                    onChange={handleInputChange}
                  />
                  
                  <div className="grid grid-cols-2 gap-3">
                    <AnimatedInput
                      type="date"
                      name="dateOfBirth"
                      label="Fecha de nacimiento"
                      placeholder="Fecha de nacimiento"
                      value={formData.dateOfBirth}
                      onChange={handleInputChange}
                    />
                    <AnimatedSelect
                      name="gender"
                      label="Género"
                      placeholder="Seleccionar género"
                      value={formData.gender}
                      onChange={(value) => handleInputChange({ target: { name: 'gender', value } })}
                      options={[
                        { value: 'male', label: 'Masculino' },
                        { value: 'female', label: 'Femenino' },
                        { value: 'other', label: 'Otro' },
                        { value: 'prefer-not-to-say', label: 'Prefiero no decir' }
                      ]}
                    />
                  </div>
                </motion.div>
              )}

              {/* Common Fields */}
              <AnimatedInput
                type="email"
                name="email"
                label="Email"
                placeholder="Email"
                value={formData.email}
                onChange={handleInputChange}
                required
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                }
              />

              <AnimatedInput
                type="password"
                name="password"
                label="Contraseña"
                placeholder="Contraseña"
                value={formData.password}
                onChange={handleInputChange}
                required
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                }
              />
              
              {mode === 'register' && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="space-y-4"
                >
                  <AnimatedInput
                    type="password"
                    name="confirmPassword"
                    label="Confirmar contraseña"
                    placeholder="Confirmar contraseña"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    required
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    }
                  />
                  
                  {/* Terms and Newsletter */}
                  <div className="space-y-3">
                    <AnimatedCheckbox
                      name="acceptTerms"
                      checked={formData.acceptTerms}
                      onChange={handleInputChange}
                      label="Acepto los términos y condiciones"
                      description={
                        <span>
                          Acepto los{' '}
                          <a href="/terms" className="text-rich-gold hover:underline">
                            términos y condiciones
                          </a>{' '}
                          y la{' '}
                          <a href="/privacy" className="text-rich-gold hover:underline">
                            política de privacidad
                          </a>
                        </span>
                      }
                      variant="warning"
                      required
                    />

                    <AnimatedCheckbox
                      name="newsletter"
                      checked={formData.newsletter}
                      onChange={handleInputChange}
                      label="Suscribirse al newsletter"
                      description="Quiero recibir ofertas exclusivas y novedades"
                      variant="success"
                    />
                  </div>
                </motion.div>
              )}

              {/* Error Message */}
              {error && (
                <motion.div
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="p-3 bg-warm-camel/10 border border-warm-camel/30 rounded-lg"
                >
                  <p className="text-sm text-warm-camel">{error}</p>
                </motion.div>
              )}

              {/* Demo Credentials */}
              {mode === 'login' && (
                <div className="p-3 bg-rich-gold/10 border border-rich-gold/30 rounded-lg">
                  <p className="text-xs text-forest-emerald dark:text-light-cloud-gray font-medium mb-1">
                    Credenciales de demo:
                  </p>
                  <p className="text-xs text-warm-camel">
                    Email: <EMAIL><br />
                    Contraseña: demo123
                  </p>
                </div>
              )}

              {/* Submit Button */}
              <AnimatedButton
                type="submit"
                variant="primary"
                className="w-full"
                loading={loading}
                disabled={loading}
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1" />
                  </svg>
                }
                iconPosition="right"
              >
                {mode === 'login' ? 'Iniciar Sesión' : 'Crear Cuenta'}
              </AnimatedButton>
            </form>

            {/* Switch Mode */}
            <div className="mt-6 text-center">
              <p className="text-sm text-warm-camel">
                {mode === 'login' ? '¿No tienes cuenta?' : '¿Ya tienes cuenta?'}
                <button
                  type="button"
                  onClick={switchMode}
                  className="ml-2 text-rich-gold hover:underline font-medium"
                >
                  {mode === 'login' ? 'Regístrate' : 'Inicia sesión'}
                </button>
              </p>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
