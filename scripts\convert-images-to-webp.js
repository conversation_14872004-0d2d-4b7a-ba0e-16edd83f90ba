#!/usr/bin/env node

/**
 * Image Conversion Script for CYTTE Products
 * Converts JPG images to WebP format for better performance
 * Maintains original folder structure and creates optimized versions
 */

const fs = require('fs').promises;
const path = require('path');

// Try to load <PERSON>, fallback to file copying if not available
let sharp = null;
try {
  sharp = require('sharp');
  console.log('✅ Sharp library loaded - will perform actual WebP conversion');
} catch (error) {
  console.log('⚠️ Sharp library not found - will copy files for now');
  console.log('💡 Run "npm install sharp" for actual WebP conversion');
}

// Configuration
const CYTTE_BASE_PATH = 'C:/2.MY_APP/TWL/V2/--materials/shoes/2. CYTTE';
const OUTPUT_BASE_PATH = 'C:/2.MY_APP/TWL/V2/--materials/shoes/2. CYTTE-WEBP';
const PUBLIC_IMAGES_PATH = 'public/products';

class ImageConverter {
  constructor() {
    this.convertedCount = 0;
    this.errorCount = 0;
    this.skippedCount = 0;
  }

  async convertCytteImages() {
    console.log('🖼️ Starting JPG to WebP conversion for CYTTE products...\n');
    
    try {
      // Create output directory
      await this.ensureDirectory(OUTPUT_BASE_PATH);
      await this.ensureDirectory(PUBLIC_IMAGES_PATH);
      
      // Process all categories
      const categories = await fs.readdir(CYTTE_BASE_PATH);
      
      for (const categoryFolder of categories) {
        if (categoryFolder.startsWith('.')) continue;
        
        const categoryPath = path.join(CYTTE_BASE_PATH, categoryFolder);
        const categoryStats = await fs.stat(categoryPath);
        
        if (categoryStats.isDirectory()) {
          await this.processCategoryFolder(categoryPath, categoryFolder);
        }
      }
      
      console.log('\n✅ Image conversion completed!');
      console.log(`📊 Converted: ${this.convertedCount} images`);
      console.log(`⚠️ Errors: ${this.errorCount} images`);
      console.log(`⏭️ Skipped: ${this.skippedCount} images`);
      
    } catch (error) {
      console.error('❌ Error during conversion:', error);
      throw error;
    }
  }

  async processCategoryFolder(categoryPath, categoryFolder) {
    console.log(`📂 Processing category: ${categoryFolder}`);
    
    const brands = await fs.readdir(categoryPath);
    
    for (const brandFolder of brands) {
      if (brandFolder.startsWith('.') || brandFolder.endsWith('.txt')) continue;
      
      const brandPath = path.join(categoryPath, brandFolder);
      const brandStats = await fs.stat(brandPath);
      
      if (brandStats.isDirectory()) {
        await this.processBrandFolder(brandPath, brandFolder, categoryFolder);
      }
    }
  }

  async processBrandFolder(brandPath, brandFolder, categoryFolder) {
    console.log(`🏷️ Processing brand: ${brandFolder}`);
    
    const genders = await fs.readdir(brandPath);
    
    for (const genderFolder of genders) {
      if (genderFolder.startsWith('.')) continue;
      
      const genderPath = path.join(brandPath, genderFolder);
      const genderStats = await fs.stat(genderPath);
      
      if (genderStats.isDirectory()) {
        await this.processGenderFolder(genderPath, genderFolder, categoryFolder, brandFolder);
      }
    }
  }

  async processGenderFolder(genderPath, genderFolder, categoryFolder, brandFolder) {
    const products = await fs.readdir(genderPath);
    
    for (const productFolder of products) {
      if (productFolder.startsWith('.')) continue;
      
      const productPath = path.join(genderPath, productFolder);
      const productStats = await fs.stat(productPath);
      
      if (productStats.isDirectory()) {
        await this.processProductFolder(productPath, productFolder, categoryFolder, brandFolder, genderFolder);
      }
    }
  }

  async processProductFolder(productPath, productFolder, categoryFolder, brandFolder, genderFolder) {
    try {
      console.log(`👟 Converting images for: ${productFolder}`);
      
      // Create output directory structure
      const outputPath = path.join(OUTPUT_BASE_PATH, categoryFolder, brandFolder, genderFolder, productFolder);
      await this.ensureDirectory(outputPath);
      
      // Also create public directory structure for web access
      const publicPath = path.join(PUBLIC_IMAGES_PATH, this.sanitizeForWeb(brandFolder), this.sanitizeForWeb(productFolder));
      await this.ensureDirectory(publicPath);
      
      // Read product files
      const files = await fs.readdir(productPath);
      const imageFiles = files.filter(file => 
        file.toLowerCase().endsWith('.jpg') || 
        file.toLowerCase().endsWith('.jpeg')
      );
      
      if (imageFiles.length === 0) {
        console.log(`⚠️ No JPG images found in ${productFolder}`);
        this.skippedCount++;
        return;
      }

      // Convert each image
      for (let i = 0; i < imageFiles.length; i++) {
        const imageFile = imageFiles[i];
        const inputPath = path.join(productPath, imageFile);
        
        // Generate WebP filename
        const webpFilename = `${path.parse(imageFile).name}.webp`;
        const outputFilePath = path.join(outputPath, webpFilename);
        const publicFilePath = path.join(publicPath, `image-${i + 1}.webp`);
        
        try {
          if (sharp) {
            // Convert to WebP with optimization using Sharp
            await sharp(inputPath)
              .webp({
                quality: 85,
                effort: 6 // Higher effort = better compression
              })
              .toFile(outputFilePath);

            // Also save to public directory with simplified naming
            await sharp(inputPath)
              .webp({
                quality: 85,
                effort: 6
              })
              .resize(800, 800, {
                fit: 'inside',
                withoutEnlargement: true
              })
              .toFile(publicFilePath);
          } else {
            // Fallback: Copy JPG files with WebP extension (placeholder)
            const fs = require('fs');
            await fs.promises.copyFile(inputPath, outputFilePath);
            await fs.promises.copyFile(inputPath, publicFilePath);
            console.log(`📋 Copied ${imageFile} (placeholder - install Sharp for WebP conversion)`);
          }

          this.convertedCount++;

        } catch (conversionError) {
          console.error(`❌ Error processing ${imageFile}:`, conversionError.message);
          this.errorCount++;
        }
      }
      
      // Copy description file if it exists
      const descriptionFile = files.find(file => file.toLowerCase() === 'description.txt');
      if (descriptionFile) {
        const descInputPath = path.join(productPath, descriptionFile);
        const descOutputPath = path.join(outputPath, descriptionFile);
        await fs.copyFile(descInputPath, descOutputPath);
      }
      
    } catch (error) {
      console.error(`❌ Error processing product ${productFolder}:`, error);
      this.errorCount++;
    }
  }

  sanitizeForWeb(folderName) {
    return folderName
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
  }

  async ensureDirectory(dirPath) {
    try {
      await fs.mkdir(dirPath, { recursive: true });
    } catch (error) {
      if (error.code !== 'EEXIST') {
        throw error;
      }
    }
  }

  async generateConversionReport() {
    const report = {
      conversion_completed_at: new Date().toISOString(),
      total_converted: this.convertedCount,
      total_errors: this.errorCount,
      total_skipped: this.skippedCount,
      output_directories: {
        webp_archive: OUTPUT_BASE_PATH,
        public_images: PUBLIC_IMAGES_PATH
      },
      next_steps: [
        'Run the product indexing script to process WebP images',
        'Update product database with new image paths',
        'Test image loading on the website'
      ]
    };

    try {
      await this.ensureDirectory('output');
      await fs.writeFile('output/image-conversion-report.json', JSON.stringify(report, null, 2));
      console.log('\n📊 Conversion report saved to output/image-conversion-report.json');
    } catch (error) {
      console.error('❌ Error saving report:', error);
    }
  }
}

// Main execution
async function main() {
  console.log('🚀 Starting CYTTE Image Conversion Process...\n');

  if (!sharp) {
    console.log('📋 Running in copy mode - images will be organized but not converted to WebP');
    console.log('💡 Install Sharp later for actual WebP conversion: npm install sharp\n');
  }
  
  const converter = new ImageConverter();
  
  try {
    await converter.convertCytteImages();
    await converter.generateConversionReport();
    
    console.log('\n🎉 Image conversion completed successfully!');
    console.log('\n📋 Next Steps:');
    console.log('1. Run the product indexing script to process WebP images');
    console.log('2. Update your website to use the new optimized images');
    console.log('3. Test image loading performance');
    
  } catch (error) {
    console.error('\n❌ Image conversion failed:', error);
    process.exit(1);
  }
}

// Run if called directly
if (require.main === module) {
  main();
}

module.exports = { ImageConverter };
