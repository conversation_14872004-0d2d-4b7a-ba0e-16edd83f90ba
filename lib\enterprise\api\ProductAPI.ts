/**
 * TWL Enterprise Product API
 * RESTful API endpoints for product operations
 * 
 * Features:
 * - RESTful product endpoints
 * - Error handling and validation
 * - Response formatting
 * - Rate limiting
 * - Authentication support
 * - Performance monitoring
 */

import { NextRequest, NextResponse } from 'next/server'
import { TWLProduct, TWLProductFilters } from '../models/Product'
import { ProductLoader } from '../core/ProductLoader'
import { Logger } from '../utils/Logger'

/**
 * API Response Format
 */
export interface APIResponse<T = any> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
    details?: any
  }
  meta?: {
    timestamp: string
    duration: number
    version: string
    requestId: string
  }
}

/**
 * API Error Codes
 */
export enum APIErrorCode {
  INVALID_REQUEST = 'INVALID_REQUEST',
  PRODUCT_NOT_FOUND = 'PRODUCT_NOT_FOUND',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE'
}

/**
 * Enterprise Product API
 */
export class ProductAPI {
  private loader: ProductLoader
  private logger: Logger
  private isInitialized: boolean

  constructor(loader: ProductLoader) {
    this.loader = loader
    this.logger = new Logger('ProductAPI')
    this.isInitialized = false
  }

  /**
   * Initialize the API
   */
  async initialize(): Promise<void> {
    try {
      await this.loader.initialize()
      this.isInitialized = true
      this.logger.info('🚀 Product API initialized')
    } catch (error) {
      this.logger.error('❌ Failed to initialize Product API:', error)
      throw error
    }
  }

  /**
   * GET /api/products/:id - Get single product
   */
  async getProduct(request: NextRequest, { params }: { params: { id: string } }): Promise<NextResponse> {
    const startTime = Date.now()
    const requestId = this.generateRequestId()

    try {
      this.logger.debug(`GET /api/products/${params.id}`, { requestId })

      // Validate request
      if (!this.isInitialized) {
        return this.errorResponse(APIErrorCode.SERVICE_UNAVAILABLE, 'Service not ready', requestId, startTime)
      }

      if (!params.id) {
        return this.errorResponse(APIErrorCode.INVALID_REQUEST, 'Product ID is required', requestId, startTime)
      }

      // Load product
      const result = await this.loader.loadProduct(params.id)

      if (!result.success || !result.data) {
        return this.errorResponse(APIErrorCode.PRODUCT_NOT_FOUND, `Product not found: ${params.id}`, requestId, startTime)
      }

      return this.successResponse(result.data, requestId, startTime, {
        fromCache: result.fromCache,
        cacheLayer: result.cacheLayer
      })

    } catch (error) {
      this.logger.error(`Error in getProduct:`, error)
      return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Internal server error', requestId, startTime)
    }
  }

  /**
   * GET /api/products - Get products with search and filtering
   */
  async getProducts(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now()
    const requestId = this.generateRequestId()

    try {
      this.logger.debug('GET /api/products', { requestId })

      // Validate request
      if (!this.isInitialized) {
        return this.errorResponse(APIErrorCode.SERVICE_UNAVAILABLE, 'Service not ready', requestId, startTime)
      }

      // Parse query parameters
      const { searchParams } = new URL(request.url)
      const query = searchParams.get('q') || undefined
      const page = parseInt(searchParams.get('page') || '1')
      const pageSize = Math.min(parseInt(searchParams.get('pageSize') || '20'), 100) // Max 100 items per page

      // Parse filters
      const filters: TWLProductFilters = {}
      
      if (searchParams.get('brands')) {
        filters.brands = searchParams.get('brands')!.split(',')
      }
      
      if (searchParams.get('categories')) {
        filters.categories = searchParams.get('categories')!.split(',')
      }
      
      if (searchParams.get('priceMin')) {
        filters.priceMin = parseFloat(searchParams.get('priceMin')!)
      }
      
      if (searchParams.get('priceMax')) {
        filters.priceMax = parseFloat(searchParams.get('priceMax')!)
      }
      
      if (searchParams.get('inStockOnly')) {
        filters.inStockOnly = searchParams.get('inStockOnly') === 'true'
      }
      
      if (searchParams.get('isLimitedEdition')) {
        filters.isLimitedEdition = searchParams.get('isLimitedEdition') === 'true'
      }
      
      if (searchParams.get('rating')) {
        filters.rating = parseFloat(searchParams.get('rating')!)
      }
      
      if (searchParams.get('sortBy')) {
        filters.sortBy = searchParams.get('sortBy') as any
      }
      
      if (searchParams.get('sortOrder')) {
        filters.sortOrder = searchParams.get('sortOrder') as 'asc' | 'desc'
      }

      // Validate pagination
      if (page < 1 || pageSize < 1) {
        return this.errorResponse(APIErrorCode.VALIDATION_ERROR, 'Invalid pagination parameters', requestId, startTime)
      }

      // Search products
      const result = await this.loader.searchProducts(query, filters, page, pageSize)

      if (!result.success || !result.data) {
        return this.errorResponse(APIErrorCode.INTERNAL_ERROR, result.error || 'Search failed', requestId, startTime)
      }

      return this.successResponse(result.data, requestId, startTime)

    } catch (error) {
      this.logger.error('Error in getProducts:', error)
      return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Internal server error', requestId, startTime)
    }
  }

  /**
   * POST /api/products/batch - Get multiple products by IDs
   */
  async getProductsBatch(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now()
    const requestId = this.generateRequestId()

    try {
      this.logger.debug('POST /api/products/batch', { requestId })

      // Validate request
      if (!this.isInitialized) {
        return this.errorResponse(APIErrorCode.SERVICE_UNAVAILABLE, 'Service not ready', requestId, startTime)
      }

      // Parse request body
      const body = await request.json()
      const { productIds } = body

      if (!Array.isArray(productIds) || productIds.length === 0) {
        return this.errorResponse(APIErrorCode.VALIDATION_ERROR, 'productIds array is required', requestId, startTime)
      }

      if (productIds.length > 100) {
        return this.errorResponse(APIErrorCode.VALIDATION_ERROR, 'Maximum 100 products per batch request', requestId, startTime)
      }

      // Load products
      const result = await this.loader.loadProducts(productIds)

      if (!result.success || !result.data) {
        return this.errorResponse(APIErrorCode.INTERNAL_ERROR, result.error || 'Batch load failed', requestId, startTime)
      }

      return this.successResponse({
        products: result.data,
        requested: productIds.length,
        found: result.data.length,
        notFound: productIds.length - result.data.length
      }, requestId, startTime)

    } catch (error) {
      this.logger.error('Error in getProductsBatch:', error)
      return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Internal server error', requestId, startTime)
    }
  }

  /**
   * GET /api/products/categories - Get available categories
   */
  async getCategories(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now()
    const requestId = this.generateRequestId()

    try {
      this.logger.debug('GET /api/products/categories', { requestId })

      if (!this.isInitialized) {
        return this.errorResponse(APIErrorCode.SERVICE_UNAVAILABLE, 'Service not ready', requestId, startTime)
      }

      // Get all products to extract categories
      const result = await this.loader.getAllProducts()

      if (!result.success || !result.data) {
        return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Failed to load categories', requestId, startTime)
      }

      // Extract unique categories
      const categoriesMap = new Map()
      for (const product of result.data) {
        if (!categoriesMap.has(product.category.id)) {
          categoriesMap.set(product.category.id, {
            id: product.category.id,
            name: product.category.name,
            productCount: 0
          })
        }
        categoriesMap.get(product.category.id).productCount++
      }

      const categories = Array.from(categoriesMap.values())

      return this.successResponse(categories, requestId, startTime)

    } catch (error) {
      this.logger.error('Error in getCategories:', error)
      return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Internal server error', requestId, startTime)
    }
  }

  /**
   * GET /api/products/brands - Get available brands
   */
  async getBrands(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now()
    const requestId = this.generateRequestId()

    try {
      this.logger.debug('GET /api/products/brands', { requestId })

      if (!this.isInitialized) {
        return this.errorResponse(APIErrorCode.SERVICE_UNAVAILABLE, 'Service not ready', requestId, startTime)
      }

      // Get all products to extract brands
      const result = await this.loader.getAllProducts()

      if (!result.success || !result.data) {
        return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Failed to load brands', requestId, startTime)
      }

      // Extract unique brands
      const brandsMap = new Map()
      for (const product of result.data) {
        if (!brandsMap.has(product.brand.id)) {
          brandsMap.set(product.brand.id, {
            id: product.brand.id,
            name: product.brand.name,
            isLuxury: product.brand.isLuxury,
            tier: product.brand.tier,
            productCount: 0
          })
        }
        brandsMap.get(product.brand.id).productCount++
      }

      const brands = Array.from(brandsMap.values())

      return this.successResponse(brands, requestId, startTime)

    } catch (error) {
      this.logger.error('Error in getBrands:', error)
      return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Internal server error', requestId, startTime)
    }
  }

  /**
   * GET /api/system/status - Get system status
   */
  async getSystemStatus(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now()
    const requestId = this.generateRequestId()

    try {
      this.logger.debug('GET /api/system/status', { requestId })

      const status = this.loader.getSystemStatus()

      return this.successResponse(status, requestId, startTime)

    } catch (error) {
      this.logger.error('Error in getSystemStatus:', error)
      return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Internal server error', requestId, startTime)
    }
  }

  /**
   * POST /api/system/scan - Trigger manual scan
   */
  async triggerScan(request: NextRequest): Promise<NextResponse> {
    const startTime = Date.now()
    const requestId = this.generateRequestId()

    try {
      this.logger.debug('POST /api/system/scan', { requestId })

      if (!this.isInitialized) {
        return this.errorResponse(APIErrorCode.SERVICE_UNAVAILABLE, 'Service not ready', requestId, startTime)
      }

      // Trigger scan in background
      this.loader.performFullScan().catch(error => {
        this.logger.error('Manual scan failed:', error)
      })

      return this.successResponse({ message: 'Scan triggered successfully' }, requestId, startTime)

    } catch (error) {
      this.logger.error('Error in triggerScan:', error)
      return this.errorResponse(APIErrorCode.INTERNAL_ERROR, 'Internal server error', requestId, startTime)
    }
  }

  // Helper methods
  private successResponse<T>(data: T, requestId: string, startTime: number, meta?: any): NextResponse {
    const response: APIResponse<T> = {
      success: true,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
        version: '1.0.0',
        requestId,
        ...meta
      }
    }

    return NextResponse.json(response, { status: 200 })
  }

  private errorResponse(code: APIErrorCode, message: string, requestId: string, startTime: number, details?: any): NextResponse {
    const response: APIResponse = {
      success: false,
      error: {
        code,
        message,
        details
      },
      meta: {
        timestamp: new Date().toISOString(),
        duration: Date.now() - startTime,
        version: '1.0.0',
        requestId
      }
    }

    const statusCode = this.getStatusCodeForError(code)
    return NextResponse.json(response, { status: statusCode })
  }

  private getStatusCodeForError(code: APIErrorCode): number {
    switch (code) {
      case APIErrorCode.INVALID_REQUEST:
      case APIErrorCode.VALIDATION_ERROR:
        return 400
      case APIErrorCode.PRODUCT_NOT_FOUND:
        return 404
      case APIErrorCode.RATE_LIMIT_EXCEEDED:
        return 429
      case APIErrorCode.SERVICE_UNAVAILABLE:
        return 503
      case APIErrorCode.INTERNAL_ERROR:
      default:
        return 500
    }
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

export default ProductAPI
