@echo off
echo CYTTE Video Copy Script
echo =======================

set "SOURCE_DIR=--materials\shoes\2. CYTTE"
set "DEST_DIR=public\products"

echo Checking for CYTTE folder...
if not exist "%SOURCE_DIR%" (
    echo ERROR: CYTTE folder not found at %SOURCE_DIR%
    pause
    exit /b 1
)

echo Creating destination directory...
if not exist "%DEST_DIR%" mkdir "%DEST_DIR%"

echo Scanning for video files...

REM Copy Nike Bode videos
if exist "%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\FQ6891-001 -- Bode x Nike Astro Grabber\Video-Bode-2.mp4" (
    echo Found: Video-Bode-2.mp4
    if not exist "%DEST_DIR%\nike\fq6891-001-bode-x-nike-astro-grabber" mkdir "%DEST_DIR%\nike\fq6891-001-bode-x-nike-astro-grabber"
    copy "%SOURCE_DIR%\1. <PERSON>NE<PERSON>KERS\1. NIKE Limited Edition\FQ6891-001 -- Bode x Nike Astro Grabber\Video-Bode-2.mp4" "%DEST_DIR%\nike\fq6891-001-bode-x-nike-astro-grabber\"
    echo Copied: Video-Bode-2.mp4
)

if exist "%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\FQ6892-100 -- Bode x Nike Astro Grabber\Video-Bode-1.mp4" (
    echo Found: Video-Bode-1.mp4
    if not exist "%DEST_DIR%\nike\fq6892-100-bode-x-nike-astro-grabber" mkdir "%DEST_DIR%\nike\fq6892-100-bode-x-nike-astro-grabber"
    copy "%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\FQ6892-100 -- Bode x Nike Astro Grabber\Video-Bode-1.mp4" "%DEST_DIR%\nike\fq6892-100-bode-x-nike-astro-grabber\"
    echo Copied: Video-Bode-1.mp4
)

REM Copy Nike x Gucci videos
if exist "%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" (
    echo Copying Nike x Gucci videos...
    if not exist "%DEST_DIR%\nike-gucci" mkdir "%DEST_DIR%\nike-gucci"
    for %%f in ("%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\*\*.mp4") do (
        echo Found Nike x Gucci video: %%~nxf
        copy "%%f" "%DEST_DIR%\nike-gucci\"
    )
)

REM Copy Off-White videos
if exist "%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE" (
    echo Copying Off-White videos...
    if not exist "%DEST_DIR%\nike-off-white" mkdir "%DEST_DIR%\nike-off-white"
    for %%f in ("%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\*\*.mp4") do (
        echo Found Off-White video: %%~nxf
        copy "%%f" "%DEST_DIR%\nike-off-white\"
    )
)

REM Copy Air Jordan videos
if exist "%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN" (
    echo Copying Air Jordan videos...
    if not exist "%DEST_DIR%\nike-jordan" mkdir "%DEST_DIR%\nike-jordan"
    for %%f in ("%SOURCE_DIR%\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\*\*\*.mp4") do (
        echo Found Air Jordan video: %%~nxf
        copy "%%f" "%DEST_DIR%\nike-jordan\"
    )
)

REM Search for other video files recursively
echo Searching for additional video files...
for /r "%SOURCE_DIR%" %%f in (*.mp4 *.mov *.avi *.webm) do (
    echo Found video: %%~nxf
    REM You can add more specific copy logic here
)

echo.
echo Video copy process complete!
echo Videos are now available in public/products/
echo.
echo Next steps:
echo 1. Run video optimization: node scripts/copy-and-optimize-videos.js
echo 2. Update product pages to include video support
echo.
pause
