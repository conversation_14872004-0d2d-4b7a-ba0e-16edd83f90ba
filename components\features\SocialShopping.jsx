'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'

export default function SocialShopping({ onClose }) {
  const [activeTab, setActiveTab] = useState('feed')
  const [socialFeed, setSocialFeed] = useState([])
  const [trendingProducts, setTrendingProducts] = useState([])
  const [userStats, setUserStats] = useState(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    loadSocialData()
  }, [])

  const loadSocialData = async () => {
    setIsLoading(true)
    
    // Simulate API calls
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    setSocialFeed(generateSocialFeed())
    setTrendingProducts(generateTrendingProducts())
    setUserStats(generateUserStats())
    setIsLoading(false)
  }

  const generateSocialFeed = () => {
    const posts = [
      {
        id: 1,
        user: {
          name: '<PERSON>',
          avatar: '👩🏻',
          level: 'Sneaker Expert',
          followers: 1250
        },
        content: {
          type: 'review',
          product: 'Nike Air Force 1',
          rating: 5,
          text: '¡Increíbles! La calidad es excepcional y son súper cómodas. Perfectas para el día a día. 💯',
          images: ['📸', '📸', '📸'],
          likes: 89,
          comments: 23,
          shares: 12
        },
        timestamp: '2 horas',
        trending: true
      },
      {
        id: 2,
        user: {
          name: 'Carlos Mendoza',
          avatar: '👨🏽',
          level: 'Style Influencer',
          followers: 3400
        },
        content: {
          type: 'outfit',
          products: ['Gucci Ace Sneakers', 'Levi\'s 501'],
          text: 'Outfit del día: casual pero elegante. Los Gucci Ace son perfectos para este look. ¿Qué opinan? 🔥',
          images: ['📸', '📸'],
          likes: 156,
          comments: 45,
          shares: 28
        },
        timestamp: '4 horas',
        trending: false
      },
      {
        id: 3,
        user: {
          name: 'Ana Rodríguez',
          avatar: '👩🏻‍🦱',
          level: 'Fashion Lover',
          followers: 890
        },
        content: {
          type: 'question',
          text: '¿Alguien ha probado los nuevos Yeezy? Estoy pensando en comprarlos pero no estoy segura del sizing. ¡Ayuda! 🤔',
          likes: 34,
          comments: 67,
          shares: 8
        },
        timestamp: '6 horas',
        trending: false
      },
      {
        id: 4,
        user: {
          name: 'Luis Herrera',
          avatar: '👨🏻',
          level: 'Collector',
          followers: 2100
        },
        content: {
          type: 'collection',
          text: 'Mi colección de Jordan 1s está creciendo. ¿Cuál debería ser el próximo? 👟',
          images: ['📸', '📸', '📸', '📸'],
          likes: 203,
          comments: 89,
          shares: 45
        },
        timestamp: '8 horas',
        trending: true
      }
    ]
    
    return posts
  }

  const generateTrendingProducts = () => {
    return [
      {
        id: 1,
        name: 'Nike Air Force 1',
        brand: 'Nike',
        price: 2500,
        trendScore: 95,
        mentions: 234,
        sentiment: 'positive',
        change: '+12%'
      },
      {
        id: 2,
        name: 'Adidas Yeezy Boost',
        brand: 'Adidas',
        price: 4500,
        trendScore: 88,
        mentions: 189,
        sentiment: 'positive',
        change: '+8%'
      },
      {
        id: 3,
        name: 'Gucci Ace Sneakers',
        brand: 'Gucci',
        price: 15000,
        trendScore: 82,
        mentions: 156,
        sentiment: 'mixed',
        change: '+5%'
      },
      {
        id: 4,
        name: 'Converse Chuck Taylor',
        brand: 'Converse',
        price: 1800,
        trendScore: 76,
        mentions: 145,
        sentiment: 'positive',
        change: '+3%'
      }
    ]
  }

  const generateUserStats = () => {
    return {
      level: 'Style Explorer',
      points: 1250,
      nextLevel: 2000,
      badges: [
        { name: 'First Purchase', icon: '🛍️', earned: true },
        { name: 'Review Master', icon: '⭐', earned: true },
        { name: 'Trendsetter', icon: '🔥', earned: false },
        { name: 'Community Helper', icon: '🤝', earned: true },
        { name: 'Collector', icon: '👟', earned: false }
      ],
      stats: {
        reviews: 12,
        likes: 89,
        followers: 45,
        following: 123
      }
    }
  }

  const handleLike = (postId) => {
    setSocialFeed(prev => prev.map(post => 
      post.id === postId 
        ? { ...post, content: { ...post.content, likes: post.content.likes + 1 } }
        : post
    ))
  }

  const handleShare = async (post) => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: `Post de ${post.user.name}`,
          text: post.content.text,
          url: window.location.href
        })
      } catch (error) {
        console.error('Error sharing:', error)
      }
    } else {
      navigator.clipboard.writeText(window.location.href)
      alert('¡Link copiado al portapapeles!')
    }
  }

  const tabs = [
    { id: 'feed', name: 'Feed Social', icon: '📱' },
    { id: 'trending', name: 'Tendencias', icon: '📈' },
    { id: 'profile', name: 'Mi Perfil', icon: '👤' }
  ]

  if (isLoading) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.9 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <Card variant="glass" className="max-w-md w-full" onClick={(e) => e.stopPropagation()}>
          <CardContent className="p-8 text-center">
            <motion.div
              className="w-16 h-16 border-4 border-rich-gold border-t-transparent rounded-full mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "linear"
              }}
            />
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Cargando Comunidad
            </h3>
            <p className="text-warm-camel">
              Conectando con la comunidad de sneakerheads...
            </p>
          </CardContent>
        </Card>
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <Card variant="glass" className="max-w-6xl w-full max-h-[90vh] overflow-hidden" onClick={(e) => e.stopPropagation()}>
        <CardContent className="p-0">
          
          {/* Header */}
          <div className="p-6 border-b border-warm-camel/20">
            <div className="flex items-center justify-between">
              <div>
                <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                  Comunidad TWL
                </h2>
                <p className="text-warm-camel">
                  Conecta, comparte y descubre con otros sneakerheads
                </p>
              </div>
              <AnimatedButton
                variant="ghost"
                onClick={onClose}
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                }
              />
            </div>

            {/* Tabs */}
            <div className="flex gap-2 mt-6">
              {tabs.map((tab) => (
                <AnimatedButton
                  key={tab.id}
                  variant={activeTab === tab.id ? 'primary' : 'ghost'}
                  onClick={() => setActiveTab(tab.id)}
                  icon={<span>{tab.icon}</span>}
                  size="sm"
                >
                  {tab.name}
                </AnimatedButton>
              ))}
            </div>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[70vh] overflow-y-auto">
            <AnimatePresence mode="wait">
              
              {/* Social Feed */}
              {activeTab === 'feed' && (
                <motion.div
                  key="feed"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  {socialFeed.map((post, index) => (
                    <motion.div
                      key={post.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card variant="default">
                        <CardContent className="p-6">
                          
                          {/* Post Header */}
                          <div className="flex items-center gap-3 mb-4">
                            <div className="w-12 h-12 bg-gradient-to-br from-rich-gold to-warm-camel rounded-full flex items-center justify-center text-2xl">
                              {post.user.avatar}
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2">
                                <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                                  {post.user.name}
                                </h4>
                                {post.trending && (
                                  <Badge variant="primary" size="sm">
                                    🔥 Trending
                                  </Badge>
                                )}
                              </div>
                              <p className="text-warm-camel text-sm">
                                {post.user.level} • {post.user.followers.toLocaleString()} seguidores
                              </p>
                            </div>
                            <span className="text-warm-camel text-sm">{post.timestamp}</span>
                          </div>

                          {/* Post Content */}
                          <div className="mb-4">
                            {post.content.type === 'review' && (
                              <div className="mb-3">
                                <div className="flex items-center gap-2 mb-2">
                                  <span className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                                    {post.content.product}
                                  </span>
                                  <div className="flex">
                                    {[...Array(post.content.rating)].map((_, i) => (
                                      <span key={i} className="text-rich-gold">⭐</span>
                                    ))}
                                  </div>
                                </div>
                              </div>
                            )}
                            
                            <p className="text-forest-emerald dark:text-light-cloud-gray mb-3">
                              {post.content.text}
                            </p>

                            {post.content.images && (
                              <div className="flex gap-2 mb-3">
                                {post.content.images.map((img, i) => (
                                  <div key={i} className="w-20 h-20 bg-warm-camel/20 rounded-lg flex items-center justify-center text-2xl">
                                    {img}
                                  </div>
                                ))}
                              </div>
                            )}
                          </div>

                          {/* Post Actions */}
                          <div className="flex items-center gap-6 pt-3 border-t border-warm-camel/20">
                            <motion.button
                              onClick={() => handleLike(post.id)}
                              className="flex items-center gap-2 text-warm-camel hover:text-rich-gold transition-colors"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <span>❤️</span>
                              <span className="text-sm">{post.content.likes}</span>
                            </motion.button>

                            <button className="flex items-center gap-2 text-warm-camel hover:text-rich-gold transition-colors">
                              <span>💬</span>
                              <span className="text-sm">{post.content.comments}</span>
                            </button>

                            <motion.button
                              onClick={() => handleShare(post)}
                              className="flex items-center gap-2 text-warm-camel hover:text-rich-gold transition-colors"
                              whileHover={{ scale: 1.05 }}
                              whileTap={{ scale: 0.95 }}
                            >
                              <span>🔗</span>
                              <span className="text-sm">{post.content.shares}</span>
                            </motion.button>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </motion.div>
              )}

              {/* Trending Products */}
              {activeTab === 'trending' && (
                <motion.div
                  key="trending"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  <Card variant="default">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4 flex items-center gap-2">
                        <span>📈</span>
                        Productos en Tendencia
                      </h3>
                      
                      <div className="space-y-4">
                        {trendingProducts.map((product, index) => (
                          <motion.div
                            key={product.id}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex items-center gap-4 p-4 bg-warm-camel/10 rounded-lg"
                          >
                            <div className="w-8 h-8 bg-rich-gold text-forest-emerald rounded-full flex items-center justify-center font-bold text-sm">
                              #{index + 1}
                            </div>
                            
                            <div className="w-16 h-16 bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray rounded-lg flex items-center justify-center">
                              <span className="text-xs text-warm-camel font-medium text-center">
                                {product.name.slice(0, 8)}...
                              </span>
                            </div>
                            
                            <div className="flex-1">
                              <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                                {product.name}
                              </h4>
                              <p className="text-warm-camel text-sm">{product.brand}</p>
                              <p className="text-forest-emerald dark:text-light-cloud-gray font-bold">
                                ${product.price.toLocaleString()} MXN
                              </p>
                            </div>
                            
                            <div className="text-right">
                              <div className="flex items-center gap-2 mb-1">
                                <span className="text-rich-gold font-bold">{product.trendScore}</span>
                                <Badge variant="success" size="sm">{product.change}</Badge>
                              </div>
                              <p className="text-warm-camel text-xs">
                                {product.mentions} menciones
                              </p>
                              <div className="flex items-center gap-1 mt-1">
                                <span className={`text-xs ${
                                  product.sentiment === 'positive' ? 'text-green-500' : 
                                  product.sentiment === 'negative' ? 'text-red-500' : 'text-yellow-500'
                                }`}>
                                  {product.sentiment === 'positive' ? '😊' : 
                                   product.sentiment === 'negative' ? '😞' : '😐'}
                                </span>
                                <span className="text-xs text-warm-camel capitalize">
                                  {product.sentiment}
                                </span>
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* User Profile */}
              {activeTab === 'profile' && userStats && (
                <motion.div
                  key="profile"
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  className="space-y-6"
                >
                  
                  {/* User Level & Progress */}
                  <Card variant="default">
                    <CardContent className="p-6">
                      <div className="text-center mb-6">
                        <div className="w-20 h-20 bg-gradient-to-br from-rich-gold to-warm-camel rounded-full flex items-center justify-center text-3xl mx-auto mb-4">
                          👤
                        </div>
                        <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                          {userStats.level}
                        </h3>
                        <p className="text-warm-camel">
                          {userStats.points} / {userStats.nextLevel} puntos
                        </p>
                      </div>

                      {/* Progress Bar */}
                      <div className="w-full bg-warm-camel/20 rounded-full h-3 mb-6">
                        <motion.div
                          className="bg-gradient-to-r from-rich-gold to-warm-camel h-3 rounded-full"
                          initial={{ width: 0 }}
                          animate={{ width: `${(userStats.points / userStats.nextLevel) * 100}%` }}
                          transition={{ duration: 1, delay: 0.5 }}
                        />
                      </div>

                      {/* Stats */}
                      <div className="grid grid-cols-4 gap-4">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                            {userStats.stats.reviews}
                          </div>
                          <div className="text-sm text-warm-camel">Reseñas</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                            {userStats.stats.likes}
                          </div>
                          <div className="text-sm text-warm-camel">Likes</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                            {userStats.stats.followers}
                          </div>
                          <div className="text-sm text-warm-camel">Seguidores</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                            {userStats.stats.following}
                          </div>
                          <div className="text-sm text-warm-camel">Siguiendo</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Badges */}
                  <Card variant="default">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                        Mis Logros
                      </h3>
                      
                      <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                        {userStats.badges.map((badge, index) => (
                          <motion.div
                            key={badge.name}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: index * 0.1 }}
                            className={`p-4 rounded-lg text-center ${
                              badge.earned 
                                ? 'bg-rich-gold/20 border-2 border-rich-gold/50' 
                                : 'bg-warm-camel/10 border-2 border-warm-camel/20 opacity-50'
                            }`}
                          >
                            <div className="text-3xl mb-2">{badge.icon}</div>
                            <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm">
                              {badge.name}
                            </h4>
                            {badge.earned && (
                              <Badge variant="success" size="sm" className="mt-2">
                                Desbloqueado
                              </Badge>
                            )}
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
