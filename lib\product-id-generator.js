// Product ID Generator for TWL - Creates consistent product IDs from file paths
// This helps create SEO-friendly URLs and consistent product identification

/**
 * Generate a product ID from file path components
 * @param {string} category - Product category (SNEAKERS, SANDALS, etc.)
 * @param {string} brand - Brand name (NIKE, GUCCI, etc.)
 * @param {string} gender - Gender (MIXTE, WOMEN, MEN)
 * @param {string} modelFamily - Model family (AIR FORCE, etc.)
 * @param {string} sku - Product SKU
 * @param {string} collaboration - Collaboration name (optional)
 * @returns {string} Generated product ID
 */
export const generateProductId = (category, brand, gender, modelFamily = '', sku = '', collaboration = '') => {
  // Clean and normalize inputs
  const cleanCategory = category.replace(/^\d+\.\s*/, '').toLowerCase().replace(/\s+/g, '-')
  const cleanBrand = brand.replace(/^\d+\.\s*/, '').toLowerCase().replace(/\s+/g, '-')
  const cleanGender = gender.replace(/^\d+\.\s*/, '').toLowerCase()
  const cleanModelFamily = modelFamily.replace(/^\d+\.\s*/, '').toLowerCase().replace(/\s+/g, '-')
  const cleanSku = sku.toLowerCase().replace(/[^a-z0-9-]/g, '-')
  
  // Build ID components
  const components = [cleanCategory, cleanBrand]
  
  if (cleanGender && cleanGender !== 'mixte') {
    components.push(cleanGender)
  } else if (cleanGender === 'mixte') {
    components.push('mixte')
  }
  
  if (cleanModelFamily) {
    components.push(cleanModelFamily)
  }
  
  if (cleanSku) {
    components.push(cleanSku)
  }
  
  return components.filter(Boolean).join('-')
}

/**
 * Parse a product ID back into its components
 * @param {string} productId - Product ID to parse
 * @returns {object} Parsed components
 */
export const parseProductId = (productId) => {
  const parts = productId.split('-')
  
  return {
    category: parts[0],
    brand: parts[1],
    gender: parts[2] === 'mixte' || parts[2] === 'women' || parts[2] === 'men' ? parts[2] : null,
    modelFamily: parts[3],
    sku: parts.slice(4).join('-')
  }
}

/**
 * Generate product IDs for common TWL products
 */
export const generateCommonProductIds = () => {
  const products = [
    // Nike Sneakers
    {
      category: 'SNEAKERS',
      brand: 'NIKE Limited Edition',
      gender: 'MIXTE',
      modelFamily: 'AIR FORCE',
      sku: 'BD7700-222',
      collaboration: 'GUCCI'
    },
    {
      category: 'SNEAKERS',
      brand: 'NIKE Limited Edition',
      gender: 'MIXTE',
      modelFamily: 'AIR JORDAN',
      sku: 'AJ1234-001',
      collaboration: 'OFF WHITE'
    },
    // Gucci Sneakers
    {
      category: 'SNEAKERS',
      brand: 'GUCCI',
      gender: 'MIXTE',
      modelFamily: '',
      sku: 'JKD129-DDK',
      collaboration: ''
    },
    // Sandals
    {
      category: 'SANDALS',
      brand: 'GUCCI',
      gender: 'WOMEN',
      modelFamily: '',
      sku: 'GS2024-001',
      collaboration: ''
    },
    // Formal
    {
      category: 'FORMAL',
      brand: 'CHANEL',
      gender: 'WOMEN',
      modelFamily: '',
      sku: 'CH2024-001',
      collaboration: ''
    },
    // Casual
    {
      category: 'CASUAL',
      brand: 'UGG',
      gender: 'MIXTE',
      modelFamily: '',
      sku: 'UG2024-001',
      collaboration: ''
    },
    // Kids
    {
      category: 'KIDS',
      brand: 'GOLDEN GOOSE',
      gender: 'MIXTE',
      modelFamily: '',
      sku: 'GG2024-001',
      collaboration: ''
    }
  ]
  
  return products.map(product => ({
    ...product,
    id: generateProductId(
      product.category,
      product.brand,
      product.gender,
      product.modelFamily,
      product.sku,
      product.collaboration
    )
  }))
}

/**
 * Validate a product ID format
 * @param {string} productId - Product ID to validate
 * @returns {boolean} Whether the ID is valid
 */
export const validateProductId = (productId) => {
  if (!productId || typeof productId !== 'string') return false
  
  const parts = productId.split('-')
  if (parts.length < 2) return false
  
  const validCategories = ['sneakers', 'sandals', 'formal', 'casual', 'kids']
  const validGenders = ['mixte', 'women', 'men']
  
  // Check if first part is a valid category
  if (!validCategories.includes(parts[0])) return false
  
  // Check if gender is valid (if present)
  if (parts.length > 2 && validGenders.includes(parts[2])) {
    return true
  }
  
  return true // Basic validation passed
}

/**
 * Create SEO-friendly product URLs
 * @param {string} productId - Product ID
 * @returns {string} SEO-friendly URL
 */
export const createProductUrl = (productId) => {
  return `/product/${productId}`
}

/**
 * Extract brand and model information from product ID
 * @param {string} productId - Product ID
 * @returns {object} Brand and model info
 */
export const extractBrandModel = (productId) => {
  const parsed = parseProductId(productId)
  
  return {
    brand: parsed.brand?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    model: parsed.modelFamily?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    category: parsed.category?.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
    gender: parsed.gender?.replace(/\b\w/g, l => l.toUpperCase())
  }
}

// Export all functions
export default {
  generateProductId,
  parseProductId,
  generateCommonProductIds,
  validateProductId,
  createProductUrl,
  extractBrandModel
}
