'use client'

import { useState } from 'react'
import { ProductImage } from '@/components/ui/LazyImage'
import { useCart } from '@/contexts/CartContext'
import { useCartNotification } from '@/contexts/CartNotificationContext'
import { ButtonLoading } from '@/components/ui/LoadingStates'
import { useWishlist } from '@/contexts/WishlistContext'
import { useAuth } from '@/contexts/AuthContext'
import Badge from '@/components/ui/Badge'
import { cleanProductName } from '@/lib/real-products-loader'

export default function CleanProductCard({ product, index = 0 }) {
  const { addItem } = useCart()
  const { showCartSuccess } = useCartNotification()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const { isAuthenticated } = useAuth()
  const [isHovered, setIsHovered] = useState(false)

  const isWishlisted = isInWishlist(product.id)
  const hasDiscount = product.originalPrice && product.originalPrice > product.price
  const discountPercentage = hasDiscount 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  const handleAddToCart = async () => {
    setIsAddingToCart(true)

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 600))

      addItem(product.id, 'M', 1) // Default size for now
      showCartSuccess(product, 'M', 1) // Show beautiful success modal
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setIsAddingToCart(false)
    }
  }

  const handleWishlistToggle = () => {
    if (isWishlisted) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product.id)
    }
  }

  // Get category badge info
  const getCategoryBadge = () => {
    const category = product.category?.toLowerCase() || 'general'
    const badges = {
      'formal': { text: 'FORMAL', color: 'bg-purple-500' },
      'sandal': { text: 'SANDAL', color: 'bg-amber-500' },
      'casual': { text: 'CASUAL', color: 'bg-green-500' },
      'tennis': { text: 'TENNIS', color: 'bg-blue-500' },
      'sneakers': { text: 'TENNIS', color: 'bg-blue-500' },
      'general': { text: 'NUEVO', color: 'bg-gray-800' }
    }
    return badges[category] || badges.general
  }

  const categoryBadge = getCategoryBadge()

  return (
    <div
      className="group cursor-pointer h-full flex flex-col bg-pure-white dark:bg-neutral-800 rounded-2xl overflow-hidden shadow-sm hover:shadow-xl transition-all duration-500 transform hover:scale-[1.02] hover:-translate-y-1"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      {/* Product Image - 70% of card height */}
      <div className="relative aspect-[4/3] bg-light-gray dark:bg-neutral-700 overflow-hidden">
        {/* First Image - Default */}
        <ProductImage
          src={product.image || product.images?.[0] || "/placeholder.jpg"}
          alt={product.name}
          className={`absolute inset-0 object-cover transition-all duration-500 ease-out ${
            isHovered && (product.images?.[1] || product.image2)
              ? 'opacity-0 scale-110'
              : 'opacity-100 group-hover:scale-110'
          }`}
          priority={index < 4} // Load first 4 images immediately
        />

        {/* Second Image - Hover Effect */}
        {(product.images?.[1] || product.image2) && (
          <ProductImage
            src={product.images?.[1] || product.image2}
            alt={`${product.name} - Vista alternativa`}
            className={`absolute inset-0 object-cover transition-all duration-500 ease-out z-10 ${
              isHovered
                ? 'opacity-100 scale-110'
                : 'opacity-0 scale-100'
            }`}
          />
        )}

        {/* NUEVO Badge - Top Left */}
        {product.isNew && (
          <div className="absolute top-3 left-3">
            <div className="bg-gray-800 text-white text-xs px-3 py-1 rounded-md font-poppins font-semibold">
              NUEVO
            </div>
          </div>
        )}

        {/* Wishlist Heart - Top Right */}
        <button
          onClick={handleWishlistToggle}
          className="absolute top-3 right-3 w-8 h-8 flex items-center justify-center transition-all duration-300 transform hover:scale-110"
        >
          <svg
            className={`w-6 h-6 transition-colors duration-300 ${
              isWishlisted
                ? 'text-primary fill-current'
                : 'text-gray-300 hover:text-primary'
            }`}
            viewBox="0 0 24 24"
            fill={isWishlisted ? "currentColor" : "none"}
            stroke="currentColor"
            strokeWidth={isWishlisted ? 0 : 1.5}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Add to Cart Button - Bottom Right Circle */}
        <ButtonLoading
          onClick={handleAddToCart}
          isLoading={isAddingToCart}
          className="absolute bottom-3 right-3 w-12 h-12 bg-primary hover:bg-dark-gray hover:text-pure-white text-pure-black rounded-full flex items-center justify-center transition-all duration-300 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 group-hover:scale-110 shadow-lg hover:shadow-xl"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9" />
          </svg>
        </ButtonLoading>
      </div>

      {/* Product Info - 30% of card height */}
      <div className="p-4 flex-1 flex flex-col justify-between">
        {/* Product Name and Category Badge */}
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-poppins font-bold text-pure-black dark:text-pure-white text-base leading-tight flex-1 pr-2">
            {cleanProductName(product.name)}
          </h3>
          <div className={`text-xs px-2 py-1 rounded text-white font-poppins font-semibold ${categoryBadge.color} flex-shrink-0`}>
            {categoryBadge.text}
          </div>
        </div>

        {/* Brand Name */}
        <p className="font-poppins text-sm text-text-gray dark:text-neutral-400 uppercase tracking-wide mb-1">
          {cleanProductName(product.brand)}
        </p>

        {/* Gender */}
        <p className="font-poppins text-xs text-text-gray dark:text-neutral-400 uppercase mb-3">
          HOMBRES
        </p>

        {/* Price */}
        <div className="flex items-center gap-2">
          {hasDiscount && (
            <span className="font-poppins text-sm text-text-gray dark:text-neutral-400 line-through">
              ${product.originalPrice}
            </span>
          )}
          <span className="font-poppins font-bold text-xl text-pure-black dark:text-pure-white">
            ${product.price}
          </span>
        </div>
      </div>
    </div>
  )
}
