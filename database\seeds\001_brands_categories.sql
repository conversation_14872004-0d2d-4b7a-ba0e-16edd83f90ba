-- TWL E-commerce Database Seeds - Brands and Categories
-- Version: 001
-- Description: Populate brands and categories with real luxury footwear data

-- Insert luxury brands
INSERT INTO brands (id, name, slug, description, logo_url, website_url, founded_year, country, brand_tier, featured, sort_order) VALUES
-- Tier 1: Ultra Luxury
(uuid_generate_v4(), 'Gucci', 'gucci', 'Casa italiana de lujo fundada en 1921, conocida por su artesanía excepcional y diseño innovador.', '/brands/gucci-logo.svg', 'https://www.gucci.com', 1921, 'Italia', 'luxury', true, 1),
(uuid_generate_v4(), '<PERSON>', 'louis-vuitton', 'Maison francesa de lujo especializada en marroquinería y calzado de alta gama desde 1854.', '/brands/lv-logo.svg', 'https://www.louisvuitton.com', 1854, 'Francia', 'luxury', true, 2),
(uuid_generate_v4(), 'Dior', 'dior', 'Casa de alta costura francesa que define la elegancia y el lujo contemporáneo.', '/brands/dior-logo.svg', 'https://www.dior.com', 1946, 'Francia', 'luxury', true, 3),
(uuid_generate_v4(), 'Balenciaga', 'balenciaga', 'Marca española de lujo conocida por su diseño vanguardista y calzado innovador.', '/brands/balenciaga-logo.svg', 'https://www.balenciaga.com', 1919, 'España', 'luxury', true, 4),
(uuid_generate_v4(), 'Bottega Veneta', 'bottega-veneta', 'Marca italiana famosa por su artesanía en cuero y diseño minimalista de lujo.', '/brands/bottega-logo.svg', 'https://www.bottegaveneta.com', 1966, 'Italia', 'luxury', true, 5),

-- Tier 2: Premium Luxury
(uuid_generate_v4(), 'Prada', 'prada', 'Casa italiana de moda que combina tradición artesanal con innovación moderna.', '/brands/prada-logo.svg', 'https://www.prada.com', 1913, 'Italia', 'luxury', true, 6),
(uuid_generate_v4(), 'Miu Miu', 'miu-miu', 'Línea contemporánea de Prada que expresa feminidad y modernidad.', '/brands/miumiu-logo.svg', 'https://www.miumiu.com', 1993, 'Italia', 'luxury', false, 7),
(uuid_generate_v4(), 'Valentino', 'valentino', 'Maison italiana de alta costura conocida por su elegancia romana.', '/brands/valentino-logo.svg', 'https://www.valentino.com', 1960, 'Italia', 'luxury', false, 8),
(uuid_generate_v4(), 'Golden Goose', 'golden-goose', 'Marca italiana especializada en sneakers de lujo con acabado vintage.', '/brands/goldengoose-logo.svg', 'https://www.goldengoose.com', 2000, 'Italia', 'luxury', true, 9),
(uuid_generate_v4(), 'Maison Margiela', 'maison-margiela', 'Casa de moda belga conocida por su enfoque conceptual y deconstructivista.', '/brands/margiela-logo.svg', 'https://www.maisonmargiela.com', 1988, 'Bélgica', 'luxury', false, 10),

-- Tier 3: Luxury Streetwear
(uuid_generate_v4(), 'Off-White', 'off-white', 'Marca de streetwear de lujo que fusiona moda urbana con alta costura.', '/brands/offwhite-logo.svg', 'https://www.off---white.com', 2012, 'Italia', 'streetwear', true, 11),
(uuid_generate_v4(), 'Fear of God', 'fear-of-god', 'Marca americana de streetwear de lujo con estética minimalista.', '/brands/fog-logo.svg', 'https://www.fearofgod.com', 2013, 'Estados Unidos', 'streetwear', false, 12),
(uuid_generate_v4(), 'Stone Island', 'stone-island', 'Marca italiana conocida por su investigación en textiles y diseño funcional.', '/brands/stoneisland-logo.svg', 'https://www.stoneisland.com', 1982, 'Italia', 'streetwear', false, 13),

-- Tier 4: Premium Sports/Lifestyle
(uuid_generate_v4(), 'Nike', 'nike', 'Marca líder mundial en calzado deportivo e innovación atlética.', '/brands/nike-logo.svg', 'https://www.nike.com', 1964, 'Estados Unidos', 'premium', true, 14),
(uuid_generate_v4(), 'Jordan', 'jordan', 'Línea premium de Nike inspirada en la leyenda del básquetbol Michael Jordan.', '/brands/jordan-logo.svg', 'https://www.nike.com/jordan', 1984, 'Estados Unidos', 'premium', true, 15),
(uuid_generate_v4(), 'Adidas', 'adidas', 'Marca alemana icónica en calzado deportivo y lifestyle.', '/brands/adidas-logo.svg', 'https://www.adidas.com', 1949, 'Alemania', 'premium', true, 16),
(uuid_generate_v4(), 'New Balance', 'new-balance', 'Marca americana especializada en calzado deportivo de alta calidad.', '/brands/newbalance-logo.svg', 'https://www.newbalance.com', 1906, 'Estados Unidos', 'premium', false, 17),
(uuid_generate_v4(), 'Converse', 'converse', 'Marca americana icónica en calzado casual y streetwear.', '/brands/converse-logo.svg', 'https://www.converse.com', 1908, 'Estados Unidos', 'mainstream', false, 18),

-- Tier 5: Streetwear Culture
(uuid_generate_v4(), 'Supreme', 'supreme', 'Marca de streetwear que define la cultura skate y urbana contemporánea.', '/brands/supreme-logo.svg', 'https://www.supremenewyork.com', 1994, 'Estados Unidos', 'streetwear', false, 19),
(uuid_generate_v4(), 'A Bathing Ape', 'bape', 'Marca japonesa pionera en streetwear y cultura urbana asiática.', '/brands/bape-logo.svg', 'https://www.bape.com', 1993, 'Japón', 'streetwear', false, 20);

-- Insert main categories
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, active) VALUES
-- Main Categories
(uuid_generate_v4(), 'Sneakers', 'sneakers', 'Calzado deportivo y lifestyle de lujo', NULL, 1, true),
(uuid_generate_v4(), 'Sandalias', 'sandals', 'Sandalias de lujo y slides premium', NULL, 2, true),
(uuid_generate_v4(), 'Zapatos Formales', 'formal-shoes', 'Calzado formal y de vestir de lujo', NULL, 3, true),
(uuid_generate_v4(), 'Botas', 'boots', 'Botas de lujo y calzado de temporada', NULL, 4, true),
(uuid_generate_v4(), 'Zapatos Casuales', 'casual-shoes', 'Calzado casual y lifestyle premium', NULL, 5, true);

-- Get category IDs for subcategories
WITH category_ids AS (
    SELECT id, slug FROM categories WHERE parent_id IS NULL
)

-- Insert subcategories for Sneakers
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, active)
SELECT 
    uuid_generate_v4(),
    subcategory.name,
    subcategory.slug,
    subcategory.description,
    category_ids.id,
    subcategory.sort_order,
    true
FROM category_ids
CROSS JOIN (VALUES
    ('High-Top Sneakers', 'high-top-sneakers', 'Sneakers de caña alta y basketball', 1),
    ('Low-Top Sneakers', 'low-top-sneakers', 'Sneakers de caña baja y lifestyle', 2),
    ('Running Sneakers', 'running-sneakers', 'Calzado deportivo para correr', 3),
    ('Chunky Sneakers', 'chunky-sneakers', 'Sneakers voluminosos y dad shoes', 4),
    ('Slip-On Sneakers', 'slip-on-sneakers', 'Sneakers sin cordones', 5)
) AS subcategory(name, slug, description, sort_order)
WHERE category_ids.slug = 'sneakers';

-- Insert subcategories for Sandals
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, active)
SELECT 
    uuid_generate_v4(),
    subcategory.name,
    subcategory.slug,
    subcategory.description,
    category_ids.id,
    subcategory.sort_order,
    true
FROM category_ids
CROSS JOIN (VALUES
    ('Slides', 'slides', 'Sandalias deslizantes de lujo', 1),
    ('Flip Flops', 'flip-flops', 'Chanclas premium y de playa', 2),
    ('Sandalias con Tiras', 'strappy-sandals', 'Sandalias con múltiples tiras', 3),
    ('Sandalias Deportivas', 'sport-sandals', 'Sandalias para actividades outdoor', 4)
) AS subcategory(name, slug, description, sort_order)
WHERE category_ids.slug = 'sandals';

-- Insert subcategories for Formal Shoes
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, active)
SELECT 
    uuid_generate_v4(),
    subcategory.name,
    subcategory.slug,
    subcategory.description,
    category_ids.id,
    subcategory.sort_order,
    true
FROM category_ids
CROSS JOIN (VALUES
    ('Oxford', 'oxford', 'Zapatos Oxford clásicos de lujo', 1),
    ('Loafers', 'loafers', 'Mocasines y zapatos sin cordones', 2),
    ('Derby', 'derby', 'Zapatos Derby y blucher de vestir', 3),
    ('Monk Strap', 'monk-strap', 'Zapatos con hebilla de lujo', 4)
) AS subcategory(name, slug, description, sort_order)
WHERE category_ids.slug = 'formal-shoes';

-- Insert subcategories for Boots
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, active)
SELECT 
    uuid_generate_v4(),
    subcategory.name,
    subcategory.slug,
    subcategory.description,
    category_ids.id,
    subcategory.sort_order,
    true
FROM category_ids
CROSS JOIN (VALUES
    ('Chelsea Boots', 'chelsea-boots', 'Botas Chelsea de lujo', 1),
    ('Combat Boots', 'combat-boots', 'Botas militares y urbanas', 2),
    ('Ankle Boots', 'ankle-boots', 'Botines de lujo', 3),
    ('Knee-High Boots', 'knee-high-boots', 'Botas altas de lujo', 4)
) AS subcategory(name, slug, description, sort_order)
WHERE category_ids.slug = 'boots';

-- Insert subcategories for Casual Shoes
INSERT INTO categories (id, name, slug, description, parent_id, sort_order, active)
SELECT 
    uuid_generate_v4(),
    subcategory.name,
    subcategory.slug,
    subcategory.description,
    category_ids.id,
    subcategory.sort_order,
    true
FROM category_ids
CROSS JOIN (VALUES
    ('Canvas Shoes', 'canvas-shoes', 'Zapatos de lona y textil', 1),
    ('Boat Shoes', 'boat-shoes', 'Zapatos náuticos de lujo', 2),
    ('Espadrilles', 'espadrilles', 'Alpargatas de lujo', 3),
    ('Mules', 'mules', 'Zapatos sin talón de lujo', 4)
) AS subcategory(name, slug, description, sort_order)
WHERE category_ids.slug = 'casual-shoes';

-- Insert initial collections
INSERT INTO collections (id, name, slug, description, image_url, featured, sort_order, active) VALUES
(uuid_generate_v4(), 'Nuevos Lanzamientos', 'new-arrivals', 'Los últimos lanzamientos en calzado de lujo', '/collections/new-arrivals.jpg', true, 1, true),
(uuid_generate_v4(), 'Ediciones Limitadas', 'limited-editions', 'Calzado exclusivo y de edición limitada', '/collections/limited-editions.jpg', true, 2, true),
(uuid_generate_v4(), 'Colaboraciones', 'collaborations', 'Colaboraciones exclusivas entre marcas', '/collections/collaborations.jpg', true, 3, true),
(uuid_generate_v4(), 'Bestsellers', 'bestsellers', 'Los productos más vendidos', '/collections/bestsellers.jpg', true, 4, true),
(uuid_generate_v4(), 'Lujo Italiano', 'italian-luxury', 'Lo mejor del calzado de lujo italiano', '/collections/italian-luxury.jpg', false, 5, true),
(uuid_generate_v4(), 'Streetwear Premium', 'premium-streetwear', 'Streetwear de lujo y cultura urbana', '/collections/streetwear.jpg', false, 6, true),
(uuid_generate_v4(), 'Temporada Primavera', 'spring-collection', 'Colección de primavera-verano', '/collections/spring.jpg', false, 7, true),
(uuid_generate_v4(), 'Clásicos Atemporales', 'timeless-classics', 'Diseños clásicos que nunca pasan de moda', '/collections/classics.jpg', false, 8, true);

-- Insert CYTTE supplier (hidden from frontend)
INSERT INTO suppliers (id, name, code, contact_email, contact_phone, address, commission_rate, active) VALUES
(uuid_generate_v4(), 'CYTTE International', 'CYTTE', '<EMAIL>', '+86-138-0013-8000', 
 '{"street": "Guangzhou Fashion District", "city": "Guangzhou", "country": "China", "postal_code": "510000"}', 
 15.00, true);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_brands_tier_featured ON brands(brand_tier, featured);
CREATE INDEX IF NOT EXISTS idx_categories_parent_sort ON categories(parent_id, sort_order);
CREATE INDEX IF NOT EXISTS idx_collections_featured_sort ON collections(featured, sort_order);
