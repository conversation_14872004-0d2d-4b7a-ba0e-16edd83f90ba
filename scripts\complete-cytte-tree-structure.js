#!/usr/bin/env node

/**
 * COMPLETE CYTTE SUPPLIER TREE STRUCTURE
 * Based on: C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE
 * 
 * STRUCTURE RULES:
 * - Numbers with dots (1., 2., 3.) = Sub-categories
 * - MIXTE = Unisex products (both men and women)
 * - WOMEN = Women-only products
 * - MEN = Men-only products
 * - Each product has a unique SKU code
 */

const COMPLETE_CYTTE_STRUCTURE = {
  "1. SNEAKERS": {
    "1. NIKE Limited Edition": {
      "1. AIR FORCE": {
        "1. MIXTE": ["DR0424-100 -- J Force 1 Low"],
        "2. WOMEN": []
      },
      "2. AIR JORDAN": {
        "1. MIXTE": {
          "1. JORDAN 1 LOW": [
            "1. TRAVIS SCOTT",
            "JKD117-FJE -- <PERSON> x <PERSON>",
            "LX1988 -- LV collab",
            "SJ9950-056 -- 2025 year of the Snake",
            "XS2024 -- The North Face collab",
            "XZ6188 -- Stussy collab",
            "ZDD474-FJS -- Goat Lows - OFF WHITE"
          ],
          "2. JORDAN 1 HIGH": [
            "33172898443003 -- Black & Black",
            "JHD101-QJZ -- Bodega",
            "JHD101-QJZ -- Comics Tay",
            "JHD101-QJZ -- <PERSON> LA",
            "JHD170-JJK -- Space",
            "JJD553-KDE -- OFF WHITE",
            "JKD170-SED -- Jeans",
            "JKD170-SED -- Jordan 1 x XX",
            "JKD435-AZH -- Wear Me",
            "JKD474-EJR -- Air Dior",
            "JKD556-DZZ",
            "JKD556-EJS -- Vernis",
            "JKD556-HDG -- Goldie",
            "JKD577-ADS -- Union LA",
            "JSD170-EZR -- CLOT",
            "JSD170-RZZ -- Vodoo",
            "JSD254-JDZ -- Goretex",
            "JSD538-SZS -- Thomas Campbell - What the Dunk",
            "JZD556-FJZ -- Moonshot",
            "ZDD170-SDJ",
            "ZZD535-EJF -- Golden boy"
          ]
        },
        "FQ7939 003 Nigel Sylvester": [],
        "JKD262-FZD -- Virgil Abloh x Off White Nike SB": [],
        "JSD068-QJE -- Nina Chanel x Jordan 2": []
      },
      "3. CORTEZ": {
        "1. MIXTE": [],
        "2. WOMEN": []
      },
      "4. DUNK LOW": {
        "1. MIXTE": []
      },
      "5. AIR MAX 1": {
        "1. MIXTE": []
      },
      "6. AIR MAX 97": {
        "1. MIXTE": []
      },
      "7. FOG": [
        "AT8087-001 -- FOG",
        "AT9915-002 -- FOG"
      ],
      "8. OFF WHITE": [
        "AA7293-200 -- Air Max 90 - Desert Ore",
        "DQ1615-200 -- Terra Forma",
        "DSD117-JDR -- Virgil Abloh",
        "JHD103-ZJZ -- Off White Dunk Pine",
        "JZD170-FJS -- Off White - Blazer Mid",
        "ZJD101-EEJ -- Nike React Hyperdunk",
        "ZZD372-FJJ -- OFF WHITE-Blazer low"
      ],
      "9. JACQUEMUS": [
        "DR0424-100 -- J Force 1 Low",
        "ZFD562-DJK -- Humara"
      ],
      "10. BLAZER": [
        "JGD534-AZJ -- Nike x Sacai Blazer Low",
        "JGD534-QDH -- Nike x Sacai Blazer Mid"
      ],
      "11. VAPOR WAFFLE": [
        "DD1875-700 -- Sacai - Vapor Waffle",
        "EFD557-PDK -- Sacai - Regasus Vaporrly",
        "JZD538-QDK -- Jean Paul Gaultier x Sacai - Vapor Waffle"
      ],
      "12. LEBRON JAMES": {
        "1. MEN": []
      },
      "FQ6891-001 -- Bode x Nike Astro Grabber": [],
      "FQ6892-100 -- Bode x Nike Astro Grabber": []
    },
    "2. ADIDAS Limited Edition": {
      "1. GUCCI": [],
      "2. BALENCIAGA": [],
      "3. Fear Of God": [],
      "4. OTHERS": []
    },
    "3. HERMES": {
      "1. MIXTE": [],
      "2. WOMEN": []
    },
    "4. GUCCI": {
      "1. MIXTE": [
        "13412565127048",
        "13413534043065",
        "13432545668006 -- Screener",
        "13432825588046",
        "13433534043065",
        "13438975870007 -- Chunky B Screener",
        "13443434043065",
        "13458855390069 -- Rython trainer",
        "13462895737048 -- Rython Vintage Trainer",
        "JFD233-ADS -- ACE",
        "JFD538-JJJ -- ACE",
        "JFD538-JJJ ACE-2",
        "JKD129-DDK -- Tennis 1977",
        "JKD129-EJZ",
        "JKD170-DZF -- Ace Low",
        "JZD129-EJJ -- Tennis 1977",
        "ZDD145-ZZJ -- Chunky B"
      ],
      "2. WOMEN": [
        "13403164833067 -- Rython Vintage",
        "13418565120X01 -- Rython Trainer",
        "13432025588046 -- Screener hightop",
        "13432065128065",
        "JGD170-AZG -- Rython"
      ],
      "3. MEN": [
        "13444066122007",
        "JKD233-EDS -- Gypsophila Diamond",
        "ZGD213-EZF"
      ]
    },
    "5. DIOR": {
      "1. MIXTE": [],
      "2. WOMEN": [],
      "3. MEN": []
    },
    "6. LV": {
      "1. WOMEN": [],
      "2. MEN": [],
      "3. MIXTE": []
    },
    "7. BALENCIAGA": {
      "1. MIXTE": [],
      "2. WOMEN": [],
      "3. MEN": []
    },
    "8. CHANEL": {
      "1. WOMEN": []
    },
    "9. LOUBOUTIN": {
      "1. MEN": []
    },
    "10. OFF WHITE": [
      "13412745668006 -- Out of Office-Virgil Abloh",
      "JZD212-EZD -- Virgil Abloh"
    ],
    "11. GIVENCHY": {
      "1. MEN": [],
      "2. MIXTE": []
    },
    "12. Maison MARGIELA": [
      "13432565128046 -- Yasuhiro Mihara",
      "13462075877007 -- 22 Classic",
      "ZZD029-SJD -- MM6",
      "ZZD029-SJJ -- MM6"
    ],
    "13. VALENTINO": {
      "1. WOMEN": [],
      "2. MIXTE": [],
      "3. MEN": []
    },
    "14. PRADA": {
      "1. MIXTE": [],
      "2.WOMEN": [],
      "3. MEN": []
    },
    "15. MIU MIU": {
      "1. MIXTE": [],
      "2. WOMEN": []
    },
    "16. BOTTEGA VENETA": {
      "1. MIXTE": []
    },
    "17. BURBERRY": {
      "1. MEN": []
    },
    "18. GOLDEN GOOSE": {
      "1. MIXTE": [],
      "2. WOMEN": []
    },
    "19. GAMA NORMAL": {
      "1. LULU LEMON": []
    },
    "Common Project": [
      "13402148663024"
    ]
  },
  "2. SANDALS": {
    "1. NIKE Collabs": {
      "1. MIXTE": []
    },
    "2. GUCCI": {
      "1. MIXTE": [],
      "2. WOMEN": []
    },
    "3. DIOR": [
      "13462975577009 -- Collab Birkenstock",
      "33514773575023 -- Revolution Slides",
      "ZDD538-GJD -- D-Wander"
    ],
    "4. LV": {
      "1. MIXTE": [],
      "2. WOMEN": []
    },
    "5. BALENCIAGA": {
      "1. MIXTE": [],
      "2. WOMEN": []
    },
    "6. CHANEL": [
      "13414773575044",
      "13483894733044",
      "26162968123025",
      "DSD538-KJH",
      "JHD029-FED",
      "JJD029-SZD --",
      "JKD029-HZK",
      "JSD029-GJE"
    ],
    "7. MAISON MARGIELA": [
      "JFD538-GJR -- Tabi"
    ],
    "8. GIVENCHY": [
      "JFD445-EJD -- Mashmallow slider wedge"
    ],
    "9. UGG": {
      "1. WOMEN": []
    },
    "10. MIU MIU": {
      "1. WOMEN": [],
      "2. MIXTE": []
    },
    "11. PRADA": [
      "32777795734042 -- Prada Slides"
    ],
    "12. HERMES": [
      "13432498733023 -- Chypre"
    ],
    "13. CROCS": [
      "19937925584023 -- Bubble Crush Clog",
      "DSD474-KZH -- Bae Clog",
      "JED361-RZK -- Cars x Crocs"
    ],
    "14. BOTTEGA VENETA": {
      "1. MEN": []
    },
    "15. BIRKENSTOCK": [
      "33484963835063 -- Boston Suede",
      "JFD262-FZG"
    ]
  },
  "3. FORMAL": {
    "1. CHANEL": [],
    "2. PRADA": [],
    "3. GUCCI": []
  },
  "4. CASUAL": {
    "1. UGG": [],
    "2. LV": [],
    "3. MIU MIU": [],
    "4. PRADA": [],
    "5. BOTTEGA VENETA": [],
    "6. GUCCI": [],
    "7. Adidas": []
  },
  "5. KIDS": {
    "1. UGG": [],
    "2. GOLDEN GOOSE": []
  }
}

// Calculate total products
function calculateTotalProducts() {
  let total = 0
  
  function countProducts(obj) {
    for (const key in obj) {
      if (Array.isArray(obj[key])) {
        total += obj[key].length
      } else if (typeof obj[key] === 'object') {
        countProducts(obj[key])
      }
    }
  }
  
  countProducts(COMPLETE_CYTTE_STRUCTURE)
  return total
}

console.log('🏗️ COMPLETE CYTTE TREE STRUCTURE')
console.log('=================================\n')
console.log(JSON.stringify(COMPLETE_CYTTE_STRUCTURE, null, 2))
console.log(`\n📊 TOTAL PRODUCTS: ${calculateTotalProducts()}`)

module.exports = { COMPLETE_CYTTE_STRUCTURE, calculateTotalProducts }
