'use client'

import { useState } from 'react'
import Link from 'next/link'
import { motion, useSpring } from 'framer-motion'
import { useCart } from '@/contexts/CartContext'
import { useAuth } from '@/contexts/AuthContext'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import WishlistButton from '@/components/ui/WishlistButton'
import { formatPrice } from '@/lib/utils'

export default function AnimatedProductCard({ product, index = 0, onAuthRequired }) {
  const { addItem } = useCart()
  const { isAuthenticated } = useAuth()
  const [isHovered, setIsHovered] = useState(false)
  const [isPressed, setIsPressed] = useState(false)
  const [selectedSize, setSelectedSize] = useState('')
  const [showSizeSelector, setShowSizeSelector] = useState(false)
  
  // Spring animations for smooth interactions
  const springConfig = { stiffness: 300, damping: 30 }
  const scale = useSpring(1, springConfig)
  const rotateX = useSpring(0, springConfig)
  const rotateY = useSpring(0, springConfig)

  const handleMouseMove = (e) => {
    if (!isHovered) return
    
    const rect = e.currentTarget.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2
    
    const mouseX = (e.clientX - centerX) / rect.width
    const mouseY = (e.clientY - centerY) / rect.height
    
    rotateX.set(mouseY * -10)
    rotateY.set(mouseX * 10)
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
    scale.set(1.03)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    scale.set(1)
    rotateX.set(0)
    rotateY.set(0)
  }

  const handleAddToCart = () => {
    if (!selectedSize) {
      setShowSizeSelector(true)
      return
    }

    addItem(product.id, selectedSize, 1)
    setShowSizeSelector(false)
    setSelectedSize('')

    // Show success feedback (in a real app, you'd use a toast notification)
    setIsPressed(true)
    setTimeout(() => setIsPressed(false), 200)
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.6, 
        delay: index * 0.1,
        type: "spring",
        stiffness: 100
      }}
      style={{
        scale,
        rotateX,
        rotateY,
        transformStyle: "preserve-3d"
      }}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={() => setIsPressed(true)}
      onMouseUp={() => setIsPressed(false)}
      className="cursor-pointer"
    >
      <Card 
        variant="default" 
        className={`group relative overflow-hidden transition-all duration-500 ${
          isHovered ? 'shadow-2xl' : 'shadow-lg'
        }`}
      >
        <CardContent className="p-0">
          {/* Product Image Container */}
          <div className="relative aspect-square overflow-hidden">
            {/* Placeholder Image with Gradient */}
            <motion.div 
              className="w-full h-full bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray flex items-center justify-center relative"
              animate={{
                background: isHovered 
                  ? "linear-gradient(135deg, #E6E9ED, #F5F7FA, #C1A888)"
                  : "linear-gradient(135deg, #E6E9ED, #F5F7FA)"
              }}
              transition={{ duration: 0.5 }}
            >
              <span className="text-warm-camel text-sm font-medium">
                {product?.name || 'Product Image'}
              </span>
              
              {/* Animated Overlay */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-t from-rich-gold/20 via-transparent to-transparent"
                initial={{ opacity: 0 }}
                animate={{ opacity: isHovered ? 1 : 0 }}
                transition={{ duration: 0.3 }}
              />
              
              {/* Shimmer Effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-rich-gold/30 to-transparent"
                initial={{ x: "-100%" }}
                animate={{ x: isHovered ? "100%" : "-100%" }}
                transition={{ duration: 0.8, ease: "easeInOut" }}
              />
            </motion.div>
            
            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2">
              {product?.isLimited && (
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.3, type: "spring", stiffness: 200 }}
                >
                  <Badge variant="limited" size="sm" pulse>
                    Limitado
                  </Badge>
                </motion.div>
              )}
              {product?.isVip && (
                <motion.div
                  initial={{ scale: 0, rotate: 180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ delay: 0.4, type: "spring", stiffness: 200 }}
                >
                  <Badge variant="vip" size="sm">
                    VIP
                  </Badge>
                </motion.div>
              )}
            </div>

            {/* Quick Actions */}
            <motion.div 
              className="absolute top-3 right-3 flex flex-col gap-2"
              initial={{ opacity: 0, x: 20 }}
              animate={{ 
                opacity: isHovered ? 1 : 0,
                x: isHovered ? 0 : 20
              }}
              transition={{ duration: 0.3 }}
            >
              <motion.div
                whileHover={{ scale: 1.1, rotate: 5 }}
                whileTap={{ scale: 0.9 }}
              >
                <WishlistButton
                  productId={product?.id}
                  size="sm"
                  variant="secondary"
                  className="h-8 w-8"
                  onAuthRequired={onAuthRequired}
                />
              </motion.div>
            </motion.div>
          </div>

          {/* Product Info */}
          <motion.div 
            className="p-4 space-y-3"
            style={{ transform: "translateZ(20px)" }}
          >
            {/* Brand & Name */}
            <Link href={`/product/${product?.id}`}>
              <div className="cursor-pointer">
                <motion.p
                  className="text-sm text-warm-camel font-medium"
                  animate={{ opacity: isHovered ? 1 : 0.8 }}
                >
                  {product?.brand || 'Premium Brand'}
                </motion.p>
                <motion.h3
                  className="font-semibold text-forest-emerald dark:text-light-cloud-gray line-clamp-2 hover:text-rich-gold transition-colors"
                  animate={{
                    color: isHovered ? "#FFD700" : "#2E4B3A"
                  }}
                  transition={{ duration: 0.3 }}
                >
                  {product?.name || 'Luxury Sneaker Collection'}
                </motion.h3>
              </div>
            </Link>

            {/* Price */}
            <motion.div 
              className="flex items-center gap-2"
              animate={{ scale: isHovered ? 1.05 : 1 }}
              transition={{ duration: 0.2 }}
            >
              <span className="text-lg font-bold text-rich-gold">
                {formatPrice(product?.price || 2500, 'MXN', 'es-MX')}
              </span>
              {product?.originalPrice && (
                <span className="text-sm text-warm-camel line-through">
                  {formatPrice(product.originalPrice, 'MXN', 'es-MX')}
                </span>
              )}
            </motion.div>

            {/* Size Selector */}
            {showSizeSelector && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="space-y-2"
              >
                <p className="text-xs text-warm-camel">Selecciona tu talla:</p>
                <div className="grid grid-cols-4 gap-1">
                  {product?.variants?.[0]?.sizes?.slice(0, 8).map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={`text-xs py-1 px-2 rounded border transition-colors ${
                        selectedSize === size
                          ? 'bg-rich-gold text-forest-emerald border-rich-gold'
                          : 'border-warm-camel text-warm-camel hover:border-rich-gold'
                      }`}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </motion.div>
            )}

            {/* Add to Cart Button */}
            <motion.div
              whileHover={{ scale: 1.02 }}
              whileTap={{ scale: 0.98 }}
            >
              <Button
                variant="primary"
                size="sm"
                className="w-full relative overflow-hidden"
                onClick={handleAddToCart}
              >
                <motion.span
                  animate={{ x: isPressed ? 2 : 0 }}
                  transition={{ duration: 0.1 }}
                >
                  {showSizeSelector ? 'Confirmar' : 'Agregar al Carrito'}
                </motion.span>

                {/* Button Ripple Effect */}
                <motion.div
                  className="absolute inset-0 bg-warm-camel/30 rounded-lg"
                  initial={{ scale: 0, opacity: 0 }}
                  animate={{
                    scale: isPressed ? 1 : 0,
                    opacity: isPressed ? 1 : 0
                  }}
                  transition={{ duration: 0.2 }}
                />
              </Button>
            </motion.div>

            {/* Cancel Size Selection */}
            {showSizeSelector && (
              <Button
                variant="ghost"
                size="sm"
                className="w-full"
                onClick={() => {
                  setShowSizeSelector(false)
                  setSelectedSize('')
                }}
              >
                Cancelar
              </Button>
            )}
          </motion.div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
