/**
 * CYTTE SUPPLIER 6-LEVEL HIERARCHY DATABASE SCHEMA
 * Based on: C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE
 * 
 * STRUCTURE:
 * 1. STYLE (sneakers, sandals, casual, formal, kids)
 * 2. BRAND (Nike, <PERSON>ucci, <PERSON><PERSON>ciaga, <PERSON><PERSON><PERSON>, Dior, etc.)
 * 3. GENDER (WOMEN, MEN, MIXTE)
 * 4. SHOE MODEL TYPE FAMILY (Jordan, Air Max, Cortez, Ace, etc.)
 * 5. COLLAB (Off-White x Nike, Sacai x Nike, <PERSON> Scott, etc.)
 * 6. PRODUCT FOLDER (SKU-CODE -- BRAND-REFERENCE)
 */

export const CYTTE_6_LEVEL_SCHEMA = {
  // Core Product Information
  id: 'string',                    // Unique product identifier
  sku: 'string',                   // CYTTE SKU code (e.g., "FC1688-133", "JKD474-EJR")
  internalReference: 'string',     // Internal brand reference (e.g., "LV", "Air Dior")
  name: 'string',                  // Product display name
  description: 'string',           // Product description
  
  // LEVEL 1: STYLE (Main Category)
  style: 'string',                 // sneakers | sandals | casual | formal | kids
  styleDisplay: 'string',          // Sneakers | Sandalias | Casual | Formal | Niños
  styleCytteId: 'string',          // 1. SNEAKERS | 2. SANDALS | 3. CASUAL | 4. FORMAL | 5. KIDS
  
  // LEVEL 2: BRAND (Manufacturer)
  brand: 'string',                 // Nike | Gucci | Balenciaga | Hermès | Dior | Adidas | Chanel
  brandId: 'string',               // nike | gucci | balenciaga | hermes | dior | adidas | chanel
  brandType: 'string',             // luxury | streetwear | athletic | designer | casual
  brandCytteId: 'string',          // 1. NIKE Limited Edition | 4. GUCCI | 5. DIOR
  
  // LEVEL 3: GENDER (Target Audience)
  gender: 'string',                // WOMEN | MEN | MIXTE
  genderDisplay: 'string',         // Mujer | Hombre | Unisex
  genderPath: 'string',            // women | men | mixte (for URL paths)
  
  // LEVEL 4: SHOE MODEL TYPE FAMILY (Product Line)
  modelFamily: 'string',           // jordan | air-max-97 | cortez | air-force | dunk-low | ace | horsebit
  modelFamilyDisplay: 'string',    // Jordan | Air Max 97 | Cortez | Air Force | Dunk Low | Ace | Horsebit
  modelVariant: 'string',          // jordan-1-high | jordan-1-low | air-max-97-silver | etc.
  modelCytteId: 'string',          // 2. AIR JORDAN | 1. AIR FORCE | ACE | etc.
  
  // LEVEL 5: COLLAB (Collaboration/Artist)
  isCollaboration: 'boolean',      // true if collaboration between brands/artists
  collaborationType: 'string',     // brand-x-brand | brand-x-artist | limited-edition | special-release
  collaborator: 'string',          // Off-White | Sacai | Travis Scott | Virgil Abloh | null
  collaboratorDisplay: 'string',   // Off-White x Nike | Sacai x Nike | Travis Scott x Jordan
  collabCytteId: 'string',         // 8. OFF WHITE | 10. BLAZER | null
  
  // LEVEL 6: PRODUCT FOLDER (Individual SKU)
  productFolder: 'string',         // "FC1688-133 -- LV" | "JKD474-EJR -- Air Dior"
  skuCode: 'string',               // FC1688-133 | JKD474-EJR
  brandReference: 'string',        // LV | Air Dior | Union LA | Bodega
  
  // File System Path (Generated from 6 levels)
  imagePath: 'string',             // /products/sneakers/nike/mixte/jordan/off-white/jkd474-ejr-air-dior/
  
  // Product Classification
  type: 'string',                  // sneaker | sandal | formal | casual | kids
  subType: 'string',               // high-top | low-top | slide | loafer | boot | trainer
  
  // Pricing & Commerce
  price: 'number',                 // Current price in MXN
  originalPrice: 'number',         // Original retail price
  currency: 'string',              // MXN | USD | EUR
  
  // Product Details
  colors: 'array',                 // Available colorways
  sizes: 'array',                  // Available sizes
  materials: 'array',              // Leather, Canvas, Suede, etc.
  
  // Special Classifications
  isLimited: 'boolean',            // Limited edition status
  isExclusive: 'boolean',          // Exclusive release
  isVip: 'boolean',                // VIP access required
  isNew: 'boolean',                // New arrival
  
  // Images & Media
  images: 'array',                 // Array of image URLs
  videos: 'array',                 // Array of video URLs
  
  // Inventory
  stock: 'number',                 // Available quantity
  availability: 'string',          // in-stock | low-stock | out-of-stock | pre-order
  
  // Metadata
  releaseDate: 'string',           // Release date
  season: 'string',                // SS25 | FW24 | etc.
  year: 'number',                  // Release year
  
  // SEO & Search
  tags: 'array',                   // Search tags
  keywords: 'array',               // SEO keywords
  searchTerms: 'array',            // Additional search terms
  
  // Ratings & Reviews
  rating: 'number',                // Average rating (1-5)
  reviews: 'number',               // Number of reviews
  
  // Created/Updated
  createdAt: 'string',
  updatedAt: 'string'
}

// 6-Level Path Generator
export const generateProductPath = (product) => {
  const {
    style,           // Level 1: sneakers
    brandId,         // Level 2: nike
    genderPath,      // Level 3: mixte
    modelFamily,     // Level 4: jordan
    collaborator,    // Level 5: off-white (or null)
    skuCode,         // Level 6: jkd474-ejr
    brandReference   // Level 6: air-dior
  } = product
  
  const collabPath = collaborator ? collaborator.toLowerCase().replace(/\s+/g, '-') : 'standard'
  const productFolderName = `${skuCode.toLowerCase()}-${brandReference.toLowerCase().replace(/\s+/g, '-')}`
  
  return `/products/${style}/${brandId}/${genderPath}/${modelFamily}/${collabPath}/${productFolderName}/`
}

// Search Index Configuration for 6-Level Structure
export const CYTTE_SEARCH_INDEXES = {
  // Level-based indexes
  level1_style: ['style', 'styleDisplay', 'styleCytteId'],
  level2_brand: ['brand', 'brandId', 'brandType', 'brandCytteId'],
  level3_gender: ['gender', 'genderDisplay', 'genderPath'],
  level4_model: ['modelFamily', 'modelFamilyDisplay', 'modelVariant', 'modelCytteId'],
  level5_collab: ['isCollaboration', 'collaborationType', 'collaborator', 'collaboratorDisplay'],
  level6_product: ['productFolder', 'skuCode', 'brandReference'],
  
  // Cross-level search indexes
  primary: ['id', 'sku', 'skuCode'],
  hierarchy: ['style', 'brand', 'gender', 'modelFamily', 'collaborator'],
  classification: ['type', 'subType', 'isLimited', 'isExclusive', 'isVip'],
  search: ['name', 'description', 'tags', 'keywords', 'searchTerms'],
  commerce: ['price', 'availability', 'stock', 'releaseDate']
}

// Advanced Search Query Builder for 6-Level Structure
export const buildCytteSearchQuery = (filters) => {
  const query = {
    bool: {
      must: [],
      filter: [],
      should: [],
      must_not: []
    }
  }
  
  // Level 1: Style filtering
  if (filters.style) query.bool.filter.push({ term: { style: filters.style } })
  
  // Level 2: Brand filtering
  if (filters.brand) query.bool.filter.push({ term: { brandId: filters.brand } })
  if (filters.brandType) query.bool.filter.push({ term: { brandType: filters.brandType } })
  
  // Level 3: Gender filtering
  if (filters.gender) query.bool.filter.push({ term: { gender: filters.gender } })
  
  // Level 4: Model family filtering
  if (filters.modelFamily) query.bool.filter.push({ term: { modelFamily: filters.modelFamily } })
  if (filters.modelVariant) query.bool.filter.push({ term: { modelVariant: filters.modelVariant } })
  
  // Level 5: Collaboration filtering
  if (filters.isCollaboration !== undefined) query.bool.filter.push({ term: { isCollaboration: filters.isCollaboration } })
  if (filters.collaborator) query.bool.filter.push({ term: { collaborator: filters.collaborator } })
  
  // Level 6: SKU/Product filtering
  if (filters.skuCode) query.bool.filter.push({ term: { skuCode: filters.skuCode } })
  if (filters.brandReference) query.bool.filter.push({ term: { brandReference: filters.brandReference } })
  
  // Text search across all levels
  if (filters.q) {
    query.bool.should.push(
      { match: { name: { query: filters.q, boost: 3 } } },
      { match: { brand: { query: filters.q, boost: 2.5 } } },
      { match: { modelFamilyDisplay: { query: filters.q, boost: 2 } } },
      { match: { collaborator: { query: filters.q, boost: 2 } } },
      { match: { brandReference: { query: filters.q, boost: 1.5 } } },
      { match: { tags: { query: filters.q, boost: 1.5 } } },
      { match: { description: filters.q } }
    )
    query.bool.minimum_should_match = 1
  }
  
  // Special filters
  if (filters.isLimited) query.bool.filter.push({ term: { isLimited: true } })
  if (filters.isVip) query.bool.filter.push({ term: { isVip: true } })
  if (filters.isNew) query.bool.filter.push({ term: { isNew: true } })
  
  // Price range
  if (filters.minPrice || filters.maxPrice) {
    const priceRange = {}
    if (filters.minPrice) priceRange.gte = filters.minPrice
    if (filters.maxPrice) priceRange.lte = filters.maxPrice
    query.bool.filter.push({ range: { price: priceRange } })
  }
  
  // Availability
  if (filters.inStock) {
    query.bool.filter.push({ range: { stock: { gt: 0 } } })
  }
  
  return query
}

export default {
  CYTTE_6_LEVEL_SCHEMA,
  generateProductPath,
  CYTTE_SEARCH_INDEXES,
  buildCytteSearchQuery
}
