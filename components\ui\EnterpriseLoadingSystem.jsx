'use client'

import { motion, AnimatePresence } from 'framer-motion'
import { useEnterprisePerformance } from '@/hooks/useEnterprisePerformance'

/**
 * ENTERPRISE-GRADE LOADING SYSTEM
 * 
 * PERFECTIONIST ENGINEERING FEATURES:
 * - Performance-aware skeleton loading
 * - Mobile-optimized animations
 * - Battery-aware optimizations
 * - Accessibility-compliant loading states
 * - Memory-efficient rendering
 */

export function EnterpriseProductCardSkeleton({ count = 8, columns = 4 }) {
  const { getAnimationSettings } = useEnterprisePerformance()
  const animationSettings = getAnimationSettings()

  return (
    <div className={`grid gap-6 ${
      columns === 2 ? 'grid-cols-2' :
      columns === 3 ? 'grid-cols-2 md:grid-cols-3' :
      columns === 4 ? 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4' :
      'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'
    }`}>
      {Array.from({ length: count }).map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{
            delay: index * 0.05,
            duration: animationSettings.duration,
            ease: animationSettings.ease
          }}
          className="bg-white dark:bg-neutral-800 rounded-2xl overflow-hidden shadow-sm"
          role="status"
          aria-label="Cargando producto"
        >
          {/* Image Skeleton */}
          <div className="relative aspect-[4/3] bg-gray-200 dark:bg-neutral-700">
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
              animate={{
                x: ['-100%', '100%']
              }}
              transition={{
                duration: animationSettings.reduceMotion ? 0 : 1.5,
                repeat: animationSettings.reduceMotion ? 0 : Infinity,
                ease: 'linear'
              }}
            />
          </div>

          {/* Content Skeleton */}
          <div className="p-4 space-y-3">
            {/* Title Skeleton */}
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-neutral-700 rounded-md w-3/4">
                <motion.div
                  className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-md"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 1.5,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear',
                    delay: 0.2
                  }}
                />
              </div>
              <div className="h-3 bg-gray-200 dark:bg-neutral-700 rounded-md w-1/2">
                <motion.div
                  className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-md"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 1.5,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear',
                    delay: 0.4
                  }}
                />
              </div>
            </div>

            {/* Price and Button Skeleton */}
            <div className="flex items-center justify-between pt-2">
              <div className="h-6 bg-gray-200 dark:bg-neutral-700 rounded-md w-1/3">
                <motion.div
                  className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-md"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 1.5,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear',
                    delay: 0.6
                  }}
                />
              </div>
              <div className="h-8 w-8 bg-gray-200 dark:bg-neutral-700 rounded-xl">
                <motion.div
                  className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-xl"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 1.5,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear',
                    delay: 0.8
                  }}
                />
              </div>
            </div>
          </div>
        </motion.div>
      ))}
    </div>
  )
}

export function EnterpriseHeroSkeleton() {
  const { getAnimationSettings } = useEnterprisePerformance()
  const animationSettings = getAnimationSettings()

  return (
    <section className="relative min-h-screen pt-32 bg-pure-white dark:bg-dark-gray flex items-center justify-center">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center h-full py-8">
          
          {/* Left Content Skeleton */}
          <div className="space-y-8 text-center lg:text-left">
            {/* Title Skeleton */}
            <div className="space-y-4">
              <div className="h-20 bg-gray-200 dark:bg-neutral-700 rounded-2xl w-full">
                <motion.div
                  className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-2xl"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 2,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear'
                  }}
                />
              </div>
              <div className="h-6 bg-gray-200 dark:bg-neutral-700 rounded-lg w-3/4 mx-auto lg:mx-0">
                <motion.div
                  className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-lg"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 2,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear',
                    delay: 0.3
                  }}
                />
              </div>
            </div>

            {/* Buttons Skeleton */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start">
              {[1, 2].map((_, index) => (
                <div key={index} className="h-12 bg-gray-200 dark:bg-neutral-700 rounded-xl w-48">
                  <motion.div
                    className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-xl"
                    animate={{
                      x: ['-100%', '100%']
                    }}
                    transition={{
                      duration: animationSettings.reduceMotion ? 0 : 2,
                      repeat: animationSettings.reduceMotion ? 0 : Infinity,
                      ease: 'linear',
                      delay: 0.6 + index * 0.2
                    }}
                  />
                </div>
              ))}
            </div>

            {/* Stats Skeleton */}
            <div className="grid grid-cols-3 gap-6 pt-8 max-w-md mx-auto lg:mx-0">
              {[1, 2, 3].map((_, index) => (
                <div key={index} className="text-center lg:text-left space-y-2">
                  <div className="h-8 bg-gray-200 dark:bg-neutral-700 rounded-lg">
                    <motion.div
                      className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-lg"
                      animate={{
                        x: ['-100%', '100%']
                      }}
                      transition={{
                        duration: animationSettings.reduceMotion ? 0 : 2,
                        repeat: animationSettings.reduceMotion ? 0 : Infinity,
                        ease: 'linear',
                        delay: 1 + index * 0.1
                      }}
                    />
                  </div>
                  <div className="h-4 bg-gray-200 dark:bg-neutral-700 rounded">
                    <motion.div
                      className="h-full bg-gradient-to-r from-transparent via-white/20 to-transparent rounded"
                      animate={{
                        x: ['-100%', '100%']
                      }}
                      transition={{
                        duration: animationSettings.reduceMotion ? 0 : 2,
                        repeat: animationSettings.reduceMotion ? 0 : Infinity,
                        ease: 'linear',
                        delay: 1.2 + index * 0.1
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Right Content Skeleton */}
          <div className="relative flex items-center justify-center lg:justify-end">
            <div className="relative w-full max-w-lg h-[450px] bg-gray-200 dark:bg-neutral-700 rounded-3xl">
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent rounded-3xl"
                animate={{
                  x: ['-100%', '100%']
                }}
                transition={{
                  duration: animationSettings.reduceMotion ? 0 : 2.5,
                  repeat: animationSettings.reduceMotion ? 0 : Infinity,
                  ease: 'linear',
                  delay: 0.5
                }}
              />
            </div>
          </div>

        </div>
      </div>
    </section>
  )
}

export function EnterpriseNavigationSkeleton() {
  const { getAnimationSettings } = useEnterprisePerformance()
  const animationSettings = getAnimationSettings()

  return (
    <header className="relative">
      {/* Top Level Skeleton */}
      <div className="bg-white/95 backdrop-blur-sm border-b border-gray-100 h-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-20">
            {/* Logo Skeleton */}
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gray-200 rounded-full">
                <motion.div
                  className="w-full h-full bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-full"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 1.5,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear'
                  }}
                />
              </div>
              <div className="hidden md:block w-32 h-6 bg-gray-200 rounded">
                <motion.div
                  className="w-full h-full bg-gradient-to-r from-transparent via-white/40 to-transparent rounded"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 1.5,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear',
                    delay: 0.2
                  }}
                />
              </div>
            </div>

            {/* Search Skeleton */}
            <div className="hidden md:flex flex-1 max-w-md mx-8">
              <div className="w-full h-12 bg-gray-200 rounded-xl">
                <motion.div
                  className="w-full h-full bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-xl"
                  animate={{
                    x: ['-100%', '100%']
                  }}
                  transition={{
                    duration: animationSettings.reduceMotion ? 0 : 1.5,
                    repeat: animationSettings.reduceMotion ? 0 : Infinity,
                    ease: 'linear',
                    delay: 0.4
                  }}
                />
              </div>
            </div>

            {/* Icons Skeleton */}
            <div className="flex items-center space-x-3">
              {[1, 2, 3, 4].map((_, index) => (
                <div key={index} className="w-10 h-10 bg-gray-200 rounded-full">
                  <motion.div
                    className="w-full h-full bg-gradient-to-r from-transparent via-white/40 to-transparent rounded-full"
                    animate={{
                      x: ['-100%', '100%']
                    }}
                    transition={{
                      duration: animationSettings.reduceMotion ? 0 : 1.5,
                      repeat: animationSettings.reduceMotion ? 0 : Infinity,
                      ease: 'linear',
                      delay: 0.6 + index * 0.1
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Bottom Level Skeleton */}
      <div className="bg-white/90 backdrop-blur-sm border-b border-white/30 h-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-center h-16">
            <div className="flex items-center space-x-8">
              {[1, 2, 3, 4, 5, 6].map((_, index) => (
                <div key={index} className="h-4 bg-gray-200 rounded w-20">
                  <motion.div
                    className="w-full h-full bg-gradient-to-r from-transparent via-white/40 to-transparent rounded"
                    animate={{
                      x: ['-100%', '100%']
                    }}
                    transition={{
                      duration: animationSettings.reduceMotion ? 0 : 1.5,
                      repeat: animationSettings.reduceMotion ? 0 : Infinity,
                      ease: 'linear',
                      delay: index * 0.1
                    }}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </header>
  )
}

export function EnterpriseLoadingSpinner({ size = 'md', color = 'primary' }) {
  const { getAnimationSettings } = useEnterprisePerformance()
  const animationSettings = getAnimationSettings()

  const sizeClasses = {
    sm: 'w-4 h-4',
    md: 'w-8 h-8',
    lg: 'w-12 h-12',
    xl: 'w-16 h-16'
  }

  const colorClasses = {
    primary: 'border-lime-green',
    white: 'border-white',
    black: 'border-black',
    gray: 'border-gray-400'
  }

  if (animationSettings.reduceMotion) {
    return (
      <div className={`${sizeClasses[size]} ${colorClasses[color]} border-2 rounded-full opacity-50`} />
    )
  }

  return (
    <motion.div
      className={`${sizeClasses[size]} border-2 ${colorClasses[color]} border-t-transparent rounded-full`}
      animate={{ rotate: 360 }}
      transition={{
        duration: 1,
        repeat: Infinity,
        ease: 'linear'
      }}
      role="status"
      aria-label="Cargando"
    />
  )
}
