'use client'

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'

const Input = forwardRef(({
  className,
  type = 'text',
  variant = 'default',
  size = 'default',
  error = false,
  icon,
  iconPosition = 'left',
  ...props
}, ref) => {
  const baseClasses = 'flex w-full transition-all duration-300 file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 dark:placeholder:text-gray-400 focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50'
  
  const variants = {
    default: 'input-glass focus:ring-2 focus:ring-rich-gold focus:border-transparent',
    outline: 'border-2 border-warm-camel dark:border-forest-emerald bg-transparent focus:border-rich-gold focus:ring-2 focus:ring-rich-gold/20',
    filled: 'bg-soft-steel-gray dark:bg-deep-pine border-0 focus:ring-2 focus:ring-rich-gold',
    ghost: 'bg-transparent border-0 focus:bg-white/10 dark:focus:bg-white/5',
  }
  
  const sizes = {
    sm: 'h-9 px-3 py-2 text-sm rounded-md',
    default: 'h-11 px-4 py-3 rounded-lg',
    lg: 'h-12 px-4 py-4 text-lg rounded-lg',
  }
  
  const errorClasses = error
    ? 'border-warm-camel focus:border-warm-camel focus:ring-warm-camel/20'
    : ''

  const inputClasses = cn(
    baseClasses,
    variants[variant],
    sizes[size],
    errorClasses,
    icon && iconPosition === 'left' ? 'pl-10' : '',
    icon && iconPosition === 'right' ? 'pr-10' : '',
    className
  )

  if (icon) {
    return (
      <div className="relative">
        {iconPosition === 'left' && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
            {icon}
          </div>
        )}
        <input
          type={type}
          className={inputClasses}
          ref={ref}
          {...props}
        />
        {iconPosition === 'right' && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 dark:text-gray-400">
            {icon}
          </div>
        )}
      </div>
    )
  }

  return (
    <input
      type={type}
      className={inputClasses}
      ref={ref}
      {...props}
    />
  )
})

Input.displayName = 'Input'

export default Input
