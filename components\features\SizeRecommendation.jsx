'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import AnimatedButton from '@/components/ui/AnimatedButton'
import AnimatedInput from '@/components/ui/AnimatedInput'
import AnimatedSelect from '@/components/ui/AnimatedSelect'
import { Card, CardContent } from '@/components/ui/Card'

const sizeConversions = {
  'nike': {
    'US': { '6': '24', '6.5': '24.5', '7': '25', '7.5': '25.5', '8': '26', '8.5': '26.5', '9': '27', '9.5': '27.5', '10': '28', '10.5': '28.5', '11': '29', '11.5': '29.5', '12': '30' },
    'EU': { '6': '38.5', '6.5': '39', '7': '40', '7.5': '40.5', '8': '41', '8.5': '42', '9': '42.5', '9.5': '43', '10': '44', '10.5': '44.5', '11': '45', '11.5': '45.5', '12': '46' },
    'CM': { '6': '24', '6.5': '24.5', '7': '25', '7.5': '25.5', '8': '26', '8.5': '26.5', '9': '27', '9.5': '27.5', '10': '28', '10.5': '28.5', '11': '29', '11.5': '29.5', '12': '30' }
  },
  'adidas': {
    'US': { '6': '24', '6.5': '24.5', '7': '25', '7.5': '25.5', '8': '26', '8.5': '26.5', '9': '27', '9.5': '27.5', '10': '28', '10.5': '28.5', '11': '29', '11.5': '29.5', '12': '30' },
    'EU': { '6': '38', '6.5': '38.5', '7': '39', '7.5': '40', '8': '40.5', '8.5': '41', '9': '42', '9.5': '42.5', '10': '43', '10.5': '44', '11': '44.5', '11.5': '45', '12': '45.5' },
    'CM': { '6': '24', '6.5': '24.5', '7': '25', '7.5': '25.5', '8': '26', '8.5': '26.5', '9': '27', '9.5': '27.5', '10': '28', '10.5': '28.5', '11': '29', '11.5': '29.5', '12': '30' }
  },
  'gucci': {
    'EU': { '35': '22.5', '36': '23', '37': '23.5', '38': '24', '39': '24.5', '40': '25', '41': '25.5', '42': '26', '43': '26.5', '44': '27', '45': '27.5' },
    'US': { '35': '5', '36': '5.5', '37': '6', '38': '6.5', '39': '7', '40': '7.5', '41': '8', '42': '8.5', '43': '9', '44': '9.5', '45': '10' },
    'CM': { '35': '22.5', '36': '23', '37': '23.5', '38': '24', '39': '24.5', '40': '25', '41': '25.5', '42': '26', '43': '26.5', '44': '27', '45': '27.5' }
  }
}

const fitPreferences = [
  { value: 'tight', label: 'Ajustado', description: 'Me gusta que quede muy justo' },
  { value: 'snug', label: 'Ceñido', description: 'Prefiero que quede bien ajustado' },
  { value: 'comfortable', label: 'Cómodo', description: 'Balance perfecto entre ajuste y comodidad' },
  { value: 'loose', label: 'Holgado', description: 'Me gusta tener espacio extra' },
  { value: 'very-loose', label: 'Muy Holgado', description: 'Prefiero mucho espacio para los pies' }
]

const footTypes = [
  { value: 'narrow', label: 'Pie Estrecho', icon: '🦶' },
  { value: 'normal', label: 'Pie Normal', icon: '👣' },
  { value: 'wide', label: 'Pie Ancho', icon: '🦶' }
]

export default function SizeRecommendation({ product, onSizeSelect, onClose }) {
  const { getSizeForBrand, setSizeForBrand } = useUserPreferences()
  const [step, setStep] = useState(1)
  const [measurements, setMeasurements] = useState({
    footLength: '',
    footWidth: '',
    footType: 'normal',
    fitPreference: 'comfortable',
    usualSize: '',
    usualBrand: ''
  })
  const [recommendation, setRecommendation] = useState(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)

  const savedSize = getSizeForBrand(product.brand.toLowerCase())

  useEffect(() => {
    if (savedSize) {
      setRecommendation({
        recommendedSize: savedSize,
        confidence: 0.95,
        reasoning: `Basado en tu talla guardada para ${product.brand}`,
        alternatives: [],
        fitNotes: 'Esta es tu talla preferida guardada para esta marca.'
      })
    }
  }, [savedSize, product.brand])

  const handleMeasurementChange = (field, value) => {
    setMeasurements(prev => ({ ...prev, [field]: value }))
  }

  const analyzeSize = async () => {
    setIsAnalyzing(true)
    
    // Simulate AI analysis
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock AI size recommendation logic
    const footLengthCm = parseFloat(measurements.footLength)
    const brand = product.brand.toLowerCase()
    
    let baseSize = '9' // Default
    
    // Convert foot length to approximate size
    if (footLengthCm) {
      if (footLengthCm <= 24) baseSize = '6'
      else if (footLengthCm <= 24.5) baseSize = '6.5'
      else if (footLengthCm <= 25) baseSize = '7'
      else if (footLengthCm <= 25.5) baseSize = '7.5'
      else if (footLengthCm <= 26) baseSize = '8'
      else if (footLengthCm <= 26.5) baseSize = '8.5'
      else if (footLengthCm <= 27) baseSize = '9'
      else if (footLengthCm <= 27.5) baseSize = '9.5'
      else if (footLengthCm <= 28) baseSize = '10'
      else if (footLengthCm <= 28.5) baseSize = '10.5'
      else if (footLengthCm <= 29) baseSize = '11'
      else baseSize = '11.5'
    }

    // Adjust for brand-specific fitting
    let adjustedSize = baseSize
    let confidence = 0.85
    let reasoning = 'Basado en la longitud de tu pie'
    
    if (brand === 'nike') {
      // Nike tends to run true to size
      reasoning += ' y el ajuste característico de Nike'
      confidence = 0.90
    } else if (brand === 'adidas') {
      // Adidas can run slightly large
      const sizeNum = parseFloat(baseSize)
      if (sizeNum > 6) {
        adjustedSize = (sizeNum - 0.5).toString()
        reasoning += '. Adidas tiende a quedar un poco grande'
        confidence = 0.88
      }
    } else if (brand === 'gucci') {
      // Luxury brands often run small
      const sizeNum = parseFloat(baseSize)
      adjustedSize = (sizeNum + 0.5).toString()
      reasoning += '. Las marcas de lujo suelen quedar pequeñas'
      confidence = 0.82
    }

    // Adjust for fit preference
    if (measurements.fitPreference === 'tight') {
      const sizeNum = parseFloat(adjustedSize)
      adjustedSize = Math.max(6, sizeNum - 0.5).toString()
      reasoning += ', ajustado para un fit más ceñido'
    } else if (measurements.fitPreference === 'loose' || measurements.fitPreference === 'very-loose') {
      const sizeNum = parseFloat(adjustedSize)
      const adjustment = measurements.fitPreference === 'very-loose' ? 1 : 0.5
      adjustedSize = (sizeNum + adjustment).toString()
      reasoning += ', ajustado para un fit más holgado'
    }

    // Adjust for foot width
    if (measurements.footType === 'wide') {
      const sizeNum = parseFloat(adjustedSize)
      adjustedSize = (sizeNum + 0.5).toString()
      reasoning += ', considerando que tienes pie ancho'
      confidence -= 0.05
    } else if (measurements.footType === 'narrow') {
      confidence += 0.05
    }

    // Generate alternatives
    const sizeNum = parseFloat(adjustedSize)
    const alternatives = [
      {
        size: Math.max(6, sizeNum - 0.5).toString(),
        reason: 'Si prefieres un ajuste más ceñido',
        confidence: confidence - 0.15
      },
      {
        size: (sizeNum + 0.5).toString(),
        reason: 'Si prefieres más espacio',
        confidence: confidence - 0.10
      }
    ]

    const fitNotes = generateFitNotes(brand, measurements.footType, measurements.fitPreference)

    setRecommendation({
      recommendedSize: adjustedSize,
      confidence,
      reasoning,
      alternatives,
      fitNotes
    })
    
    setIsAnalyzing(false)
  }

  const generateFitNotes = (brand, footType, fitPreference) => {
    const notes = []
    
    if (brand === 'nike') {
      notes.push('Nike generalmente tiene un ajuste fiel a la talla')
      if (footType === 'wide') notes.push('Considera modelos con "Wide" si están disponibles')
    } else if (brand === 'adidas') {
      notes.push('Adidas puede quedar ligeramente grande')
      notes.push('Los modelos Boost tienden a ser más cómodos')
    } else if (brand === 'gucci') {
      notes.push('Las marcas de lujo italiana suelen quedar pequeñas')
      notes.push('Considera probarte en tienda si es posible')
    }

    if (fitPreference === 'tight') {
      notes.push('Recuerda que los zapatos pueden aflojarse con el uso')
    } else if (fitPreference === 'loose') {
      notes.push('Puedes usar plantillas si queda muy holgado')
    }

    return notes.join('. ') + '.'
  }

  const handleSizeConfirm = (size) => {
    // Save size preference for this brand
    setSizeForBrand(product.brand.toLowerCase(), size)
    onSizeSelect(size)
    onClose()
  }

  const nextStep = () => {
    if (step === 2) {
      analyzeSize()
    }
    setStep(prev => prev + 1)
  }

  const prevStep = () => {
    setStep(prev => prev - 1)
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <Card variant="glass" className="max-w-2xl w-full max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <CardContent className="p-8">
          
          {/* Header */}
          <div className="text-center mb-8">
            <motion.div
              className="text-6xl mb-4"
              animate={{
                scale: isAnalyzing ? [1, 1.1, 1] : 1
              }}
              transition={{
                duration: 1,
                repeat: isAnalyzing ? Infinity : 0
              }}
            >
              📏
            </motion.div>
            <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
              Recomendación de Talla IA
            </h2>
            <p className="text-warm-camel">
              {product.brand} {product.name}
            </p>
          </div>

          {/* Progress Indicator */}
          <div className="flex justify-center mb-8">
            <div className="flex items-center space-x-4">
              {[1, 2, 3].map((stepNum) => (
                <div key={stepNum} className="flex items-center">
                  <motion.div
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-bold ${
                      step >= stepNum 
                        ? 'bg-rich-gold text-forest-emerald' 
                        : 'bg-warm-camel/20 text-warm-camel'
                    }`}
                    animate={{
                      scale: step === stepNum ? [1, 1.1, 1] : 1
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    {stepNum}
                  </motion.div>
                  {stepNum < 3 && (
                    <div className={`w-8 h-1 mx-2 ${
                      step > stepNum ? 'bg-rich-gold' : 'bg-warm-camel/20'
                    }`} />
                  )}
                </div>
              ))}
            </div>
          </div>

          <AnimatePresence mode="wait">
            {/* Step 1: Basic Measurements */}
            {step === 1 && (
              <motion.div
                key="step1"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  Medidas de tu Pie
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <AnimatedInput
                    label="Longitud del pie (cm)"
                    placeholder="25.5"
                    value={measurements.footLength}
                    onChange={(e) => handleMeasurementChange('footLength', e.target.value)}
                    type="number"
                    step="0.1"
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21l3-3-3-3m8 6l3-3-3-3M5 7h14M5 17h14" />
                      </svg>
                    }
                  />
                  
                  <AnimatedInput
                    label="Ancho del pie (cm)"
                    placeholder="9.5"
                    value={measurements.footWidth}
                    onChange={(e) => handleMeasurementChange('footWidth', e.target.value)}
                    type="number"
                    step="0.1"
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4" />
                      </svg>
                    }
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                    Tipo de Pie
                  </label>
                  <div className="grid grid-cols-3 gap-3">
                    {footTypes.map((type) => (
                      <motion.button
                        key={type.value}
                        onClick={() => handleMeasurementChange('footType', type.value)}
                        className={`p-4 rounded-lg border-2 transition-all duration-200 ${
                          measurements.footType === type.value
                            ? 'border-rich-gold bg-rich-gold/10 text-forest-emerald'
                            : 'border-warm-camel/30 hover:border-warm-camel text-warm-camel'
                        }`}
                        whileHover={{ scale: 1.02 }}
                        whileTap={{ scale: 0.98 }}
                      >
                        <div className="text-2xl mb-2">{type.icon}</div>
                        <div className="text-sm font-medium">{type.label}</div>
                      </motion.button>
                    ))}
                  </div>
                </div>

                <div className="bg-warm-camel/10 p-4 rounded-lg">
                  <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-2">
                    💡 Cómo medir tu pie:
                  </h4>
                  <ul className="text-sm text-warm-camel space-y-1">
                    <li>• Coloca una hoja de papel en el suelo contra la pared</li>
                    <li>• Pon tu pie sobre el papel con el talón contra la pared</li>
                    <li>• Marca el punto más largo de tu pie</li>
                    <li>• Mide la distancia desde la pared hasta la marca</li>
                  </ul>
                </div>
              </motion.div>
            )}

            {/* Step 2: Preferences */}
            {step === 2 && (
              <motion.div
                key="step2"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  Preferencias de Ajuste
                </h3>
                
                <div>
                  <label className="block text-sm font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                    ¿Cómo prefieres que te queden los zapatos?
                  </label>
                  <div className="space-y-3">
                    {fitPreferences.map((pref) => (
                      <motion.button
                        key={pref.value}
                        onClick={() => handleMeasurementChange('fitPreference', pref.value)}
                        className={`w-full p-4 rounded-lg border-2 text-left transition-all duration-200 ${
                          measurements.fitPreference === pref.value
                            ? 'border-rich-gold bg-rich-gold/10'
                            : 'border-warm-camel/30 hover:border-warm-camel'
                        }`}
                        whileHover={{ scale: 1.01 }}
                        whileTap={{ scale: 0.99 }}
                      >
                        <div className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                          {pref.label}
                        </div>
                        <div className="text-sm text-warm-camel">
                          {pref.description}
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <AnimatedInput
                    label="Talla usual (opcional)"
                    placeholder="9"
                    value={measurements.usualSize}
                    onChange={(e) => handleMeasurementChange('usualSize', e.target.value)}
                  />
                  
                  <AnimatedInput
                    label="Marca usual (opcional)"
                    placeholder="Nike"
                    value={measurements.usualBrand}
                    onChange={(e) => handleMeasurementChange('usualBrand', e.target.value)}
                  />
                </div>
              </motion.div>
            )}

            {/* Step 3: Results */}
            {step === 3 && (
              <motion.div
                key="step3"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                className="space-y-6"
              >
                {isAnalyzing ? (
                  <div className="text-center py-12">
                    <motion.div
                      className="w-16 h-16 border-4 border-rich-gold border-t-transparent rounded-full mx-auto mb-4"
                      animate={{ rotate: 360 }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    />
                    <p className="text-warm-camel">
                      Analizando tus medidas con IA...
                    </p>
                  </div>
                ) : recommendation && (
                  <div className="space-y-6">
                    <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray text-center">
                      Tu Talla Recomendada
                    </h3>
                    
                    {/* Main Recommendation */}
                    <Card variant="default" className="border-rich-gold/50 bg-rich-gold/10">
                      <CardContent className="p-6 text-center">
                        <div className="text-4xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                          Talla {recommendation.recommendedSize}
                        </div>
                        <div className="text-rich-gold font-medium mb-2">
                          {Math.round(recommendation.confidence * 100)}% de confianza
                        </div>
                        <p className="text-warm-camel text-sm">
                          {recommendation.reasoning}
                        </p>
                      </CardContent>
                    </Card>

                    {/* Alternatives */}
                    {recommendation.alternatives.length > 0 && (
                      <div>
                        <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-3">
                          Alternativas:
                        </h4>
                        <div className="space-y-2">
                          {recommendation.alternatives.map((alt, index) => (
                            <motion.button
                              key={index}
                              onClick={() => handleSizeConfirm(alt.size)}
                              className="w-full p-3 rounded-lg border border-warm-camel/30 hover:border-rich-gold hover:bg-rich-gold/5 transition-all duration-200 text-left"
                              whileHover={{ scale: 1.01 }}
                              whileTap={{ scale: 0.99 }}
                            >
                              <div className="flex justify-between items-center">
                                <span className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                                  Talla {alt.size}
                                </span>
                                <span className="text-xs text-warm-camel">
                                  {Math.round(alt.confidence * 100)}%
                                </span>
                              </div>
                              <div className="text-sm text-warm-camel">
                                {alt.reason}
                              </div>
                            </motion.button>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* Fit Notes */}
                    <Card variant="default">
                      <CardContent className="p-4">
                        <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray mb-2">
                          Notas sobre el ajuste:
                        </h4>
                        <p className="text-warm-camel text-sm">
                          {recommendation.fitNotes}
                        </p>
                      </CardContent>
                    </Card>

                    {/* Confirm Button */}
                    <AnimatedButton
                      variant="primary"
                      size="lg"
                      className="w-full"
                      onClick={() => handleSizeConfirm(recommendation.recommendedSize)}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      }
                    >
                      Confirmar Talla {recommendation.recommendedSize}
                    </AnimatedButton>
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Navigation */}
          <div className="flex justify-between mt-8">
            <AnimatedButton
              variant="ghost"
              onClick={step === 1 ? onClose : prevStep}
              disabled={isAnalyzing}
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              }
            >
              {step === 1 ? 'Cancelar' : 'Anterior'}
            </AnimatedButton>

            {step < 3 && (
              <AnimatedButton
                variant="primary"
                onClick={nextStep}
                disabled={
                  (step === 1 && !measurements.footLength) ||
                  (step === 2 && !measurements.fitPreference)
                }
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                }
                iconPosition="right"
              >
                {step === 2 ? 'Analizar' : 'Siguiente'}
              </AnimatedButton>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
