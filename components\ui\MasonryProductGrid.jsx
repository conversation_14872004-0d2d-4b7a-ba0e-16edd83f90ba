'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { cn } from '@/lib/utils'
import Badge from '@/components/ui/Badge'

export default function MasonryProductGrid({ products, className }) {
  const [hoveredProduct, setHoveredProduct] = useState(null)

  // Define different aspect ratios for masonry effect
  const getCardAspectRatio = (index) => {
    const patterns = [
      'aspect-square',      // 1:1
      'aspect-[4/5]',      // 4:5 (portrait)
      'aspect-[3/4]',      // 3:4 (portrait)
      'aspect-square',      // 1:1
      'aspect-[5/4]',      // 5:4 (landscape)
      'aspect-square',      // 1:1
      'aspect-[4/5]',      // 4:5 (portrait)
      'aspect-[3/4]'       // 3:4 (portrait)
    ]
    return patterns[index % patterns.length]
  }

  const handleProductClick = (product) => {
    console.log('Product clicked:', product)
    // Navigate to product page
  }

  return (
    <div className={cn('w-full', className)}>
      {/* Mobile-First Product Grid */}
      <div className="grid grid-cols-2 gap-2 sm:gap-3 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 w-full max-w-full overflow-hidden">
        {products.map((product, index) => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.05 }} // Faster on mobile
            className="relative"
            onMouseEnter={() => setHoveredProduct(product.id)}
            onMouseLeave={() => setHoveredProduct(null)}
            onTouchStart={() => setHoveredProduct(product.id)} // Touch support
            onTouchEnd={() => setHoveredProduct(null)}
          >
            <div
              className={cn(
                'relative overflow-hidden cursor-pointer group',
                'transition-all duration-300 hover:shadow-lg touch-target',
                // Mobile-first rounded corners
                'rounded-lg sm:rounded-xl',
                // Uniform aspect ratio for consistent height
                'aspect-[4/5]'
              )}
              onClick={() => handleProductClick(product)}
            >
              {/* Product Image */}
              <div className="relative w-full h-full">
                <Image
                  src={product.image || "/placeholder.jpg"}
                  alt={product.name}
                  fill
                  className="object-cover transition-transform duration-300 group-hover:scale-105"
                  sizes="(max-width: 640px) 50vw, (max-width: 768px) 33vw, (max-width: 1024px) 25vw, 16vw"
                  priority={index < 4} // Prioritize first 4 images
                />

                {/* Mobile-optimized gradient overlay */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-200" />

                {/* Mobile-optimized badges */}
                <div className="absolute top-2 left-2 flex flex-col gap-1 z-10">
                  {product.isNew && (
                    <Badge variant="new" size="sm" className="text-xs px-2 py-1">
                      NUEVO
                    </Badge>
                  )}
                  {product.isLimited && (
                    <Badge variant="limited" size="sm" className="text-xs px-2 py-1">
                      LIMITADO
                    </Badge>
                  )}
                  {product.isExclusive && (
                    <Badge variant="exclusive" size="sm" className="text-xs px-2 py-1">
                      EXCLUSIVO
                    </Badge>
                  )}
                </div>

                {/* Mobile-optimized "Ver +" Overlay */}
                {hoveredProduct === product.id && (
                  <motion.div
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="absolute inset-0 flex items-center justify-center z-20"
                  >
                    <div className="bg-white/95 backdrop-blur-sm rounded-full px-3 py-2 shadow-lg sm:px-4">
                      <span className="text-black font-medium text-xs sm:text-sm">Ver +</span>
                    </div>
                  </motion.div>
                )}

                {/* Mobile-first Product Info - Always visible on mobile */}
                <div className="absolute bottom-0 left-0 right-0 p-2 sm:p-3 text-white bg-gradient-to-t from-black/60 to-transparent sm:transform sm:translate-y-full sm:group-hover:translate-y-0 sm:transition-transform sm:duration-300 sm:bg-transparent">
                  <h3 className="font-poppins font-semibold text-xs sm:text-sm mb-1 line-clamp-2 min-h-[2.5rem] sm:min-h-[3rem] flex items-center">
                    {product.name}
                  </h3>
                  <p className="font-poppins text-xs opacity-90 uppercase tracking-wide mb-1">
                    {product.brand}
                  </p>
                  <div className="flex items-center gap-2">
                    <span className="font-poppins font-bold text-xs sm:text-sm">
                      ${product.price}
                    </span>
                    {product.originalPrice && product.originalPrice > product.price && (
                      <span className="font-poppins text-xs opacity-75 line-through">
                        ${product.originalPrice}
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
