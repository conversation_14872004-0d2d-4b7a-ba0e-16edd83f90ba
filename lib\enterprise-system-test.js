// 🧪 TWL ENTERPRISE SYSTEM COMPREHENSIVE TEST SUITE
// 🎯 Tests all components of the enterprise product data integration system

import { enterpriseProductSystem, PricingUtils } from './enterpriseProductDataSystem.js';
import { enterpriseIntegration, IntegrationUtils } from './enterpriseIntegrationService.js';

/**
 * 🧪 ENTERPRISE SYSTEM TEST SUITE
 * 
 * Tests:
 * - Description file parsing
 * - Mexican market pricing calculations
 * - Product object creation
 * - Integration service functionality
 * - Cart compatibility
 * - Homepage component compatibility
 */
export class TWLEnterpriseSystemTest {
  constructor() {
    this.testResults = {
      passed: 0,
      failed: 0,
      total: 0,
      details: []
    };
  }

  /**
   * 🚀 Run all tests
   */
  async runAllTests() {
    console.log('🧪 Starting TWL Enterprise System Test Suite...\n');
    
    // Core system tests
    await this.testDescriptionParsing();
    await this.testPricingCalculations();
    await this.testProductCreation();
    await this.testCleanProductNames();
    
    // Integration tests
    await this.testIntegrationService();
    await this.testCartCompatibility();
    await this.testHomepageCompatibility();
    
    // Utility tests
    await this.testPricingUtils();
    await this.testIntegrationUtils();
    
    // Performance tests
    await this.testBatchProcessing();
    
    this.printResults();
    return this.testResults;
  }

  /**
   * 📄 Test description file parsing
   */
  async testDescriptionParsing() {
    this.test('Description File Parsing', () => {
      const sampleDescription = `💰300 -- 50$
Nike Air Force 1 x Gucci Limited Edition está decorado con detalles de lujo
Tamaño: 36 37 38 39 40 41 42 43 44 45
ID: BD7700-222`;

      const result = enterpriseProductSystem.parseDescriptionFile(sampleDescription);
      
      this.assert(result !== null, 'Should parse valid description');
      this.assert(result.supplierCostRMB === 300, 'Should extract RMB cost');
      this.assert(result.supplierCostUSD === 50, 'Should extract USD cost');
      this.assert(result.productName.includes('Nike Air Force'), 'Should extract product name');
      this.assert(result.availableSizes.length === 10, 'Should extract sizes');
      this.assert(result.sku === 'BD7700-222', 'Should extract SKU');
    });
  }

  /**
   * 💰 Test Mexican market pricing calculations
   */
  async testPricingCalculations() {
    this.test('Mexican Market Pricing', () => {
      const supplierCost = 50; // $50 USD
      const pricing = enterpriseProductSystem.calculateMexicanPricing(supplierCost);
      
      // Total cost should be supplier + transport ($50 + $35 = $85)
      this.assert(pricing.backend.totalCost === 85, 'Should calculate total cost correctly');
      
      // Pricing tiers
      this.assert(pricing.pricing.suggested === 213, 'Should calculate suggested price (85 * 2.5)');
      this.assert(pricing.pricing.premium === 255, 'Should calculate premium price (85 * 3.0)');
      this.assert(pricing.pricing.luxury === 340, 'Should calculate luxury price (85 * 4.0)');
      
      // Margins
      this.assert(pricing.margins.suggested === 150, 'Should calculate 150% margin');
      this.assert(pricing.margins.premium === 200, 'Should calculate 200% margin');
      this.assert(pricing.margins.luxury === 300, 'Should calculate 300% margin');
      
      // Backend data should be marked confidential
      this.assert(pricing.backend.confidential === true, 'Backend data should be confidential');
    });
  }

  /**
   * 🏷️ Test enterprise product creation
   */
  async testProductCreation() {
    this.test('Enterprise Product Creation', () => {
      const descriptionData = {
        supplierCostRMB: 300,
        supplierCostUSD: 50,
        productName: '1. Nike Air Force 1 x Gucci Limited Edition',
        availableSizes: [36, 37, 38, 39, 40, 41, 42, 43, 44, 45],
        sku: 'BD7700-222'
      };

      const product = enterpriseProductSystem.createEnterpriseProduct(
        descriptionData, 
        'sneakers', 
        'nike', 
        'mixte'
      );

      // Core properties
      this.assert(product.id === 'sneakers-nike-mixte-bd7700-222', 'Should generate correct ID');
      this.assert(product.name === 'Nike Air Force 1 x Gucci Limited Edition', 'Should clean product name');
      this.assert(product.brand === 'Nike', 'Should format brand name');
      this.assert(product.price === 213, 'Should use suggested pricing');
      this.assert(product.originalPrice === 340, 'Should use luxury as original price');
      
      // Product status
      this.assert(product.inStock === true, 'Should be in stock by default');
      this.assert(product.isVip === false, 'Should not be VIP (price < $300)');
      this.assert(product.limitedEdition === true, 'Should detect limited edition');
      
      // Sizing
      this.assert(product.sizing.availableSizes.length === 10, 'Should include all sizes');
      this.assert(product.sizing.sizeChart === 'european', 'Should use European sizing');
      
      // Backend data should exist but be hidden
      this.assert(product._backend !== undefined, 'Should include backend data');
      this.assert(product._backend.confidential === true, 'Backend should be confidential');
      
      // Search keywords
      this.assert(product.searchKeywords.includes('nike'), 'Should include brand in keywords');
      this.assert(product.searchKeywords.includes('sneakers'), 'Should include category in keywords');
      this.assert(product.searchKeywords.includes('tenis'), 'Should include Spanish keywords');
    });
  }

  /**
   * 🧹 Test clean product names
   */
  async testCleanProductNames() {
    this.test('Clean Product Names', () => {
      const testCases = [
        { input: '1. Nike Air Force', expected: 'Nike Air Force' },
        { input: '10. Gucci Ace Sneaker', expected: 'Gucci Ace Sneaker' },
        { input: 'Nike Air Force', expected: 'Nike Air Force' },
        { input: '', expected: 'Product Name' },
        { input: null, expected: 'Product Name' }
      ];

      testCases.forEach(({ input, expected }) => {
        const result = enterpriseProductSystem.cleanProductName(input);
        this.assert(result === expected, `Should clean "${input}" to "${expected}"`);
      });
    });
  }

  /**
   * 🔗 Test integration service
   */
  async testIntegrationService() {
    this.test('Integration Service', async () => {
      // Test initialization
      this.assert(enterpriseIntegration.isInitialized === true, 'Should be initialized');
      
      // Test status
      const status = enterpriseIntegration.getStatus();
      this.assert(status.initialized === true, 'Status should show initialized');
      this.assert(typeof status.settings === 'object', 'Should have settings');
      
      // Test product retrieval
      const product = await enterpriseIntegration.getProductForHomepage(
        'sneakers-nike-mixte-air-force-bd7700-222', 
        'featured'
      );
      
      this.assert(product !== null, 'Should return product');
      this.assert(product.id !== undefined, 'Product should have ID');
      this.assert(product.name !== undefined, 'Product should have name');
      this.assert(product.price !== undefined, 'Product should have price');
    });
  }

  /**
   * 🛒 Test cart compatibility
   */
  async testCartCompatibility() {
    this.test('Cart Compatibility', async () => {
      const cartProduct = await enterpriseIntegration.getProductForCart(
        'sneakers-nike-mixte-air-force-bd7700-222'
      );

      this.assert(cartProduct.cartCompatible === true, 'Should be cart compatible');
      this.assert(cartProduct.quickAddEnabled === true, 'Should enable quick add');
      this.assert(cartProduct.sizeRequired === true, 'Should require size selection');
      this.assert(Array.isArray(cartProduct.sizes), 'Should have sizes array');
      this.assert(cartProduct.sizes.length > 0, 'Should have available sizes');
    });
  }

  /**
   * 🏠 Test homepage compatibility
   */
  async testHomepageCompatibility() {
    this.test('Homepage Compatibility', async () => {
      const sections = ['featured', 'limited', 'collection'];
      
      for (const section of sections) {
        const product = await enterpriseIntegration.getProductForHomepage(
          'sneakers-nike-mixte-air-force-bd7700-222',
          section
        );

        this.assert(product !== null, `Should return product for ${section} section`);
        this.assert(product.image !== undefined, 'Should have main image');
        this.assert(Array.isArray(product.images), 'Should have images array');
        
        // Section-specific tests
        if (section === 'featured') {
          this.assert(product.featured === true, 'Featured product should have featured flag');
        }
        if (section === 'limited') {
          this.assert(product.isLimited === true, 'Limited product should have limited flag');
        }
      }
    });
  }

  /**
   * 💰 Test pricing utilities
   */
  async testPricingUtils() {
    this.test('Pricing Utilities', () => {
      // Test price formatting
      const formattedPrice = PricingUtils.formatPrice(150);
      this.assert(typeof formattedPrice === 'string', 'Should return formatted string');
      this.assert(formattedPrice.includes('$'), 'Should include currency symbol');
      
      // Test discount calculation
      const discount = PricingUtils.calculateDiscount(200, 150);
      this.assert(discount === 25, 'Should calculate 25% discount');
      
      // Test price range
      const priceRange = PricingUtils.getPriceRange(50);
      this.assert(priceRange.min === 213, 'Should return correct minimum price');
      this.assert(priceRange.max === 340, 'Should return correct maximum price');
    });
  }

  /**
   * 🔧 Test integration utilities
   */
  async testIntegrationUtils() {
    this.test('Integration Utilities', async () => {
      // Test product retrieval
      const product = await IntegrationUtils.getProduct('sneakers-nike-mixte-air-force-bd7700-222');
      this.assert(product !== null, 'Should retrieve product');
      
      // Test cart product retrieval
      const cartProduct = await IntegrationUtils.getCartProduct('sneakers-nike-mixte-air-force-bd7700-222');
      this.assert(cartProduct.cartCompatible === true, 'Should be cart compatible');
      
      // Test name cleaning
      const cleanName = IntegrationUtils.cleanName('1. Nike Air Force');
      this.assert(cleanName === 'Nike Air Force', 'Should clean product name');
      
      // Test price formatting
      const formattedPrice = IntegrationUtils.formatPrice(150);
      this.assert(typeof formattedPrice === 'string', 'Should format price');
    });
  }

  /**
   * ⚡ Test batch processing performance
   */
  async testBatchProcessing() {
    this.test('Batch Processing Performance', async () => {
      const mockPaths = [
        '/products/test1/Description.txt',
        '/products/test2/Description.txt',
        '/products/test3/Description.txt'
      ];

      const startTime = Date.now();
      
      // This would normally process real files, but we'll simulate
      const results = {
        success: [],
        failed: mockPaths.map(path => ({ path, error: 'Mock test' })),
        total: mockPaths.length
      };
      
      const endTime = Date.now();
      const processingTime = endTime - startTime;

      this.assert(results.total === 3, 'Should process all paths');
      this.assert(processingTime < 1000, 'Should complete within 1 second for mock test');
    });
  }

  /**
   * 🧪 Test helper method
   */
  test(name, testFunction) {
    this.testResults.total++;
    
    try {
      if (testFunction.constructor.name === 'AsyncFunction') {
        return testFunction().then(() => {
          this.testResults.passed++;
          this.testResults.details.push({ name, status: 'PASSED', error: null });
          console.log(`✅ ${name}`);
        }).catch(error => {
          this.testResults.failed++;
          this.testResults.details.push({ name, status: 'FAILED', error: error.message });
          console.log(`❌ ${name}: ${error.message}`);
        });
      } else {
        testFunction();
        this.testResults.passed++;
        this.testResults.details.push({ name, status: 'PASSED', error: null });
        console.log(`✅ ${name}`);
      }
    } catch (error) {
      this.testResults.failed++;
      this.testResults.details.push({ name, status: 'FAILED', error: error.message });
      console.log(`❌ ${name}: ${error.message}`);
    }
  }

  /**
   * ✅ Assert helper method
   */
  assert(condition, message) {
    if (!condition) {
      throw new Error(message);
    }
  }

  /**
   * 📊 Print test results
   */
  printResults() {
    console.log('\n🧪 TWL Enterprise System Test Results:');
    console.log(`📊 Total Tests: ${this.testResults.total}`);
    console.log(`✅ Passed: ${this.testResults.passed}`);
    console.log(`❌ Failed: ${this.testResults.failed}`);
    console.log(`📈 Success Rate: ${Math.round((this.testResults.passed / this.testResults.total) * 100)}%`);
    
    if (this.testResults.failed > 0) {
      console.log('\n❌ Failed Tests:');
      this.testResults.details
        .filter(test => test.status === 'FAILED')
        .forEach(test => console.log(`   - ${test.name}: ${test.error}`));
    }
    
    console.log('\n🎉 Test suite complete!');
  }
}

// Export test runner
export const runEnterpriseSystemTests = async () => {
  const testSuite = new TWLEnterpriseSystemTest();
  return await testSuite.runAllTests();
};

// Auto-run tests in development
if (process.env.NODE_ENV === 'development') {
  runEnterpriseSystemTests().catch(console.error);
}
