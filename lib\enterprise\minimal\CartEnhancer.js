/**
 * TWL Minimal Cart Enhancer
 * 
 * This is a lightweight, non-breaking enhancement layer that adds enterprise features
 * to the existing cart without replacing or modifying the core cart functionality.
 * 
 * ✅ Zero breaking changes
 * ✅ Optional enhancement
 * ✅ Backward compatible
 * ✅ Easy to enable/disable
 */

class CartEnhancer {
  constructor() {
    this.isEnabled = true
    this.cache = new Map()
    this.metrics = {
      enhancedProducts: 0,
      fallbackProducts: 0,
      cacheHits: 0,
      errors: 0
    }
  }

  /**
   * Enhance a product with enterprise data (non-breaking)
   * Falls back to original product if enhancement fails
   */
  async enhanceProduct(originalProduct) {
    if (!this.isEnabled || !originalProduct) {
      return originalProduct
    }

    try {
      // Check cache first
      const cacheKey = `product_${originalProduct.id}`
      if (this.cache.has(cacheKey)) {
        this.metrics.cacheHits++
        return this.cache.get(cacheKey)
      }

      // Try to enhance with enterprise data
      const enhanced = await this.tryEnhanceProduct(originalProduct)
      
      if (enhanced) {
        // Cache the enhanced product
        this.cache.set(cache<PERSON>ey, enhanced)
        this.metrics.enhancedProducts++
        
        console.log(`✨ Enhanced product: ${originalProduct.name}`)
        return enhanced
      }

      // Fallback to original
      this.metrics.fallbackProducts++
      return originalProduct

    } catch (error) {
      this.metrics.errors++
      console.warn(`⚠️ Enhancement failed for ${originalProduct.id}, using fallback:`, error.message)
      return originalProduct
    }
  }

  /**
   * Try to enhance product with enterprise data
   */
  async tryEnhanceProduct(product) {
    try {
      // Try to load from enterprise API
      const response = await fetch(`/api/enterprise/products/${product.id}`)
      
      if (response.ok) {
        const data = await response.json()
        
        if (data.success && data.data) {
          // Merge enterprise data with original product (non-destructive)
          return {
            ...product, // Keep all original fields
            
            // Add enterprise enhancements
            originalPrice: data.data.originalPrice || product.price,
            discountPercent: data.data.discountPercent || 0,
            isOnSale: data.data.isOnSale || false,
            isLimitedEdition: data.data.isLimitedEdition || false,
            stockLevel: data.data.stockLevel || 10,
            rating: data.data.rating || 4.0,
            reviewCount: data.data.reviewCount || 0,
            
            // Enterprise metadata (doesn't interfere with existing code)
            _enhanced: true,
            _source: 'enterprise',
            _loadedAt: new Date().toISOString()
          }
        }
      }

      return null // Enhancement failed, will use fallback
    } catch (error) {
      console.warn('Enterprise enhancement failed:', error.message)
      return null
    }
  }

  /**
   * Enhance cart summary with additional data (non-breaking)
   */
  enhanceCartSummary(originalSummary, cartItems) {
    if (!this.isEnabled) {
      return originalSummary
    }

    try {
      // Calculate enhanced metrics
      const enhancedItems = cartItems.filter(item => item._enhanced)
      const totalSavings = cartItems.reduce((sum, item) => {
        if (item.originalPrice && item.originalPrice > item.price) {
          return sum + ((item.originalPrice - item.price) * (item.quantity || 1))
        }
        return sum
      }, 0)

      const limitedEditionCount = cartItems.filter(item => item.isLimitedEdition).length

      // Return enhanced summary (non-destructive)
      return {
        ...originalSummary, // Keep all original fields
        
        // Add enterprise enhancements
        totalSavings: Math.round(totalSavings),
        enhancedItemsCount: enhancedItems.length,
        limitedEditionCount: limitedEditionCount,
        hasEnhancements: enhancedItems.length > 0,
        
        // Enterprise metadata
        _enhanced: true,
        _enhancedAt: new Date().toISOString()
      }
    } catch (error) {
      console.warn('Summary enhancement failed:', error.message)
      return originalSummary
    }
  }

  /**
   * Get simple product recommendations (non-breaking)
   */
  async getRecommendations(cartItems, limit = 4) {
    if (!this.isEnabled || !cartItems.length) {
      return []
    }

    try {
      // Simple recommendation logic
      const brands = [...new Set(cartItems.map(item => item.brand).filter(Boolean))]
      
      if (brands.length === 0) {
        return []
      }

      // Try enterprise API for recommendations
      const params = new URLSearchParams({
        brands: brands.slice(0, 2).join(','),
        inStockOnly: 'true',
        pageSize: limit.toString()
      })

      const response = await fetch(`/api/enterprise/products?${params}`)
      
      if (response.ok) {
        const data = await response.json()
        
        if (data.success && data.data.products) {
          // Filter out products already in cart
          const cartProductIds = cartItems.map(item => item.id)
          const recommendations = data.data.products
            .filter(product => !cartProductIds.includes(product.id))
            .slice(0, limit)

          console.log(`💡 Found ${recommendations.length} recommendations`)
          return recommendations
        }
      }

      return []
    } catch (error) {
      console.warn('Recommendations failed:', error.message)
      return []
    }
  }

  /**
   * Validate cart items (non-breaking)
   */
  async validateCartItems(cartItems) {
    if (!this.isEnabled || !cartItems.length) {
      return {}
    }

    try {
      const validationResults = {}

      for (const item of cartItems) {
        try {
          const enhanced = await this.enhanceProduct(item)
          
          validationResults[item.id] = {
            isValid: enhanced.stockLevel > 0,
            hasStockIssue: enhanced.stockLevel < (item.quantity || 1),
            hasPriceChange: enhanced.price !== item.price,
            currentPrice: enhanced.price,
            stockLevel: enhanced.stockLevel,
            isLowStock: enhanced.stockLevel < 5
          }
        } catch (error) {
          validationResults[item.id] = {
            isValid: true, // Assume valid if validation fails
            hasError: true,
            error: error.message
          }
        }
      }

      return validationResults
    } catch (error) {
      console.warn('Cart validation failed:', error.message)
      return {}
    }
  }

  /**
   * Get enhancement metrics
   */
  getMetrics() {
    const total = this.metrics.enhancedProducts + this.metrics.fallbackProducts
    
    return {
      ...this.metrics,
      totalProducts: total,
      enhancementRate: total > 0 ? ((this.metrics.enhancedProducts / total) * 100).toFixed(1) : 0,
      cacheSize: this.cache.size,
      isEnabled: this.isEnabled
    }
  }

  /**
   * Enable/disable enhancements
   */
  setEnabled(enabled) {
    this.isEnabled = enabled
    console.log(`🔧 Cart enhancements ${enabled ? 'enabled' : 'disabled'}`)
  }

  /**
   * Clear cache
   */
  clearCache() {
    this.cache.clear()
    console.log('🗑️ Enhancement cache cleared')
  }

  /**
   * Reset metrics
   */
  resetMetrics() {
    this.metrics = {
      enhancedProducts: 0,
      fallbackProducts: 0,
      cacheHits: 0,
      errors: 0
    }
    console.log('📊 Enhancement metrics reset')
  }
}

// Create singleton instance
const cartEnhancer = new CartEnhancer()

// Auto-initialize (safe, non-breaking)
if (typeof window !== 'undefined') {
  // Check if enterprise API is available
  fetch('/api/enterprise/system/status')
    .then(response => {
      if (!response.ok) {
        console.log('🔄 Enterprise API not available, enhancements disabled')
        cartEnhancer.setEnabled(false)
      } else {
        console.log('✅ Enterprise enhancements available')
      }
    })
    .catch(() => {
      console.log('🔄 Enterprise API check failed, enhancements disabled')
      cartEnhancer.setEnabled(false)
    })
}

export default cartEnhancer
export { CartEnhancer }
