/**
 * 🎯 MOBILE GESTURE UTILITIES - THE MAESTRO'S TOUCH LIBRARY
 * 
 * Advanced gesture recognition and touch interaction utilities
 * Built for cutting-edge mobile experiences with precision and performance
 * 
 * Features:
 * - Multi-touch gesture recognition
 * - Velocity-based momentum calculations
 * - Pressure-sensitive interactions
 * - Advanced haptic feedback patterns
 * - Performance-optimized event handling
 * - Cross-platform compatibility
 */

export interface TouchPoint {
  x: number
  y: number
  timestamp: number
  force?: number
  identifier?: number
}

export interface GestureConfig {
  threshold: number
  velocityThreshold: number
  timeThreshold: number
  enableHaptics: boolean
  enableMomentum: boolean
  resistanceMultiplier: number
}

export interface SwipeGesture {
  direction: 'left' | 'right' | 'up' | 'down'
  distance: number
  velocity: number
  duration: number
  startPoint: TouchPoint
  endPoint: TouchPoint
}

export interface PinchGesture {
  scale: number
  velocity: number
  center: TouchPoint
  startDistance: number
  currentDistance: number
}

export interface RotationGesture {
  angle: number
  velocity: number
  center: TouchPoint
  startAngle: number
  currentAngle: number
}

// Default configuration for optimal mobile UX
export const DEFAULT_GESTURE_CONFIG: GestureConfig = {
  threshold: 50,
  velocityThreshold: 0.3,
  timeThreshold: 300,
  enableHaptics: true,
  enableMomentum: true,
  resistanceMultiplier: 0.3
}

/**
 * Advanced haptic feedback patterns for different interaction types
 */
export class HapticEngine {
  private static patterns = {
    light: [10],
    medium: [20],
    heavy: [30, 10, 30],
    success: [10, 50, 10],
    error: [100, 50, 100],
    selection: [5, 10, 5],
    impact: [15],
    notification: [10, 100, 10],
    warning: [50, 50, 50],
    heartbeat: [25, 25, 25, 25],
    tick: [5],
    click: [10, 5, 10]
  }

  static trigger(pattern: keyof typeof HapticEngine.patterns, intensity: number = 1) {
    try {
      if ('vibrate' in navigator && navigator.vibrate) {
        const basePattern = HapticEngine.patterns[pattern]
        const adjustedPattern = basePattern.map(duration => Math.round(duration * intensity))
        navigator.vibrate(adjustedPattern)
      }
    } catch (error) {
      // Silently fail if haptics not supported
      console.debug('Haptic feedback not supported:', error)
    }
  }

  static isSupported(): boolean {
    return 'vibrate' in navigator && typeof navigator.vibrate === 'function'
  }
}

/**
 * Calculate distance between two points
 */
export function calculateDistance(point1: TouchPoint, point2: TouchPoint): number {
  const dx = point2.x - point1.x
  const dy = point2.y - point1.y
  return Math.sqrt(dx * dx + dy * dy)
}

/**
 * Calculate velocity between two points
 */
export function calculateVelocity(point1: TouchPoint, point2: TouchPoint): { vx: number; vy: number; magnitude: number } {
  const timeDiff = point2.timestamp - point1.timestamp
  if (timeDiff === 0) return { vx: 0, vy: 0, magnitude: 0 }

  const vx = (point2.x - point1.x) / timeDiff
  const vy = (point2.y - point1.y) / timeDiff
  const magnitude = Math.sqrt(vx * vx + vy * vy)

  return { vx, vy, magnitude }
}

/**
 * Calculate angle between two points
 */
export function calculateAngle(point1: TouchPoint, point2: TouchPoint): number {
  return Math.atan2(point2.y - point1.y, point2.x - point1.x) * (180 / Math.PI)
}

/**
 * Detect swipe gesture from touch points
 */
export function detectSwipe(
  startPoint: TouchPoint, 
  endPoint: TouchPoint, 
  config: Partial<GestureConfig> = {}
): SwipeGesture | null {
  const finalConfig = { ...DEFAULT_GESTURE_CONFIG, ...config }
  
  const distance = calculateDistance(startPoint, endPoint)
  const duration = endPoint.timestamp - startPoint.timestamp
  const velocity = calculateVelocity(startPoint, endPoint)
  
  // Check if gesture meets thresholds
  if (distance < finalConfig.threshold || duration > finalConfig.timeThreshold) {
    return null
  }
  
  // Determine direction
  const deltaX = endPoint.x - startPoint.x
  const deltaY = endPoint.y - startPoint.y
  const absX = Math.abs(deltaX)
  const absY = Math.abs(deltaY)
  
  let direction: SwipeGesture['direction']
  
  // Use velocity for fast swipes, distance for slower ones
  if (velocity.magnitude > finalConfig.velocityThreshold) {
    direction = Math.abs(velocity.vx) > Math.abs(velocity.vy)
      ? (velocity.vx > 0 ? 'right' : 'left')
      : (velocity.vy > 0 ? 'down' : 'up')
  } else {
    direction = absX > absY
      ? (deltaX > 0 ? 'right' : 'left')
      : (deltaY > 0 ? 'down' : 'up')
  }
  
  return {
    direction,
    distance,
    velocity: velocity.magnitude,
    duration,
    startPoint,
    endPoint
  }
}

/**
 * Detect pinch gesture from two touch points
 */
export function detectPinch(
  touch1Start: TouchPoint,
  touch1Current: TouchPoint,
  touch2Start: TouchPoint,
  touch2Current: TouchPoint
): PinchGesture {
  const startDistance = calculateDistance(touch1Start, touch2Start)
  const currentDistance = calculateDistance(touch1Current, touch2Current)
  
  const scale = currentDistance / startDistance
  
  // Calculate center point
  const center: TouchPoint = {
    x: (touch1Current.x + touch2Current.x) / 2,
    y: (touch1Current.y + touch2Current.y) / 2,
    timestamp: Math.max(touch1Current.timestamp, touch2Current.timestamp)
  }
  
  // Calculate velocity (simplified)
  const timeDiff = center.timestamp - Math.max(touch1Start.timestamp, touch2Start.timestamp)
  const velocity = timeDiff > 0 ? (currentDistance - startDistance) / timeDiff : 0
  
  return {
    scale,
    velocity,
    center,
    startDistance,
    currentDistance
  }
}

/**
 * Detect rotation gesture from two touch points
 */
export function detectRotation(
  touch1Start: TouchPoint,
  touch1Current: TouchPoint,
  touch2Start: TouchPoint,
  touch2Current: TouchPoint
): RotationGesture {
  const startAngle = calculateAngle(touch1Start, touch2Start)
  const currentAngle = calculateAngle(touch1Current, touch2Current)
  
  let angle = currentAngle - startAngle
  
  // Normalize angle to -180 to 180 degrees
  while (angle > 180) angle -= 360
  while (angle < -180) angle += 360
  
  // Calculate center point
  const center: TouchPoint = {
    x: (touch1Current.x + touch2Current.x) / 2,
    y: (touch1Current.y + touch2Current.y) / 2,
    timestamp: Math.max(touch1Current.timestamp, touch2Current.timestamp)
  }
  
  // Calculate velocity (simplified)
  const timeDiff = center.timestamp - Math.max(touch1Start.timestamp, touch2Start.timestamp)
  const velocity = timeDiff > 0 ? angle / timeDiff : 0
  
  return {
    angle,
    velocity,
    center,
    startAngle,
    currentAngle
  }
}

/**
 * Advanced momentum calculation for smooth animations
 */
export function calculateMomentum(
  velocity: { vx: number; vy: number },
  resistance: number = 0.95,
  threshold: number = 0.1
): { x: number; y: number; duration: number } {
  const magnitude = Math.sqrt(velocity.vx * velocity.vx + velocity.vy * velocity.vy)
  
  if (magnitude < threshold) {
    return { x: 0, y: 0, duration: 0 }
  }
  
  // Calculate momentum distance with resistance
  const momentumMultiplier = 100
  const x = velocity.vx * momentumMultiplier * resistance
  const y = velocity.vy * momentumMultiplier * resistance
  
  // Calculate duration based on velocity
  const duration = Math.min(Math.max(magnitude * 500, 200), 1000)
  
  return { x, y, duration }
}

/**
 * Pressure-sensitive interaction utilities
 */
export class PressureEngine {
  static normalizeForce(force: number): number {
    // Normalize force to 0-1 range (typical range is 0-1 on supported devices)
    return Math.min(Math.max(force, 0), 1)
  }
  
  static getPressureLevel(force: number): 'light' | 'medium' | 'heavy' {
    const normalized = PressureEngine.normalizeForce(force)
    
    if (normalized < 0.3) return 'light'
    if (normalized < 0.7) return 'medium'
    return 'heavy'
  }
  
  static isForceSupported(): boolean {
    // Check if device supports force touch
    return 'ontouchforcechange' in window || 'webkitForce' in TouchEvent.prototype
  }
}

/**
 * Device capability detection
 */
export class DeviceCapabilities {
  static isTouchDevice(): boolean {
    return 'ontouchstart' in window || navigator.maxTouchPoints > 0
  }
  
  static isHighDPI(): boolean {
    return window.devicePixelRatio > 1
  }
  
  static supportsPassiveEvents(): boolean {
    let supportsPassive = false
    try {
      const opts = Object.defineProperty({}, 'passive', {
        get: () => { supportsPassive = true }
      })
      window.addEventListener('testPassive', () => {}, opts)
      window.removeEventListener('testPassive', () => {}, opts)
    } catch (e) {}
    return supportsPassive
  }
  
  static getScreenInfo() {
    return {
      width: window.screen.width,
      height: window.screen.height,
      availWidth: window.screen.availWidth,
      availHeight: window.screen.availHeight,
      pixelRatio: window.devicePixelRatio,
      orientation: window.screen.orientation?.type || 'unknown'
    }
  }
  
  static isIOS(): boolean {
    return /iPad|iPhone|iPod/.test(navigator.userAgent)
  }
  
  static isAndroid(): boolean {
    return /Android/.test(navigator.userAgent)
  }
  
  static isSafari(): boolean {
    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent)
  }
}

/**
 * Performance-optimized event listener utilities
 */
export class OptimizedEventManager {
  private static listeners = new Map<string, Set<EventListener>>()
  
  static addListener(
    element: Element | Window | Document,
    event: string,
    handler: EventListener,
    options?: AddEventListenerOptions
  ) {
    const key = `${event}-${element.constructor.name}`
    
    if (!OptimizedEventManager.listeners.has(key)) {
      OptimizedEventManager.listeners.set(key, new Set())
    }
    
    OptimizedEventManager.listeners.get(key)!.add(handler)
    
    // Use passive listeners for better performance where appropriate
    const passiveEvents = ['touchstart', 'touchmove', 'wheel', 'scroll']
    const finalOptions = {
      passive: passiveEvents.includes(event),
      ...options
    }
    
    element.addEventListener(event, handler, finalOptions)
  }
  
  static removeListener(
    element: Element | Window | Document,
    event: string,
    handler: EventListener
  ) {
    const key = `${event}-${element.constructor.name}`
    const listeners = OptimizedEventManager.listeners.get(key)
    
    if (listeners) {
      listeners.delete(handler)
      if (listeners.size === 0) {
        OptimizedEventManager.listeners.delete(key)
      }
    }
    
    element.removeEventListener(event, handler)
  }
  
  static cleanup() {
    OptimizedEventManager.listeners.clear()
  }
}
