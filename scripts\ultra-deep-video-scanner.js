#!/usr/bin/env node

/**
 * ULTRA DEEP CYTTE VIDEO SCANNER & OPTIMIZER
 * 
 * This script performs:
 * 1. Ultra-deep recursive scan of ALL CYTTE folders/subfolders
 * 2. Finds every single video file (.mp4, .mov, .avi, .webm, .mkv, .m4v)
 * 3. Copies videos to public/products with organized structure
 * 4. Optimizes videos for web (compression, format conversion)
 * 5. Generates thumbnails and multiple formats
 * 6. Creates comprehensive video database
 * 
 * Requirements: FFmpeg (now installed)
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CYTTE_BASE_PATH = path.join(process.cwd(), '--materials', 'shoes', '2. CYTTE');
const PUBLIC_VIDEOS_PATH = path.join(process.cwd(), 'public', 'products');
const VIDEO_DATABASE_PATH = path.join(process.cwd(), 'lib', 'data', 'cytte-videos.json');

// Video file extensions to scan for
const VIDEO_EXTENSIONS = ['.mp4', '.mov', '.avi', '.webm', '.mkv', '.m4v', '.flv', '.wmv'];

// Optimization settings
const OPTIMIZATION_CONFIG = {
  // Quality settings
  crf: 23,              // Lower = better quality (18-28 range)
  maxWidth: 1920,       // Max width for desktop
  maxHeight: 1080,      // Max height
  mobileMaxWidth: 720,  // Max width for mobile version
  mobileMaxHeight: 480, // Max height for mobile
  fps: 30,              // Frame rate
  
  // Compression
  preset: 'medium',     // FFmpeg preset
  audioCodec: 'aac',
  audioBitrate: '128k',
  
  // Output formats
  formats: ['mp4', 'webm'], // Multiple formats for browser compatibility
  
  // Thumbnail settings
  thumbnailTime: '00:00:02',
  thumbnailFormat: 'webp',
  thumbnailSizes: [400, 200, 100] // Different thumbnail sizes
};

class UltraDeepVideoScanner {
  constructor() {
    this.allVideos = [];
    this.processedVideos = [];
    this.errors = [];
    this.totalScanned = 0;
    this.totalFound = 0;
    this.totalProcessed = 0;
    this.totalSizeReduction = 0;
    this.startTime = Date.now();
  }

  async initialize() {
    console.log('🎬 ULTRA DEEP CYTTE VIDEO SCANNER & OPTIMIZER');
    console.log('==============================================');
    console.log(`📁 Scanning: ${CYTTE_BASE_PATH}`);
    console.log(`📦 Output: ${PUBLIC_VIDEOS_PATH}`);
    
    // Verify FFmpeg installation
    try {
      // Try different FFmpeg paths
      let ffmpegCommand = 'ffmpeg';
      try {
        execSync('ffmpeg -version', { encoding: 'utf8', stdio: 'ignore' });
      } catch {
        // Try common installation paths
        const possiblePaths = [
          'C:\\ffmpeg\\bin\\ffmpeg.exe',
          'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
          '%USERPROFILE%\\AppData\\Local\\Microsoft\\WinGet\\Packages\\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\\ffmpeg-7.1.1-full_build\\bin\\ffmpeg.exe'
        ];

        for (const testPath of possiblePaths) {
          try {
            execSync(`"${testPath}" -version`, { encoding: 'utf8', stdio: 'ignore' });
            ffmpegCommand = `"${testPath}"`;
            break;
          } catch {
            continue;
          }
        }
      }

      const ffmpegVersion = execSync(`${ffmpegCommand} -version`, { encoding: 'utf8' });
      console.log('✅ FFmpeg detected:', ffmpegVersion.split('\n')[0]);

      // Store the working command for later use
      this.ffmpegCommand = ffmpegCommand;

    } catch (error) {
      console.error('❌ FFmpeg not found. Please restart your terminal after FFmpeg installation.');
      console.error('Or manually add FFmpeg to your PATH environment variable.');
      process.exit(1);
    }

    // Create directories
    await this.createDirectories();
  }

  async createDirectories() {
    const dirs = [
      PUBLIC_VIDEOS_PATH,
      path.dirname(VIDEO_DATABASE_PATH)
    ];

    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`✅ Directory ready: ${path.relative(process.cwd(), dir)}`);
      } catch (error) {
        console.log(`📁 Directory exists: ${path.relative(process.cwd(), dir)}`);
      }
    }
  }

  async ultraDeepScan() {
    console.log('\n🔍 Starting ultra-deep scan...');
    console.log('Scanning ALL folders, subfolders, and sub-subfolders...');
    
    try {
      await this.scanDirectory(CYTTE_BASE_PATH, 0);
      
      console.log(`\n📊 SCAN COMPLETE!`);
      console.log(`📁 Folders scanned: ${this.totalScanned}`);
      console.log(`🎬 Videos found: ${this.totalFound}`);
      console.log(`📦 Total size: ${this.calculateTotalSize()} MB`);
      
      return this.allVideos;
      
    } catch (error) {
      console.error('❌ Error during ultra-deep scan:', error);
      throw error;
    }
  }

  async scanDirectory(dirPath, depth) {
    try {
      this.totalScanned++;
      
      // Show progress every 50 folders
      if (this.totalScanned % 50 === 0) {
        console.log(`   📁 Scanned ${this.totalScanned} folders, found ${this.totalFound} videos...`);
      }
      
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item.name);
        
        if (item.isDirectory()) {
          // Recursively scan subdirectories (unlimited depth)
          await this.scanDirectory(fullPath, depth + 1);
        } else if (item.isFile() && this.isVideoFile(item.name)) {
          // Found a video file!
          await this.processFoundVideo(fullPath, dirPath);
        }
      }
    } catch (error) {
      console.error(`❌ Error scanning ${dirPath}:`, error.message);
      this.errors.push({ path: dirPath, error: error.message });
    }
  }

  isVideoFile(fileName) {
    const ext = path.extname(fileName).toLowerCase();
    return VIDEO_EXTENSIONS.includes(ext);
  }

  async processFoundVideo(videoPath, parentDir) {
    try {
      const stats = await fs.stat(videoPath);
      const videoInfo = this.extractVideoInfo(videoPath, parentDir);
      
      const video = {
        id: `video-${this.totalFound + 1}`,
        sourcePath: videoPath,
        fileName: path.basename(videoPath),
        fileSize: stats.size,
        fileSizeMB: Math.round(stats.size / (1024 * 1024) * 100) / 100,
        ...videoInfo,
        processed: false,
        optimizedPaths: {},
        thumbnails: {},
        createdAt: new Date().toISOString()
      };
      
      this.allVideos.push(video);
      this.totalFound++;
      
      console.log(`🎬 Found: ${video.fileName} (${video.fileSizeMB}MB) - ${video.brand} ${video.model}`);
      
    } catch (error) {
      console.error(`❌ Error processing video ${videoPath}:`, error.message);
      this.errors.push({ path: videoPath, error: error.message });
    }
  }

  extractVideoInfo(videoPath, parentDir) {
    // Extract detailed product information from path structure
    const pathParts = videoPath.split(path.sep);
    const relativePath = path.relative(CYTTE_BASE_PATH, videoPath);
    const relativePathParts = relativePath.split(path.sep);
    
    let category = 'unknown';
    let brand = 'unknown';
    let model = 'unknown';
    let gender = 'unisex';
    let collaboration = null;
    let sku = 'unknown';
    let productName = path.parse(videoPath).name;
    
    // Extract category (1. SNEAKERS, 2. SANDALS, etc.)
    if (relativePathParts[0]) {
      const categoryMatch = relativePathParts[0].match(/\d+\.\s*(.+)/);
      if (categoryMatch) {
        category = categoryMatch[1].toLowerCase().replace(/\s+/g, '-');
      }
    }
    
    // Extract brand information
    for (const part of relativePathParts) {
      if (part.includes('NIKE')) brand = 'nike';
      else if (part.includes('ADIDAS')) brand = 'adidas';
      else if (part.includes('GUCCI')) brand = 'gucci';
      else if (part.includes('DIOR')) brand = 'dior';
      else if (part.includes('LV')) brand = 'louis-vuitton';
      else if (part.includes('BALENCIAGA')) brand = 'balenciaga';
      else if (part.includes('CHANEL')) brand = 'chanel';
      else if (part.includes('HERMES')) brand = 'hermes';
      else if (part.includes('PRADA')) brand = 'prada';
      else if (part.includes('VALENTINO')) brand = 'valentino';
      else if (part.includes('GIVENCHY')) brand = 'givenchy';
      else if (part.includes('MARGIELA')) brand = 'maison-margiela';
      else if (part.includes('OFF WHITE')) brand = 'off-white';
      else if (part.includes('LOUBOUTIN')) brand = 'christian-louboutin';
      
      // Extract model information
      if (part.includes('AIR FORCE')) model = 'air-force';
      else if (part.includes('AIR JORDAN')) model = 'air-jordan';
      else if (part.includes('DUNK')) model = 'dunk';
      else if (part.includes('BLAZER')) model = 'blazer';
      else if (part.includes('AIR MAX')) model = 'air-max';
      else if (part.includes('CORTEZ')) model = 'cortez';
      else if (part.includes('GAZELLE')) model = 'gazelle';
      
      // Extract gender
      if (part.includes('MIXTE')) gender = 'unisex';
      else if (part.includes('WOMEN')) gender = 'women';
      else if (part.includes('MEN')) gender = 'men';
      
      // Extract collaboration info
      if (part.includes('x ') || part.includes(' x ')) {
        collaboration = part.toLowerCase().replace(/\s+/g, '-');
      }
      
      // Extract SKU pattern
      const skuMatch = part.match(/^([A-Z0-9]+-[A-Z0-9]+)/);
      if (skuMatch) {
        sku = skuMatch[1].toLowerCase();
        const nameParts = part.split(' -- ');
        if (nameParts.length > 1) {
          productName = nameParts.slice(1).join(' ');
        }
      }
    }
    
    return {
      category,
      brand,
      model,
      gender,
      collaboration,
      sku,
      productName,
      relativePath,
      pathDepth: relativePathParts.length
    };
  }

  calculateTotalSize() {
    return Math.round(
      this.allVideos.reduce((total, video) => total + video.fileSizeMB, 0) * 100
    ) / 100;
  }

  async optimizeAllVideos() {
    console.log('\n🚀 Starting video optimization process...');
    console.log(`Processing ${this.allVideos.length} videos...`);
    
    for (let i = 0; i < this.allVideos.length; i++) {
      const video = this.allVideos[i];
      console.log(`\n📹 Processing ${i + 1}/${this.allVideos.length}: ${video.fileName}`);
      
      try {
        await this.optimizeVideo(video);
        this.totalProcessed++;
      } catch (error) {
        console.error(`❌ Failed to optimize ${video.fileName}:`, error.message);
        this.errors.push({ video: video.fileName, error: error.message });
      }
      
      // Show progress
      const progress = Math.round((i + 1) / this.allVideos.length * 100);
      console.log(`📊 Progress: ${progress}% (${this.totalProcessed} processed, ${this.errors.length} errors)`);
    }
  }

  async optimizeVideo(video) {
    const outputDir = this.generateOutputPath(video);
    await fs.mkdir(outputDir, { recursive: true });
    
    const originalSize = video.fileSizeMB;
    
    // Generate optimized versions
    for (const format of OPTIMIZATION_CONFIG.formats) {
      // Desktop version
      const desktopPath = path.join(outputDir, `${video.sku}-desktop.${format}`);
      await this.convertVideo(video.sourcePath, desktopPath, format, 'desktop');
      
      // Mobile version
      const mobilePath = path.join(outputDir, `${video.sku}-mobile.${format}`);
      await this.convertVideo(video.sourcePath, mobilePath, format, 'mobile');
      
      video.optimizedPaths[format] = {
        desktop: path.relative(PUBLIC_VIDEOS_PATH, desktopPath),
        mobile: path.relative(PUBLIC_VIDEOS_PATH, mobilePath)
      };
    }
    
    // Generate thumbnails
    for (const size of OPTIMIZATION_CONFIG.thumbnailSizes) {
      const thumbPath = path.join(outputDir, `${video.sku}-thumb-${size}.${OPTIMIZATION_CONFIG.thumbnailFormat}`);
      await this.generateThumbnail(video.sourcePath, thumbPath, size);
      
      video.thumbnails[`${size}px`] = path.relative(PUBLIC_VIDEOS_PATH, thumbPath);
    }
    
    // Calculate size reduction
    const newSize = await this.calculateOptimizedSize(outputDir);
    const reduction = originalSize - newSize;
    this.totalSizeReduction += reduction;
    
    video.processed = true;
    video.optimizedSizeMB = newSize;
    video.sizeReductionMB = reduction;
    video.sizeReductionPercent = Math.round((reduction / originalSize) * 100);
    
    console.log(`   ✅ Optimized: ${originalSize}MB → ${newSize}MB (${video.sizeReductionPercent}% reduction)`);
  }

  generateOutputPath(video) {
    // Create organized folder structure
    return path.join(
      PUBLIC_VIDEOS_PATH,
      video.category,
      video.brand,
      video.model || 'general',
      video.sku
    );
  }

  async convertVideo(inputPath, outputPath, format, version) {
    const config = OPTIMIZATION_CONFIG;
    const isDesktop = version === 'desktop';
    const maxWidth = isDesktop ? config.maxWidth : config.mobileMaxWidth;
    const maxHeight = isDesktop ? config.maxHeight : config.mobileMaxHeight;
    
    let command;
    
    if (format === 'mp4') {
      command = `${this.ffmpegCommand} -i "${inputPath}" -c:v libx264 -crf ${config.crf} -preset ${config.preset} -vf "scale='min(${maxWidth},iw)':'min(${maxHeight},ih)':force_original_aspect_ratio=decrease" -r ${config.fps} -c:a ${config.audioCodec} -b:a ${config.audioBitrate} -movflags +faststart -y "${outputPath}"`;
    } else if (format === 'webm') {
      command = `${this.ffmpegCommand} -i "${inputPath}" -c:v libvpx-vp9 -crf ${config.crf} -vf "scale='min(${maxWidth},iw)':'min(${maxHeight},ih)':force_original_aspect_ratio=decrease" -r ${config.fps} -c:a libopus -b:a ${config.audioBitrate} -y "${outputPath}"`;
    }
    
    execSync(command, { stdio: 'ignore' });
  }

  async generateThumbnail(inputPath, outputPath, size) {
    const command = `${this.ffmpegCommand} -i "${inputPath}" -ss ${OPTIMIZATION_CONFIG.thumbnailTime} -vframes 1 -vf "scale=${size}:${size}:force_original_aspect_ratio=decrease" -y "${outputPath}"`;
    execSync(command, { stdio: 'ignore' });
  }

  async calculateOptimizedSize(outputDir) {
    try {
      const files = await fs.readdir(outputDir);
      let totalSize = 0;
      
      for (const file of files) {
        const filePath = path.join(outputDir, file);
        const stats = await fs.stat(filePath);
        totalSize += stats.size;
      }
      
      return Math.round(totalSize / (1024 * 1024) * 100) / 100;
    } catch (error) {
      return 0;
    }
  }

  async saveVideoDatabase() {
    console.log('\n💾 Saving video database...');
    
    const database = {
      metadata: {
        totalVideos: this.allVideos.length,
        processedVideos: this.totalProcessed,
        totalSizeReductionMB: Math.round(this.totalSizeReduction * 100) / 100,
        scanDurationMs: Date.now() - this.startTime,
        lastUpdated: new Date().toISOString(),
        errors: this.errors.length
      },
      videos: this.allVideos,
      errors: this.errors
    };
    
    await fs.writeFile(VIDEO_DATABASE_PATH, JSON.stringify(database, null, 2));
    console.log(`✅ Database saved: ${VIDEO_DATABASE_PATH}`);
  }

  printFinalSummary() {
    const duration = Math.round((Date.now() - this.startTime) / 1000);
    
    console.log('\n🎉 ULTRA DEEP SCAN COMPLETE!');
    console.log('============================');
    console.log(`⏱️  Duration: ${duration} seconds`);
    console.log(`📁 Folders scanned: ${this.totalScanned}`);
    console.log(`🎬 Videos found: ${this.totalFound}`);
    console.log(`✅ Videos processed: ${this.totalProcessed}`);
    console.log(`💾 Size reduction: ${Math.round(this.totalSizeReduction * 100) / 100} MB`);
    console.log(`❌ Errors: ${this.errors.length}`);
    
    if (this.totalFound > 0) {
      console.log('\n📊 BREAKDOWN BY BRAND:');
      const brandCounts = {};
      this.allVideos.forEach(video => {
        brandCounts[video.brand] = (brandCounts[video.brand] || 0) + 1;
      });
      
      Object.entries(brandCounts)
        .sort(([,a], [,b]) => b - a)
        .forEach(([brand, count]) => {
          console.log(`   ${brand}: ${count} videos`);
        });
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ ERRORS ENCOUNTERED:');
      this.errors.slice(0, 10).forEach(error => {
        console.log(`   ${error.video || error.path}: ${error.error}`);
      });
      if (this.errors.length > 10) {
        console.log(`   ... and ${this.errors.length - 10} more errors`);
      }
    }
    
    console.log('\n🚀 Videos are now optimized and ready for fast web playback!');
  }
}

// Main execution
async function main() {
  const scanner = new UltraDeepVideoScanner();
  
  try {
    await scanner.initialize();
    
    // Ultra-deep scan
    await scanner.ultraDeepScan();
    
    if (scanner.allVideos.length === 0) {
      console.log('ℹ️  No videos found in CYTTE catalog');
      return;
    }
    
    // Optimize all videos
    await scanner.optimizeAllVideos();
    
    // Save database
    await scanner.saveVideoDatabase();
    
    // Print summary
    scanner.printFinalSummary();
    
  } catch (error) {
    console.error('❌ Ultra-deep scan failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = UltraDeepVideoScanner;
