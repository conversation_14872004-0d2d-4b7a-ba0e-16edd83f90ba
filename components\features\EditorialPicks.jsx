'use client'

import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'

export default function EditorialPicks() {

  // Placeholder component - will be expanded later
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[1, 2, 3].map((item) => (
        <Card key={item} variant="gradient" className="group">
          <CardContent className="p-6">
            <div className="aspect-video bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-800 rounded-lg mb-4 flex items-center justify-center">
              <span className="text-gray-500 text-sm">Editorial Pick {item}</span>
            </div>
            <h3 className="text-lg font-semibold mb-2">Editorial Pick {item}</h3>
            <p className="text-gray-600 dark:text-gray-300 text-sm mb-4">
              Curated selection showcasing the latest trends in luxury streetwear.
            </p>
            <Button variant="ghost" size="sm" className="w-full">
              Explorar
            </Button>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
