# CYTTE Video Copy Script for Windows PowerShell
# Copies all video files from CYTTE materials to public/products

Write-Host "🎬 CYTTE Video Copy Script" -ForegroundColor Cyan
Write-Host "=========================" -ForegroundColor Cyan

# Paths
$cytteBasePath = ".\--materials\shoes\2. CYTTE"
$publicPath = ".\public\products"

# Check if CYTTE folder exists
if (-not (Test-Path $cytteBasePath)) {
    Write-Host "❌ CYTTE folder not found at: $cytteBasePath" -ForegroundColor Red
    exit 1
}

# Create public products directory if it doesn't exist
if (-not (Test-Path $publicPath)) {
    New-Item -ItemType Directory -Path $publicPath -Force | Out-Null
    Write-Host "✅ Created public/products directory" -ForegroundColor Green
}

# Video file extensions to look for
$videoExtensions = @("*.mp4", "*.mov", "*.avi", "*.mkv", "*.webm", "*.m4v")

Write-Host "`n🔍 Scanning for video files..." -ForegroundColor Yellow

$videosFound = @()
$totalSize = 0

# Scan for video files recursively
foreach ($extension in $videoExtensions) {
    $files = Get-ChildItem -Path $cytteBasePath -Filter $extension -Recurse -File
    foreach ($file in $files) {
        $videosFound += $file
        $totalSize += $file.Length
    }
}

Write-Host "📊 Found $($videosFound.Count) video files" -ForegroundColor Green
Write-Host "📦 Total size: $([math]::Round($totalSize / 1MB, 2)) MB" -ForegroundColor Green

if ($videosFound.Count -eq 0) {
    Write-Host "ℹ️  No video files found in CYTTE catalog" -ForegroundColor Yellow
    exit 0
}

Write-Host "`n📹 Videos found:" -ForegroundColor Cyan
foreach ($video in $videosFound) {
    $relativePath = $video.FullName.Replace($cytteBasePath, "").TrimStart("\")
    $sizeMB = [math]::Round($video.Length / 1MB, 2)
    Write-Host "   $($video.Name) ($sizeMB MB)" -ForegroundColor White
    Write-Host "      Path: $relativePath" -ForegroundColor Gray
}

Write-Host "`n🚀 Starting video copy process..." -ForegroundColor Yellow

$copiedCount = 0
$errorCount = 0

foreach ($video in $videosFound) {
    try {
        # Extract product information from path
        $pathParts = $video.DirectoryName.Split('\')
        
        # Find brand and product info
        $brand = "unknown"
        $productSku = "unknown"
        
        foreach ($part in $pathParts) {
            if ($part -match "NIKE") { $brand = "nike" }
            elseif ($part -match "ADIDAS") { $brand = "adidas" }
            elseif ($part -match "GUCCI") { $brand = "gucci" }
            elseif ($part -match "DIOR") { $brand = "dior" }
            elseif ($part -match "LV") { $brand = "lv" }
            elseif ($part -match "BALENCIAGA") { $brand = "balenciaga" }
            
            # Look for SKU pattern
            if ($part -match "^[A-Z0-9]+-[A-Z0-9]+") {
                $productSku = $part.Split(' ')[0].ToLower()
            }
        }
        
        # Create destination path
        $destDir = Join-Path $publicPath "$brand\$productSku"
        
        # Create directory if it doesn't exist
        if (-not (Test-Path $destDir)) {
            New-Item -ItemType Directory -Path $destDir -Force | Out-Null
        }
        
        # Copy video file
        $destPath = Join-Path $destDir $video.Name
        Copy-Item -Path $video.FullName -Destination $destPath -Force
        
        Write-Host "✅ Copied: $($video.Name) -> $brand\$productSku" -ForegroundColor Green
        $copiedCount++
        
    } catch {
        Write-Host "❌ Error copying $($video.Name): $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host "`n📊 COPY SUMMARY" -ForegroundColor Cyan
Write-Host "===============" -ForegroundColor Cyan
Write-Host "Videos found: $($videosFound.Count)" -ForegroundColor White
Write-Host "Videos copied: $copiedCount" -ForegroundColor Green
Write-Host "Errors: $errorCount" -ForegroundColor $(if ($errorCount -gt 0) { "Red" } else { "Green" })

if ($copiedCount -gt 0) {
    Write-Host "`n🎉 Video copy complete!" -ForegroundColor Green
    Write-Host "Videos are now available in public/products/" -ForegroundColor Green
    Write-Host "`nNext steps:" -ForegroundColor Yellow
    Write-Host "1. Run video optimization script: node scripts/copy-and-optimize-videos.js" -ForegroundColor White
    Write-Host "2. Update product pages to include video support" -ForegroundColor White
} else {
    Write-Host "`n⚠️  No videos were copied successfully" -ForegroundColor Yellow
}

Write-Host "`nPress any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
