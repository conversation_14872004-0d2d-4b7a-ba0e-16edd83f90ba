'use client'

import { forwardRef, useState } from 'react'
import { motion, useSpring } from 'framer-motion'
import { cn } from '@/lib/utils'

const AnimatedButton = forwardRef(({ 
  className, 
  variant = 'primary', 
  size = 'default', 
  children,
  disabled = false,
  loading = false,
  onClick,
  icon = null,
  iconPosition = 'left',
  rippleColor = 'white',
  ...props 
}, ref) => {
  const [isPressed, setIsPressed] = useState(false)
  const [isHovered, setIsHovered] = useState(false)
  const [ripples, setRipples] = useState([])
  
  // Spring animations
  const springConfig = { stiffness: 400, damping: 30 }
  const scale = useSpring(1, springConfig)
  const brightness = useSpring(1, springConfig)
  const shadowIntensity = useSpring(0.2, springConfig)

  const baseClasses = `
    relative inline-flex items-center justify-center
    font-medium transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    overflow-hidden
  `

  const variants = {
    primary: `
      bg-primary
      text-white
      hover:bg-primary-dark
      focus:ring-primary/50
      shadow-lg hover:shadow-xl
    `,
    secondary: `
      bg-secondary
      text-white
      hover:bg-secondary-dark
      focus:ring-secondary/50
      shadow-lg hover:shadow-xl
    `,
    ghost: `
      bg-transparent
      text-neutral-900 dark:text-white
      hover:bg-neutral-100 dark:hover:bg-neutral-800
      focus:ring-neutral/50
    `,
    outline: `
      border-2 border-primary
      bg-transparent
      text-primary
      hover:bg-primary hover:text-white
      focus:ring-primary/50
    `,
    danger: `
      bg-error
      text-white
      hover:bg-error-dark
      focus:ring-error/50
      shadow-lg hover:shadow-xl
    `
  }

  const sizes = {
    sm: 'h-9 px-3 text-sm rounded-md',
    default: 'h-11 px-6 py-3 rounded-lg',
    lg: 'h-12 px-8 py-4 text-lg rounded-lg',
    xl: 'h-14 px-10 py-5 text-xl rounded-xl',
    icon: 'h-11 w-11 rounded-lg p-0',
  }

  const handleMouseDown = (e) => {
    if (disabled || loading) return
    
    setIsPressed(true)
    scale.set(0.95)
    brightness.set(1.1)
    shadowIntensity.set(0.4)
    
    // Create ripple effect
    const rect = e.currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top
    
    const newRipple = {
      id: Date.now(),
      x,
      y
    }
    
    setRipples(prev => [...prev, newRipple])
    
    // Remove ripple after animation
    setTimeout(() => {
      setRipples(prev => prev.filter(ripple => ripple.id !== newRipple.id))
    }, 600)
  }

  const handleMouseUp = () => {
    setIsPressed(false)
    scale.set(isHovered ? 1.02 : 1)
    brightness.set(1)
    shadowIntensity.set(isHovered ? 0.3 : 0.2)
  }

  const handleMouseEnter = () => {
    setIsHovered(true)
    scale.set(1.02)
    shadowIntensity.set(0.3)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    setIsPressed(false)
    scale.set(1)
    brightness.set(1)
    shadowIntensity.set(0.2)
  }

  const handleClick = (e) => {
    if (disabled || loading) return
    
    // Pulse effect on click
    scale.set(0.98)
    setTimeout(() => {
      scale.set(isHovered ? 1.02 : 1)
    }, 100)
    
    onClick?.(e)
  }

  return (
    <motion.button
      ref={ref}
      style={{ 
        scale,
        filter: `brightness(${brightness.get()})`,
        boxShadow: `0 ${shadowIntensity.get() * 10}px ${shadowIntensity.get() * 25}px rgba(0,0,0,${shadowIntensity.get()})`
      }}
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        className
      )}
      disabled={disabled || loading}
      onClick={handleClick}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      whileHover={{ 
        y: disabled || loading ? 0 : -1,
        transition: { duration: 0.2 }
      }}
      whileTap={{ 
        y: disabled || loading ? 0 : 1,
        transition: { duration: 0.1 }
      }}
      {...props}
    >
      {/* Ripple Effects */}
      {ripples.map(ripple => (
        <motion.div
          key={ripple.id}
          className={`absolute rounded-full pointer-events-none`}
          style={{
            left: ripple.x - 10,
            top: ripple.y - 10,
            width: 20,
            height: 20,
            backgroundColor: `${rippleColor}30`
          }}
          initial={{ scale: 0, opacity: 1 }}
          animate={{ scale: 4, opacity: 0 }}
          transition={{ duration: 0.6, ease: "easeOut" }}
        />
      ))}
      
      {/* Shimmer Effect */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent"
        initial={{ x: "-100%" }}
        animate={{
          x: isPressed ? "100%" : "-100%"
        }}
        transition={{
          duration: 0.6,
          ease: "easeInOut"
        }}
      />
      
      {/* Breathing Glow */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-rich-gold/0 via-rich-gold/10 to-rich-gold/0 rounded-lg"
        animate={{
          opacity: isHovered ? [0.3, 0.6, 0.3] : 0
        }}
        transition={{
          duration: 2,
          repeat: isHovered ? Infinity : 0,
          ease: "easeInOut"
        }}
      />
      
      {/* Content */}
      <motion.div
        className="relative z-10 flex items-center justify-center gap-2"
        animate={{
          opacity: loading ? 0.7 : 1
        }}
      >
        {/* Loading Spinner */}
        {loading && (
          <motion.div
            className="w-4 h-4 border-2 border-current border-t-transparent rounded-full"
            animate={{ rotate: 360 }}
            transition={{
              duration: 1,
              repeat: Infinity,
              ease: "linear"
            }}
          />
        )}
        
        {/* Left Icon */}
        {icon && iconPosition === 'left' && !loading && (
          <motion.span
            className="flex items-center"
            animate={{
              rotate: isPressed ? [0, -5, 5, 0] : 0
            }}
            transition={{ duration: 0.3 }}
          >
            {icon}
          </motion.span>
        )}
        
        {/* Text Content */}
        <motion.span
          animate={{
            scale: isPressed ? [1, 0.98, 1] : 1
          }}
          transition={{ duration: 0.2 }}
        >
          {children}
        </motion.span>
        
        {/* Right Icon */}
        {icon && iconPosition === 'right' && !loading && (
          <motion.span
            className="flex items-center"
            animate={{
              rotate: isPressed ? [0, 5, -5, 0] : 0
            }}
            transition={{ duration: 0.3 }}
          >
            {icon}
          </motion.span>
        )}
      </motion.div>
      
      {/* Success Pulse */}
      <motion.div
        className="absolute inset-0 bg-forest-emerald/20 rounded-lg"
        initial={{ scale: 0, opacity: 0 }}
        animate={{
          scale: isPressed ? [0, 1.2, 0] : 0,
          opacity: isPressed ? [0, 0.5, 0] : 0
        }}
        transition={{ duration: 0.4 }}
      />
      
      {/* Magnetic Field Effect */}
      <motion.div
        className="absolute inset-0 border-2 border-rich-gold/30 rounded-lg"
        animate={{
          scale: isHovered ? [1, 1.05, 1] : 1,
          opacity: isHovered ? [0.3, 0.6, 0.3] : 0
        }}
        transition={{
          duration: 1.5,
          repeat: isHovered ? Infinity : 0,
          ease: "easeInOut"
        }}
      />
    </motion.button>
  )
})

AnimatedButton.displayName = 'AnimatedButton'

export default AnimatedButton
