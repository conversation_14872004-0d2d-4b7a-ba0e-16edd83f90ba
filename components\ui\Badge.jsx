'use client'

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'

const Badge = forwardRef(({
  className,
  variant = 'default',
  size = 'default',
  pulse = false,
  children,
  ...props
}, ref) => {
  const baseClasses = 'inline-flex items-center font-medium transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2'
  
  const variants = {
    default: 'bg-platinum-silver text-jet-black dark:bg-graphite-gray dark:text-ice-white',
    primary: 'bg-primary text-jet-black shadow-neon-glow',
    secondary: 'bg-secondary text-ice-white shadow-chrome-glow',
    accent: 'bg-accent text-jet-black shadow-platinum-glow',
    success: 'bg-success text-white shadow-md',
    warning: 'bg-warning text-white shadow-md',
    error: 'bg-error text-white shadow-md',
    info: 'bg-info text-white shadow-md',
    outline: 'border border-primary text-primary dark:border-primary dark:text-primary',
    limited: 'bg-primary text-jet-black shadow-neon-glow animate-pulse',
    vip: 'bg-chrome-metallic text-ice-white shadow-chrome-glow',
    exclusive: 'bg-graphite-gray text-primary shadow-neon-glow',
    collab: 'bg-primary text-jet-black shadow-neon-glow animate-pulse-neon',
  }
  
  const sizes = {
    sm: 'px-2 py-1 text-xs rounded-md',
    default: 'px-3 py-1 text-sm rounded-lg',
    lg: 'px-4 py-2 text-base rounded-lg',
  }
  
  const pulseClasses = pulse ? 'animate-pulse-neon' : ''

  return (
    <div
      className={cn(
        baseClasses,
        variants[variant],
        sizes[size],
        pulseClasses,
        className
      )}
      ref={ref}
      {...props}
    >
      {children}
    </div>
  )
})

Badge.displayName = 'Badge'

export default Badge
