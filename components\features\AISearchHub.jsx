'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import EnhancedVoiceSearch from '@/components/features/EnhancedVoiceSearch'
import VisualSearch from '@/components/features/VisualSearch'
import SmartRecommendations from '@/components/features/SmartRecommendations'

export default function AISearchHub({ 
  onSearch,
  className = '',
  showRecommendations = true 
}) {
  const [activeModal, setActiveModal] = useState(null) // 'voice', 'visual', null
  const [searchResults, setSearchResults] = useState([])
  const [searchQuery, setSearchQuery] = useState('')

  const handleVoiceResults = (results, query) => {
    setSearchResults(results)
    setSearchQuery(query)
    if (onSearch) {
      onSearch(query, results)
    }
  }

  const handleVisualSearch = (searchTerm) => {
    setSearchQuery(searchTerm)
    if (onSearch) {
      onSearch(searchTerm)
    }
    setActiveModal(null)
  }

  const handleTextSearch = (query) => {
    setSearchQuery(query)
    if (onSearch) {
      onSearch(query)
    }
    setActiveModal(null)
  }

  const searchMethods = [
    {
      id: 'voice',
      title: 'Búsqueda por Voz',
      description: 'Di lo que buscas y nuestra IA lo encontrará',
      icon: '🎤',
      gradient: 'from-blue-500 to-purple-600',
      features: ['Reconocimiento en español', 'Procesamiento de lenguaje natural', 'Búsqueda inteligente']
    },
    {
      id: 'visual',
      title: 'Búsqueda Visual',
      description: 'Sube una foto para encontrar productos similares',
      icon: '📸',
      gradient: 'from-green-500 to-teal-600',
      features: ['Análisis de imagen con IA', 'Detección de características', 'Productos similares']
    },
    {
      id: 'smart',
      title: 'Recomendaciones IA',
      description: 'Productos personalizados para tu estilo',
      icon: '🧠',
      gradient: 'from-orange-500 to-red-600',
      features: ['Aprendizaje automático', 'Personalización avanzada', 'Tendencias en tiempo real']
    }
  ]

  return (
    <div className={`space-y-8 ${className}`}>
      
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="text-center"
      >
        <h2 className="text-3xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
          🤖 Centro de Búsqueda AI
        </h2>
        <p className="text-warm-camel text-lg max-w-3xl mx-auto">
          Descubre productos de manera inteligente con nuestras herramientas de IA avanzadas
        </p>
      </motion.div>

      {/* AI Search Methods */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {searchMethods.map((method, index) => (
          <motion.div
            key={method.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card variant="glass" className="group hover:shadow-xl transition-all duration-300 h-full">
              <CardContent className="p-6 h-full flex flex-col">
                
                {/* Icon with Gradient Background */}
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${method.gradient} flex items-center justify-center text-2xl text-white mb-4 group-hover:scale-110 transition-transform duration-300`}>
                  {method.icon}
                </div>

                {/* Content */}
                <div className="flex-1">
                  <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2 group-hover:text-rich-gold transition-colors">
                    {method.title}
                  </h3>
                  
                  <p className="text-warm-camel text-sm mb-4">
                    {method.description}
                  </p>

                  {/* Features */}
                  <ul className="space-y-1 mb-6">
                    {method.features.map((feature, featureIndex) => (
                      <li key={featureIndex} className="flex items-center gap-2 text-xs text-warm-camel">
                        <div className="w-1 h-1 bg-rich-gold rounded-full"></div>
                        {feature}
                      </li>
                    ))}
                  </ul>
                </div>

                {/* Action Button */}
                <AnimatedButton
                  variant="primary"
                  onClick={() => setActiveModal(method.id)}
                  className="w-full"
                  icon={
                    method.id === 'voice' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                      </svg>
                    ) : method.id === 'visual' ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                      </svg>
                    ) : (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    )
                  }
                >
                  {method.id === 'voice' ? 'Hablar Ahora' : 
                   method.id === 'visual' ? 'Subir Imagen' : 
                   'Ver Recomendaciones'}
                </AnimatedButton>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* AI Features Showcase */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <Card variant="glass">
          <CardContent className="p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                ⚡ Potenciado por Inteligencia Artificial
              </h3>
              <p className="text-warm-camel">
                Tecnología de vanguardia para una experiencia de compra única
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                { icon: '🎯', title: 'Precisión 94%', desc: 'En recomendaciones' },
                { icon: '⚡', title: '< 2 segundos', desc: 'Tiempo de respuesta' },
                { icon: '🌍', title: 'Español + Inglés', desc: 'Reconocimiento de voz' },
                { icon: '📊', title: '1M+ productos', desc: 'Base de datos' }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 + index * 0.1 }}
                  className="text-center"
                >
                  <div className="text-3xl mb-2">{stat.icon}</div>
                  <div className="text-lg font-bold text-forest-emerald dark:text-light-cloud-gray">
                    {stat.title}
                  </div>
                  <div className="text-sm text-warm-camel">
                    {stat.desc}
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Smart Recommendations Section */}
      {showRecommendations && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <SmartRecommendations 
            type="personalized"
            title="🤖 Recomendaciones Personalizadas por IA"
            limit={6}
          />
        </motion.div>
      )}

      {/* Search Results */}
      {searchResults.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-6"
        >
          <div className="flex items-center justify-between">
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
              🎯 Resultados de búsqueda AI
            </h3>
            <button
              onClick={() => setSearchResults([])}
              className="text-warm-camel hover:text-rich-gold transition-colors text-sm"
            >
              Limpiar resultados
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {searchResults.map((product, index) => (
              <motion.div
                key={product.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card variant="default" className="hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-4">
                    <div className="aspect-square bg-warm-camel/10 rounded-lg flex items-center justify-center mb-4">
                      <span className="text-3xl">👟</span>
                    </div>
                    
                    <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-1">
                      {product.name}
                    </h4>
                    <p className="text-warm-camel text-sm mb-2">{product.brand}</p>
                    
                    <div className="flex items-center justify-between">
                      <span className="font-bold text-forest-emerald dark:text-light-cloud-gray">
                        ${product.price?.toLocaleString()} MXN
                      </span>
                      {product.confidence && (
                        <span className="text-xs bg-green-100 text-green-800 px-2 py-1 rounded-full">
                          {Math.round(product.confidence * 100)}% match
                        </span>
                      )}
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>
      )}

      {/* Modals */}
      <EnhancedVoiceSearch
        isOpen={activeModal === 'voice'}
        onClose={() => setActiveModal(null)}
        onResults={handleVoiceResults}
      />

      <VisualSearch
        isOpen={activeModal === 'visual'}
        onClose={() => setActiveModal(null)}
        onSearch={handleVisualSearch}
      />

      {/* Quick Tips */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="text-center"
      >
        <Card variant="default">
          <CardContent className="p-6">
            <h4 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
              💡 Consejos para mejores resultados
            </h4>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-warm-camel">
              <div>
                <strong>🎤 Búsqueda por voz:</strong><br />
                Habla claro y menciona marca, modelo o características específicas
              </div>
              <div>
                <strong>📸 Búsqueda visual:</strong><br />
                Usa imágenes claras con buena iluminación y enfoque en el producto
              </div>
              <div>
                <strong>🧠 Recomendaciones:</strong><br />
                Interactúa más con productos para mejorar las sugerencias de IA
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
