export async function GET(request) {
  console.log('🧪🧪🧪 TEST CATEGORY API CALLED!')
  
  try {
    // Test the Enterprise API
    const response = await fetch(`${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001'}/api/enterprise/products?category=sneakers&pageSize=5`)
    
    if (!response.ok) {
      throw new Error(`Enterprise API error: ${response.status}`)
    }
    
    const data = await response.json()
    console.log('🧪 Enterprise API Response:', data)
    
    return Response.json({
      success: true,
      message: 'Category API test successful',
      enterpriseData: data,
      timestamp: new Date().toISOString()
    })
  } catch (error) {
    console.error('🧪 Test Category API Error:', error)
    return Response.json({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }, { status: 500 })
  }
}
