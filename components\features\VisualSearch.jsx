'use client'

import { useState, useRef, useCallback } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'

export default function VisualSearch({ onSearch, onClose, isOpen = true }) {
  const [uploadedImage, setUploadedImage] = useState(null)
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysisResults, setAnalysisResults] = useState(null)
  const [dragActive, setDragActive] = useState(false)
  const fileInputRef = useRef(null)

  // Simulate AI image analysis
  const analyzeImage = useCallback(async (imageFile) => {
    setIsAnalyzing(true)
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    // Mock AI analysis results
    const mockProducts = [
      { id: 1, name: 'Nike Air Force 1', brand: 'Nike', price: 2500, image: '/placeholder.jpg' },
      { id: 2, name: 'Adidas Stan Smith', brand: 'Adidas', price: 2200, image: '/placeholder.jpg' },
      { id: 3, name: 'Gucci Ace Sneakers', brand: 'Gucci', price: 15000, image: '/placeholder.jpg' },
      { id: 4, name: 'Nike Air Max 90', brand: 'Nike', price: 2800, image: '/placeholder.jpg' },
      { id: 5, name: 'Converse Chuck Taylor', brand: 'Converse', price: 1800, image: '/placeholder.jpg' },
      { id: 6, name: 'Vans Old Skool', brand: 'Vans', price: 1900, image: '/placeholder.jpg' }
    ]

    const mockAnalysis = {
      detectedFeatures: [
        { feature: 'Tipo', value: 'Sneakers', confidence: 0.95 },
        { feature: 'Color principal', value: 'Blanco', confidence: 0.88 },
        { feature: 'Color secundario', value: 'Negro', confidence: 0.72 },
        { feature: 'Estilo', value: 'Deportivo', confidence: 0.91 },
        { feature: 'Marca detectada', value: 'Nike', confidence: 0.67 }
      ],
      suggestedProducts: mockProducts.slice(0, 6).map(product => ({
        ...product,
        similarity: Math.random() * 0.4 + 0.6 // 60-100% similarity
      })).sort((a, b) => b.similarity - a.similarity),
      searchTerms: ['sneakers blancos', 'nike air force', 'zapatos deportivos blancos']
    }
    
    setAnalysisResults(mockAnalysis)
    setIsAnalyzing(false)
  }, [])

  const handleFileSelect = (file) => {
    if (file && file.type.startsWith('image/')) {
      const reader = new FileReader()
      reader.onload = (e) => {
        setUploadedImage(e.target.result)
        analyzeImage(file)
      }
      reader.readAsDataURL(file)
    }
  }

  const handleDrop = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
    
    const files = e.dataTransfer.files
    if (files && files[0]) {
      handleFileSelect(files[0])
    }
  }, [])

  const handleDragOver = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(true)
  }, [])

  const handleDragLeave = useCallback((e) => {
    e.preventDefault()
    e.stopPropagation()
    setDragActive(false)
  }, [])

  const handleFileInputChange = (e) => {
    const file = e.target.files?.[0]
    if (file) {
      handleFileSelect(file)
    }
  }

  const handleProductClick = (product) => {
    onSearch(`${product.brand} ${product.name}`)
    onClose()
  }

  const handleSearchTermClick = (term) => {
    onSearch(term)
    onClose()
  }

  const resetSearch = () => {
    setUploadedImage(null)
    setAnalysisResults(null)
    setIsAnalyzing(false)
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <Card variant="glass" className="max-w-4xl w-full max-h-[90vh] overflow-y-auto" onClick={(e) => e.stopPropagation()}>
        <CardContent className="p-8">
          
          {/* Header */}
          <div className="text-center mb-8">
            <motion.div
              className="text-6xl mb-4"
              animate={{
                rotate: isAnalyzing ? [0, 10, -10, 0] : 0
              }}
              transition={{
                duration: 1,
                repeat: isAnalyzing ? Infinity : 0
              }}
            >
              📸
            </motion.div>
            <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
              Búsqueda Visual
            </h2>
            <p className="text-warm-camel">
              Sube una foto de zapatos y encuentra productos similares
            </p>
          </div>

          {!uploadedImage ? (
            /* Upload Area */
            <motion.div
              className={`border-2 border-dashed rounded-lg p-12 text-center transition-all duration-300 ${
                dragActive 
                  ? 'border-rich-gold bg-rich-gold/10' 
                  : 'border-warm-camel/30 hover:border-warm-camel'
              }`}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              whileHover={{ scale: 1.02 }}
            >
              <div className="space-y-6">
                <motion.div
                  className="text-6xl"
                  animate={{
                    y: dragActive ? [-5, 5, -5] : 0
                  }}
                  transition={{
                    duration: 1,
                    repeat: dragActive ? Infinity : 0
                  }}
                >
                  📤
                </motion.div>
                
                <div>
                  <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    {dragActive ? '¡Suelta la imagen aquí!' : 'Arrastra una imagen o haz clic para seleccionar'}
                  </h3>
                  <p className="text-warm-camel text-sm">
                    Formatos soportados: JPG, PNG, WebP (máx. 10MB)
                  </p>
                </div>

                <AnimatedButton
                  variant="primary"
                  onClick={() => fileInputRef.current?.click()}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                  }
                >
                  Seleccionar Imagen
                </AnimatedButton>

                <input
                  ref={fileInputRef}
                  type="file"
                  accept="image/*"
                  onChange={handleFileInputChange}
                  className="hidden"
                />
              </div>
            </motion.div>
          ) : (
            /* Analysis Results */
            <div className="space-y-8">
              
              {/* Uploaded Image */}
              <div className="flex justify-center">
                <motion.div
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  className="relative"
                >
                  <img
                    src={uploadedImage}
                    alt="Imagen subida"
                    className="max-w-xs max-h-48 object-contain rounded-lg shadow-lg"
                  />
                  <AnimatedButton
                    variant="ghost"
                    size="sm"
                    onClick={resetSearch}
                    className="absolute -top-2 -right-2"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                      </svg>
                    }
                  />
                </motion.div>
              </div>

              {/* Analysis Loading */}
              {isAnalyzing && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center"
                >
                  <div className="flex justify-center mb-4">
                    <motion.div
                      className="w-8 h-8 border-4 border-rich-gold border-t-transparent rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    />
                  </div>
                  <p className="text-warm-camel">
                    Analizando imagen con IA...
                  </p>
                </motion.div>
              )}

              {/* Analysis Results */}
              {analysisResults && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-6"
                >
                  
                  {/* Detected Features */}
                  <Card variant="default">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                        Características Detectadas
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {analysisResults.detectedFeatures.map((feature, index) => (
                          <motion.div
                            key={feature.feature}
                            initial={{ opacity: 0, x: -20 }}
                            animate={{ opacity: 1, x: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="flex justify-between items-center p-3 bg-warm-camel/10 rounded-lg"
                          >
                            <span className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                              {feature.feature}:
                            </span>
                            <div className="text-right">
                              <span className="text-warm-camel">{feature.value}</span>
                              <div className="text-xs text-warm-camel/70">
                                {Math.round(feature.confidence * 100)}% confianza
                              </div>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Search Suggestions */}
                  <Card variant="default">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                        Términos de Búsqueda Sugeridos
                      </h3>
                      <div className="flex flex-wrap gap-2">
                        {analysisResults.searchTerms.map((term, index) => (
                          <motion.button
                            key={term}
                            initial={{ opacity: 0, scale: 0.8 }}
                            animate={{ opacity: 1, scale: 1 }}
                            transition={{ delay: index * 0.1 }}
                            onClick={() => handleSearchTermClick(term)}
                            className="px-4 py-2 bg-rich-gold/20 text-forest-emerald dark:text-light-cloud-gray rounded-full hover:bg-rich-gold/30 transition-colors duration-200"
                            whileHover={{ scale: 1.05 }}
                            whileTap={{ scale: 0.95 }}
                          >
                            {term}
                          </motion.button>
                        ))}
                      </div>
                    </CardContent>
                  </Card>

                  {/* Similar Products */}
                  <Card variant="default">
                    <CardContent className="p-6">
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                        Productos Similares
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {analysisResults.suggestedProducts.map((product, index) => (
                          <motion.div
                            key={product.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            onClick={() => handleProductClick(product)}
                            className="cursor-pointer group"
                          >
                            <Card variant="glass" className="hover:shadow-lg transition-all duration-300">
                              <CardContent className="p-4">
                                <div className="relative mb-3">
                                  <img
                                    src={product.image}
                                    alt={product.name}
                                    className="w-full h-32 object-cover rounded-lg group-hover:scale-105 transition-transform duration-300"
                                  />
                                  <div className="absolute top-2 right-2 bg-rich-gold text-forest-emerald text-xs font-bold px-2 py-1 rounded-full">
                                    {Math.round(product.similarity * 100)}%
                                  </div>
                                </div>
                                <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm mb-1 line-clamp-2">
                                  {product.name}
                                </h4>
                                <p className="text-warm-camel text-xs mb-2">{product.brand}</p>
                                <p className="text-forest-emerald dark:text-light-cloud-gray font-bold">
                                  ${product.price.toLocaleString()} MXN
                                </p>
                              </CardContent>
                            </Card>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}
            </div>
          )}

          {/* Footer Actions */}
          <div className="flex justify-center gap-4 mt-8">
            {uploadedImage && !isAnalyzing && (
              <AnimatedButton
                variant="secondary"
                onClick={resetSearch}
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                }
              >
                Nueva Búsqueda
              </AnimatedButton>
            )}
            
            <AnimatedButton
              variant="ghost"
              onClick={onClose}
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              }
            >
              Cerrar
            </AnimatedButton>
          </div>

          {/* Tips */}
          <div className="mt-8 text-center">
            <p className="text-xs text-warm-camel mb-2">💡 Consejos para mejores resultados:</p>
            <div className="text-xs text-warm-camel/80 space-y-1">
              <p>• Usa imágenes claras con buena iluminación</p>
              <p>• Enfoca el zapato completo en la imagen</p>
              <p>• Evita fondos muy complicados o distractores</p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
    </AnimatePresence>
  )
}
