/**
 * Next.js API Route: GET /api/enterprise/products/[id]
 * Enterprise product retrieval endpoint
 */

import { NextRequest, NextResponse } from 'next/server'
import { getTWLSystem } from '@/lib/enterprise/TWLEnterpriseSystem'

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // Get enterprise system instance
    const system = getTWLSystem()
    
    // Ensure system is initialized
    if (!system.isReady()) {
      await system.initialize()
    }
    
    // Use the enterprise API
    const api = system.getAPI()
    return await api.getProduct(request, { params })
    
  } catch (error) {
    console.error('Enterprise API Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: { 
          code: 'INTERNAL_ERROR', 
          message: 'Internal server error' 
        } 
      },
      { status: 500 }
    )
  }
}
