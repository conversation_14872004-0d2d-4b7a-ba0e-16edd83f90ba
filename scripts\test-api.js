#!/usr/bin/env node

/**
 * TWL API Testing Script
 * Tests the API endpoints to ensure they're working correctly
 */

const https = require('https');
const http = require('http');

class APITester {
  constructor(baseUrl = 'http://localhost:3001') {
    this.baseUrl = baseUrl;
    this.results = [];
  }

  async makeRequest(path, method = 'GET', data = null) {
    return new Promise((resolve, reject) => {
      const url = new URL(path, this.baseUrl);
      const isHttps = url.protocol === 'https:';
      const client = isHttps ? https : http;

      const options = {
        hostname: url.hostname,
        port: url.port || (isHttps ? 443 : 80),
        path: url.pathname + url.search,
        method,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': 'TWL-API-Tester/1.0'
        }
      };

      if (data && method !== 'GET') {
        const jsonData = JSON.stringify(data);
        options.headers['Content-Length'] = Buffer.byteLength(jsonData);
      }

      const req = client.request(options, (res) => {
        let body = '';
        
        res.on('data', (chunk) => {
          body += chunk;
        });

        res.on('end', () => {
          try {
            const jsonBody = body ? JSON.parse(body) : {};
            resolve({
              status: res.statusCode,
              headers: res.headers,
              body: jsonBody
            });
          } catch (error) {
            resolve({
              status: res.statusCode,
              headers: res.headers,
              body: body,
              parseError: error.message
            });
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (data && method !== 'GET') {
        req.write(JSON.stringify(data));
      }

      req.end();
    });
  }

  async testEndpoint(name, path, expectedStatus = 200, method = 'GET', data = null) {
    console.log(`🔍 Testing ${name}...`);
    
    try {
      const startTime = Date.now();
      const response = await this.makeRequest(path, method, data);
      const duration = Date.now() - startTime;

      const success = response.status === expectedStatus;
      const result = {
        name,
        path,
        method,
        expectedStatus,
        actualStatus: response.status,
        duration,
        success,
        response: response.body
      };

      this.results.push(result);

      if (success) {
        console.log(`✅ ${name} - ${response.status} (${duration}ms)`);
      } else {
        console.log(`❌ ${name} - Expected ${expectedStatus}, got ${response.status} (${duration}ms)`);
        if (response.body && response.body.error) {
          console.log(`   Error: ${response.body.error}`);
        }
      }

      return result;
    } catch (error) {
      console.log(`❌ ${name} - Request failed: ${error.message}`);
      const result = {
        name,
        path,
        method,
        expectedStatus,
        actualStatus: 'ERROR',
        duration: 0,
        success: false,
        error: error.message
      };
      this.results.push(result);
      return result;
    }
  }

  async runTests() {
    console.log('🚀 Starting TWL API Tests\n');
    console.log(`Base URL: ${this.baseUrl}\n`);

    // Test basic endpoints
    await this.testEndpoint('Health Check', '/api/health', 404); // Expected 404 since we haven't created this yet
    
    // Test brands endpoint
    await this.testEndpoint('Get Brands', '/api/brands');
    await this.testEndpoint('Get Featured Brands', '/api/brands?featured=true');
    await this.testEndpoint('Get Luxury Brands', '/api/brands?tier=luxury');
    
    // Test products endpoint
    await this.testEndpoint('Get Products', '/api/products');
    await this.testEndpoint('Get Featured Products', '/api/products?featured=true');
    await this.testEndpoint('Get Products with Limit', '/api/products?limit=5');
    
    // Test product detail (this will likely fail until we have products)
    await this.testEndpoint('Get Product Detail', '/api/products/test-product', 404);
    
    // Test search
    await this.testEndpoint('Search Products', '/api/products?search=nike');
    
    // Test non-existent endpoints
    await this.testEndpoint('Non-existent Endpoint', '/api/nonexistent', 404);

    this.printSummary();
  }

  printSummary() {
    console.log('\n📊 Test Summary\n');
    
    const totalTests = this.results.length;
    const passedTests = this.results.filter(r => r.success).length;
    const failedTests = totalTests - passedTests;
    
    console.log(`Total Tests: ${totalTests}`);
    console.log(`✅ Passed: ${passedTests}`);
    console.log(`❌ Failed: ${failedTests}`);
    console.log(`Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%\n`);

    if (failedTests > 0) {
      console.log('❌ Failed Tests:');
      this.results
        .filter(r => !r.success)
        .forEach(result => {
          console.log(`   - ${result.name}: ${result.actualStatus} (expected ${result.expectedStatus})`);
          if (result.error) {
            console.log(`     Error: ${result.error}`);
          }
        });
      console.log('');
    }

    // Performance summary
    const avgDuration = this.results
      .filter(r => r.duration > 0)
      .reduce((sum, r) => sum + r.duration, 0) / this.results.filter(r => r.duration > 0).length;
    
    if (avgDuration) {
      console.log(`⚡ Average Response Time: ${avgDuration.toFixed(0)}ms`);
    }

    // Recommendations
    console.log('\n💡 Next Steps:');
    
    if (failedTests === 0) {
      console.log('   🎉 All tests passed! Your API is working correctly.');
      console.log('   📝 Consider adding more test cases for edge cases.');
    } else {
      console.log('   🔧 Fix failing endpoints before proceeding.');
      console.log('   📋 Check database connection and migrations.');
      console.log('   🔍 Review error messages above for specific issues.');
    }
    
    console.log('   📊 Run database migrations: npm run db:migrate');
    console.log('   🌱 Seed database: npm run db:seed');
    console.log('   🔄 Restart development server: npm run dev');
  }
}

// Run tests
async function main() {
  const args = process.argv.slice(2);
  const baseUrl = args[0] || 'http://localhost:3001';
  
  const tester = new APITester(baseUrl);
  await tester.runTests();
}

main().catch(error => {
  console.error('❌ Test runner failed:', error);
  process.exit(1);
});
