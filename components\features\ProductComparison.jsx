'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

export default function ProductComparison({ 
  products = [], 
  onRemoveProduct, 
  onClose,
  isOpen = false 
}) {
  const [compareProducts, setCompareProducts] = useState(products)

  useEffect(() => {
    setCompareProducts(products)
  }, [products])

  const handleRemoveProduct = (productId) => {
    const updatedProducts = compareProducts.filter(p => p.id !== productId)
    setCompareProducts(updatedProducts)
    onRemoveProduct?.(productId)
    
    if (updatedProducts.length === 0) {
      onClose?.()
    }
  }

  const getComparisonFeatures = () => {
    if (compareProducts.length === 0) return []
    
    const allFeatures = new Set()
    compareProducts.forEach(product => {
      product.features?.forEach(feature => allFeatures.add(feature))
    })
    
    return Array.from(allFeatures)
  }

  const hasFeature = (product, feature) => {
    return product.features?.includes(feature)
  }

  const getBestValue = (products, key) => {
    if (key === 'price') {
      return Math.min(...products.map(p => p.price))
    }
    if (key === 'rating') {
      return Math.max(...products.map(p => p.rating || 0))
    }
    return null
  }

  if (!isOpen || compareProducts.length === 0) {
    return null
  }

  const features = getComparisonFeatures()
  const bestPrice = getBestValue(compareProducts, 'price')
  const bestRating = getBestValue(compareProducts, 'rating')

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className="max-w-6xl w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <Card variant="glass">
            <CardContent className="p-8">
              
              {/* Header */}
              <div className="flex items-center justify-between mb-8">
                <div>
                  <h2 className="text-3xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    Comparar Productos
                  </h2>
                  <p className="text-warm-camel">
                    Compara hasta {compareProducts.length} productos lado a lado
                  </p>
                </div>
                
                <AnimatedButton
                  variant="ghost"
                  size="sm"
                  onClick={onClose}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  }
                >
                  Cerrar
                </AnimatedButton>
              </div>

              {/* Products Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
                {compareProducts.map((product, index) => (
                  <motion.div
                    key={product.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.1 }}
                  >
                    <Card variant="default" className="relative">
                      <CardContent className="p-6">
                        
                        {/* Remove Button */}
                        <button
                          onClick={() => handleRemoveProduct(product.id)}
                          className="absolute top-2 right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center hover:bg-red-600 transition-colors"
                        >
                          <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>

                        {/* Product Image */}
                        <div className="aspect-square bg-warm-camel/10 rounded-lg flex items-center justify-center mb-4">
                          <span className="text-4xl">👟</span>
                        </div>

                        {/* Product Info */}
                        <div className="space-y-3">
                          <div>
                            <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray line-clamp-2">
                              {product.name}
                            </h3>
                            <p className="text-warm-camel text-sm">{product.brand}</p>
                          </div>

                          {/* Price */}
                          <div className="flex items-center gap-2">
                            <span className={`text-xl font-bold ${
                              product.price === bestPrice 
                                ? 'text-green-600 dark:text-green-400' 
                                : 'text-forest-emerald dark:text-light-cloud-gray'
                            }`}>
                              ${product.price?.toLocaleString()} MXN
                            </span>
                            {product.price === bestPrice && (
                              <Badge variant="success" size="sm">Mejor precio</Badge>
                            )}
                          </div>

                          {/* Rating */}
                          <div className="flex items-center gap-2">
                            <div className="flex items-center gap-1">
                              <svg className="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                              </svg>
                              <span className={`font-medium ${
                                product.rating === bestRating 
                                  ? 'text-green-600 dark:text-green-400' 
                                  : 'text-forest-emerald dark:text-light-cloud-gray'
                              }`}>
                                {product.rating || 'N/A'}
                              </span>
                            </div>
                            {product.rating === bestRating && (
                              <Badge variant="success" size="sm">Mejor valorado</Badge>
                            )}
                          </div>

                          {/* Reviews */}
                          <p className="text-warm-camel text-sm">
                            {product.reviews?.toLocaleString() || 0} reseñas
                          </p>

                          {/* Actions */}
                          <div className="space-y-2 pt-2">
                            <TransitionLink href={`/product/${product.id}`}>
                              <AnimatedButton
                                variant="primary"
                                size="sm"
                                className="w-full"
                                icon={
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                                  </svg>
                                }
                              >
                                Ver Detalles
                              </AnimatedButton>
                            </TransitionLink>
                            
                            <AnimatedButton
                              variant="secondary"
                              size="sm"
                              className="w-full"
                              icon={
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                                </svg>
                              }
                            >
                              Agregar al Carrito
                            </AnimatedButton>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>

              {/* Features Comparison */}
              {features.length > 0 && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                >
                  <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Comparación de Características
                  </h3>
                  
                  <Card variant="default">
                    <CardContent className="p-0">
                      <div className="overflow-x-auto">
                        <table className="w-full">
                          <thead>
                            <tr className="border-b border-warm-camel/20">
                              <th className="text-left p-4 font-medium text-forest-emerald dark:text-light-cloud-gray">
                                Característica
                              </th>
                              {compareProducts.map(product => (
                                <th key={product.id} className="text-center p-4 font-medium text-forest-emerald dark:text-light-cloud-gray">
                                  {product.name}
                                </th>
                              ))}
                            </tr>
                          </thead>
                          <tbody>
                            {features.map((feature, index) => (
                              <tr key={feature} className={index % 2 === 0 ? 'bg-warm-camel/5' : ''}>
                                <td className="p-4 font-medium text-forest-emerald dark:text-light-cloud-gray">
                                  {feature}
                                </td>
                                {compareProducts.map(product => (
                                  <td key={product.id} className="text-center p-4">
                                    {hasFeature(product, feature) ? (
                                      <svg className="w-5 h-5 text-green-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                                      </svg>
                                    ) : (
                                      <svg className="w-5 h-5 text-red-500 mx-auto" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                                      </svg>
                                    )}
                                  </td>
                                ))}
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Footer Actions */}
              <div className="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                <AnimatedButton
                  variant="secondary"
                  onClick={onClose}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  }
                >
                  Cerrar Comparación
                </AnimatedButton>
                
                <TransitionLink href="/shop">
                  <AnimatedButton
                    variant="primary"
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    }
                  >
                    Buscar Más Productos
                  </AnimatedButton>
                </TransitionLink>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
