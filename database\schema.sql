-- TWL E-commerce Database Schema
-- Designed for multi-supplier product management with supplier anonymity

-- Enable UUID extension
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Suppliers table (hidden from frontend)
CREATE TABLE suppliers (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL, -- e.g., 'CYTTE'
    contact_info JSONB,
    commission_rate DECIMAL(5,2), -- Supplier commission percentage
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'suspended')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Brands table
CREATE TABLE brands (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    logo_url VARCHAR(500),
    website_url VARCHAR(500),
    founded_year INTEGER,
    country VARCHAR(100),
    brand_tier VARCHAR(20) DEFAULT 'luxury' CHECK (brand_tier IN ('luxury', 'premium', 'mainstream', 'streetwear')),
    featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Categories table
CREATE TABLE categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    parent_id UUID REFERENCES categories(id),
    image_url VARCHAR(500),
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Products table (main product catalog)
CREATE TABLE products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sku VARCHAR(50) UNIQUE NOT NULL, -- TWL internal SKU
    supplier_id UUID REFERENCES suppliers(id),
    supplier_sku VARCHAR(100), -- Supplier's internal SKU (hidden from frontend)
    brand_id UUID REFERENCES brands(id) NOT NULL,
    category_id UUID REFERENCES categories(id) NOT NULL,
    
    -- Product details
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL,
    description TEXT,
    short_description VARCHAR(500),
    
    -- Pricing
    price DECIMAL(10,2) NOT NULL,
    original_price DECIMAL(10,2),
    cost_price DECIMAL(10,2), -- Supplier cost (hidden from frontend)
    currency VARCHAR(3) DEFAULT 'MXN',
    
    -- Product attributes
    gender VARCHAR(20) CHECK (gender IN ('men', 'women', 'unisex', 'kids')),
    target_age_group VARCHAR(20) CHECK (target_age_group IN ('adult', 'teen', 'kids', 'toddler')),
    season VARCHAR(20) CHECK (season IN ('spring', 'summer', 'fall', 'winter', 'all_season')),
    
    -- Product status
    status VARCHAR(20) DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'draft', 'discontinued')),
    availability VARCHAR(20) DEFAULT 'in_stock' CHECK (availability IN ('in_stock', 'out_of_stock', 'pre_order', 'discontinued')),
    
    -- Marketing flags
    featured BOOLEAN DEFAULT false,
    is_new BOOLEAN DEFAULT false,
    is_limited_edition BOOLEAN DEFAULT false,
    is_exclusive BOOLEAN DEFAULT false,
    is_collaboration BOOLEAN DEFAULT false,
    
    -- SEO
    meta_title VARCHAR(255),
    meta_description VARCHAR(500),
    meta_keywords TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    published_at TIMESTAMP WITH TIME ZONE,
    
    -- Indexes
    UNIQUE(slug),
    INDEX idx_products_brand (brand_id),
    INDEX idx_products_category (category_id),
    INDEX idx_products_status (status),
    INDEX idx_products_featured (featured),
    INDEX idx_products_price (price),
    INDEX idx_products_gender (gender)
);

-- Product variants (sizes, colors, etc.)
CREATE TABLE product_variants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    sku VARCHAR(50) UNIQUE NOT NULL, -- Variant-specific SKU
    
    -- Variant attributes
    size VARCHAR(20),
    color VARCHAR(50),
    color_hex VARCHAR(7), -- Hex color code
    material VARCHAR(100),
    
    -- Pricing (can override product price)
    price DECIMAL(10,2),
    original_price DECIMAL(10,2),
    
    -- Inventory
    stock_quantity INTEGER DEFAULT 0,
    reserved_quantity INTEGER DEFAULT 0,
    low_stock_threshold INTEGER DEFAULT 5,
    
    -- Variant status
    is_active BOOLEAN DEFAULT true,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_variants_product (product_id),
    INDEX idx_variants_size (size),
    INDEX idx_variants_color (color),
    INDEX idx_variants_stock (stock_quantity)
);

-- Product images
CREATE TABLE product_images (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    variant_id UUID REFERENCES product_variants(id) ON DELETE CASCADE, -- Optional: variant-specific images
    
    -- Image details
    url VARCHAR(500) NOT NULL,
    alt_text VARCHAR(255),
    title VARCHAR(255),
    
    -- Image metadata
    width INTEGER,
    height INTEGER,
    file_size INTEGER, -- in bytes
    format VARCHAR(10), -- webp, jpg, png
    
    -- Organization
    sort_order INTEGER DEFAULT 0,
    is_primary BOOLEAN DEFAULT false,
    image_type VARCHAR(20) DEFAULT 'product' CHECK (image_type IN ('product', 'lifestyle', 'detail', 'size_guide')),
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Indexes
    INDEX idx_images_product (product_id),
    INDEX idx_images_variant (variant_id),
    INDEX idx_images_primary (is_primary)
);

-- Product tags (for flexible categorization)
CREATE TABLE tags (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL UNIQUE,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    color VARCHAR(7), -- Hex color for UI
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product-tag relationships
CREATE TABLE product_tags (
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    tag_id UUID REFERENCES tags(id) ON DELETE CASCADE,
    PRIMARY KEY (product_id, tag_id)
);

-- Product collections (curated groups)
CREATE TABLE collections (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    slug VARCHAR(100) NOT NULL UNIQUE,
    description TEXT,
    image_url VARCHAR(500),
    featured BOOLEAN DEFAULT false,
    sort_order INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Product-collection relationships
CREATE TABLE collection_products (
    collection_id UUID REFERENCES collections(id) ON DELETE CASCADE,
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    sort_order INTEGER DEFAULT 0,
    PRIMARY KEY (collection_id, product_id)
);

-- Inventory movements (for tracking stock changes)
CREATE TABLE inventory_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    variant_id UUID REFERENCES product_variants(id),
    movement_type VARCHAR(20) CHECK (movement_type IN ('in', 'out', 'adjustment', 'reserved', 'released')),
    quantity INTEGER NOT NULL,
    reason VARCHAR(100),
    reference_id UUID, -- Order ID, adjustment ID, etc.
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_by UUID -- User ID who made the change
);

-- Product reviews (for future implementation)
CREATE TABLE product_reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID REFERENCES products(id) ON DELETE CASCADE,
    user_id UUID, -- Reference to users table
    rating INTEGER CHECK (rating >= 1 AND rating <= 5),
    title VARCHAR(255),
    content TEXT,
    verified_purchase BOOLEAN DEFAULT false,
    is_approved BOOLEAN DEFAULT false,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Insert initial data
INSERT INTO suppliers (name, code, status) VALUES 
('CYTTE', 'CYTTE', 'active');

-- Insert categories
INSERT INTO categories (name, slug, description, sort_order) VALUES 
('Sneakers', 'sneakers', 'Athletic and lifestyle sneakers', 1),
('Sandals', 'sandals', 'Casual and luxury sandals', 2),
('Formal', 'formal', 'Dress shoes and formal footwear', 3),
('Casual', 'casual', 'Everyday casual shoes', 4),
('Kids', 'kids', 'Children footwear', 5);

-- Insert major brands
INSERT INTO brands (name, slug, description, brand_tier, featured, sort_order) VALUES 
('Nike', 'nike', 'Global athletic footwear and apparel leader', 'premium', true, 1),
('Adidas', 'adidas', 'German multinational corporation', 'premium', true, 2),
('Gucci', 'gucci', 'Italian luxury fashion house', 'luxury', true, 3),
('Dior', 'dior', 'French luxury fashion house', 'luxury', true, 4),
('Louis Vuitton', 'louis-vuitton', 'French luxury fashion house', 'luxury', true, 5),
('Balenciaga', 'balenciaga', 'Spanish luxury fashion house', 'luxury', true, 6),
('Off-White', 'off-white', 'Italian luxury streetwear brand', 'streetwear', true, 7),
('Chanel', 'chanel', 'French luxury fashion house', 'luxury', false, 8),
('Christian Louboutin', 'christian-louboutin', 'French luxury footwear designer', 'luxury', false, 9),
('Hermès', 'hermes', 'French luxury goods manufacturer', 'luxury', false, 10);

-- Insert common tags
INSERT INTO tags (name, slug, description, color) VALUES 
('Limited Edition', 'limited-edition', 'Limited production run', '#FF6B6B'),
('Collaboration', 'collaboration', 'Brand collaboration pieces', '#4ECDC4'),
('New Arrival', 'new-arrival', 'Recently added products', '#45B7D1'),
('Best Seller', 'best-seller', 'Top selling products', '#96CEB4'),
('Exclusive', 'exclusive', 'TWL exclusive products', '#FFEAA7'),
('Sale', 'sale', 'Discounted products', '#FD79A8');

-- Create indexes for performance
CREATE INDEX idx_products_search ON products USING gin(to_tsvector('spanish', name || ' ' || description));
CREATE INDEX idx_products_price_range ON products (price) WHERE status = 'active';
CREATE INDEX idx_variants_availability ON product_variants (stock_quantity) WHERE is_active = true;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Apply updated_at triggers
CREATE TRIGGER update_products_updated_at BEFORE UPDATE ON products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_brands_updated_at BEFORE UPDATE ON brands FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_categories_updated_at BEFORE UPDATE ON categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_variants_updated_at BEFORE UPDATE ON product_variants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
