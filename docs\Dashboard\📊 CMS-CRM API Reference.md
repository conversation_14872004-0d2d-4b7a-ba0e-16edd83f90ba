Below is a comprehensive CMS/CRM API Reference for The White Laces (TWL) Admin Dashboard , tailored to your luxury streetwear e-commerce platform , built with a Mexico-first strategy , and featuring a glassmorphic UI powered by Next.js + Tailwind CSS .

This reference includes:

✅ RESTful API routes
✅ Request/response formats
✅ Authentication headers
✅ CRUD operations per module
🧠 AI-powered endpoints (voice search logs, visual search gallery)
🌐 Localization & multi-language support
🔒 Role-based access control
📊 The White Laces – CMS/CRM API Reference
Next.js | Glassmorphism UI | Mexico-First Strategy
👟 Luxury Footwear Meets Gen Z Culture | E-commerce Platform 

🛠️ Overview
🎯 Purpose:
To provide a structured, secure, and scalable API interface for managing all aspects of TWL from the admin dashboard, including:

Product Catalog Management
Order Tracking
User Profiles
UGC Moderation
Localization Content
Marketing Tools
Analytics Dashboards
AI Feature Logs
All endpoints are protected via Firebase Auth , JWT , or Clerk authentication.

🚦 Base URL
https://api.twl.com/admin 

Use environment variables to manage staging vs production:
NEXT_PUBLIC_API_URL=https://api.twl.com/admin 
ADMIN_API_TOKEN=your-admin-api-key


🔐 Authentication
All endpoints require an Authorization header using Bearer tokens:

Authorization: Bearer <your-jwt-token>

Tokens can be obtained via /auth/login endpoint.

Example Login Flow:

POST /auth/login
{
  "email": "<EMAIL>",
  "password": "securePassword123"
}


Response:

{
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
  "role": "admin",
  "name": "TWL Admin"
}



🛒 Module 1: Product Management
📦 Endpoints
GET /products
Fetch list of products with filters

Query Params

Param,Description
limit,Number of items per page
offset,Pagination offset
brand,"Filter by brand (Nike, Gucci, etc.)"
gender,Filter by gender
type,Filter by shoe type
isLimitedEdition,Boolean filter


Example Request

GET /products?brand=Nike&type=sneakers&limit=20
Authorization: Bearer <token>


Response

[
  {
    "id": "product_001",
    "name": "Nike x Off-White Dunk",
    "brand": "Nike",
    "type": "sneakers",
    "gender": "unisex",
    "price": 180,
    "currency": "USD",
    "isLimitedEdition": true,
    "images": ["url1.jpg", "url2.jpg"],
    "createdAt": "2024-07-10T12:00:00Z"
  }
]


POST /products
Add new product

Request Body

{
  "name": "New Balance x Aimé Leon Dore",
  "brand": "New Balance",
  "type": "sneakers",
  "gender": "men",
  "price": 150,
  "currency": "MXN",
  "isLimitedEdition": false,
  "description": "Classic retro sneakers with luxury detailing.",
  "images": ["url1.jpg", "url2.jpg"]
}


Response

{
  "id": "product_002",
  "status": "created"
}


PUT /products/:id
Update existing product

Request Body

{
  "role": "admin"
}


Response

{
  "userId": "user_001",
  "newRole": "admin"
}


