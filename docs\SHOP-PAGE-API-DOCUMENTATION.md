# 🔌 TWL SHOP PAGE - API DOCUMENTATION

## 📡 COMPREHENSIVE API SPECIFICATION

**API Version:** 2.0  
**Last Updated:** 2025-06-20  
**Base URL:** `https://thewhitelaces.com/api`  
**Authentication:** JWT Bearer Token  

---

## 🎯 API OVERVIEW

The TWL Shop Page API provides comprehensive endpoints for product browsing, filtering, searching, and cart management. All endpoints support **Mexican Spanish localization** and **MXN currency**.

### **API FEATURES**
- ✅ **RESTful Design** - Standard HTTP methods and status codes
- ✅ **Real-time Filtering** - Dynamic product filtering capabilities
- ✅ **Advanced Search** - Full-text search with suggestions
- ✅ **Pagination** - Efficient data loading with cursor-based pagination
- ✅ **Caching** - Optimized response caching for performance
- ✅ **Rate Limiting** - Protection against abuse
- ✅ **Error Handling** - Comprehensive error responses

---

## 🛍️ PRODUCTS API

### **GET /api/products**

Retrieve products with filtering, sorting, and pagination.

#### **Request Parameters**

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `page` | integer | No | Page number (default: 1) | `1` |
| `limit` | integer | No | Items per page (default: 20, max: 100) | `20` |
| `category` | string | No | Filter by category | `sneakers` |
| `brand` | string[] | No | Filter by brand(s) | `Nike,Adidas` |
| `minPrice` | number | No | Minimum price in MXN | `500` |
| `maxPrice` | number | No | Maximum price in MXN | `5000` |
| `sortBy` | string | No | Sort criteria | `price-asc` |
| `inStock` | boolean | No | Filter in-stock items only | `true` |
| `onSale` | boolean | No | Filter sale items only | `true` |
| `search` | string | No | Search term | `air force` |

#### **Sort Options**
- `price-asc` - Price: Low to High
- `price-desc` - Price: High to Low
- `name-asc` - Name: A to Z
- `name-desc` - Name: Z to A
- `newest` - Newest First
- `popular` - Most Popular

#### **Example Request**
```http
GET /api/products?category=sneakers&brand=Nike&minPrice=1000&maxPrice=3000&sortBy=price-asc&page=1&limit=20
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

#### **Example Response**
```json
{
  "success": true,
  "data": {
    "products": [
      {
        "id": "13442465838006",
        "name": "Nike Air Force 1 Premium",
        "brand": "Nike",
        "category": "sneakers",
        "subcategory": "lifestyle",
        "price": 2130,
        "originalPrice": 3400,
        "currency": "MXN",
        "discount": 37,
        "images": [
          "/products-organized/1. SNEAKERS/1. NIKE Limited Edition/13442465838006 -- Originals/o_1hd8s9f8s9d8f.webp",
          "/products-organized/1. SNEAKERS/1. NIKE Limited Edition/13442465838006 -- Originals/o_2hd8s9f8s9d8f.webp"
        ],
        "videos": [
          "/products-organized/1. SNEAKERS/1. NIKE Limited Edition/13442465838006 -- Originals/Video-Originals-1.mp4"
        ],
        "description": "Nike Air Force 1 Premium - Tenis deportivos de cuero premium con suela de goma",
        "features": [
          "Cuero premium",
          "Suela de goma antideslizante",
          "Diseño clásico",
          "Comodidad todo el día"
        ],
        "sizes": [35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46],
        "colors": ["Blanco", "Negro"],
        "inStock": true,
        "onSale": true,
        "isNew": false,
        "isLimited": true,
        "tags": ["premium", "lifestyle", "classic"],
        "rating": 4.8,
        "reviewCount": 156,
        "createdAt": "2024-12-15T10:30:00Z",
        "updatedAt": "2025-01-10T14:20:00Z"
      }
    ],
    "pagination": {
      "currentPage": 1,
      "totalPages": 8,
      "totalItems": 156,
      "itemsPerPage": 20,
      "hasNextPage": true,
      "hasPreviousPage": false
    },
    "filters": {
      "appliedFilters": {
        "category": "sneakers",
        "brand": ["Nike"],
        "priceRange": {
          "min": 1000,
          "max": 3000
        }
      },
      "availableFilters": {
        "categories": [
          {
            "id": "sneakers",
            "name": "Tenis",
            "count": 156,
            "subcategories": [
              { "id": "lifestyle", "name": "Lifestyle", "count": 89 },
              { "id": "running", "name": "Running", "count": 67 }
            ]
          }
        ],
        "brands": [
          { "id": "nike", "name": "Nike", "count": 45 },
          { "id": "adidas", "name": "Adidas", "count": 32 },
          { "id": "prada", "name": "Prada", "count": 28 }
        ],
        "priceRange": {
          "min": 500,
          "max": 15000
        },
        "sizes": [35, 36, 37, 38, 39, 40, 41, 42, 43, 44, 45, 46],
        "colors": ["Blanco", "Negro", "Gris", "Azul", "Rojo"]
      }
    }
  },
  "meta": {
    "timestamp": "2025-06-20T10:30:00Z",
    "requestId": "req_1234567890",
    "processingTime": "145ms"
  }
}
```

#### **Error Responses**
```json
// 400 Bad Request
{
  "success": false,
  "error": {
    "code": "INVALID_PARAMETERS",
    "message": "Parámetros de consulta inválidos",
    "details": {
      "minPrice": "Debe ser un número positivo",
      "limit": "No puede exceder 100"
    }
  }
}

// 429 Too Many Requests
{
  "success": false,
  "error": {
    "code": "RATE_LIMIT_EXCEEDED",
    "message": "Demasiadas solicitudes. Intenta de nuevo en 60 segundos.",
    "retryAfter": 60
  }
}
```

---

## 🔍 SEARCH API

### **GET /api/products/search**

Advanced product search with suggestions and autocomplete.

#### **Request Parameters**

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `q` | string | Yes | Search query | `nike air force` |
| `suggestions` | boolean | No | Include search suggestions | `true` |
| `autocomplete` | boolean | No | Enable autocomplete | `true` |
| `limit` | integer | No | Max results (default: 20) | `10` |
| `category` | string | No | Limit search to category | `sneakers` |

#### **Example Request**
```http
GET /api/products/search?q=air+force&suggestions=true&autocomplete=true&limit=10
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json
```

#### **Example Response**
```json
{
  "success": true,
  "data": {
    "query": "air force",
    "products": [
      {
        "id": "13442465838006",
        "name": "Nike Air Force 1 Premium",
        "brand": "Nike",
        "price": 2130,
        "images": ["/products/nike-air-force-1.webp"],
        "relevanceScore": 0.95,
        "matchedFields": ["name", "description"]
      }
    ],
    "suggestions": [
      "Nike Air Force 1",
      "Air Force One",
      "Nike Air Force Low",
      "Air Force High Top"
    ],
    "autocomplete": [
      "air force 1",
      "air force one",
      "air force low",
      "air force high"
    ],
    "totalResults": 12,
    "searchTime": "23ms"
  }
}
```

---

## 🏷️ FILTERS API

### **GET /api/products/filters**

Get available filter options for the product catalog.

#### **Request Parameters**

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `category` | string | No | Get filters for specific category | `sneakers` |
| `includeCount` | boolean | No | Include product counts | `true` |

#### **Example Response**
```json
{
  "success": true,
  "data": {
    "categories": [
      {
        "id": "sneakers",
        "name": "Tenis",
        "slug": "tenis",
        "count": 156,
        "image": "/categories/sneakers.webp",
        "subcategories": [
          {
            "id": "lifestyle",
            "name": "Lifestyle",
            "count": 89
          },
          {
            "id": "running",
            "name": "Running",
            "count": 67
          }
        ]
      },
      {
        "id": "sandals",
        "name": "Sandalias",
        "slug": "sandalias",
        "count": 89,
        "image": "/categories/sandals.webp"
      }
    ],
    "brands": [
      {
        "id": "nike",
        "name": "Nike",
        "logo": "/brands/nike.svg",
        "count": 45,
        "featured": true
      },
      {
        "id": "adidas",
        "name": "Adidas",
        "logo": "/brands/adidas.svg",
        "count": 32,
        "featured": true
      }
    ],
    "priceRanges": [
      {
        "id": "under-1000",
        "label": "Menos de $1,000",
        "min": 0,
        "max": 1000,
        "count": 23
      },
      {
        "id": "1000-3000",
        "label": "$1,000 - $3,000",
        "min": 1000,
        "max": 3000,
        "count": 89
      }
    ],
    "sizes": [
      { "value": 35, "label": "35", "count": 45 },
      { "value": 36, "label": "36", "count": 52 },
      { "value": 37, "label": "37", "count": 67 }
    ],
    "colors": [
      {
        "id": "white",
        "name": "Blanco",
        "hex": "#FFFFFF",
        "count": 78
      },
      {
        "id": "black",
        "name": "Negro",
        "hex": "#000000",
        "count": 65
      }
    ]
  }
}
```

---

## 🛒 CART API

### **POST /api/cart/add**

Add product to shopping cart.

#### **Request Body**
```json
{
  "productId": "13442465838006",
  "quantity": 1,
  "size": 42,
  "color": "white"
}
```

#### **Example Response**
```json
{
  "success": true,
  "data": {
    "cartItem": {
      "id": "cart_item_123",
      "productId": "13442465838006",
      "product": {
        "name": "Nike Air Force 1 Premium",
        "brand": "Nike",
        "price": 2130,
        "image": "/products/nike-air-force-1.webp"
      },
      "quantity": 1,
      "size": 42,
      "color": "white",
      "unitPrice": 2130,
      "totalPrice": 2130
    },
    "cart": {
      "itemCount": 3,
      "totalItems": 5,
      "subtotal": 6390,
      "tax": 1022,
      "total": 7412,
      "currency": "MXN"
    }
  }
}
```

### **GET /api/cart**

Get current cart contents.

#### **Example Response**
```json
{
  "success": true,
  "data": {
    "cart": {
      "id": "cart_user_123",
      "items": [
        {
          "id": "cart_item_123",
          "productId": "13442465838006",
          "product": {
            "name": "Nike Air Force 1 Premium",
            "brand": "Nike",
            "price": 2130,
            "image": "/products/nike-air-force-1.webp"
          },
          "quantity": 2,
          "size": 42,
          "color": "white",
          "unitPrice": 2130,
          "totalPrice": 4260
        }
      ],
      "summary": {
        "itemCount": 1,
        "totalItems": 2,
        "subtotal": 4260,
        "tax": 681,
        "shipping": 0,
        "total": 4941,
        "currency": "MXN"
      },
      "updatedAt": "2025-06-20T10:30:00Z"
    }
  }
}
```

---

## ❤️ WISHLIST API

### **POST /api/wishlist/add**

Add product to wishlist.

#### **Request Body**
```json
{
  "productId": "13442465838006"
}
```

#### **Example Response**
```json
{
  "success": true,
  "data": {
    "wishlistItem": {
      "id": "wishlist_item_123",
      "productId": "13442465838006",
      "product": {
        "name": "Nike Air Force 1 Premium",
        "brand": "Nike",
        "price": 2130,
        "image": "/products/nike-air-force-1.webp"
      },
      "addedAt": "2025-06-20T10:30:00Z"
    },
    "wishlist": {
      "itemCount": 5
    }
  }
}
```

### **GET /api/wishlist**

Get user's wishlist.

#### **Example Response**
```json
{
  "success": true,
  "data": {
    "wishlist": {
      "id": "wishlist_user_123",
      "items": [
        {
          "id": "wishlist_item_123",
          "productId": "13442465838006",
          "product": {
            "name": "Nike Air Force 1 Premium",
            "brand": "Nike",
            "price": 2130,
            "originalPrice": 3400,
            "image": "/products/nike-air-force-1.webp",
            "inStock": true,
            "onSale": true
          },
          "addedAt": "2025-06-20T10:30:00Z"
        }
      ],
      "itemCount": 5,
      "updatedAt": "2025-06-20T10:30:00Z"
    }
  }
}
```

---

## 📊 ANALYTICS API

### **POST /api/analytics/track**

Track user interactions for analytics.

#### **Request Body**
```json
{
  "event": "product_viewed",
  "data": {
    "productId": "13442465838006",
    "productName": "Nike Air Force 1 Premium",
    "category": "sneakers",
    "brand": "Nike",
    "price": 2130,
    "timestamp": "2025-06-20T10:30:00Z",
    "sessionId": "session_123",
    "userId": "user_456"
  }
}
```

#### **Supported Events**
- `product_viewed` - Product page view
- `product_added_to_cart` - Add to cart action
- `product_added_to_wishlist` - Add to wishlist action
- `filter_applied` - Filter usage
- `search_performed` - Search query
- `page_viewed` - Page navigation

---

## 🔐 AUTHENTICATION

### **Authentication Methods**

#### **JWT Bearer Token**
```http
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### **API Key (for server-to-server)**
```http
X-API-Key: twl_live_sk_1234567890abcdef
```

### **Token Refresh**
```http
POST /api/auth/refresh
Content-Type: application/json

{
  "refreshToken": "refresh_token_here"
}
```

---

## ⚡ RATE LIMITING

### **Rate Limits**

| Endpoint | Limit | Window | Scope |
|----------|-------|--------|-------|
| `/api/products` | 100 requests | 1 minute | Per IP |
| `/api/products/search` | 50 requests | 1 minute | Per IP |
| `/api/cart/*` | 200 requests | 1 minute | Per user |
| `/api/wishlist/*` | 100 requests | 1 minute | Per user |

### **Rate Limit Headers**
```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1640995200
```

---

## 🚨 ERROR HANDLING

### **Standard Error Response**
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "Human readable error message",
    "details": {
      "field": "Specific field error"
    },
    "timestamp": "2025-06-20T10:30:00Z",
    "requestId": "req_1234567890"
  }
}
```

### **HTTP Status Codes**

| Code | Description | Usage |
|------|-------------|-------|
| `200` | OK | Successful request |
| `201` | Created | Resource created |
| `400` | Bad Request | Invalid request parameters |
| `401` | Unauthorized | Authentication required |
| `403` | Forbidden | Access denied |
| `404` | Not Found | Resource not found |
| `429` | Too Many Requests | Rate limit exceeded |
| `500` | Internal Server Error | Server error |

---

*This API documentation provides **COMPLETE INTEGRATION DETAILS** for The White Laces Shop Page with **RESTful ENDPOINTS**, **COMPREHENSIVE ERROR HANDLING**, and **PRODUCTION-READY SPECIFICATIONS** ensuring **SEAMLESS INTEGRATION** and **OPTIMAL PERFORMANCE** for **MEXICO MARKET LAUNCH** and **GLOBAL EXPANSION**.*
