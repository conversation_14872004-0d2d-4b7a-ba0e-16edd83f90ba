'use client'

import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
import Link from 'next/link'
import { useCart } from '@/contexts/CartContext'

export default function CartSuccessModal({ 
  isOpen, 
  onClose, 
  product, 
  size = 'M', 
  quantity = 1 
}) {
  const { getItemsCount } = useCart()

  if (!isOpen || !product) return null

  // Auto close after 3 seconds
  setTimeout(() => {
    onClose()
  }, 3000)

  return (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
          onClick={onClose}
        >
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 50 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.8, y: 50 }}
            transition={{
              type: "spring",
              damping: 25,
              stiffness: 400,
              duration: 0.4
            }}
            className="bg-pure-white dark:bg-dark-gray rounded-2xl shadow-2xl max-w-md w-full overflow-hidden border border-neutral-200 dark:border-neutral-700"
            onClick={(e) => e.stopPropagation()}
          >
            {/* Success Header with Green Check */}
            <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 text-center">
              <motion.div
                initial={{ scale: 0, rotate: -180 }}
                animate={{ scale: 1, rotate: 0 }}
                transition={{ 
                  delay: 0.2, 
                  type: "spring", 
                  stiffness: 200,
                  damping: 15
                }}
                className="w-16 h-16 bg-white rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <motion.svg
                  initial={{ pathLength: 0 }}
                  animate={{ pathLength: 1 }}
                  transition={{ delay: 0.5, duration: 0.5 }}
                  className="w-8 h-8 text-green-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <motion.path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={3}
                    d="M5 13l4 4L19 7"
                  />
                </motion.svg>
              </motion.div>
              
              <motion.h2
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 }}
                className="text-2xl font-godber font-bold text-white tracking-godber-sm"
              >
                ¡Agregado al Carrito!
              </motion.h2>
              
              <motion.p
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.4 }}
                className="text-green-100 font-poppins text-sm mt-2"
              >
                Tu producto se agregó exitosamente
              </motion.p>
            </div>

            {/* Product Details */}
            <div className="p-6">
              <motion.div
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.5 }}
                className="flex items-center gap-4 mb-6"
              >
                {/* Product Image */}
                <div className="w-16 h-16 bg-light-gray dark:bg-neutral-700 rounded-lg overflow-hidden flex-shrink-0">
                  <Image
                    src={product.images?.[0] || product.image || "/placeholder.jpg"}
                    alt={product.name}
                    width={64}
                    height={64}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Product Info */}
                <div className="flex-1 min-w-0">
                  <h3 className="font-godber font-bold text-pure-black dark:text-pure-white text-sm line-clamp-2 tracking-godber-xs">
                    {product.name}
                  </h3>
                  <p className="font-poppins text-xs text-text-gray dark:text-neutral-400 uppercase tracking-wide mt-1">
                    {product.brand}
                  </p>
                  <div className="flex items-center gap-3 mt-2">
                    <span className="font-poppins text-xs text-text-gray dark:text-neutral-400">
                      Talla: <span className="font-semibold text-pure-black dark:text-pure-white">{size}</span>
                    </span>
                    <span className="font-poppins text-xs text-text-gray dark:text-neutral-400">
                      Cantidad: <span className="font-semibold text-pure-black dark:text-pure-white">{quantity}</span>
                    </span>
                  </div>
                </div>

                {/* Price */}
                <div className="text-right">
                  <span className="font-poppins font-bold text-lg text-pure-black dark:text-pure-white">
                    ${product.price?.toLocaleString('es-MX')}
                  </span>
                  {product.originalPrice && product.originalPrice > product.price && (
                    <p className="font-poppins text-xs text-text-gray dark:text-neutral-400 line-through">
                      ${product.originalPrice?.toLocaleString('es-MX')}
                    </p>
                  )}
                </div>
              </motion.div>

              {/* Cart Summary */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.6 }}
                className="bg-light-gray dark:bg-neutral-800 rounded-lg p-4 mb-6"
              >
                <div className="flex items-center justify-between">
                  <span className="font-poppins text-sm text-text-gray dark:text-neutral-400">
                    Total de productos en carrito:
                  </span>
                  <span className="font-poppins font-bold text-primary">
                    {getItemsCount()} {getItemsCount() === 1 ? 'producto' : 'productos'}
                  </span>
                </div>
              </motion.div>

              {/* Action Buttons */}
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 }}
                className="flex gap-3"
              >
                <button
                  onClick={onClose}
                  className="flex-1 px-4 py-3 bg-pure-white border-2 border-orange-500 text-pure-black font-poppins font-medium rounded-xl hover:bg-orange-500 hover:text-pure-black transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md"
                >
                  Seguir Comprando
                </button>
                
                <Link href="/cart" className="flex-1">
                  <button
                    onClick={onClose}
                    className="w-full px-4 py-3 bg-pure-white border-2 border-primary text-pure-black font-poppins font-medium rounded-xl hover:bg-primary hover:text-pure-black transition-all duration-300 transform hover:scale-105 shadow-sm hover:shadow-md"
                  >
                    Ver Carrito
                  </button>
                </Link>
              </motion.div>
            </div>

            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 w-8 h-8 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-all duration-200"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}
