#!/usr/bin/env node

/**
 * QUICK IMAGE PATH FIX
 * Temporarily fixes image paths to make them web-accessible
 * Converts --materials paths to /images/products paths
 */

const fs = require('fs')
const path = require('path')

const PRODUCTS_DATA_PATH = path.join(process.cwd(), 'lib', 'data', 'products.js')

function fixImagePaths() {
  console.log('🔧 FIXING IMAGE PATHS FOR WEB ACCESS...')
  
  try {
    // Read current products data
    const productsContent = fs.readFileSync(PRODUCTS_DATA_PATH, 'utf8')
    
    // Create backup
    const backupPath = PRODUCTS_DATA_PATH + '.backup.' + Date.now()
    fs.writeFileSync(backupPath, productsContent)
    console.log(`📋 Created backup: ${path.basename(backupPath)}`)
    
    // Fix image paths - convert from --materials to web-accessible paths
    let updatedContent = productsContent.replace(
      /\/--materials\/shoes\/2\. CYTTE\/([^"]+)/g,
      '/images/products/$1'
    )
    
    // Also fix any remaining --materials references
    updatedContent = updatedContent.replace(
      /"\/--materials\//g,
      '"/images/'
    )
    
    // Write updated content
    fs.writeFileSync(PRODUCTS_DATA_PATH, updatedContent)
    console.log('✅ Image paths updated to web-accessible format')
    
    // Count changes
    const originalMatches = (productsContent.match(/\/--materials\//g) || []).length
    const updatedMatches = (updatedContent.match(/\/--materials\//g) || []).length
    const changesCount = originalMatches - updatedMatches
    
    console.log(`📊 Fixed ${changesCount} image path references`)
    
    if (updatedMatches > 0) {
      console.log(`⚠️  ${updatedMatches} --materials references still remain`)
    }
    
  } catch (error) {
    console.error('❌ Error fixing image paths:', error.message)
    process.exit(1)
  }
}

// Create placeholder images directory structure
function createImageDirectoryStructure() {
  console.log('\n📁 CREATING IMAGE DIRECTORY STRUCTURE...')
  
  const publicImagesPath = path.join(process.cwd(), 'public', 'images')
  const productsPath = path.join(publicImagesPath, 'products')
  
  try {
    // Create directories
    if (!fs.existsSync(publicImagesPath)) {
      fs.mkdirSync(publicImagesPath, { recursive: true })
      console.log('📂 Created /public/images directory')
    }
    
    if (!fs.existsSync(productsPath)) {
      fs.mkdirSync(productsPath, { recursive: true })
      console.log('📂 Created /public/images/products directory')
    }
    
    // Create placeholder image
    const placeholderPath = path.join(publicImagesPath, 'placeholder.jpg')
    if (!fs.existsSync(placeholderPath)) {
      // Create a simple placeholder file (you can replace this with an actual image)
      fs.writeFileSync(placeholderPath, '')
      console.log('🖼️  Created placeholder.jpg')
    }
    
    console.log('✅ Image directory structure ready')
    
  } catch (error) {
    console.error('❌ Error creating directories:', error.message)
  }
}

// Add fallback image handling to SimpleProductCard
function createImageFallbackComponent() {
  console.log('\n🔧 CREATING IMAGE FALLBACK COMPONENT...')
  
  const componentPath = path.join(process.cwd(), 'components', 'ui', 'OptimizedImage.jsx')
  
  const componentContent = `'use client'

import { useState } from 'react'
import Image from 'next/image'

export default function OptimizedImage({ 
  src, 
  alt, 
  fill = false, 
  width, 
  height, 
  className = '', 
  priority = false,
  ...props 
}) {
  const [imgSrc, setImgSrc] = useState(src)
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleError = () => {
    setHasError(true)
    setImgSrc('/images/placeholder.jpg')
  }

  const handleLoad = () => {
    setIsLoading(false)
  }

  return (
    <div className={className}>
      {isLoading && !hasError && (
        <div className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded" />
      )}
      
      <Image
        src={imgSrc}
        alt={alt}
        fill={fill}
        width={width}
        height={height}
        className={className}
        priority={priority}
        onError={handleError}
        onLoad={handleLoad}
        {...props}
      />
      
      {hasError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-500 text-sm">
          Imagen no disponible
        </div>
      )}
    </div>
  )
}`

  try {
    fs.writeFileSync(componentPath, componentContent)
    console.log('✅ Created OptimizedImage component with fallback handling')
  } catch (error) {
    console.error('❌ Error creating component:', error.message)
  }
}

// Main execution
function main() {
  console.log('🚀 STARTING IMAGE PATH FIX PROCESS...\n')
  
  fixImagePaths()
  createImageDirectoryStructure()
  createImageFallbackComponent()
  
  console.log('\n✅ IMAGE PATH FIX COMPLETE!')
  console.log('\n📋 NEXT STEPS:')
  console.log('1. Copy actual images to /public/images/products/')
  console.log('2. Run image conversion script for WebP optimization')
  console.log('3. Update SimpleProductCard to use OptimizedImage component')
  console.log('4. Test image loading on the website')
}

if (require.main === module) {
  main()
}

module.exports = {
  fixImagePaths,
  createImageDirectoryStructure,
  createImageFallbackComponent
}
