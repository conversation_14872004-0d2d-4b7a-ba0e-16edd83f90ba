'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import GamificationSystem from './GamificationSystem'

export default function FloatingGamificationWidget() {
  const [isExpanded, setIsExpanded] = useState(false)
  const [userStats, setUserStats] = useState(null)
  const [dailyProgress, setDailyProgress] = useState(0)
  const [hasNewRewards, setHasNewRewards] = useState(false)
  const [showFullSystem, setShowFullSystem] = useState(false)

  useEffect(() => {
    loadUserStats()
    checkDailyProgress()
  }, [])

  const loadUserStats = async () => {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    setUserStats({
      level: 8,
      currentXP: 2450,
      nextLevelXP: 3000,
      tier: 'Gold',
      points: 8750,
      streak: 3,
      completedChallenges: 2,
      totalChallenges: 3
    })
    
    // Check for new rewards
    setHasNewRewards(Math.random() > 0.7) // 30% chance of new rewards
  }

  const checkDailyProgress = () => {
    // Simulate daily progress calculation
    const completed = Math.floor(Math.random() * 3) + 1 // 1-3 completed challenges
    const total = 3
    setDailyProgress((completed / total) * 100)
  }

  const getXPProgress = () => {
    if (!userStats) return 0
    return (userStats.currentXP / userStats.nextLevelXP) * 100
  }

  const getTierColor = (tier) => {
    switch (tier) {
      case 'Bronze': return 'from-orange-600 to-orange-800'
      case 'Silver': return 'from-gray-400 to-gray-600'
      case 'Gold': return 'from-yellow-400 to-yellow-600'
      case 'Platinum': return 'from-purple-400 to-purple-600'
      case 'Diamond': return 'from-blue-400 to-blue-600'
      default: return 'from-gray-400 to-gray-600'
    }
  }

  if (!userStats) {
    return null
  }

  return (
    <>
      {/* Floating Widget - Mobile Responsive */}
      <motion.div
        initial={{ opacity: 0, x: 100 }}
        animate={{ opacity: 1, x: 0 }}
        className="fixed top-1/3 right-2 lg:top-1/2 lg:right-6 transform -translate-y-1/2 z-30 lg:z-40"
      >
        <AnimatePresence>
          {!isExpanded && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              className="relative"
            >
              <motion.button
                onClick={() => setIsExpanded(true)}
                className={`w-12 h-12 lg:w-16 lg:h-16 rounded-full bg-gradient-to-r ${getTierColor(userStats.tier)} text-white shadow-md lg:shadow-lg hover:shadow-lg lg:hover:shadow-xl flex items-center justify-center transition-all duration-300 relative overflow-hidden`}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <span className="text-lg lg:text-xl font-bold">{userStats.level}</span>
                
                {/* XP Progress Ring */}
                <svg className="absolute inset-0 w-full h-full transform -rotate-90" viewBox="0 0 64 64">
                  <circle
                    cx="32"
                    cy="32"
                    r="28"
                    fill="none"
                    stroke="rgba(255,255,255,0.2)"
                    strokeWidth="3"
                  />
                  <motion.circle
                    cx="32"
                    cy="32"
                    r="28"
                    fill="none"
                    stroke="rgba(255,255,255,0.8)"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeDasharray={`${2 * Math.PI * 28}`}
                    initial={{ strokeDashoffset: 2 * Math.PI * 28 }}
                    animate={{ strokeDashoffset: 2 * Math.PI * 28 * (1 - getXPProgress() / 100) }}
                    transition={{ duration: 1, ease: "easeOut" }}
                  />
                </svg>
              </motion.button>

              {/* Notification Badges */}
              {hasNewRewards && (
                <motion.div
                  className="absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs font-bold"
                  animate={{
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                  }}
                >
                  !
                </motion.div>
              )}

              {/* Streak Indicator */}
              {userStats.streak > 0 && (
                <motion.div
                  className="absolute -bottom-2 -left-2 bg-orange-500 text-white rounded-full px-2 py-1 text-xs font-bold flex items-center gap-1"
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: 0.5 }}
                >
                  <span>🔥</span>
                  <span>{userStats.streak}</span>
                </motion.div>
              )}
            </motion.div>
          )}

          {/* Expanded Widget */}
          {isExpanded && (
            <motion.div
              initial={{ opacity: 0, scale: 0.8, x: 50 }}
              animate={{ opacity: 1, scale: 1, x: 0 }}
              exit={{ opacity: 0, scale: 0.8, x: 50 }}
              className="w-72 lg:w-80"
            >
              <Card variant="glass">
                <CardContent className="p-6">
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-bold text-gray-800">TWL Rewards</h3>
                    <div className="flex gap-2">
                      <button
                        onClick={() => setShowFullSystem(true)}
                        className="text-gray-500 hover:text-gray-700 transition-colors"
                        title="Ver sistema completo"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                        </svg>
                      </button>
                      <button
                        onClick={() => setIsExpanded(false)}
                        className="text-gray-500 hover:text-gray-700 transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Level Progress */}
                  <div className="mb-6">
                    <div className="flex items-center gap-3 mb-3">
                      <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${getTierColor(userStats.tier)} flex items-center justify-center text-white font-bold`}>
                        {userStats.level}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="font-semibold text-gray-800">Nivel {userStats.level}</span>
                          <Badge variant="primary" size="sm">{userStats.tier}</Badge>
                        </div>
                        <div className="text-sm text-gray-600">
                          {userStats.currentXP} / {userStats.nextLevelXP} XP
                        </div>
                      </div>
                    </div>
                    
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        className="bg-[#BFFF00] h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${getXPProgress()}%` }}
                        transition={{ duration: 1, ease: "easeOut" }}
                      />
                    </div>
                  </div>

                  {/* Daily Progress */}
                  <div className="mb-6">
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-sm font-medium text-gray-700">Progreso Diario</span>
                      <span className="text-sm text-gray-500">
                        {userStats.completedChallenges}/{userStats.totalChallenges} desafíos
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        className="bg-orange-500 h-2 rounded-full"
                        initial={{ width: 0 }}
                        animate={{ width: `${dailyProgress}%` }}
                        transition={{ duration: 1, ease: "easeOut", delay: 0.2 }}
                      />
                    </div>
                  </div>

                  {/* Quick Stats */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-[#BFFF00]">{userStats.points.toLocaleString()}</div>
                      <div className="text-xs text-gray-600">Puntos</div>
                    </div>
                    <div className="text-center p-3 bg-gray-50 rounded-lg">
                      <div className="text-2xl font-bold text-orange-500 flex items-center justify-center gap-1">
                        <span>🔥</span>
                        <span>{userStats.streak}</span>
                      </div>
                      <div className="text-xs text-gray-600">Racha</div>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="space-y-2">
                    <AnimatedButton
                      variant="accent"
                      size="sm"
                      className="w-full"
                      onClick={() => setShowFullSystem(true)}
                    >
                      Ver Desafíos Diarios
                    </AnimatedButton>
                    
                    {hasNewRewards && (
                      <AnimatedButton
                        variant="primary"
                        size="sm"
                        className="w-full"
                        icon={
                          <motion.span
                            animate={{ rotate: [0, 10, -10, 0] }}
                            transition={{ duration: 0.5, repeat: Infinity, repeatDelay: 2 }}
                          >
                            🎁
                          </motion.span>
                        }
                      >
                        Reclamar Recompensas
                      </AnimatedButton>
                    )}
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Full Gamification System Modal */}
      <AnimatePresence>
        {showFullSystem && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
            onClick={() => setShowFullSystem(false)}
          >
            <motion.div
              initial={{ opacity: 0, scale: 0.9, y: 20 }}
              animate={{ opacity: 1, scale: 1, y: 0 }}
              exit={{ opacity: 0, scale: 0.9, y: 20 }}
              className="max-w-6xl w-full max-h-[90vh] overflow-y-auto"
              onClick={(e) => e.stopPropagation()}
            >
              <div className="relative">
                <button
                  onClick={() => setShowFullSystem(false)}
                  className="absolute top-4 right-4 z-10 w-8 h-8 bg-white/10 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/20 transition-colors"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
                <GamificationSystem />
              </div>
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
