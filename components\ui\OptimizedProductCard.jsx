'use client'

import { useState, useCallback } from 'react'
import Link from 'next/link'
import { motion } from 'framer-motion'
import { useCart } from '@/contexts/CartContext'
import { Card, CardContent } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'
import WishlistButton from '@/components/ui/WishlistButton'
import { ShoppingCart } from 'lucide-react'

export default function OptimizedProductCard({ product, index = 0, onAuthRequired }) {
  const { addItem } = useCart()
  const [isHovered, setIsHovered] = useState(false)
  const [imageError, setImageError] = useState(false)
  const [selectedSize, setSelectedSize] = useState('')
  const [showSizeSelector, setShowSizeSelector] = useState(false)
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  // Optimized hover handlers
  const handleMouseEnter = useCallback(() => {
    setIsHovered(true)
  }, [])

  const handleMouseLeave = useCallback(() => {
    setIsHovered(false)
  }, [])

  // Optimized cart handler
  const handleAddToCart = useCallback(async (e) => {
    e.stopPropagation()
    
    if (!selectedSize) {
      setShowSizeSelector(true)
      return
    }

    if (isAddingToCart) return
    
    setIsAddingToCart(true)
    try {
      await addItem(product.id, selectedSize, 1)
      setShowSizeSelector(false)
      setSelectedSize('')
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setIsAddingToCart(false)
    }
  }, [selectedSize, isAddingToCart, addItem, product.id])

  // Get product images with fallback
  const getProductImages = () => {
    if (product?.images && product.images.length > 0) {
      return {
        primary: product.images[0],
        secondary: product.images[1] || product.images[0] // Use first image as fallback
      }
    }
    
    // Fallback for products without images
    return {
      primary: product?.image || '/images/placeholder-shoe.jpg',
      secondary: product?.image2 || product?.image || '/images/placeholder-shoe.jpg'
    }
  }

  const { primary, secondary } = getProductImages()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.4, 
        delay: index * 0.05,
        ease: "easeOut"
      }}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      className="group cursor-pointer h-full"
    >
      <Card
        variant="default"
        className="h-full flex flex-col overflow-hidden bg-pure-white dark:bg-neutral-800 border border-gray-200 dark:border-gray-700 hover:border-lime-green dark:hover:border-lime-green transition-all duration-300 hover:shadow-xl"
      >
        <CardContent className="p-0 h-full flex flex-col">
          {/* Optimized Image Container */}
          <div className="relative aspect-square overflow-hidden bg-gray-100 dark:bg-gray-800">
            {/* Primary Image */}
            <motion.img
              src={primary}
              alt={product?.name || 'Product Image'}
              className="w-full h-full object-cover"
              initial={{ opacity: 1 }}
              animate={{ 
                opacity: isHovered && secondary !== primary ? 0 : 1,
                scale: isHovered ? 1.05 : 1
              }}
              transition={{ duration: 0.3 }}
              onError={() => setImageError(true)}
              loading={index < 4 ? "eager" : "lazy"}
            />

            {/* Secondary Image (Hover Effect) */}
            {secondary && secondary !== primary && !imageError && (
              <motion.img
                src={secondary}
                alt={`${product?.name} - Vista alternativa`}
                className="absolute inset-0 w-full h-full object-cover"
                initial={{ opacity: 0 }}
                animate={{ 
                  opacity: isHovered ? 1 : 0,
                  scale: isHovered ? 1.05 : 1
                }}
                transition={{ duration: 0.3 }}
                onError={() => setImageError(true)}
              />
            )}

            {/* Hover Overlay */}
            <motion.div
              className="absolute inset-0 bg-black/10"
              initial={{ opacity: 0 }}
              animate={{ opacity: isHovered ? 1 : 0 }}
              transition={{ duration: 0.3 }}
            />

            {/* Badges */}
            <div className="absolute top-3 left-3 flex flex-col gap-2 z-10">
              {product?.isLimited && (
                <Badge variant="limited" size="sm">
                  Limitado
                </Badge>
              )}
              {product?.isVip && (
                <Badge variant="vip" size="sm">
                  VIP
                </Badge>
              )}
              {product?.isNew && (
                <Badge variant="success" size="sm">
                  Nuevo
                </Badge>
              )}
            </div>

            {/* Quick Actions */}
            <motion.div 
              className="absolute top-3 right-3 z-10"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ 
                opacity: isHovered ? 1 : 0,
                scale: isHovered ? 1 : 0.8
              }}
              transition={{ duration: 0.2 }}
            >
              <WishlistButton
                productId={product?.id}
                size="sm"
                variant="secondary"
                className="h-8 w-8 bg-white/90 hover:bg-white"
                onAuthRequired={onAuthRequired}
              />
            </motion.div>
          </div>

          {/* Product Info */}
          <div className="p-4 flex-1 flex flex-col justify-between">
            {/* Brand & Name */}
            <Link href={`/product/${product?.id}`} className="block mb-3">
              <p className="text-xs text-gray-500 dark:text-gray-400 font-medium font-poppins uppercase tracking-wide mb-1">
                {product?.brand || 'Premium Brand'}
              </p>
              <h3 className="font-semibold text-pure-black dark:text-pure-white line-clamp-2 group-hover:text-lime-green transition-colors font-poppins text-sm leading-tight">
                {product?.name || 'Luxury Sneaker Collection'}
              </h3>
            </Link>

            {/* Price */}
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg font-bold text-lime-green-dark font-poppins">
                  ${product?.price || 210}
                </span>
                {product?.originalPrice && product.originalPrice > product.price && (
                  <span className="text-sm text-gray-500 line-through font-poppins">
                    ${product.originalPrice}
                  </span>
                )}
              </div>

              {/* Cart Button */}
              <motion.button
                onClick={handleAddToCart}
                disabled={isAddingToCart}
                className="w-10 h-10 bg-lime-green rounded-full flex items-center justify-center shadow-lg hover:shadow-xl transition-all duration-300 disabled:opacity-50"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
              >
                {isAddingToCart ? (
                  <div className="w-4 h-4 border-2 border-pure-black border-t-transparent rounded-full animate-spin" />
                ) : (
                  <ShoppingCart className="w-4 h-4 text-pure-black" />
                )}
              </motion.button>
            </div>

            {/* Size Selector */}
            {showSizeSelector && (
              <motion.div
                initial={{ opacity: 0, height: 0 }}
                animate={{ opacity: 1, height: 'auto' }}
                exit={{ opacity: 0, height: 0 }}
                className="mt-3 space-y-2"
              >
                <p className="text-xs text-gray-600 dark:text-gray-400">Selecciona tu talla:</p>
                <div className="grid grid-cols-4 gap-1">
                  {(product?.sizes || ['36', '37', '38', '39', '40', '41', '42', '43']).slice(0, 8).map((size) => (
                    <button
                      key={size}
                      onClick={() => setSelectedSize(size)}
                      className={`text-xs py-1 px-2 rounded border transition-colors font-poppins ${
                        selectedSize === size
                          ? 'bg-lime-green text-pure-black border-lime-green'
                          : 'border-gray-300 text-gray-600 hover:border-lime-green hover:text-lime-green'
                      }`}
                    >
                      {size}
                    </button>
                  ))}
                </div>
              </motion.div>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  )
}
