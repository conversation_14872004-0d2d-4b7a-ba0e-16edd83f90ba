# TWL Enterprise Troubleshooting Guide

**🔧 Comprehensive Troubleshooting and FAQ for Enterprise Operations**

This guide provides solutions to common issues, debugging techniques, and frequently asked questions for the TWL Enterprise Product System.

## 📋 Table of Contents

- [Quick Diagnostics](#-quick-diagnostics)
- [Common Issues](#-common-issues)
- [Performance Issues](#-performance-issues)
- [Cache Issues](#-cache-issues)
- [API Issues](#-api-issues)
- [Deployment Issues](#-deployment-issues)
- [Monitoring & Debugging](#-monitoring--debugging)
- [FAQ](#-faq)

## 🚀 Quick Diagnostics

### **System Health Check**

```bash
# Check system status
curl http://localhost:3000/api/enterprise/system/status

# Expected healthy response
{
  "success": true,
  "data": {
    "status": "healthy",
    "components": {
      "loader": "healthy",
      "cache": "healthy",
      "api": "healthy",
      "scanner": "healthy"
    }
  }
}
```

### **Quick Debug Commands**

```bash
# Test enterprise system
npm run test:enterprise

# Check product loading
node -e "
const { getTWLSystem } = require('./lib/enterprise/TWLEnterpriseSystem');
getTWLSystem().getHealth().then(console.log);
"

# Check cache performance
node -e "
const { getTWLSystem } = require('./lib/enterprise/TWLEnterpriseSystem');
getTWLSystem().getMetrics().then(console.log);
"

# Check products directory
ls -la public/products/
find public/products/ -name "*.webp" | wc -l
```

### **Log Analysis**

```bash
# Check application logs
tail -f logs/application.log

# Check error logs
grep -i error logs/application.log | tail -20

# Check performance logs
grep -i "slow\|timeout\|performance" logs/application.log
```

## ❌ Common Issues

### **1. System Won't Initialize**

**Symptoms:**
- System startup fails
- "System not ready" errors
- Initialization timeout

**Diagnosis:**
```bash
# Check Node.js version
node --version  # Should be 18+

# Check products directory
ls -la public/products/

# Check permissions
ls -la public/

# Run with debug logging
NODE_ENV=development npm run test:enterprise
```

**Solutions:**

#### **Missing Products Directory**
```bash
# Create products directory
mkdir -p public/products

# Copy your products
cp -r /path/to/your/products/* public/products/

# Set correct permissions
chmod -R 755 public/products/
```

#### **Node.js Version Issues**
```bash
# Update Node.js
nvm install 18
nvm use 18

# Or using package manager
sudo apt update
sudo apt install nodejs npm
```

#### **Memory Issues**
```bash
# Increase Node.js memory limit
node --max-old-space-size=4096 your-app.js

# Or set environment variable
export NODE_OPTIONS="--max-old-space-size=4096"
```

### **2. Products Not Loading**

**Symptoms:**
- Empty product search results
- "Product not found" errors
- Zero products in system status

**Diagnosis:**
```typescript
// Check product scanning
const system = getTWLSystem()
const status = system.getSystemStatus()
console.log('Products loaded:', status.productsLoaded)

// Manual scan
await system.triggerScan()
```

**Solutions:**

#### **Incorrect Products Path**
```typescript
// Check configuration
const config = {
  productsBasePath: 'public/products', // Correct path
  // not: '/absolute/path/to/products'
}
```

#### **File Permissions**
```bash
# Fix permissions
sudo chown -R $USER:$USER public/products/
chmod -R 755 public/products/
```

#### **Missing WebP Images**
```bash
# Check for WebP files
find public/products/ -name "*.webp" | head -10

# Convert JPG to WebP if needed
for file in public/products/**/*.jpg; do
  cwebp "$file" -o "${file%.jpg}.webp"
done
```

### **3. Poor Performance**

**Symptoms:**
- Slow API responses (> 200ms)
- High memory usage
- Timeouts

**Diagnosis:**
```typescript
// Check performance metrics
const metrics = system.getMetrics()
console.log('Average response time:', metrics.performance.averageResponseTime)
console.log('Cache hit rate:', metrics.cache.hitRate)
console.log('Memory usage:', metrics.cache.memoryUsage)
```

**Solutions:**

#### **Low Cache Hit Rate**
```typescript
// Increase cache size
const config = {
  cacheMemorySize: 200, // Increase from 100MB
  cacheFileEnabled: true,
  cacheRedisEnabled: true // Enable Redis if available
}
```

#### **Memory Leaks**
```bash
# Monitor memory usage
node --inspect your-app.js
# Open chrome://inspect in Chrome

# Use clinic.js for analysis
npm install -g clinic
clinic doctor -- node your-app.js
```

#### **CPU Bottlenecks**
```typescript
// Enable profiling
const config = {
  enableProfiling: true,
  maxConcurrentLoads: 5 // Reduce concurrent operations
}
```

## ⚡ Performance Issues

### **Slow API Responses**

**Target:** < 100ms average response time

**Diagnosis:**
```bash
# Test API performance
time curl http://localhost:3000/api/enterprise/products/test-product

# Load test
npm install -g autocannon
autocannon -c 10 -d 30 http://localhost:3000/api/enterprise/products
```

**Solutions:**

1. **Enable All Cache Layers**
```typescript
const config = {
  enableCache: true,
  cacheMemorySize: 200,
  cacheFileEnabled: true,
  cacheRedisEnabled: true
}
```

2. **Optimize Database Queries**
```typescript
// Use batch loading
const products = await system.loadProducts([id1, id2, id3])

// Instead of individual calls
const product1 = await system.getProduct(id1)
const product2 = await system.getProduct(id2)
```

3. **Enable Compression**
```javascript
// In your Next.js config
module.exports = {
  compress: true,
  experimental: {
    gzipSize: true
  }
}
```

### **High Memory Usage**

**Target:** < 512MB memory usage

**Diagnosis:**
```bash
# Monitor memory
node --expose-gc --inspect your-app.js

# Check heap usage
node -e "console.log(process.memoryUsage())"
```

**Solutions:**

1. **Optimize Cache Size**
```typescript
const config = {
  cacheMemorySize: 100, // Reduce if needed
  cacheFileEnabled: true // Offload to file cache
}
```

2. **Enable Garbage Collection**
```bash
# Force GC periodically
node --expose-gc --gc-interval=100 your-app.js
```

3. **Clear Cache Periodically**
```typescript
// Clear cache when memory is high
setInterval(() => {
  const usage = process.memoryUsage()
  if (usage.heapUsed > 400 * 1024 * 1024) { // 400MB
    cache.clear()
  }
}, 60000) // Check every minute
```

## 🗄️ Cache Issues

### **Low Cache Hit Rate**

**Target:** > 95% cache hit rate

**Diagnosis:**
```typescript
const metrics = system.getMetrics()
console.log('Cache metrics:', {
  hitRate: metrics.cache.hitRate,
  memoryUsage: metrics.cache.memoryUsage,
  fileUsage: metrics.cache.fileUsage
})
```

**Solutions:**

1. **Increase Cache Size**
```typescript
const config = {
  cacheMemorySize: 200, // Increase memory cache
  cacheFileEnabled: true,
  fileCacheSize: 1000 // Increase file cache
}
```

2. **Optimize Cache TTL**
```typescript
const config = {
  memoryTTL: 7200, // 2 hours instead of 1
  fileTTL: 172800  // 2 days instead of 1
}
```

3. **Warm Up Cache**
```typescript
// Pre-load popular products
const popularProducts = ['product1', 'product2', 'product3']
for (const id of popularProducts) {
  await system.getProduct(id)
}
```

### **Cache Corruption**

**Symptoms:**
- Inconsistent data
- Stale cache entries
- Cache errors

**Solutions:**

1. **Clear All Caches**
```bash
# Clear file cache
rm -rf .cache/products/*

# Restart application to clear memory cache
pm2 restart twl-enterprise
```

2. **Rebuild Cache**
```typescript
// Force cache rebuild
await system.cache.clear()
await system.triggerScan()
```

## 🌐 API Issues

### **Rate Limiting Errors**

**Symptoms:**
- 429 status codes
- "Rate limit exceeded" errors

**Solutions:**

1. **Increase Rate Limits**
```typescript
const config = {
  rateLimitRequests: 200, // Increase from 100
  rateLimitWindow: 60
}
```

2. **Implement Exponential Backoff**
```typescript
async function apiCallWithRetry(url, maxRetries = 3) {
  for (let i = 0; i < maxRetries; i++) {
    try {
      const response = await fetch(url)
      if (response.status === 429) {
        const delay = Math.pow(2, i) * 1000 // Exponential backoff
        await new Promise(resolve => setTimeout(resolve, delay))
        continue
      }
      return response
    } catch (error) {
      if (i === maxRetries - 1) throw error
    }
  }
}
```

### **Authentication Issues**

**Symptoms:**
- 401/403 errors
- "Unauthorized" responses

**Solutions:**

1. **Check API Key**
```bash
# Verify API key is set
echo $TWL_API_KEY

# Test with curl
curl -H "Authorization: Bearer $TWL_API_KEY" \
     http://localhost:3000/api/enterprise/system/status
```

2. **Regenerate API Key**
```typescript
// Generate new API key
const crypto = require('crypto')
const newApiKey = crypto.randomBytes(32).toString('hex')
console.log('New API key:', newApiKey)
```

## 🚢 Deployment Issues

### **Docker Build Failures**

**Common Issues:**

1. **Node.js Version Mismatch**
```dockerfile
# Use specific Node.js version
FROM node:18.17.0-alpine
```

2. **Missing Dependencies**
```dockerfile
# Install all dependencies
RUN apk add --no-cache libc6-compat python3 make g++
```

3. **Build Context Issues**
```bash
# Check .dockerignore
echo "node_modules" >> .dockerignore
echo ".git" >> .dockerignore
echo "*.log" >> .dockerignore
```

### **Kubernetes Deployment Issues**

**Common Issues:**

1. **Resource Limits**
```yaml
resources:
  requests:
    memory: "512Mi"
    cpu: "250m"
  limits:
    memory: "1Gi"    # Increase if needed
    cpu: "500m"
```

2. **Health Check Failures**
```yaml
livenessProbe:
  httpGet:
    path: /api/enterprise/system/status
    port: 3000
  initialDelaySeconds: 60  # Increase startup time
  timeoutSeconds: 10       # Increase timeout
```

## 🔍 Monitoring & Debugging

### **Enable Debug Mode**

```typescript
// Enable comprehensive debugging
const system = await initializeTWLSystem({
  logLevel: 'debug',
  enableProfiling: true,
  enableMetrics: true
})

// Check logs
const logs = system.getLogger().getLogBuffer()
console.log('Debug logs:', logs)
```

### **Performance Profiling**

```bash
# CPU profiling
node --prof your-app.js
node --prof-process isolate-*.log > profile.txt

# Memory profiling
node --inspect your-app.js
# Open chrome://inspect
```

### **Custom Monitoring**

```typescript
// Add custom metrics
class CustomMonitor {
  constructor(system) {
    this.system = system
    this.startMonitoring()
  }

  startMonitoring() {
    setInterval(() => {
      const metrics = this.system.getMetrics()
      
      // Log performance issues
      if (metrics.performance.averageResponseTime > 100) {
        console.warn('High response time:', metrics.performance.averageResponseTime)
      }
      
      if (metrics.cache.hitRate < 90) {
        console.warn('Low cache hit rate:', metrics.cache.hitRate)
      }
      
      if (metrics.cache.memoryUsage > 400) {
        console.warn('High memory usage:', metrics.cache.memoryUsage)
      }
    }, 30000) // Check every 30 seconds
  }
}
```

## ❓ FAQ

### **Q: How do I add new products?**

A: Simply add product folders to `public/products/` following the existing structure. The system will automatically scan and index them on the next scan cycle.

```bash
# Add new product
mkdir -p "public/products/1. SNEAKERS/1. NIKE Limited Edition/NEW_PRODUCT"
# Add images and videos to the folder
# Trigger manual scan
curl -X POST http://localhost:3000/api/enterprise/system/scan
```

### **Q: How do I update product information?**

A: Update the product files and trigger a rescan:

```bash
# Update product files
# Then trigger scan
npm run scan:products
```

### **Q: How do I backup the system?**

A: Backup the products directory and configuration:

```bash
# Backup products
tar -czf products-backup-$(date +%Y%m%d).tar.gz public/products/

# Backup configuration
cp -r config/ config-backup-$(date +%Y%m%d)/

# Backup cache (optional)
tar -czf cache-backup-$(date +%Y%m%d).tar.gz .cache/
```

### **Q: How do I scale the system?**

A: Use horizontal scaling with load balancers:

```yaml
# docker-compose.yml
services:
  app:
    deploy:
      replicas: 5  # Scale to 5 instances
```

### **Q: How do I monitor production?**

A: Use the built-in monitoring endpoints:

```bash
# System health
curl http://your-domain.com/api/enterprise/system/status

# Performance metrics
curl http://your-domain.com/api/enterprise/system/metrics
```

### **Q: How do I troubleshoot memory leaks?**

A: Use Node.js profiling tools:

```bash
# Install clinic.js
npm install -g clinic

# Profile memory
clinic doctor -- node your-app.js

# Analyze heap
clinic heapprofiler -- node your-app.js
```

### **Q: How do I optimize for mobile?**

A: The system is already mobile-optimized, but you can:

1. Enable image compression
2. Use smaller cache sizes on mobile
3. Implement lazy loading
4. Use CDN for static assets

---

**🔧 This troubleshooting guide covers the most common issues and their solutions. For additional support, check the system logs and monitoring dashboards.**

**📞 For enterprise support, contact the TWL engineering team with detailed error logs and system metrics.**
