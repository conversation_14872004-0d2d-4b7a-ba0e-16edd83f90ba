// Product Catalog System for TWL - Manages all products across categories
import { generateProductId, parseProductId, validateProductId } from './product-id-generator.js'

/**
 * Product catalog structure based on CYTTE directory organization
 */
export const PRODUCT_CATALOG = {
  SNEAKERS: {
    'NIKE Limited Edition': {
      'AIR FORCE': {
        MIXTE: {
          GUCCI: ['BD7700-222', 'JGD212-EJD', 'JGD212-ZZF', 'ZED212-E<PERSON>', 'ZED212-EJD', 'ZED212-JJG', 'ZED482-JDK', 'ZED482-JJJ'],
          LV: ['20214673875005', 'JGD212-EJ<PERSON>', 'JGD212-EJ<PERSON>', 'JGD212-EJ<PERSON>', 'JGD534-ZJJ', 'JZD474-EJH', 'ZED212-ZZS'],
          SUPREME: ['JGD212-<PERSON>J<PERSON>', 'JGD212-EJS', 'ZDD577-<PERSON>J<PERSON>', 'ZDD577-EJ<PERSON>', 'ZED212-E<PERSON>', 'ZED212-JJ<PERSON>'],
          STUSSY: ['JGD212-EZD', 'ZDD101-EJD', 'ZED212-EJE', 'ZED212-EJF', 'ZED212-EJJ', 'ZED212-EJZ', 'ZED212-ZJZ'],
          'THE NORTH FACE': ['JGD212-EJJ', 'ZDD101-EJS', 'ZED212-EJF', 'ZED212-EJZ', 'ZED212-JJK', 'ZED212-ZZS', 'ZED482-EDF'],
          BAPE: ['DQ1098-349', 'JGD482-ZJR', 'ZED212-EDJ', 'ZED212-ZZE', 'ZED212-ZZH'],
          DIOR: ['13872628583005', 'JGD482-ZJR', 'JZD101-ZZH', 'ZED212-EJE', 'ZED482-EDK', 'ZED482-JDG'],
          UNDEFEATED: ['13832628583005', 'JGD212-EJK', 'JSD212-EDJ', 'ZED212-ZZK'],
          LEVIS: ['ZED482-EDK', 'ZED482-JDK'],
          'NOCTA Drake': ['NO0224-027', 'NO0224-028'],
          'OFF WHITE': ['AO4606-001', 'JSD121-EDJ'],
          TIFFANY: ['EDD515-KDZ', 'ZED212-KZF', 'ZFD212-KDF'],
          OTHERS: ['DM7926-300', 'JGD212-EDS', 'JGD212-JJZ', 'JGD482-EJR', 'JGD482-ZJR', 'ZDD577-EJK', 'ZED212-JJK', 'ZED482-EJK', 'ZED482-EJS', 'ZED482-JJD', 'ZJD212-EJJ']
        },
        WOMEN: {
          OTHERS: ['Various models available']
        }
      },
      'AIR JORDAN': {
        MIXTE: {
          OTHERS: ['Various Jordan models available']
        }
      },
      CORTEZ: {
        MIXTE: {
          OTHERS: ['Various Cortez models available']
        },
        WOMEN: {
          OTHERS: ['Various Cortez models available']
        }
      },
      'DUNK LOW': {
        MIXTE: {
          OTHERS: ['Various Dunk models available']
        }
      },
      'AIR MAX 1': {
        MIXTE: {
          OTHERS: ['Various Air Max 1 models available']
        }
      },
      'AIR MAX 97': {
        MIXTE: {
          OTHERS: ['Various Air Max 97 models available']
        }
      }
    },
    'ADIDAS Limited Edition': {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various Adidas models available']
        }
      }
    },
    HERMES: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various Hermes models available']
        }
      }
    },
    GUCCI: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['13412565127048', '13413534043065', '13432545668006', '13432825588046', '13433534043065', '13438975870007', '13443434043065', '13458855390069', '13462895737048', 'JFD233-ADS', 'JFD538-JJJ', 'JKD129-DDK', 'JKD129-EJZ', 'JKD170-DZF', 'JZD129-EJJ', 'ZDD145-ZZJ']
        },
        WOMEN: {
          OTHERS: ['13403164833067', '13418565120X01', '13432025588046', '13432065128065', 'JGD170-AZG']
        },
        MEN: {
          OTHERS: ['13444066122007', 'JKD233-EDS', 'ZGD213-EZF']
        }
      }
    }
  },
  SANDALS: {
    'NIKE Collabs': {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various Nike sandal collabs available']
        }
      }
    },
    GUCCI: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various Gucci sandals available']
        }
      }
    },
    DIOR: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various Dior sandals available']
        }
      }
    },
    LV: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various LV sandals available']
        }
      }
    }
  },
  FORMAL: {
    CHANEL: {
      OTHERS: {
        WOMEN: {
          OTHERS: ['Various Chanel formal shoes available']
        }
      }
    },
    GUCCI: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various Gucci formal shoes available']
        }
      }
    }
  },
  CASUAL: {
    UGG: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various UGG casual shoes available']
        }
      }
    },
    LV: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various LV casual shoes available']
        }
      }
    },
    'MIU MIU': {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various Miu Miu casual shoes available']
        }
      }
    }
  },
  KIDS: {
    UGG: {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various UGG kids shoes available']
        }
      }
    },
    'GOLDEN GOOSE': {
      OTHERS: {
        MIXTE: {
          OTHERS: ['Various Golden Goose kids shoes available']
        }
      }
    }
  }
}

/**
 * Get all available product IDs from the catalog
 * @returns {Array} Array of all product IDs
 */
export const getAllProductIds = () => {
  const productIds = []
  
  Object.keys(PRODUCT_CATALOG).forEach(category => {
    Object.keys(PRODUCT_CATALOG[category]).forEach(brand => {
      Object.keys(PRODUCT_CATALOG[category][brand]).forEach(modelFamily => {
        Object.keys(PRODUCT_CATALOG[category][brand][modelFamily]).forEach(gender => {
          Object.keys(PRODUCT_CATALOG[category][brand][modelFamily][gender]).forEach(collaboration => {
            const skus = PRODUCT_CATALOG[category][brand][modelFamily][gender][collaboration]
            skus.forEach(sku => {
              if (sku !== 'Various models available' && sku !== 'Various Nike sandal collabs available' && !sku.includes('Various')) {
                const productId = generateProductId(category, brand, gender, modelFamily, sku, collaboration)
                productIds.push(productId)
              }
            })
          })
        })
      })
    })
  })
  
  return productIds
}

/**
 * Get products by category
 * @param {string} category - Category name
 * @returns {Array} Array of product IDs in the category
 */
export const getProductsByCategory = (category) => {
  const categoryKey = category.toUpperCase()
  if (!PRODUCT_CATALOG[categoryKey]) return []
  
  const productIds = []
  Object.keys(PRODUCT_CATALOG[categoryKey]).forEach(brand => {
    Object.keys(PRODUCT_CATALOG[categoryKey][brand]).forEach(modelFamily => {
      Object.keys(PRODUCT_CATALOG[categoryKey][brand][modelFamily]).forEach(gender => {
        Object.keys(PRODUCT_CATALOG[categoryKey][brand][modelFamily][gender]).forEach(collaboration => {
          const skus = PRODUCT_CATALOG[categoryKey][brand][modelFamily][gender][collaboration]
          skus.forEach(sku => {
            if (!sku.includes('Various')) {
              const productId = generateProductId(categoryKey, brand, gender, modelFamily, sku, collaboration)
              productIds.push(productId)
            }
          })
        })
      })
    })
  })
  
  return productIds
}

/**
 * Get products by brand
 * @param {string} brand - Brand name
 * @returns {Array} Array of product IDs for the brand
 */
export const getProductsByBrand = (brand) => {
  const productIds = []
  const brandKey = brand.toUpperCase()
  
  Object.keys(PRODUCT_CATALOG).forEach(category => {
    if (PRODUCT_CATALOG[category][brandKey]) {
      Object.keys(PRODUCT_CATALOG[category][brandKey]).forEach(modelFamily => {
        Object.keys(PRODUCT_CATALOG[category][brandKey][modelFamily]).forEach(gender => {
          Object.keys(PRODUCT_CATALOG[category][brandKey][modelFamily][gender]).forEach(collaboration => {
            const skus = PRODUCT_CATALOG[category][brandKey][modelFamily][gender][collaboration]
            skus.forEach(sku => {
              if (!sku.includes('Various')) {
                const productId = generateProductId(category, brandKey, gender, modelFamily, sku, collaboration)
                productIds.push(productId)
              }
            })
          })
        })
      })
    }
  })
  
  return productIds
}

/**
 * Check if a product exists in the catalog
 * @param {string} productId - Product ID to check
 * @returns {boolean} Whether the product exists
 */
export const productExists = (productId) => {
  if (!validateProductId(productId)) return false
  
  const allIds = getAllProductIds()
  return allIds.includes(productId)
}

/**
 * Get featured products (limited editions and collaborations)
 * @returns {Array} Array of featured product IDs
 */
export const getFeaturedProducts = () => {
  const featuredIds = []
  
  // Get Nike x Gucci collaboration products
  const nikeGucciProducts = getProductsByBrand('NIKE Limited Edition')
    .filter(id => id.includes('gucci'))
    .slice(0, 8) // Limit to 8 featured products
  
  featuredIds.push(...nikeGucciProducts)
  
  return featuredIds
}

export default {
  PRODUCT_CATALOG,
  getAllProductIds,
  getProductsByCategory,
  getProductsByBrand,
  productExists,
  getFeaturedProducts
}
