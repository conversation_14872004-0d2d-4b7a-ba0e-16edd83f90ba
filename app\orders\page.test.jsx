import { render, screen } from '@testing-library/react'
import OrdersPage from './page'

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
}))

// Mock components
jest.mock('@/components/ui/Card', () => ({
  Card: ({ children, ...props }) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }) => <div {...props}>{children}</div>,
}))

jest.mock('@/components/ui/AnimatedButton', () => {
  return function AnimatedButton({ children, ...props }) {
    return <button {...props}>{children}</button>
  }
})

jest.mock('@/components/transitions/RouteTransitionProvider', () => ({
  TransitionLink: ({ children, href, ...props }) => <a href={href} {...props}>{children}</a>,
}))

describe('OrdersPage', () => {
  it('renders without crashing', () => {
    render(<OrdersPage />)
    expect(screen.getByText('Orders')).toBeInTheDocument()
  })

  it('displays the page title', () => {
    render(<OrdersPage />)
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('Orders')
  })

  it('displays main content', () => {
    render(<OrdersPage />)
    expect(screen.getByText('Main Content')).toBeInTheDocument()
  })

  it('displays feature cards', () => {
    render(<OrdersPage />)
    expect(screen.getByText('Feature 1')).toBeInTheDocument()
    expect(screen.getByText('Feature 2')).toBeInTheDocument()
    expect(screen.getByText('Feature 3')).toBeInTheDocument()
  })

  it('has a back to home link', () => {
    render(<OrdersPage />)
    const homeLink = screen.getByText('Back to Home').closest('a')
    expect(homeLink).toHaveAttribute('href', '/')
  })
})
