// 🔐 TWL USER SIGNUP API ENDPOINT
// 🎯 Secure user registration with profile creation

import { NextResponse } from 'next/server'
import { supabase, supabaseAdmin } from '@/lib/supabase'

// 📝 POST /api/auth/signup - Register new user
export async function POST(request) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const { email, password, firstName, lastName, phone, preferredLanguage = 'es-MX' } = body
    
    if (!email || !password) {
      return NextResponse.json(
        { error: 'Email and password are required' },
        { status: 400 }
      )
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      return NextResponse.json(
        { error: 'Invalid email format' },
        { status: 400 }
      )
    }

    // Validate password strength
    if (password.length < 8) {
      return NextResponse.json(
        { error: 'Password must be at least 8 characters long' },
        { status: 400 }
      )
    }

    // Check if user already exists
    const { data: existingUser } = await supabaseAdmin
      .from('auth.users')
      .select('id')
      .eq('email', email)
      .single()

    if (existingUser) {
      return NextResponse.json(
        { error: 'User with this email already exists' },
        { status: 409 }
      )
    }

    // Create user in Supabase Auth
    const { data: authData, error: authError } = await supabaseAdmin.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm for now
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        phone,
        preferred_language: preferredLanguage
      }
    })

    if (authError) {
      console.error('Auth error:', authError)
      return NextResponse.json(
        { error: 'Failed to create user account', details: authError.message },
        { status: 500 }
      )
    }

    // Create user profile in users table
    const { data: profile, error: profileError } = await supabaseAdmin
      .from('users')
      .insert({
        id: authData.user.id,
        email,
        first_name: firstName,
        last_name: lastName,
        phone,
        preferred_language: preferredLanguage,
        preferences: {
          newsletter: true,
          marketing: false,
          size_preferences: {},
          favorite_brands: []
        },
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (profileError) {
      console.error('Profile creation error:', profileError)
      
      // Cleanup: delete auth user if profile creation failed
      await supabaseAdmin.auth.admin.deleteUser(authData.user.id)
      
      return NextResponse.json(
        { error: 'Failed to create user profile', details: profileError.message },
        { status: 500 }
      )
    }

    // Create default wishlist
    const { error: wishlistError } = await supabaseAdmin
      .from('wishlists')
      .insert({
        user_id: authData.user.id,
        name: 'Mi Lista de Deseos',
        description: 'Mi lista de deseos principal',
        is_default: true,
        is_public: false,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })

    if (wishlistError) {
      console.error('Wishlist creation error:', wishlistError)
      // Don't fail the signup for wishlist creation error
    }

    // Generate access token for immediate login
    const { data: sessionData, error: sessionError } = await supabaseAdmin.auth.admin.generateLink({
      type: 'magiclink',
      email,
      options: {
        redirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`
      }
    })

    return NextResponse.json({
      success: true,
      data: {
        user: {
          id: authData.user.id,
          email: authData.user.email,
          profile: profile
        },
        message: 'Account created successfully'
      }
    }, { status: 201 })

  } catch (error) {
    console.error('Signup API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

// 🔧 Helper function to validate user data
function validateUserData(data) {
  const errors = []

  // Email validation
  if (!data.email) {
    errors.push('Email is required')
  } else {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(data.email)) {
      errors.push('Invalid email format')
    }
  }

  // Password validation
  if (!data.password) {
    errors.push('Password is required')
  } else {
    if (data.password.length < 8) {
      errors.push('Password must be at least 8 characters long')
    }
    
    // Check for at least one number and one letter
    if (!/(?=.*[a-zA-Z])(?=.*\d)/.test(data.password)) {
      errors.push('Password must contain at least one letter and one number')
    }
  }

  // Name validation
  if (data.firstName && data.firstName.trim().length < 2) {
    errors.push('First name must be at least 2 characters long')
  }

  if (data.lastName && data.lastName.trim().length < 2) {
    errors.push('Last name must be at least 2 characters long')
  }

  // Phone validation (optional)
  if (data.phone) {
    const phoneRegex = /^\+?[\d\s\-\(\)]{10,}$/
    if (!phoneRegex.test(data.phone)) {
      errors.push('Invalid phone number format')
    }
  }

  // Language validation
  const validLanguages = ['es-MX', 'en-US', 'pt-BR']
  if (data.preferredLanguage && !validLanguages.includes(data.preferredLanguage)) {
    errors.push(`Preferred language must be one of: ${validLanguages.join(', ')}`)
  }

  return errors
}

// 🔧 Helper function to generate username from email
function generateUsername(email, firstName, lastName) {
  // Try first name + last name
  if (firstName && lastName) {
    const username = `${firstName.toLowerCase()}${lastName.toLowerCase()}`
    if (username.length >= 3) {
      return username
    }
  }

  // Try first name + random number
  if (firstName) {
    const randomNum = Math.floor(Math.random() * 1000)
    return `${firstName.toLowerCase()}${randomNum}`
  }

  // Fallback to email prefix + random number
  const emailPrefix = email.split('@')[0].toLowerCase()
  const randomNum = Math.floor(Math.random() * 1000)
  return `${emailPrefix}${randomNum}`
}

// 🔧 Helper function to create default user preferences
function createDefaultPreferences(preferredLanguage = 'es-MX') {
  return {
    language: preferredLanguage,
    currency: 'USD',
    newsletter: true,
    marketing: false,
    push_notifications: true,
    email_notifications: {
      order_updates: true,
      promotions: false,
      new_arrivals: true,
      price_drops: true
    },
    size_preferences: {
      // Will be populated as user shops
    },
    favorite_brands: [],
    style_preferences: [],
    privacy: {
      profile_public: false,
      wishlist_public: false,
      activity_public: false
    }
  }
}
