'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import { useWishlist } from '@/contexts/WishlistContext'

const styleQuestions = [
  {
    id: 'occasion',
    question: '¿Para qué ocasiones compras zapatos principalmente?',
    options: [
      { id: 'casual', label: 'Uso diario/Casual', icon: '👟' },
      { id: 'work', label: 'Trabajo/Formal', icon: '👔' },
      { id: 'sport', label: 'Deportes/Gym', icon: '🏃‍♂️' },
      { id: 'party', label: 'Fiestas/Eventos', icon: '🎉' },
      { id: 'street', label: 'Streetwear/Moda', icon: '🔥' }
    ]
  },
  {
    id: 'colors',
    question: '¿Qué colores prefieres?',
    options: [
      { id: 'neutral', label: 'Neutros (<PERSON>, <PERSON>, <PERSON>)', icon: '⚪' },
      { id: 'earth', label: 'Tierra (Beige, Marrón, Camel)', icon: '🤎' },
      { id: 'bold', label: 'Llamativos (Rojo, Azul, Verde)', icon: '🌈' },
      { id: 'pastel', label: 'Pasteles (Rosa, Lavanda, Mint)', icon: '🌸' },
      { id: 'neon', label: 'Neón (Lima, Naranja, Fucsia)', icon: '⚡' }
    ]
  },
  {
    id: 'brands',
    question: '¿Qué tipo de marcas prefieres?',
    options: [
      { id: 'luxury', label: 'Lujo (Gucci, Dior, Balenciaga)', icon: '💎' },
      { id: 'sport', label: 'Deportivas (Nike, Adidas, Jordan)', icon: '🏆' },
      { id: 'street', label: 'Streetwear (Off-White, Supreme)', icon: '🛹' },
      { id: 'classic', label: 'Clásicas (Converse, Vans)', icon: '⭐' },
      { id: 'emerging', label: 'Emergentes/Independientes', icon: '🌟' }
    ]
  },
  {
    id: 'budget',
    question: '¿Cuál es tu rango de presupuesto típico?',
    options: [
      { id: 'budget', label: '$1,000 - $3,000 MXN', icon: '💰' },
      { id: 'mid', label: '$3,000 - $8,000 MXN', icon: '💳' },
      { id: 'premium', label: '$8,000 - $15,000 MXN', icon: '💎' },
      { id: 'luxury', label: '$15,000+ MXN', icon: '👑' },
      { id: 'varies', label: 'Varía según el producto', icon: '🎯' }
    ]
  }
]

export default function StyleMatchAI({ onClose, onRecommendations }) {
  const [currentStep, setCurrentStep] = useState(0)
  const [answers, setAnswers] = useState({})
  const [isAnalyzing, setIsAnalyzing] = useState(false)
  const [analysis, setAnalysis] = useState(null)
  const [recommendations, setRecommendations] = useState([])
  const { stylePreferences, updateStylePreferences } = useUserPreferences()
  const { wishlistItems } = useWishlist()

  const handleAnswer = (questionId, answerId) => {
    const newAnswers = { ...answers, [questionId]: answerId }
    setAnswers(newAnswers)
    
    if (currentStep < styleQuestions.length - 1) {
      setTimeout(() => setCurrentStep(currentStep + 1), 300)
    } else {
      analyzeStyle(newAnswers)
    }
  }

  const analyzeStyle = async (userAnswers) => {
    setIsAnalyzing(true)
    
    // Simulate AI analysis
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    const styleAnalysis = generateStyleAnalysis(userAnswers)
    const productRecommendations = generateRecommendations(styleAnalysis)
    
    setAnalysis(styleAnalysis)
    setRecommendations(productRecommendations)
    
    // Update user preferences
    updateStylePreferences({
      ...stylePreferences,
      dominantStyle: styleAnalysis.primaryStyle,
      colorPreferences: styleAnalysis.colorProfile,
      brandAffinities: styleAnalysis.brandProfile,
      budgetRange: styleAnalysis.budgetProfile,
      lastAnalysis: new Date().toISOString()
    })
    
    setIsAnalyzing(false)
  }

  const generateStyleAnalysis = (answers) => {
    const styleProfiles = {
      casual: { name: 'Casual Chic', description: 'Comodidad con estilo para el día a día' },
      work: { name: 'Professional', description: 'Elegancia y sofisticación para el trabajo' },
      sport: { name: 'Athletic', description: 'Rendimiento y funcionalidad deportiva' },
      party: { name: 'Statement', description: 'Llamativo y audaz para destacar' },
      street: { name: 'Streetwear', description: 'Tendencias urbanas y cultura street' }
    }

    const colorProfiles = {
      neutral: { name: 'Minimalista', vibe: 'Elegante y versátil' },
      earth: { name: 'Natural', vibe: 'Cálido y sofisticado' },
      bold: { name: 'Audaz', vibe: 'Vibrante y expresivo' },
      pastel: { name: 'Suave', vibe: 'Delicado y moderno' },
      neon: { name: 'Futurista', vibe: 'Energético y vanguardista' }
    }

    return {
      primaryStyle: styleProfiles[answers.occasion],
      colorProfile: colorProfiles[answers.colors],
      brandProfile: answers.brands,
      budgetProfile: answers.budget,
      compatibility: Math.floor(Math.random() * 20) + 80, // 80-100%
      uniqueness: Math.floor(Math.random() * 30) + 70, // 70-100%
      trendAlignment: Math.floor(Math.random() * 25) + 75 // 75-100%
    }
  }

  const generateRecommendations = (analysis) => {
    // Mock recommendations based on style analysis
    const mockProducts = [
      { id: 1, name: 'Nike Air Force 1 Low', brand: 'Nike', price: 2500, match: 95, reason: 'Perfecto para tu estilo casual chic' },
      { id: 2, name: 'Gucci Ace Sneakers', brand: 'Gucci', price: 15000, match: 88, reason: 'Lujo que complementa tu perfil' },
      { id: 3, name: 'Jordan 1 Retro High', brand: 'Jordan', price: 4200, match: 92, reason: 'Icónico streetwear para tu estilo' },
      { id: 4, name: 'Balenciaga Triple S', brand: 'Balenciaga', price: 18000, match: 85, reason: 'Vanguardista como tu personalidad' },
      { id: 5, name: 'Off-White Vulc Low', brand: 'Off-White', price: 9500, match: 90, reason: 'Tendencia que se alinea contigo' }
    ]

    return mockProducts.sort((a, b) => b.match - a.match).slice(0, 4)
  }

  const restartQuiz = () => {
    setCurrentStep(0)
    setAnswers({})
    setAnalysis(null)
    setRecommendations([])
    setIsAnalyzing(false)
  }

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className="max-w-4xl w-full max-h-[90vh] overflow-y-auto"
          onClick={(e) => e.stopPropagation()}
        >
          <Card variant="glass">
            <CardContent className="p-8">
              
              {/* Header */}
              <div className="text-center mb-8">
                <motion.div
                  className="text-6xl mb-4"
                  animate={isAnalyzing ? {
                    rotate: [0, 10, -10, 0],
                    scale: [1, 1.1, 1]
                  } : {}}
                  transition={{
                    duration: 1,
                    repeat: isAnalyzing ? Infinity : 0
                  }}
                >
                  ✨
                </motion.div>
                <h2 className="text-3xl font-bold text-gray-800 mb-2">
                  Style Match AI
                </h2>
                <p className="text-gray-600">
                  Descubre tu estilo único y recibe recomendaciones personalizadas
                </p>
              </div>

              {!isAnalyzing && !analysis && (
                /* Quiz Questions */
                <div className="space-y-8">
                  {/* Progress Bar */}
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      className="bg-[#BFFF00] h-2 rounded-full"
                      initial={{ width: 0 }}
                      animate={{ width: `${((currentStep + 1) / styleQuestions.length) * 100}%` }}
                      transition={{ duration: 0.5 }}
                    />
                  </div>

                  {/* Current Question */}
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={currentStep}
                      initial={{ opacity: 0, x: 50 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: -50 }}
                      className="space-y-6"
                    >
                      <div className="text-center">
                        <Badge variant="outline" size="sm" className="mb-4">
                          Pregunta {currentStep + 1} de {styleQuestions.length}
                        </Badge>
                        <h3 className="text-xl font-semibold text-gray-800 mb-6">
                          {styleQuestions[currentStep].question}
                        </h3>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {styleQuestions[currentStep].options.map((option, index) => (
                          <motion.button
                            key={option.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            onClick={() => handleAnswer(styleQuestions[currentStep].id, option.id)}
                            className="p-6 rounded-xl border-2 border-gray-200 hover:border-[#BFFF00] hover:bg-[#BFFF00]/5 transition-all duration-300 text-left group"
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <div className="flex items-center gap-4">
                              <span className="text-3xl">{option.icon}</span>
                              <div>
                                <h4 className="font-semibold text-gray-800 group-hover:text-gray-900">
                                  {option.label}
                                </h4>
                              </div>
                            </div>
                          </motion.button>
                        ))}
                      </div>
                    </motion.div>
                  </AnimatePresence>
                </div>
              )}

              {/* Analysis Loading */}
              {isAnalyzing && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="text-center py-12"
                >
                  <div className="flex justify-center mb-6">
                    <motion.div
                      className="w-16 h-16 border-4 border-[#BFFF00] border-t-transparent rounded-full"
                      animate={{ rotate: 360 }}
                      transition={{
                        duration: 1,
                        repeat: Infinity,
                        ease: "linear"
                      }}
                    />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-800 mb-2">
                    Analizando tu estilo...
                  </h3>
                  <p className="text-gray-600">
                    Nuestro AI está procesando tus respuestas para crear tu perfil único
                  </p>
                </motion.div>
              )}

              {/* Analysis Results */}
              {analysis && (
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  className="space-y-8"
                >
                  {/* Style Profile */}
                  <Card variant="default">
                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold text-gray-800 mb-4">Tu Perfil de Estilo</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h4 className="font-semibold text-gray-700 mb-2">Estilo Principal</h4>
                          <div className="bg-[#BFFF00]/10 rounded-lg p-4">
                            <h5 className="font-bold text-gray-800">{analysis.primaryStyle.name}</h5>
                            <p className="text-sm text-gray-600">{analysis.primaryStyle.description}</p>
                          </div>
                        </div>
                        <div>
                          <h4 className="font-semibold text-gray-700 mb-2">Perfil de Color</h4>
                          <div className="bg-orange-100 rounded-lg p-4">
                            <h5 className="font-bold text-gray-800">{analysis.colorProfile.name}</h5>
                            <p className="text-sm text-gray-600">{analysis.colorProfile.vibe}</p>
                          </div>
                        </div>
                      </div>
                      
                      <div className="grid grid-cols-3 gap-4 mt-6">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-[#BFFF00]">{analysis.compatibility}%</div>
                          <div className="text-xs text-gray-600">Compatibilidad</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-orange-500">{analysis.uniqueness}%</div>
                          <div className="text-xs text-gray-600">Originalidad</div>
                        </div>
                        <div className="text-center">
                          <div className="text-2xl font-bold text-gray-700">{analysis.trendAlignment}%</div>
                          <div className="text-xs text-gray-600">Tendencia</div>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Recommendations */}
                  <Card variant="default">
                    <CardContent className="p-6">
                      <h3 className="text-xl font-bold text-gray-800 mb-4">Recomendaciones Personalizadas</h3>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {recommendations.map((product, index) => (
                          <motion.div
                            key={product.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                            className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                          >
                            <div className="flex justify-between items-start mb-2">
                              <h4 className="font-semibold text-gray-800">{product.name}</h4>
                              <Badge variant="success" size="sm">{product.match}% match</Badge>
                            </div>
                            <p className="text-sm text-gray-600 mb-2">{product.brand}</p>
                            <p className="text-xs text-gray-500 mb-3">{product.reason}</p>
                            <div className="flex justify-between items-center">
                              <span className="font-bold text-gray-800">${product.price.toLocaleString()} MXN</span>
                              <AnimatedButton variant="primary" size="sm">Ver</AnimatedButton>
                            </div>
                          </motion.div>
                        ))}
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )}

              {/* Footer Actions */}
              <div className="flex justify-center gap-4 mt-8">
                {analysis && (
                  <AnimatedButton
                    variant="secondary"
                    onClick={restartQuiz}
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                      </svg>
                    }
                  >
                    Nuevo Análisis
                  </AnimatedButton>
                )}
                
                <AnimatedButton
                  variant="ghost"
                  onClick={onClose}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  }
                >
                  Cerrar
                </AnimatedButton>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  )
}
