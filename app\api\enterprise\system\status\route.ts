/**
 * Next.js API Route: GET /api/enterprise/system/status
 * Enterprise system health and status endpoint
 */

import { NextRequest, NextResponse } from 'next/server'
import { getTWLSystem } from '@/lib/enterprise/TWLEnterpriseSystem'

export async function GET(request: NextRequest) {
  try {
    // Get enterprise system instance
    const system = getTWLSystem()
    
    // Use the enterprise API
    const api = system.getAPI()
    return await api.getSystemStatus(request)
    
  } catch (error) {
    console.error('Enterprise System Status Error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: { 
          code: 'INTERNAL_ERROR', 
          message: 'Internal server error' 
        } 
      },
      { status: 500 }
    )
  }
}
