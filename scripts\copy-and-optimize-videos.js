#!/usr/bin/env node

/**
 * CYTTE VIDEO COPY & OPTIMIZATION SCRIPT
 * 
 * This script:
 * 1. Scans CYTTE materials folder for video files
 * 2. Copies videos to public/products structure
 * 3. Optimizes videos for web (compression, format conversion)
 * 4. Generates video thumbnails
 * 5. Creates multiple formats (MP4, WebM) for browser compatibility
 * 
 * Requirements: FFmpeg must be installed
 * Install FFmpeg: https://ffmpeg.org/download.html
 */

const fs = require('fs').promises;
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const CYTTE_BASE_PATH = path.join(process.cwd(), '--materials', 'shoes', '2. CYTTE');
const PUBLIC_VIDEOS_PATH = path.join(process.cwd(), 'public', 'products');
const TEMP_PATH = path.join(process.cwd(), 'temp-video-processing');

// Video optimization settings
const VIDEO_CONFIG = {
  // Output quality settings
  crf: 28,              // Constant Rate Factor (18-28 for good quality, lower = better quality)
  maxWidth: 1280,       // Maximum width for mobile-first
  maxHeight: 720,       // Maximum height
  fps: 30,              // Frame rate
  
  // Compression settings
  preset: 'medium',     // FFmpeg preset (ultrafast, fast, medium, slow, veryslow)
  audioCodec: 'aac',    // Audio codec
  audioBitrate: '128k', // Audio bitrate
  
  // Format settings
  formats: ['mp4', 'webm'], // Output formats
  
  // Thumbnail settings
  thumbnailTime: '00:00:01', // Time to extract thumbnail
  thumbnailFormat: 'webp'    // Thumbnail format
};

class VideoOptimizer {
  constructor() {
    this.processedVideos = [];
    this.errors = [];
    this.totalVideosFound = 0;
    this.totalVideosProcessed = 0;
    this.totalSizeReduction = 0;
  }

  async initialize() {
    console.log('🎬 CYTTE Video Copy & Optimization Script');
    console.log('==========================================');
    
    // Check if FFmpeg is installed
    try {
      execSync('ffmpeg -version', { stdio: 'ignore' });
      console.log('✅ FFmpeg detected');
    } catch (error) {
      console.error('❌ FFmpeg not found. Please install FFmpeg first.');
      console.error('Download: https://ffmpeg.org/download.html');
      process.exit(1);
    }

    // Create temp directory
    try {
      await fs.mkdir(TEMP_PATH, { recursive: true });
      console.log('✅ Temp directory created');
    } catch (error) {
      console.log('📁 Temp directory already exists');
    }

    // Ensure public videos directory exists
    try {
      await fs.mkdir(PUBLIC_VIDEOS_PATH, { recursive: true });
      console.log('✅ Public videos directory ready');
    } catch (error) {
      console.log('📁 Public videos directory already exists');
    }
  }

  async scanForVideos() {
    console.log('\n🔍 Scanning CYTTE folder for videos...');
    
    const videos = [];
    
    try {
      await this.scanDirectory(CYTTE_BASE_PATH, videos);
      
      this.totalVideosFound = videos.length;
      console.log(`📊 Found ${videos.length} videos in CYTTE catalog`);
      
      if (videos.length > 0) {
        console.log('\n📹 Videos found:');
        videos.forEach((video, index) => {
          console.log(`   ${index + 1}. ${path.basename(video.sourcePath)}`);
          console.log(`      Product: ${video.productName}`);
          console.log(`      Size: ${video.sizeKB} KB`);
        });
      }
      
      return videos;
      
    } catch (error) {
      console.error('❌ Error scanning for videos:', error);
      throw error;
    }
  }

  async scanDirectory(dirPath, videos) {
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item.name);
        
        if (item.isDirectory()) {
          // Recursively scan subdirectories
          await this.scanDirectory(fullPath, videos);
        } else if (item.isFile() && this.isVideoFile(item.name)) {
          // Found a video file
          const stats = await fs.stat(fullPath);
          const productInfo = this.extractProductInfo(fullPath);
          
          videos.push({
            sourcePath: fullPath,
            fileName: item.name,
            sizeKB: Math.round(stats.size / 1024),
            sizeMB: Math.round(stats.size / (1024 * 1024) * 100) / 100,
            productName: productInfo.productName,
            brand: productInfo.brand,
            model: productInfo.model,
            sku: productInfo.sku
          });
        }
      }
    } catch (error) {
      console.error(`❌ Error scanning directory ${dirPath}:`, error);
    }
  }

  isVideoFile(fileName) {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    const ext = path.extname(fileName).toLowerCase();
    return videoExtensions.includes(ext);
  }

  extractProductInfo(videoPath) {
    // Extract product information from the path structure
    const pathParts = videoPath.split(path.sep);
    
    // Find the product folder (contains SKU)
    let productName = 'Unknown Product';
    let brand = 'Unknown Brand';
    let model = 'Unknown Model';
    let sku = 'unknown-sku';
    
    for (let i = pathParts.length - 1; i >= 0; i--) {
      const part = pathParts[i];
      
      // Look for SKU pattern (letters/numbers with dashes)
      if (part.match(/^[A-Z0-9]+-[A-Z0-9]+/)) {
        const [skuPart, ...nameParts] = part.split(' -- ');
        sku = skuPart;
        productName = nameParts.join(' ') || productName;
        break;
      }
      
      // Extract brand from path
      if (part.includes('NIKE')) brand = 'Nike';
      else if (part.includes('ADIDAS')) brand = 'Adidas';
      else if (part.includes('GUCCI')) brand = 'Gucci';
      else if (part.includes('DIOR')) brand = 'Dior';
      else if (part.includes('LV')) brand = 'Louis Vuitton';
      
      // Extract model from path
      if (part.includes('AIR FORCE')) model = 'Air Force';
      else if (part.includes('AIR JORDAN')) model = 'Air Jordan';
      else if (part.includes('DUNK')) model = 'Dunk';
      else if (part.includes('BLAZER')) model = 'Blazer';
    }
    
    return { productName, brand, model, sku };
  }

  async processVideos(videos) {
    console.log('\n🎬 Starting video optimization...');
    
    for (let i = 0; i < videos.length; i++) {
      const video = videos[i];
      console.log(`\n📹 Processing video ${i + 1}/${videos.length}: ${video.fileName}`);
      
      try {
        await this.processVideo(video);
        this.totalVideosProcessed++;
      } catch (error) {
        console.error(`❌ Error processing ${video.fileName}:`, error);
        this.errors.push({ video: video.fileName, error: error.message });
      }
    }
  }

  async processVideo(video) {
    const outputDir = this.generateOutputPath(video);
    
    // Create output directory
    await fs.mkdir(outputDir, { recursive: true });
    
    const originalSize = video.sizeMB;
    
    // Process each format
    for (const format of VIDEO_CONFIG.formats) {
      const outputPath = path.join(outputDir, `${path.parse(video.fileName).name}.${format}`);
      
      console.log(`   🔄 Converting to ${format.toUpperCase()}...`);
      
      await this.convertVideo(video.sourcePath, outputPath, format);
      
      // Check output file size
      const stats = await fs.stat(outputPath);
      const newSizeMB = Math.round(stats.size / (1024 * 1024) * 100) / 100;
      const reduction = Math.round(((originalSize - newSizeMB) / originalSize) * 100);
      
      console.log(`   ✅ ${format.toUpperCase()}: ${originalSize}MB → ${newSizeMB}MB (${reduction}% reduction)`);
      
      this.totalSizeReduction += (originalSize - newSizeMB);
    }
    
    // Generate thumbnail
    const thumbnailPath = path.join(outputDir, `${path.parse(video.fileName).name}-thumb.${VIDEO_CONFIG.thumbnailFormat}`);
    console.log('   🖼️  Generating thumbnail...');
    
    await this.generateThumbnail(video.sourcePath, thumbnailPath);
    
    console.log('   ✅ Thumbnail generated');
    
    // Add to processed list
    this.processedVideos.push({
      ...video,
      outputDir,
      formats: VIDEO_CONFIG.formats,
      thumbnail: thumbnailPath
    });
  }

  generateOutputPath(video) {
    // Create a clean path structure in public/products
    const cleanSku = video.sku.toLowerCase().replace(/[^a-z0-9]/g, '-');
    const cleanBrand = video.brand.toLowerCase().replace(/[^a-z0-9]/g, '-');
    
    return path.join(PUBLIC_VIDEOS_PATH, cleanBrand, cleanSku);
  }

  async convertVideo(inputPath, outputPath, format) {
    const { crf, maxWidth, maxHeight, fps, preset, audioCodec, audioBitrate } = VIDEO_CONFIG;
    
    let ffmpegCommand;
    
    if (format === 'mp4') {
      ffmpegCommand = `ffmpeg -i "${inputPath}" -c:v libx264 -crf ${crf} -preset ${preset} -vf "scale='min(${maxWidth},iw)':'min(${maxHeight},ih)':force_original_aspect_ratio=decrease" -r ${fps} -c:a ${audioCodec} -b:a ${audioBitrate} -movflags +faststart -y "${outputPath}"`;
    } else if (format === 'webm') {
      ffmpegCommand = `ffmpeg -i "${inputPath}" -c:v libvpx-vp9 -crf ${crf} -vf "scale='min(${maxWidth},iw)':'min(${maxHeight},ih)':force_original_aspect_ratio=decrease" -r ${fps} -c:a libopus -b:a ${audioBitrate} -y "${outputPath}"`;
    }
    
    try {
      execSync(ffmpegCommand, { stdio: 'ignore' });
    } catch (error) {
      throw new Error(`FFmpeg conversion failed: ${error.message}`);
    }
  }

  async generateThumbnail(inputPath, outputPath) {
    const { thumbnailTime, thumbnailFormat } = VIDEO_CONFIG;
    
    const ffmpegCommand = `ffmpeg -i "${inputPath}" -ss ${thumbnailTime} -vframes 1 -vf "scale='min(400,iw)':'min(400,ih)':force_original_aspect_ratio=decrease" -y "${outputPath}"`;
    
    try {
      execSync(ffmpegCommand, { stdio: 'ignore' });
    } catch (error) {
      throw new Error(`Thumbnail generation failed: ${error.message}`);
    }
  }

  async cleanup() {
    console.log('\n🧹 Cleaning up temporary files...');
    
    try {
      await fs.rmdir(TEMP_PATH, { recursive: true });
      console.log('✅ Cleanup complete');
    } catch (error) {
      console.log('⚠️  Cleanup warning:', error.message);
    }
  }

  printSummary() {
    console.log('\n📊 VIDEO OPTIMIZATION SUMMARY');
    console.log('==============================');
    console.log(`Videos found: ${this.totalVideosFound}`);
    console.log(`Videos processed: ${this.totalVideosProcessed}`);
    console.log(`Total size reduction: ${Math.round(this.totalSizeReduction * 100) / 100} MB`);
    console.log(`Formats generated: ${VIDEO_CONFIG.formats.join(', ')}`);
    
    if (this.errors.length > 0) {
      console.log('\n❌ Errors encountered:');
      this.errors.forEach(error => {
        console.log(`   ${error.video}: ${error.error}`);
      });
    }
    
    if (this.processedVideos.length > 0) {
      console.log('\n✅ Successfully processed videos:');
      this.processedVideos.forEach(video => {
        console.log(`   ${video.fileName} (${video.brand} - ${video.productName})`);
      });
    }
  }
}

// Main execution
async function main() {
  const optimizer = new VideoOptimizer();
  
  try {
    await optimizer.initialize();
    
    const videos = await optimizer.scanForVideos();
    
    if (videos.length === 0) {
      console.log('ℹ️  No videos found in CYTTE catalog');
      return;
    }
    
    await optimizer.processVideos(videos);
    await optimizer.cleanup();
    
    optimizer.printSummary();
    
    console.log('\n🎉 Video optimization complete!');
    console.log('Videos are now ready for fast web rendering and playback.');
    
  } catch (error) {
    console.error('❌ Script failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = VideoOptimizer;
