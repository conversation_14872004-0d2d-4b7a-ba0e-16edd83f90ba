'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import EnhancedVoiceSearch from './EnhancedVoiceSearch'
import VisualSearch from './VisualSearch'

export default function EnhancedSearchBar({ onSearch, placeholder = "Buscar productos..." }) {
  const [query, setQuery] = useState('')
  const [isFocused, setIsFocused] = useState(false)
  const [showVoiceSearch, setShowVoiceSearch] = useState(false)
  const [showVisualSearch, setShowVisualSearch] = useState(false)
  const [searchSuggestions, setSearchSuggestions] = useState([])

  const popularSearches = [
    'Nike Air Force 1',
    'Adidas Yeezy',
    'Gucci Ace',
    'Jordan 1',
    'Sneakers blancos',
    'Zapatos formales',
    'Tacones negros',
    'Botas de mujer'
  ]

  const handleSubmit = (e) => {
    e.preventDefault()
    if (query.trim()) {
      onSearch(query.trim())
      setIsFocused(false)
    }
  }

  const handleQueryChange = (e) => {
    const value = e.target.value
    setQuery(value)
    
    // Generate search suggestions
    if (value.length > 1) {
      const suggestions = popularSearches.filter(search =>
        search.toLowerCase().includes(value.toLowerCase())
      ).slice(0, 5)
      setSearchSuggestions(suggestions)
    } else {
      setSearchSuggestions([])
    }
  }

  const handleSuggestionClick = (suggestion) => {
    setQuery(suggestion)
    onSearch(suggestion)
    setIsFocused(false)
    setSearchSuggestions([])
  }

  const handleVoiceSearch = (results, query) => {
    setQuery(query)
    onSearch(query)
    setShowVoiceSearch(false)
  }

  const handleVisualSearch = (searchTerm) => {
    setQuery(searchTerm)
    onSearch(searchTerm)
  }

  return (
    <>
      <motion.div
        className="relative w-full max-w-2xl"
        whileHover={{ scale: 1.01 }}
      >
        <form onSubmit={handleSubmit} className="relative">
          <div className="relative">
            <input
              type="text"
              value={query}
              onChange={handleQueryChange}
              onFocus={() => setIsFocused(true)}
              onBlur={() => setTimeout(() => setIsFocused(false), 200)}
              placeholder={placeholder}
              className={`
                w-full px-4 py-4 pl-12 pr-24
                glass-card
                border border-warm-camel/20
                rounded-2xl
                text-forest-emerald dark:text-light-cloud-gray 
                placeholder-warm-camel/60
                focus:outline-none focus:ring-2 focus:ring-rich-gold/50 focus:border-rich-gold/50
                transition-all duration-300
                ${isFocused ? 'bg-white/20 dark:bg-black/20 border-rich-gold/30' : ''}
              `}
            />
            
            {/* Search Icon */}
            <motion.div
              className="absolute left-4 top-1/2 transform -translate-y-1/2"
              animate={{
                scale: isFocused ? 1.1 : 1,
                color: isFocused ? '#FFD166' : '#C1A888'
              }}
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </motion.div>

            {/* Action Buttons */}
            <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center gap-1">
              {/* Voice Search Button */}
              <motion.button
                type="button"
                onClick={() => setShowVoiceSearch(true)}
                className="p-2 rounded-full hover:bg-warm-camel/20 transition-colors group"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="Búsqueda por voz"
              >
                <svg className="w-5 h-5 text-warm-camel group-hover:text-rich-gold transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                </svg>
              </motion.button>

              {/* Visual Search Button */}
              <motion.button
                type="button"
                onClick={() => setShowVisualSearch(true)}
                className="p-2 rounded-full hover:bg-warm-camel/20 transition-colors group"
                whileHover={{ scale: 1.1 }}
                whileTap={{ scale: 0.9 }}
                title="Búsqueda visual"
              >
                <svg className="w-5 h-5 text-warm-camel group-hover:text-rich-gold transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </motion.button>

              {/* Submit Button */}
              <AnimatePresence>
                {query && (
                  <motion.button
                    type="submit"
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    exit={{ opacity: 0, scale: 0.8 }}
                    className="p-2 bg-rich-gold text-forest-emerald rounded-full hover:bg-rich-gold/90 transition-colors"
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  </motion.button>
                )}
              </AnimatePresence>
            </div>
          </div>
        </form>

        {/* Search Suggestions */}
        <AnimatePresence>
          {isFocused && (searchSuggestions.length > 0 || query.length === 0) && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="absolute top-full left-0 right-0 mt-2 glass-card border border-warm-camel/20 rounded-2xl p-4 z-50"
            >
              {searchSuggestions.length > 0 ? (
                <div>
                  <h4 className="text-sm font-medium text-warm-camel mb-3">Sugerencias:</h4>
                  <div className="space-y-2">
                    {searchSuggestions.map((suggestion, index) => (
                      <motion.button
                        key={suggestion}
                        onClick={() => handleSuggestionClick(suggestion)}
                        className="w-full text-left p-2 rounded-lg hover:bg-warm-camel/10 transition-colors group"
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        transition={{ delay: index * 0.05 }}
                        whileHover={{ x: 4 }}
                      >
                        <div className="flex items-center gap-3">
                          <svg className="w-4 h-4 text-warm-camel group-hover:text-rich-gold transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                          </svg>
                          <span className="text-forest-emerald dark:text-light-cloud-gray group-hover:text-rich-gold transition-colors">
                            {suggestion}
                          </span>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>
              ) : (
                <div>
                  <h4 className="text-sm font-medium text-warm-camel mb-3">Búsquedas populares:</h4>
                  <div className="grid grid-cols-2 gap-2">
                    {popularSearches.slice(0, 6).map((search, index) => (
                      <motion.button
                        key={search}
                        onClick={() => handleSuggestionClick(search)}
                        className="text-left p-2 rounded-lg hover:bg-warm-camel/10 transition-colors group"
                        initial={{ opacity: 0, scale: 0.9 }}
                        animate={{ opacity: 1, scale: 1 }}
                        transition={{ delay: index * 0.05 }}
                        whileHover={{ scale: 1.02 }}
                      >
                        <div className="flex items-center gap-2">
                          <span className="text-rich-gold">🔥</span>
                          <span className="text-forest-emerald dark:text-light-cloud-gray text-sm group-hover:text-rich-gold transition-colors">
                            {search}
                          </span>
                        </div>
                      </motion.button>
                    ))}
                  </div>
                </div>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </motion.div>

      {/* Voice Search Modal */}
      <AnimatePresence>
        {showVoiceSearch && (
          <EnhancedVoiceSearch
            isOpen={true}
            onResults={handleVoiceSearch}
            onClose={() => setShowVoiceSearch(false)}
          />
        )}
      </AnimatePresence>

      {/* Visual Search Modal */}
      <AnimatePresence>
        {showVisualSearch && (
          <VisualSearch
            onSearch={handleVisualSearch}
            onClose={() => setShowVisualSearch(false)}
          />
        )}
      </AnimatePresence>
    </>
  )
}
