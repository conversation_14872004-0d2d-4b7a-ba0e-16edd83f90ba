# TWL Database Schema Documentation

## 🗄️ Database Overview

The White Laces uses Supabase (PostgreSQL) as the primary database with a carefully designed schema optimized for e-commerce operations, multi-supplier support, and AI-powered features.

## 📊 Entity Relationship Diagram

```mermaid
erDiagram
    %% User Management
    USERS {
        uuid id PK
        string email UK
        string username UK
        string first_name
        string last_name
        string phone
        jsonb preferences
        string avatar_url
        timestamp created_at
        timestamp updated_at
        boolean email_verified
        string preferred_language
    }

    %% Product Catalog
    BRANDS {
        uuid id PK
        string name UK
        string slug UK
        string description
        string logo_url
        string country
        integer founded_year
        string category
        boolean featured
        timestamp created_at
    }

    CATEGORIES {
        uuid id PK
        string name UK
        string slug UK
        string description
        string parent_id FK
        integer sort_order
        boolean active
    }

    PRODUCTS {
        uuid id PK
        string sku UK
        string name
        text description
        uuid brand_id FK
        uuid category_id FK
        decimal price
        decimal original_price
        string currency
        jsonb images
        jsonb sizes
        jsonb colors
        jsonb features
        integer stock_quantity
        boolean in_stock
        boolean featured
        boolean is_new
        decimal rating
        integer review_count
        string gender
        jsonb metadata
        timestamp created_at
        timestamp updated_at
    }

    %% Inventory Management
    PRODUCT_VARIANTS {
        uuid id PK
        uuid product_id FK
        string size
        string color
        string sku UK
        integer stock_quantity
        decimal price_adjustment
        boolean active
    }

    SUPPLIERS {
        uuid id PK
        string name UK
        string code UK
        string contact_email
        string contact_phone
        jsonb address
        boolean active
        timestamp created_at
    }

    SUPPLIER_PRODUCTS {
        uuid id PK
        uuid supplier_id FK
        uuid product_id FK
        string supplier_sku
        decimal cost_price
        integer lead_time_days
        integer minimum_order_qty
        timestamp last_updated
    }

    %% Shopping & Orders
    CARTS {
        uuid id PK
        uuid user_id FK
        jsonb items
        decimal total_amount
        string currency
        timestamp created_at
        timestamp updated_at
        timestamp expires_at
    }

    WISHLISTS {
        uuid id PK
        uuid user_id FK
        string name
        text description
        boolean is_public
        boolean is_default
        timestamp created_at
        timestamp updated_at
    }

    WISHLIST_ITEMS {
        uuid id PK
        uuid wishlist_id FK
        uuid product_id FK
        string size
        string color
        timestamp added_at
    }

    ORDERS {
        uuid id PK
        string order_number UK
        uuid user_id FK
        string status
        decimal subtotal
        decimal tax_amount
        decimal shipping_amount
        decimal total_amount
        string currency
        jsonb shipping_address
        jsonb billing_address
        string payment_method
        string payment_status
        timestamp created_at
        timestamp updated_at
    }

    ORDER_ITEMS {
        uuid id PK
        uuid order_id FK
        uuid product_id FK
        string size
        string color
        integer quantity
        decimal unit_price
        decimal total_price
    }

    %% Reviews & Social
    REVIEWS {
        uuid id PK
        uuid product_id FK
        uuid user_id FK
        integer rating
        text title
        text content
        jsonb images
        boolean verified_purchase
        integer helpful_count
        timestamp created_at
    }

    UGC_POSTS {
        uuid id PK
        uuid user_id FK
        uuid product_id FK
        string title
        text description
        jsonb images
        jsonb tags
        integer likes_count
        boolean featured
        timestamp created_at
    }

    %% Analytics & Search
    SEARCH_LOGS {
        uuid id PK
        uuid user_id FK
        string query
        string search_type
        jsonb filters
        integer results_count
        timestamp created_at
    }

    PRODUCT_VIEWS {
        uuid id PK
        uuid product_id FK
        uuid user_id FK
        string session_id
        timestamp viewed_at
    }

    %% Relationships
    USERS ||--o{ CARTS : owns
    USERS ||--o{ WISHLISTS : creates
    USERS ||--o{ ORDERS : places
    USERS ||--o{ REVIEWS : writes
    USERS ||--o{ UGC_POSTS : creates

    BRANDS ||--o{ PRODUCTS : has
    CATEGORIES ||--o{ PRODUCTS : contains
    CATEGORIES ||--o{ CATEGORIES : parent_of

    PRODUCTS ||--o{ PRODUCT_VARIANTS : has
    PRODUCTS ||--o{ ORDER_ITEMS : ordered_as
    PRODUCTS ||--o{ WISHLIST_ITEMS : wishlisted_as
    PRODUCTS ||--o{ REVIEWS : receives
    PRODUCTS ||--o{ UGC_POSTS : featured_in
    PRODUCTS ||--o{ PRODUCT_VIEWS : viewed
    PRODUCTS ||--o{ SUPPLIER_PRODUCTS : supplied_by

    SUPPLIERS ||--o{ SUPPLIER_PRODUCTS : supplies

    WISHLISTS ||--o{ WISHLIST_ITEMS : contains
    ORDERS ||--o{ ORDER_ITEMS : contains
```

## 🔑 Key Design Decisions

### **UUID Primary Keys**
- All tables use UUID primary keys for better scalability
- Prevents enumeration attacks
- Enables distributed system compatibility

### **JSONB Fields**
- `preferences`, `images`, `metadata` use JSONB for flexibility
- Enables complex queries with GIN indexes
- Reduces need for additional tables

### **Soft Deletes**
- Critical data uses soft delete patterns
- Maintains referential integrity
- Enables data recovery and auditing

### **Timestamp Tracking**
- All entities track `created_at` and `updated_at`
- Enables temporal queries and analytics
- Supports audit trails

## 📈 Performance Optimizations

### **Indexes Strategy**
```sql
-- Primary indexes (automatic on PKs)
-- Unique indexes on email, username, sku
-- Composite indexes for common queries
CREATE INDEX idx_products_brand_category ON products(brand_id, category_id);
CREATE INDEX idx_products_featured_new ON products(featured, is_new) WHERE in_stock = true;
CREATE INDEX idx_orders_user_status ON orders(user_id, status);
CREATE INDEX idx_product_views_product_date ON product_views(product_id, viewed_at);

-- Full-text search indexes
CREATE INDEX idx_products_search ON products USING gin(to_tsvector('english', name || ' ' || description));
CREATE INDEX idx_brands_search ON brands USING gin(to_tsvector('english', name || ' ' || description));

-- JSONB indexes for complex queries
CREATE INDEX idx_products_metadata ON products USING gin(metadata);
CREATE INDEX idx_user_preferences ON users USING gin(preferences);
```

### **Query Optimization**
- Materialized views for complex aggregations
- Partial indexes for filtered queries
- Connection pooling for high concurrency

## 🔐 Security Implementation

### **Row Level Security (RLS)**
```sql
-- Users can only access their own data
CREATE POLICY user_own_data ON users FOR ALL USING (auth.uid() = id);

-- Products are publicly readable, admin writable
CREATE POLICY products_public_read ON products FOR SELECT USING (true);
CREATE POLICY products_admin_write ON products FOR ALL USING (auth.jwt() ->> 'role' = 'admin');

-- Orders belong to users
CREATE POLICY orders_user_access ON orders FOR ALL USING (auth.uid() = user_id);
```

### **Data Validation**
- Check constraints on critical fields
- Foreign key constraints for referential integrity
- Trigger-based validation for complex rules

## 🚀 Migration Strategy

### **Version Control**
- All schema changes tracked in migrations
- Rollback procedures for each migration
- Environment-specific configurations

### **Data Seeding**
- Brand data seeding scripts
- Category hierarchy setup
- Test data generation

---

*Last Updated: 2025-06-15*
*Version: 1.0*
