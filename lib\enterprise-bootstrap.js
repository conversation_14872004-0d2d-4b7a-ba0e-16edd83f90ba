// 🚀 TWL ENTERPRISE SYSTEM BOOTSTRAP
// 🎯 Initializes and connects all enterprise components

import { enterpriseProductSystem } from './enterpriseProductDataSystem.js';
import { enterpriseIntegration } from './enterpriseIntegrationService.js';
import { runEnterpriseSystemTests } from './enterprise-system-test.js';

/**
 * 🚀 TWL ENTERPRISE SYSTEM BOOTSTRAP
 * 
 * This script initializes the complete enterprise product data integration system:
 * - Enterprise Product Data System
 * - Integration Service
 * - Test Suite Validation
 * - Performance Monitoring
 * - Error Handling & Fallbacks
 */
export class TWLEnterpriseBootstrap {
  constructor() {
    this.isBootstrapped = false;
    this.bootTime = null;
    this.systemStatus = {
      productDataSystem: 'not_initialized',
      integrationService: 'not_initialized',
      testSuite: 'not_run',
      overallHealth: 'unknown'
    };
    
    console.log('🚀 TWL Enterprise Bootstrap initialized');
  }

  /**
   * 🎯 Bootstrap the complete enterprise system
   */
  async bootstrap() {
    const startTime = Date.now();
    console.log('🚀 Starting TWL Enterprise System Bootstrap...\n');

    try {
      // Step 1: Initialize Product Data System
      await this.initializeProductDataSystem();
      
      // Step 2: Initialize Integration Service
      await this.initializeIntegrationService();
      
      // Step 3: Run System Tests (in development)
      if (process.env.NODE_ENV === 'development') {
        await this.runSystemTests();
      }
      
      // Step 4: Verify System Health
      await this.verifySystemHealth();
      
      // Step 5: Setup Monitoring
      this.setupMonitoring();
      
      this.bootTime = Date.now() - startTime;
      this.isBootstrapped = true;
      
      console.log(`\n🎉 TWL Enterprise System Bootstrap Complete!`);
      console.log(`⏱️  Boot Time: ${this.bootTime}ms`);
      console.log(`📊 System Status:`, this.systemStatus);
      
      return {
        success: true,
        bootTime: this.bootTime,
        status: this.systemStatus
      };
      
    } catch (error) {
      console.error('❌ Enterprise System Bootstrap Failed:', error);
      
      // Enable fallback mode
      await this.enableFallbackMode();
      
      return {
        success: false,
        error: error.message,
        fallbackEnabled: true,
        status: this.systemStatus
      };
    }
  }

  /**
   * 🏗️ Initialize Product Data System
   */
  async initializeProductDataSystem() {
    console.log('🏗️ Initializing Enterprise Product Data System...');
    
    try {
      // Test core functionality
      const testDescription = `💰300 -- 50$
Nike Air Force 1 x Gucci Limited Edition
Tamaño: 36 37 38 39 40 41 42 43 44 45
ID: TEST-BOOTSTRAP`;

      const parsed = enterpriseProductSystem.parseDescriptionFile(testDescription);
      if (!parsed || !parsed.productName) {
        throw new Error('Product Data System test failed');
      }

      const pricing = enterpriseProductSystem.calculateMexicanPricing(50);
      if (!pricing || !pricing.pricing) {
        throw new Error('Pricing calculation test failed');
      }

      const product = enterpriseProductSystem.createEnterpriseProduct(
        parsed, 'sneakers', 'nike', 'mixte'
      );
      if (!product || !product.id) {
        throw new Error('Product creation test failed');
      }

      this.systemStatus.productDataSystem = 'initialized';
      console.log('✅ Enterprise Product Data System initialized successfully');
      
    } catch (error) {
      this.systemStatus.productDataSystem = 'failed';
      throw new Error(`Product Data System initialization failed: ${error.message}`);
    }
  }

  /**
   * 🔗 Initialize Integration Service
   */
  async initializeIntegrationService() {
    console.log('🔗 Initializing Enterprise Integration Service...');
    
    try {
      // Ensure integration service is initialized
      if (!enterpriseIntegration.isInitialized) {
        await enterpriseIntegration.initialize();
      }

      // Test integration functionality
      const testProduct = await enterpriseIntegration.getProductForHomepage(
        'test-product-id', 'featured'
      );
      
      if (!testProduct) {
        throw new Error('Integration service test failed');
      }

      const status = enterpriseIntegration.getStatus();
      if (!status.initialized) {
        throw new Error('Integration service not properly initialized');
      }

      this.systemStatus.integrationService = 'initialized';
      console.log('✅ Enterprise Integration Service initialized successfully');
      
    } catch (error) {
      this.systemStatus.integrationService = 'failed';
      throw new Error(`Integration Service initialization failed: ${error.message}`);
    }
  }

  /**
   * 🧪 Run System Tests
   */
  async runSystemTests() {
    console.log('🧪 Running Enterprise System Tests...');
    
    try {
      const testResults = await runEnterpriseSystemTests();
      
      if (testResults.failed > 0) {
        console.warn(`⚠️ Some tests failed: ${testResults.failed}/${testResults.total}`);
        this.systemStatus.testSuite = 'partial_pass';
      } else {
        console.log(`✅ All tests passed: ${testResults.passed}/${testResults.total}`);
        this.systemStatus.testSuite = 'all_passed';
      }
      
    } catch (error) {
      console.error('❌ Test suite execution failed:', error);
      this.systemStatus.testSuite = 'failed';
      // Don't throw - tests failing shouldn't prevent bootstrap
    }
  }

  /**
   * 🏥 Verify System Health
   */
  async verifySystemHealth() {
    console.log('🏥 Verifying System Health...');
    
    const healthChecks = {
      productDataSystem: this.systemStatus.productDataSystem === 'initialized',
      integrationService: this.systemStatus.integrationService === 'initialized',
      testSuite: ['all_passed', 'partial_pass'].includes(this.systemStatus.testSuite)
    };

    const healthyComponents = Object.values(healthChecks).filter(Boolean).length;
    const totalComponents = Object.keys(healthChecks).length;

    if (healthyComponents === totalComponents) {
      this.systemStatus.overallHealth = 'excellent';
      console.log('✅ System Health: Excellent - All components operational');
    } else if (healthyComponents >= totalComponents * 0.7) {
      this.systemStatus.overallHealth = 'good';
      console.log('⚠️ System Health: Good - Most components operational');
    } else {
      this.systemStatus.overallHealth = 'poor';
      console.log('❌ System Health: Poor - Multiple component failures');
      throw new Error('System health check failed');
    }
  }

  /**
   * 📊 Setup Monitoring
   */
  setupMonitoring() {
    console.log('📊 Setting up System Monitoring...');
    
    // Performance monitoring
    this.performanceMonitor = {
      startTime: Date.now(),
      requestCount: 0,
      errorCount: 0,
      averageResponseTime: 0
    };

    // Error tracking
    this.errorTracker = {
      errors: [],
      maxErrors: 100,
      lastError: null
    };

    // Health check interval (every 5 minutes)
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 5 * 60 * 1000);

    console.log('✅ System Monitoring active');
  }

  /**
   * 🔄 Enable Fallback Mode
   */
  async enableFallbackMode() {
    console.log('🔄 Enabling Fallback Mode...');
    
    try {
      // Configure integration service for fallback
      enterpriseIntegration.updateSettings({
        useEnterpriseData: false,
        fallbackToMockData: true,
        enablePricingIntelligence: false
      });

      this.systemStatus.overallHealth = 'fallback';
      console.log('✅ Fallback Mode enabled - Using mock data');
      
    } catch (error) {
      console.error('❌ Failed to enable fallback mode:', error);
      this.systemStatus.overallHealth = 'critical';
    }
  }

  /**
   * 🏥 Perform Health Check
   */
  async performHealthCheck() {
    try {
      const status = enterpriseIntegration.getStatus();
      
      if (!status.initialized) {
        console.warn('⚠️ Health Check: Integration service not initialized');
        await this.initializeIntegrationService();
      }
      
      // Test basic functionality
      const testProduct = await enterpriseIntegration.getProductForHomepage(
        'health-check-product', 'test'
      );
      
      if (!testProduct) {
        throw new Error('Health check product retrieval failed');
      }
      
      console.log('✅ Health Check: System operational');
      
    } catch (error) {
      console.error('❌ Health Check Failed:', error);
      this.logError(error);
    }
  }

  /**
   * 📝 Log Error
   */
  logError(error) {
    const errorEntry = {
      timestamp: new Date().toISOString(),
      message: error.message,
      stack: error.stack
    };

    this.errorTracker.errors.push(errorEntry);
    this.errorTracker.lastError = errorEntry;
    this.errorTracker.errorCount++;

    // Keep only last 100 errors
    if (this.errorTracker.errors.length > this.errorTracker.maxErrors) {
      this.errorTracker.errors.shift();
    }
  }

  /**
   * 📊 Get System Metrics
   */
  getSystemMetrics() {
    return {
      isBootstrapped: this.isBootstrapped,
      bootTime: this.bootTime,
      status: this.systemStatus,
      performance: this.performanceMonitor,
      errors: {
        count: this.errorTracker.errorCount,
        lastError: this.errorTracker.lastError
      },
      uptime: this.performanceMonitor ? Date.now() - this.performanceMonitor.startTime : 0
    };
  }

  /**
   * 🛑 Shutdown System
   */
  shutdown() {
    console.log('🛑 Shutting down Enterprise System...');
    
    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }
    
    this.isBootstrapped = false;
    console.log('✅ Enterprise System shutdown complete');
  }
}

// Export singleton instance
export const enterpriseBootstrap = new TWLEnterpriseBootstrap();

// Auto-bootstrap in production
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'production') {
  enterpriseBootstrap.bootstrap().catch(console.error);
}

// Export utilities
export const EnterpriseUtils = {
  /**
   * 🚀 Initialize enterprise system
   */
  async initialize() {
    return await enterpriseBootstrap.bootstrap();
  },

  /**
   * 📊 Get system status
   */
  getStatus() {
    return enterpriseBootstrap.getSystemMetrics();
  },

  /**
   * 🏥 Check system health
   */
  async healthCheck() {
    return await enterpriseBootstrap.performHealthCheck();
  },

  /**
   * 🛑 Shutdown system
   */
  shutdown() {
    enterpriseBootstrap.shutdown();
  }
};
