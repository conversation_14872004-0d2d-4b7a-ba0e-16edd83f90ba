/**
 * TWL Enterprise System - Real Data Testing (JavaScript)
 * Comprehensive testing with actual product data
 */

const fs = require('fs')
const path = require('path')

async function analyzeProductStructure() {
  console.log('\n📁 Analyzing Real Product Directory Structure...')
  
  const productsPath = 'public/products'
  const stats = {
    categories: 0,
    brands: 0,
    products: 0,
    images: 0,
    videos: 0,
    descriptions: 0,
    sampleProducts: []
  }

  function scanDirectory(dirPath, level = 0) {
    try {
      const items = fs.readdirSync(dirPath)
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item)
        const stat = fs.statSync(itemPath)
        
        if (stat.isDirectory()) {
          if (level === 0) stats.categories++
          if (level === 1) stats.brands++
          if (level >= 2) {
            stats.products++
            
            // Check if this is a product folder (contains Description.txt)
            const descPath = path.join(itemPath, 'Description.txt')
            if (fs.existsSync(descPath)) {
              try {
                const description = fs.readFileSync(descPath, 'utf8')
                const lines = description.split('\n').filter(line => line.trim())
                
                if (lines.length >= 3) {
                  const priceLine = lines[1] // 💰280 -- 35$
                  const nameLine = lines[2]  // Nike Bode
                  const sizeLine = lines[3]  // Tamaño: 36 36,5...
                  
                  // Parse price
                  const priceMatch = priceLine.match(/💰(\d+)\s*--\s*(\d+)\$/)
                  const originalPrice = priceMatch ? parseInt(priceMatch[1]) : 0
                  const currentPrice = priceMatch ? parseInt(priceMatch[2]) : 0
                  
                  // Parse sizes
                  const sizeMatch = sizeLine.match(/Tamaño:\s*(.+)/)
                  const sizes = sizeMatch ? sizeMatch[1].split(' ').filter(s => s.trim()) : []
                  
                  // Count media files
                  const mediaFiles = fs.readdirSync(itemPath)
                  const images = mediaFiles.filter(f => f.endsWith('.webp') || f.endsWith('.jpg')).length
                  const videos = mediaFiles.filter(f => f.endsWith('.mp4')).length
                  
                  const productData = {
                    id: path.basename(itemPath),
                    name: nameLine.trim(),
                    originalPrice,
                    currentPrice,
                    sizes: sizes.length,
                    images,
                    videos,
                    path: itemPath.replace('public/', ''),
                    category: dirPath.split(path.sep)[1] || 'Unknown',
                    brand: dirPath.split(path.sep)[2] || 'Unknown'
                  }
                  
                  if (stats.sampleProducts.length < 10) {
                    stats.sampleProducts.push(productData)
                  }
                }
              } catch (error) {
                console.log(`  ⚠️ Error reading ${descPath}: ${error.message}`)
              }
            }
          }
          
          scanDirectory(itemPath, level + 1)
        } else {
          if (item.endsWith('.webp') || item.endsWith('.jpg') || item.endsWith('.png')) {
            stats.images++
          } else if (item.endsWith('.mp4') || item.endsWith('.webm')) {
            stats.videos++
          } else if (item === 'Description.txt') {
            stats.descriptions++
          }
        }
      }
    } catch (error) {
      console.error(`Error scanning ${dirPath}:`, error.message)
    }
  }

  if (fs.existsSync(productsPath)) {
    scanDirectory(productsPath)
  } else {
    console.error(`❌ Products directory not found: ${productsPath}`)
    return null
  }
  
  console.log(`📊 Directory Structure Analysis:`)
  console.log(`  Categories: ${stats.categories}`)
  console.log(`  Brands: ${stats.brands}`)
  console.log(`  Product folders: ${stats.products}`)
  console.log(`  Images: ${stats.images}`)
  console.log(`  Videos: ${stats.videos}`)
  console.log(`  Description files: ${stats.descriptions}`)
  
  return stats
}

async function testProductParsing(stats) {
  console.log('\n🧪 Testing Product Data Parsing...')
  
  if (!stats.sampleProducts || stats.sampleProducts.length === 0) {
    console.log('❌ No sample products found for testing')
    return
  }
  
  console.log(`Testing ${stats.sampleProducts.length} sample products:`)
  
  for (const product of stats.sampleProducts) {
    console.log(`\n  📦 ${product.name}`)
    console.log(`    ID: ${product.id}`)
    console.log(`    Category: ${product.category}`)
    console.log(`    Brand: ${product.brand}`)
    console.log(`    Price: $${product.currentPrice} (was $${product.originalPrice})`)
    console.log(`    Sizes: ${product.sizes} available`)
    console.log(`    Media: ${product.images} images, ${product.videos} videos`)
    console.log(`    Path: ${product.path}`)
    
    // Verify files exist
    const fullPath = path.join('public', product.path)
    if (fs.existsSync(fullPath)) {
      const files = fs.readdirSync(fullPath)
      const actualImages = files.filter(f => f.endsWith('.webp') || f.endsWith('.jpg')).length
      const actualVideos = files.filter(f => f.endsWith('.mp4')).length
      
      if (actualImages === product.images && actualVideos === product.videos) {
        console.log(`    ✅ Media files verified`)
      } else {
        console.log(`    ⚠️ Media count mismatch: expected ${product.images}/${product.videos}, found ${actualImages}/${actualVideos}`)
      }
    } else {
      console.log(`    ❌ Product directory not found: ${fullPath}`)
    }
  }
}

async function testPerformance(stats) {
  console.log('\n⚡ Performance Analysis...')
  
  const totalFiles = stats.images + stats.videos + stats.descriptions
  const avgFilesPerProduct = stats.products > 0 ? (totalFiles / stats.products).toFixed(1) : 0
  
  console.log(`📊 Performance Metrics:`)
  console.log(`  Total files: ${totalFiles.toLocaleString()}`)
  console.log(`  Average files per product: ${avgFilesPerProduct}`)
  console.log(`  Storage efficiency: ${stats.descriptions === stats.products ? '✅' : '⚠️'} (${stats.descriptions}/${stats.products} products have descriptions)`)
  
  // Estimate memory usage
  const estimatedMemoryMB = Math.round((stats.products * 0.1) + (stats.images * 0.05) + (stats.videos * 0.1))
  console.log(`  Estimated cache memory: ~${estimatedMemoryMB}MB`)
  
  // Performance recommendations
  console.log(`\n🎯 Performance Recommendations:`)
  if (stats.products > 1000) {
    console.log(`  ⚡ Large catalog detected (${stats.products} products) - enable Redis caching`)
  }
  if (stats.images > 10000) {
    console.log(`  🖼️ High image count (${stats.images}) - consider CDN integration`)
  }
  if (stats.videos > 500) {
    console.log(`  🎥 Many videos (${stats.videos}) - implement lazy loading`)
  }
  
  return {
    totalFiles,
    avgFilesPerProduct,
    estimatedMemoryMB
  }
}

async function generateReport(stats, performance) {
  console.log('\n📋 COMPREHENSIVE ANALYSIS REPORT')
  console.log('=' .repeat(50))
  
  console.log(`\n📊 Data Overview:`)
  console.log(`  ✅ Categories: ${stats.categories}`)
  console.log(`  ✅ Brands: ${stats.brands}`)
  console.log(`  ✅ Products: ${stats.products}`)
  console.log(`  ✅ Images: ${stats.images.toLocaleString()}`)
  console.log(`  ✅ Videos: ${stats.videos.toLocaleString()}`)
  console.log(`  ✅ Descriptions: ${stats.descriptions}`)
  
  console.log(`\n⚡ Performance Metrics:`)
  console.log(`  📁 Total files: ${performance.totalFiles.toLocaleString()}`)
  console.log(`  📊 Avg files/product: ${performance.avgFilesPerProduct}`)
  console.log(`  💾 Est. memory usage: ${performance.estimatedMemoryMB}MB`)
  
  console.log(`\n🎯 System Readiness:`)
  const readiness = {
    dataStructure: stats.products > 0 ? '✅' : '❌',
    mediaFiles: stats.images > 0 ? '✅' : '❌',
    descriptions: stats.descriptions > 0 ? '✅' : '❌',
    scalability: stats.products < 10000 ? '✅' : '⚠️'
  }
  
  console.log(`  Data Structure: ${readiness.dataStructure} ${stats.products > 0 ? 'Ready' : 'No products found'}`)
  console.log(`  Media Files: ${readiness.mediaFiles} ${stats.images > 0 ? 'Ready' : 'No images found'}`)
  console.log(`  Product Info: ${readiness.descriptions} ${stats.descriptions > 0 ? 'Ready' : 'No descriptions found'}`)
  console.log(`  Scalability: ${readiness.scalability} ${stats.products < 10000 ? 'Optimal' : 'Consider optimization'}`)
  
  const overallReady = Object.values(readiness).every(status => status === '✅')
  console.log(`\n🚀 Overall Status: ${overallReady ? '✅ READY FOR ENTERPRISE SYSTEM' : '⚠️ NEEDS ATTENTION'}`)
  
  if (overallReady) {
    console.log(`\n🎉 Your product data is perfectly structured for the TWL Enterprise System!`)
    console.log(`   The system can handle your ${stats.products} products with ${stats.images.toLocaleString()} images efficiently.`)
  }
  
  return {
    stats,
    performance,
    readiness,
    overallReady
  }
}

async function runCompleteAnalysis() {
  console.log('🧪 TWL Enterprise System - Real Data Analysis')
  console.log('Analyzing actual product data from your directory')
  console.log('=' .repeat(60))

  try {
    // 1. Analyze directory structure
    const stats = await analyzeProductStructure()
    if (!stats) {
      console.error('❌ Failed to analyze product structure')
      return
    }

    // 2. Test product parsing
    await testProductParsing(stats)

    // 3. Performance analysis
    const performance = await testPerformance(stats)

    // 4. Generate comprehensive report
    const report = await generateReport(stats, performance)

    console.log('\n🎯 Next Steps:')
    if (report.overallReady) {
      console.log('  1. ✅ Initialize the TWL Enterprise System')
      console.log('  2. ✅ Test with your existing cart system')
      console.log('  3. ✅ Deploy to production')
    } else {
      console.log('  1. 🔧 Fix any data structure issues')
      console.log('  2. 📁 Ensure all products have Description.txt files')
      console.log('  3. 🖼️ Verify image and video files are accessible')
    }

    return report

  } catch (error) {
    console.error('❌ Analysis failed:', error)
    return null
  }
}

// Run the analysis
if (require.main === module) {
  runCompleteAnalysis()
    .then(report => {
      if (report && report.overallReady) {
        console.log('\n🚀 Ready to proceed with enterprise system integration!')
      }
    })
    .catch(console.error)
}

module.exports = {
  runCompleteAnalysis,
  analyzeProductStructure,
  testProductParsing,
  testPerformance
}
