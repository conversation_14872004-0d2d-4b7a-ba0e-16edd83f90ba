'use client'

import { useState } from 'react'
import { motion, useSpring } from 'framer-motion'

export default function AnimatedCheckbox({
  checked = false,
  onChange,
  label = '',
  description = '',
  disabled = false,
  size = 'md',
  variant = 'default',
  className = '',
  ...props
}) {
  const [isHovered, setIsHovered] = useState(false)
  const [isPressed, setIsPressed] = useState(false)
  
  // Spring animations
  const springConfig = { stiffness: 400, damping: 30 }
  const scale = useSpring(1, springConfig)
  const checkScale = useSpring(checked ? 1 : 0, springConfig)
  const borderScale = useSpring(1, springConfig)

  const sizes = {
    sm: { box: 'w-4 h-4', text: 'text-sm', check: 'w-2.5 h-2.5' },
    md: { box: 'w-5 h-5', text: 'text-base', check: 'w-3 h-3' },
    lg: { box: 'w-6 h-6', text: 'text-lg', check: 'w-4 h-4' }
  }

  const variants = {
    default: {
      bg: checked ? 'bg-rich-gold' : 'bg-transparent',
      border: checked ? 'border-rich-gold' : 'border-soft-steel-gray dark:border-warm-camel/30',
      check: 'text-forest-emerald',
      label: 'text-forest-emerald dark:text-light-cloud-gray',
      description: 'text-warm-camel'
    },
    success: {
      bg: checked ? 'bg-forest-emerald' : 'bg-transparent',
      border: checked ? 'border-forest-emerald' : 'border-soft-steel-gray dark:border-warm-camel/30',
      check: 'text-white',
      label: 'text-forest-emerald dark:text-light-cloud-gray',
      description: 'text-warm-camel'
    },
    warning: {
      bg: checked ? 'bg-warm-camel' : 'bg-transparent',
      border: checked ? 'border-warm-camel' : 'border-soft-steel-gray dark:border-warm-camel/30',
      check: 'text-white',
      label: 'text-forest-emerald dark:text-light-cloud-gray',
      description: 'text-warm-camel'
    }
  }

  const currentSize = sizes[size]
  const currentVariant = variants[variant]

  const handleMouseEnter = () => {
    setIsHovered(true)
    scale.set(1.05)
    borderScale.set(1.1)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
    scale.set(1)
    borderScale.set(1)
  }

  const handleMouseDown = () => {
    setIsPressed(true)
    scale.set(0.95)
  }

  const handleMouseUp = () => {
    setIsPressed(false)
    scale.set(isHovered ? 1.05 : 1)
  }

  const handleChange = (e) => {
    if (disabled) return
    
    // Trigger check animation
    checkScale.set(e.target.checked ? 1 : 0)
    
    // Ripple effect
    scale.set(0.9)
    setTimeout(() => {
      scale.set(isHovered ? 1.05 : 1)
    }, 100)
    
    onChange?.(e)
  }

  return (
    <label
      className={`
        flex items-start gap-3 cursor-pointer select-none
        ${disabled ? 'opacity-50 cursor-not-allowed' : ''}
        ${className}
      `}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
      onMouseDown={handleMouseDown}
      onMouseUp={handleMouseUp}
    >
      {/* Checkbox Container */}
      <div className="relative flex-shrink-0 mt-0.5">
        {/* Background Glow */}
        <motion.div
          className={`absolute inset-0 rounded ${currentVariant.bg} opacity-20`}
          style={{ scale: borderScale }}
          animate={{
            opacity: isHovered ? 0.3 : 0.2
          }}
          transition={{ duration: 0.2 }}
        />
        
        {/* Checkbox Box */}
        <motion.div
          style={{ scale }}
          className={`
            relative ${currentSize.box} 
            border-2 ${currentVariant.border}
            rounded transition-colors duration-200
            ${currentVariant.bg}
            flex items-center justify-center
            overflow-hidden
          `}
        >
          {/* Ripple Effect */}
          <motion.div
            className="absolute inset-0 bg-rich-gold/30 rounded"
            initial={{ scale: 0, opacity: 0 }}
            animate={{
              scale: isPressed ? 1.5 : 0,
              opacity: isPressed ? 1 : 0
            }}
            transition={{ duration: 0.3 }}
          />
          
          {/* Checkmark */}
          <motion.div
            style={{ scale: checkScale }}
            className={`${currentVariant.check} ${currentSize.check}`}
          >
            <motion.svg
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth={3}
              className="w-full h-full"
              initial={{ pathLength: 0 }}
              animate={{ pathLength: checked ? 1 : 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
            >
              <motion.path
                strokeLinecap="round"
                strokeLinejoin="round"
                d="M5 13l4 4L19 7"
                initial={{ pathLength: 0, opacity: 0 }}
                animate={{
                  pathLength: checked ? 1 : 0,
                  opacity: checked ? 1 : 0
                }}
                transition={{ duration: 0.4, ease: "easeInOut" }}
              />
            </motion.svg>
          </motion.div>
          
          {/* Shimmer Effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            initial={{ x: "-100%" }}
            animate={{
              x: checked ? "100%" : "-100%"
            }}
            transition={{
              duration: 0.6,
              ease: "easeInOut"
            }}
          />
        </motion.div>
        
        {/* Outer Glow Ring */}
        <motion.div
          className={`absolute inset-0 rounded border-2 border-rich-gold`}
          style={{
            scale: borderScale,
            opacity: isHovered ? 0.4 : 0
          }}
          animate={{
            scale: isHovered ? 1.2 : 1,
            opacity: isHovered ? 0.4 : 0
          }}
          transition={{ duration: 0.3 }}
        />
        
        {/* Hidden Input */}
        <input
          type="checkbox"
          checked={checked}
          onChange={handleChange}
          disabled={disabled}
          className="absolute inset-0 opacity-0 cursor-pointer"
          {...props}
        />
      </div>

      {/* Label Content */}
      {(label || description) && (
        <div className="flex-1 min-w-0">
          {label && (
            <motion.div
              className={`${currentVariant.label} ${currentSize.text} font-medium leading-tight`}
              animate={{
                color: checked ? "#FFD700" : undefined
              }}
              transition={{ duration: 0.2 }}
            >
              {label}
            </motion.div>
          )}
          
          {description && (
            <motion.div
              className={`${currentVariant.description} text-sm mt-1 leading-relaxed`}
              initial={{ opacity: 0.8 }}
              animate={{
                opacity: isHovered ? 1 : 0.8
              }}
              transition={{ duration: 0.2 }}
            >
              {description}
            </motion.div>
          )}
        </div>
      )}
    </label>
  )
}
