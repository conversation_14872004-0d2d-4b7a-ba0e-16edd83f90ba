'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'

export default function ProductVariants({
  product,
  selectedVariant,
  onVariantChange,
  onAddToCart,
  className = ''
}) {
  const [selectedSize, setSelectedSize] = useState(null)
  const [selectedColor, setSelectedColor] = useState(null)
  const [quantity, setQuantity] = useState(1)
  const [isAddingToCart, setIsAddingToCart] = useState(false)

  // Initialize with first available variant
  useEffect(() => {
    if (product?.variants && product.variants.length > 0) {
      const firstVariant = product.variants[0]
      setSelectedColor(firstVariant.color)
      setSelectedSize(firstVariant.sizes?.[0])
      onVariantChange?.(firstVariant)
    }
  }, [product, onVariantChange])

  // Get available colors
  const availableColors = product?.variants?.reduce((colors, variant) => {
    if (!colors.find(c => c.id === variant.color.id)) {
      colors.push(variant.color)
    }
    return colors
  }, []) || []

  // Get available sizes for selected color
  const availableSizes = product?.variants?.find(
    variant => variant.color.id === selectedColor?.id
  )?.sizes || []

  // Get current variant
  const currentVariant = product?.variants?.find(
    variant => variant.color.id === selectedColor?.id
  )

  // Check if current selection is in stock
  const isInStock = currentVariant?.stock > 0
  const stockLevel = currentVariant?.stock || 0

  const handleColorChange = (color) => {
    setSelectedColor(color)
    const newVariant = product.variants.find(v => v.color.id === color.id)
    if (newVariant) {
      setSelectedSize(newVariant.sizes?.[0] || null)
      onVariantChange?.(newVariant)
    }
  }

  const handleSizeChange = (size) => {
    setSelectedSize(size)
  }

  const handleAddToCart = async () => {
    if (!selectedColor || !selectedSize || !isInStock) return

    setIsAddingToCart(true)

    try {
      await onAddToCart?.({
        productId: product.id,
        variantId: currentVariant.id,
        color: selectedColor,
        size: selectedSize,
        quantity
      })
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setIsAddingToCart(false)
    }
  }

  const getStockStatus = () => {
    if (stockLevel === 0) return { text: 'Agotado', variant: 'danger' }
    if (stockLevel <= 5) return { text: `Solo ${stockLevel} disponibles`, variant: 'warning' }
    if (stockLevel <= 10) return { text: 'Pocas unidades', variant: 'warning' }
    return { text: 'En stock', variant: 'success' }
  }

  const stockStatus = getStockStatus()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`space-y-6 ${className}`}
    >
      {/* Color Selection */}
      <div>
        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-3">
          Color: {selectedColor?.name}
        </h3>
        <div className="flex flex-wrap gap-3">
          {availableColors.map((color) => (
            <motion.button
              key={color.id}
              onClick={() => handleColorChange(color)}
              className={`relative w-12 h-12 rounded-full border-2 transition-all duration-200 ${
                selectedColor?.id === color.id
                  ? 'border-rich-gold shadow-lg scale-110'
                  : 'border-warm-camel/30 hover:border-warm-camel'
              }`}
              style={{ backgroundColor: color.hex }}
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.95 }}
              title={color.name}
            >
              {selectedColor?.id === color.id && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="absolute inset-0 flex items-center justify-center"
                >
                  <svg className="w-6 h-6 text-white drop-shadow-lg" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                </motion.div>
              )}
            </motion.button>
          ))}
        </div>
      </div>

      {/* Size Selection */}
      <div>
        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-3">
          Talla: {selectedSize || 'Selecciona una talla'}
        </h3>
        <div className="grid grid-cols-4 sm:grid-cols-6 gap-2">
          {availableSizes.map((size) => (
            <motion.button
              key={size}
              onClick={() => handleSizeChange(size)}
              className={`p-3 text-center border rounded-lg transition-all duration-200 ${
                selectedSize === size
                  ? 'border-rich-gold bg-rich-gold/10 text-forest-emerald dark:text-light-cloud-gray font-semibold'
                  : 'border-warm-camel/30 text-warm-camel hover:border-warm-camel hover:bg-warm-camel/5'
              }`}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              {size}
            </motion.button>
          ))}
        </div>

        {availableSizes.length === 0 && (
          <p className="text-warm-camel text-sm">
            No hay tallas disponibles para este color
          </p>
        )}
      </div>

      {/* Stock Status */}
      <div className="flex items-center gap-2">
        <Badge variant={stockStatus.variant} size="sm">
          {stockStatus.text}
        </Badge>
        {currentVariant?.sku && (
          <span className="text-xs text-warm-camel">
            SKU: {currentVariant.sku}
          </span>
        )}
      </div>

      {/* Quantity Selection */}
      <div>
        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-3">
          Cantidad
        </h3>
        <div className="flex items-center gap-3">
          <button
            onClick={() => setQuantity(Math.max(1, quantity - 1))}
            disabled={quantity <= 1}
            className="w-10 h-10 rounded-lg border border-warm-camel/30 flex items-center justify-center text-warm-camel hover:border-warm-camel disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
            </svg>
          </button>

          <span className="w-12 text-center font-semibold text-forest-emerald dark:text-light-cloud-gray">
            {quantity}
          </span>

          <button
            onClick={() => setQuantity(Math.min(stockLevel, quantity + 1))}
            disabled={quantity >= stockLevel}
            className="w-10 h-10 rounded-lg border border-warm-camel/30 flex items-center justify-center text-warm-camel hover:border-warm-camel disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
        </div>
      </div>

      {/* Price Display */}
      {currentVariant && (
        <Card variant="glass">
          <CardContent className="p-4">
            <div className="flex justify-between items-center">
              <div>
                <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                  ${(currentVariant.price * quantity).toLocaleString()} MXN
                </div>
                {quantity > 1 && (
                  <div className="text-sm text-warm-camel">
                    ${currentVariant.price.toLocaleString()} MXN c/u
                  </div>
                )}
              </div>
              {currentVariant.originalPrice && currentVariant.originalPrice > currentVariant.price && (
                <div className="text-right">
                  <div className="text-sm text-warm-camel line-through">
                    ${(currentVariant.originalPrice * quantity).toLocaleString()} MXN
                  </div>
                  <Badge variant="success" size="sm">
                    {Math.round((1 - currentVariant.price / currentVariant.originalPrice) * 100)}% OFF
                  </Badge>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Add to Cart Button */}
      <AnimatedButton
        variant="primary"
        size="lg"
        className="w-full"
        onClick={handleAddToCart}
        disabled={!selectedColor || !selectedSize || !isInStock || isAddingToCart}
        loading={isAddingToCart}
        icon={
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
          </svg>
        }
      >
        {isAddingToCart ? 'Agregando...' :
         !isInStock ? 'Agotado' :
         !selectedColor || !selectedSize ? 'Selecciona opciones' :
         'Agregar al Carrito'}
      </AnimatedButton>

      {/* Size Guide Link */}
      <div className="text-center">
        <button className="text-sm text-warm-camel hover:text-rich-gold transition-colors underline">
          📏 Guía de Tallas
        </button>
      </div>
    </motion.div>
  )
}
