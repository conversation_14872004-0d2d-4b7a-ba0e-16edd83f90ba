'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import EnhancedSearchBar from '@/components/features/EnhancedSearchBar'
import SmartRecommendations from '@/components/features/SmartRecommendations'
import VoiceSearch from '@/components/features/VoiceSearch'
import VisualSearch from '@/components/features/VisualSearch'
import SizeRecommendation from '@/components/features/SizeRecommendation'
import ARTryOn from '@/components/features/ARTryOn'
import SmartWishlist from '@/components/features/SmartWishlist'
import SocialShopping from '@/components/features/SocialShopping'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'
// Mock products data
const products = [
  { id: 1, name: 'Nike Air Force 1', brand: 'Nike', price: 2500, category: 'sneakers' },
  { id: 2, name: 'Adidas Stan Smith', brand: 'Adidas', price: 2200, category: 'sneakers' },
  { id: 3, name: 'Gucci Ace Sneakers', brand: 'Gucci', price: 15000, category: 'luxury' },
  { id: 4, name: 'Nike Air Max 90', brand: 'Nike', price: 2800, category: 'sneakers' },
  { id: 5, name: 'Converse Chuck Taylor', brand: 'Converse', price: 1800, category: 'casual' },
  { id: 6, name: 'Vans Old Skool', brand: 'Vans', price: 1900, category: 'casual' }
]

export default function UXDemo() {
  const [activeDemo, setActiveDemo] = useState('search')
  const [showVoiceSearch, setShowVoiceSearch] = useState(false)
  const [showVisualSearch, setShowVisualSearch] = useState(false)
  const [showSizeRecommendation, setShowSizeRecommendation] = useState(false)
  const [showARTryOn, setShowARTryOn] = useState(false)
  const [showSmartWishlist, setShowSmartWishlist] = useState(false)
  const [showSocialShopping, setShowSocialShopping] = useState(false)
  const [searchResults, setSearchResults] = useState([])

  const demoSections = [
    {
      id: 'search',
      name: 'Búsqueda Inteligente',
      icon: '🔍',
      description: 'Búsqueda por texto, voz e imagen con IA'
    },
    {
      id: 'recommendations',
      name: 'Recomendaciones IA',
      icon: '🤖',
      description: 'Sugerencias personalizadas basadas en preferencias'
    },
    {
      id: 'size',
      name: 'Tallas Inteligentes',
      icon: '📏',
      description: 'Recomendación de talla con IA y medidas'
    },
    {
      id: 'voice',
      name: 'Búsqueda por Voz',
      icon: '🎤',
      description: 'Reconocimiento de voz en español mexicano'
    },
    {
      id: 'visual',
      name: 'Búsqueda Visual',
      icon: '📸',
      description: 'Encuentra productos similares con fotos'
    },
    {
      id: 'ar',
      name: 'Realidad Aumentada',
      icon: '🥽',
      description: 'Pruébate zapatos virtualmente'
    },
    {
      id: 'wishlist',
      name: 'Wishlist Inteligente',
      icon: '💝',
      description: 'Análisis IA de tus productos favoritos'
    },
    {
      id: 'social',
      name: 'Shopping Social',
      icon: '👥',
      description: 'Comunidad y tendencias sociales'
    }
  ]

  const handleSearch = (query) => {
    // Mock search results
    const results = products.filter(product =>
      product.name.toLowerCase().includes(query.toLowerCase()) ||
      product.brand.toLowerCase().includes(query.toLowerCase())
    ).slice(0, 6)
    
    setSearchResults(results)
  }

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Experiencia de Usuario Avanzada
          </h1>
          <p className="text-warm-camel text-lg max-w-3xl mx-auto">
            Descubre las características de vanguardia que diferencian a The White Laces: 
            IA, búsqueda inteligente, recomendaciones personalizadas y más.
          </p>
        </motion.div>

        {/* Demo Navigation */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="flex flex-wrap justify-center gap-3 mb-12"
        >
          {demoSections.map((section) => (
            <AnimatedButton
              key={section.id}
              variant={activeDemo === section.id ? 'primary' : 'ghost'}
              onClick={() => setActiveDemo(section.id)}
              icon={<span className="text-lg">{section.icon}</span>}
            >
              {section.name}
            </AnimatedButton>
          ))}
        </motion.div>

        {/* Demo Content */}
        <motion.div
          key={activeDemo}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ duration: 0.3 }}
        >
          
          {/* Enhanced Search Demo */}
          {activeDemo === 'search' && (
            <div className="space-y-8">
              <Card variant="glass">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6 text-center">
                    Búsqueda Inteligente Multimodal
                  </h2>
                  
                  <div className="max-w-2xl mx-auto mb-8">
                    <EnhancedSearchBar
                      placeholder="Prueba: 'Nike Air Force blancos' o usa voz/imagen..."
                      onSearch={handleSearch}
                    />
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                    <Card variant="default">
                      <CardContent className="p-6 text-center">
                        <div className="text-4xl mb-4">🔍</div>
                        <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                          Búsqueda por Texto
                        </h3>
                        <p className="text-warm-camel text-sm">
                          Autocompletado inteligente con sugerencias contextuales
                        </p>
                      </CardContent>
                    </Card>

                    <Card variant="default">
                      <CardContent className="p-6 text-center">
                        <div className="text-4xl mb-4">🎤</div>
                        <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                          Búsqueda por Voz
                        </h3>
                        <p className="text-warm-camel text-sm">
                          Reconocimiento de voz en español mexicano con IA
                        </p>
                      </CardContent>
                    </Card>

                    <Card variant="default">
                      <CardContent className="p-6 text-center">
                        <div className="text-4xl mb-4">📸</div>
                        <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                          Búsqueda Visual
                        </h3>
                        <p className="text-warm-camel text-sm">
                          Sube una foto y encuentra productos similares
                        </p>
                      </CardContent>
                    </Card>
                  </div>

                  {searchResults.length > 0 && (
                    <div>
                      <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                        Resultados de Búsqueda:
                      </h3>
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {searchResults.map((product, index) => (
                          <motion.div
                            key={product.id}
                            initial={{ opacity: 0, y: 20 }}
                            animate={{ opacity: 1, y: 0 }}
                            transition={{ delay: index * 0.1 }}
                          >
                            <Card variant="glass">
                              <CardContent className="p-4">
                                <div className="aspect-square bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray rounded-lg flex items-center justify-center mb-3">
                                  <span className="text-warm-camel font-medium text-center px-2">
                                    {product.name}
                                  </span>
                                </div>
                                <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm mb-1">
                                  {product.name}
                                </h4>
                                <p className="text-warm-camel text-xs mb-2">{product.brand}</p>
                                <p className="text-forest-emerald dark:text-light-cloud-gray font-bold text-sm">
                                  ${product.price.toLocaleString()} MXN
                                </p>
                              </CardContent>
                            </Card>
                          </motion.div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Smart Recommendations Demo */}
          {activeDemo === 'recommendations' && (
            <div className="space-y-8">
              <Card variant="glass">
                <CardContent className="p-8">
                  <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6 text-center">
                    Recomendaciones Inteligentes con IA
                  </h2>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                    {[
                      {
                        title: 'Personalizadas',
                        description: 'Basadas en tus preferencias y historial',
                        icon: '🎯'
                      },
                      {
                        title: 'Similares',
                        description: 'Productos relacionados al que estás viendo',
                        icon: '🔗'
                      },
                      {
                        title: 'Tendencias',
                        description: 'Lo más popular y trending del momento',
                        icon: '📈'
                      },
                      {
                        title: 'Completa tu Look',
                        description: 'Productos que combinan perfectamente',
                        icon: '✨'
                      }
                    ].map((type, index) => (
                      <motion.div
                        key={type.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card variant="default">
                          <CardContent className="p-4 text-center">
                            <div className="text-3xl mb-3">{type.icon}</div>
                            <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                              {type.title}
                            </h3>
                            <p className="text-warm-camel text-sm">
                              {type.description}
                            </p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <SmartRecommendations
                type="personalized"
                title="Recomendaciones Personalizadas"
                limit={6}
              />
            </div>
          )}

          {/* Size Recommendation Demo */}
          {activeDemo === 'size' && (
            <div className="space-y-8">
              <Card variant="glass">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                    Recomendación de Talla con IA
                  </h2>
                  
                  <div className="max-w-2xl mx-auto mb-8">
                    <p className="text-warm-camel mb-6">
                      Nuestro sistema de IA analiza las medidas de tu pie, preferencias de ajuste 
                      y características específicas de cada marca para recomendarte la talla perfecta.
                    </p>
                    
                    <AnimatedButton
                      variant="primary"
                      size="lg"
                      onClick={() => setShowSizeRecommendation(true)}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                        </svg>
                      }
                    >
                      Probar Recomendación de Talla
                    </AnimatedButton>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      {
                        title: 'Medidas Precisas',
                        description: 'Guía paso a paso para medir tu pie correctamente',
                        icon: '📏'
                      },
                      {
                        title: 'Análisis de Marca',
                        description: 'Considera las diferencias de tallaje entre marcas',
                        icon: '🏷️'
                      },
                      {
                        title: 'Preferencias Personales',
                        description: 'Ajusta según tu preferencia de ajuste',
                        icon: '⚙️'
                      }
                    ].map((feature, index) => (
                      <motion.div
                        key={feature.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <Card variant="default">
                          <CardContent className="p-6 text-center">
                            <div className="text-4xl mb-4">{feature.icon}</div>
                            <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                              {feature.title}
                            </h3>
                            <p className="text-warm-camel text-sm">
                              {feature.description}
                            </p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Voice Search Demo */}
          {activeDemo === 'voice' && (
            <div className="space-y-8">
              <Card variant="glass">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                    Búsqueda por Voz Avanzada
                  </h2>
                  
                  <div className="max-w-2xl mx-auto mb-8">
                    <p className="text-warm-camel mb-6">
                      Utiliza tu voz para buscar productos de manera natural. Nuestro sistema 
                      entiende español mexicano y genera sugerencias inteligentes.
                    </p>
                    
                    <AnimatedButton
                      variant="primary"
                      size="lg"
                      onClick={() => setShowVoiceSearch(true)}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                        </svg>
                      }
                    >
                      Probar Búsqueda por Voz
                    </AnimatedButton>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {[
                      {
                        title: 'Reconocimiento Natural',
                        description: 'Habla de manera natural, como si fuera una conversación',
                        examples: ['"Busco tenis Nike blancos"', '"Quiero zapatos formales negros"']
                      },
                      {
                        title: 'Sugerencias Inteligentes',
                        description: 'El sistema genera sugerencias basadas en lo que dijiste',
                        examples: ['"Nike Air Force 1"', '"Zapatos de vestir"']
                      }
                    ].map((feature, index) => (
                      <motion.div
                        key={feature.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <Card variant="default">
                          <CardContent className="p-6">
                            <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-3">
                              {feature.title}
                            </h3>
                            <p className="text-warm-camel text-sm mb-4">
                              {feature.description}
                            </p>
                            <div className="space-y-2">
                              {feature.examples.map((example, i) => (
                                <div key={i} className="text-xs bg-warm-camel/10 text-warm-camel px-3 py-2 rounded-lg">
                                  {example}
                                </div>
                              ))}
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Visual Search Demo */}
          {activeDemo === 'visual' && (
            <div className="space-y-8">
              <Card variant="glass">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                    Búsqueda Visual con IA
                  </h2>

                  <div className="max-w-2xl mx-auto mb-8">
                    <p className="text-warm-camel mb-6">
                      Sube una foto de zapatos y nuestro sistema de IA analizará la imagen
                      para encontrar productos similares en nuestro catálogo.
                    </p>

                    <AnimatedButton
                      variant="primary"
                      size="lg"
                      onClick={() => setShowVisualSearch(true)}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                        </svg>
                      }
                    >
                      Probar Búsqueda Visual
                    </AnimatedButton>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      {
                        title: 'Análisis de IA',
                        description: 'Detecta tipo, color, estilo y marca automáticamente',
                        icon: '🤖'
                      },
                      {
                        title: 'Productos Similares',
                        description: 'Encuentra productos con características similares',
                        icon: '🔍'
                      },
                      {
                        title: 'Porcentaje de Similitud',
                        description: 'Muestra qué tan similar es cada producto',
                        icon: '📊'
                      }
                    ].map((feature, index) => (
                      <motion.div
                        key={feature.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <Card variant="default">
                          <CardContent className="p-6 text-center">
                            <div className="text-4xl mb-4">{feature.icon}</div>
                            <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                              {feature.title}
                            </h3>
                            <p className="text-warm-camel text-sm">
                              {feature.description}
                            </p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* AR Try-On Demo */}
          {activeDemo === 'ar' && (
            <div className="space-y-8">
              <Card variant="glass">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                    Realidad Aumentada
                  </h2>

                  <div className="max-w-2xl mx-auto mb-8">
                    <p className="text-warm-camel mb-6">
                      Experimenta cómo te quedan los zapatos antes de comprarlos usando
                      realidad aumentada directamente desde tu navegador.
                    </p>

                    <AnimatedButton
                      variant="primary"
                      size="lg"
                      onClick={() => setShowARTryOn(true)}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 13a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                      }
                    >
                      Probar Realidad Aumentada
                    </AnimatedButton>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      {
                        title: 'Prueba Virtual',
                        description: 'Ve cómo te quedan los zapatos en tiempo real',
                        icon: '👟'
                      },
                      {
                        title: 'Captura y Comparte',
                        description: 'Toma fotos AR y compártelas en redes sociales',
                        icon: '📱'
                      },
                      {
                        title: 'Sin Instalación',
                        description: 'Funciona directamente en tu navegador web',
                        icon: '🌐'
                      }
                    ].map((feature, index) => (
                      <motion.div
                        key={feature.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <Card variant="default">
                          <CardContent className="p-6 text-center">
                            <div className="text-4xl mb-4">{feature.icon}</div>
                            <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                              {feature.title}
                            </h3>
                            <p className="text-warm-camel text-sm">
                              {feature.description}
                            </p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Smart Wishlist Demo */}
          {activeDemo === 'wishlist' && (
            <div className="space-y-8">
              <Card variant="glass">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                    Wishlist Inteligente
                  </h2>

                  <div className="max-w-2xl mx-auto mb-8">
                    <p className="text-warm-camel mb-6">
                      Tu wishlist analizada por IA para darte insights personalizados,
                      alertas de precio y recomendaciones inteligentes.
                    </p>

                    <AnimatedButton
                      variant="primary"
                      size="lg"
                      onClick={() => setShowSmartWishlist(true)}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      }
                    >
                      Ver Wishlist Inteligente
                    </AnimatedButton>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      {
                        title: 'Análisis de Estilo',
                        description: 'Entiende tus preferencias y patrones de compra',
                        icon: '🎨'
                      },
                      {
                        title: 'Alertas de Precio',
                        description: 'Notificaciones cuando bajan los precios',
                        icon: '🚨'
                      },
                      {
                        title: 'Insights Personalizados',
                        description: 'Recomendaciones basadas en tu wishlist',
                        icon: '💡'
                      }
                    ].map((feature, index) => (
                      <motion.div
                        key={feature.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <Card variant="default">
                          <CardContent className="p-6 text-center">
                            <div className="text-4xl mb-4">{feature.icon}</div>
                            <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                              {feature.title}
                            </h3>
                            <p className="text-warm-camel text-sm">
                              {feature.description}
                            </p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}

          {/* Social Shopping Demo */}
          {activeDemo === 'social' && (
            <div className="space-y-8">
              <Card variant="glass">
                <CardContent className="p-8 text-center">
                  <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                    Shopping Social
                  </h2>

                  <div className="max-w-2xl mx-auto mb-8">
                    <p className="text-warm-camel mb-6">
                      Conecta con otros sneakerheads, descubre tendencias,
                      comparte outfits y obtén recomendaciones de la comunidad.
                    </p>

                    <AnimatedButton
                      variant="primary"
                      size="lg"
                      onClick={() => setShowSocialShopping(true)}
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                      }
                    >
                      Explorar Comunidad
                    </AnimatedButton>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                    {[
                      {
                        title: 'Feed Social',
                        description: 'Ve reseñas, outfits y posts de la comunidad',
                        icon: '📱'
                      },
                      {
                        title: 'Tendencias',
                        description: 'Descubre qué productos están trending',
                        icon: '📈'
                      },
                      {
                        title: 'Gamificación',
                        description: 'Gana puntos, badges y sube de nivel',
                        icon: '🏆'
                      }
                    ].map((feature, index) => (
                      <motion.div
                        key={feature.title}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ delay: 0.3 + index * 0.1 }}
                      >
                        <Card variant="default">
                          <CardContent className="p-6 text-center">
                            <div className="text-4xl mb-4">{feature.icon}</div>
                            <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                              {feature.title}
                            </h3>
                            <p className="text-warm-camel text-sm">
                              {feature.description}
                            </p>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </motion.div>

        {/* Features Summary */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mt-16"
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6 text-center">
                Tecnologías de Vanguardia
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[
                  {
                    icon: "🧠",
                    title: "Machine Learning",
                    description: "Algoritmos que aprenden de tus preferencias"
                  },
                  {
                    icon: "🎯",
                    title: "Personalización",
                    description: "Experiencia única para cada usuario"
                  },
                  {
                    icon: "⚡",
                    title: "Tiempo Real",
                    description: "Respuestas instantáneas y precisas"
                  },
                  {
                    icon: "🔒",
                    title: "Privacidad",
                    description: "Tus datos están seguros y protegidos"
                  }
                ].map((tech, index) => (
                  <motion.div
                    key={tech.title}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 1 + index * 0.1 }}
                    className="text-center"
                  >
                    <div className="text-3xl mb-3">{tech.icon}</div>
                    <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                      {tech.title}
                    </h3>
                    <p className="text-warm-camel text-sm">
                      {tech.description}
                    </p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Modals */}
      {showVoiceSearch && (
        <VoiceSearch
          onSearch={handleSearch}
          onClose={() => setShowVoiceSearch(false)}
        />
      )}

      {showVisualSearch && (
        <VisualSearch
          onSearch={handleSearch}
          onClose={() => setShowVisualSearch(false)}
        />
      )}

      {showSizeRecommendation && (
        <SizeRecommendation
          product={products[0]} // Demo product
          onSizeSelect={(size) => console.log('Selected size:', size)}
          onClose={() => setShowSizeRecommendation(false)}
        />
      )}

      {showARTryOn && (
        <ARTryOn
          product={products[0]} // Demo product
          onClose={() => setShowARTryOn(false)}
        />
      )}

      {showSmartWishlist && (
        <SmartWishlist
          onClose={() => setShowSmartWishlist(false)}
        />
      )}

      {showSocialShopping && (
        <SocialShopping
          onClose={() => setShowSocialShopping(false)}
        />
      )}
    </div>
  )
}
