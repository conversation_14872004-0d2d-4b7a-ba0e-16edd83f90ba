'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'

// Mock reviews data
const mockReviews = [
  {
    id: 1,
    user: {
      id: 'user1',
      name: '<PERSON>',
      username: '@maria_sneaks',
      avatar: '👩🏻',
      verified: true,
      reviewCount: 23
    },
    rating: 5,
    title: 'Perfectos como siempre',
    content: 'Los Jordan 1 Chicago llegaron en perfectas condiciones. La calidad es increíble y el servicio de TWL es de otro nivel. Definitivamente volveré a comprar aquí.',
    images: ['/placeholder-review-1.jpg', '/placeholder-review-2.jpg'],
    helpful: 45,
    notHelpful: 2,
    verified: true,
    size: '9',
    fit: 'true-to-size',
    comfort: 5,
    quality: 5,
    style: 5,
    createdAt: '2024-01-20T10:30:00Z',
    likes: 23,
    replies: [
      {
        id: 'reply-1',
        user: { name: 'TWL Team', avatar: '🏪', official: true },
        content: '¡Gracias por tu reseña <PERSON>! Nos alegra saber que estás satisfecha con tu compra.',
        createdAt: '2024-01-20T14:30:00Z'
      }
    ]
  },
  {
    id: 2,
    user: {
      id: 'user2',
      name: 'Carlos Mendoza',
      username: '@carlos_style',
      avatar: '👨🏽',
      verified: false,
      reviewCount: 8
    },
    rating: 4,
    title: 'Muy buenos, pero...',
    content: 'Los Yeezy están geniales, la calidad es excelente. Solo que tardaron un poco más de lo esperado en llegar. Pero en general muy satisfecho con la compra.',
    images: [],
    helpful: 28,
    notHelpful: 5,
    verified: true,
    size: '10',
    fit: 'runs-small',
    comfort: 4,
    quality: 5,
    style: 5,
    createdAt: '2024-01-18T15:45:00Z',
    likes: 12,
    replies: []
  },
  {
    id: 3,
    user: {
      id: 'user3',
      name: 'Ana Rodríguez',
      username: '@ana_kicks',
      avatar: '👩🏻‍🦱',
      verified: true,
      reviewCount: 45
    },
    rating: 5,
    title: 'Increíbles Air Force 1',
    content: 'Estos Air Force 1 son perfectos para el día a día. Cómodos, versátiles y la calidad es excelente. El envío fue súper rápido.',
    images: ['/placeholder-review-3.jpg'],
    helpful: 67,
    notHelpful: 1,
    verified: true,
    size: '7.5',
    fit: 'true-to-size',
    comfort: 5,
    quality: 5,
    style: 4,
    createdAt: '2024-01-15T12:20:00Z',
    likes: 34,
    replies: []
  }
]

const fitOptions = {
  'runs-small': { label: 'Queda pequeño', color: 'text-red-500' },
  'true-to-size': { label: 'Talla correcta', color: 'text-green-500' },
  'runs-large': { label: 'Queda grande', color: 'text-orange-500' }
}

export default function ReviewsAndRatings({
  productId,
  className = '',
  showWriteReview = true,
  maxReviews = 10
}) {
  const [reviews, setReviews] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [sortBy, setSortBy] = useState('newest') // newest, oldest, highest, lowest, helpful
  const [filterBy, setFilterBy] = useState('all') // all, 5, 4, 3, 2, 1
  const [isWritingReview, setIsWritingReview] = useState(false)
  const [expandedReview, setExpandedReview] = useState(null)

  // Review stats
  const [reviewStats, setReviewStats] = useState({
    average: 4.6,
    total: 156,
    distribution: {
      5: 89,
      4: 45,
      3: 15,
      2: 5,
      1: 2
    }
  })

  useEffect(() => {
    // Simulate loading reviews
    const loadReviews = async () => {
      setIsLoading(true)
      await new Promise(resolve => setTimeout(resolve, 1000))

      let filteredReviews = [...mockReviews]

      // Apply filters
      if (filterBy !== 'all') {
        filteredReviews = filteredReviews.filter(review => review.rating === parseInt(filterBy))
      }

      // Apply sorting
      switch (sortBy) {
        case 'oldest':
          filteredReviews.sort((a, b) => new Date(a.createdAt) - new Date(b.createdAt))
          break
        case 'highest':
          filteredReviews.sort((a, b) => b.rating - a.rating)
          break
        case 'lowest':
          filteredReviews.sort((a, b) => a.rating - b.rating)
          break
        case 'helpful':
          filteredReviews.sort((a, b) => b.helpful - a.helpful)
          break
        default: // newest
          filteredReviews.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
      }

      setReviews(filteredReviews.slice(0, maxReviews))
      setIsLoading(false)
    }

    loadReviews()
  }, [sortBy, filterBy, maxReviews])

  const handleHelpful = (reviewId, isHelpful) => {
    setReviews(reviews.map(review =>
      review.id === reviewId
        ? {
            ...review,
            helpful: isHelpful ? review.helpful + 1 : review.helpful,
            notHelpful: !isHelpful ? review.notHelpful + 1 : review.notHelpful
          }
        : review
    ))
  }

  const handleLike = (reviewId) => {
    setReviews(reviews.map(review =>
      review.id === reviewId
        ? { ...review, likes: review.likes + 1 }
        : review
    ))
  }

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const reviewTime = new Date(timestamp)
    const diffInDays = Math.floor((now - reviewTime) / (1000 * 60 * 60 * 24))

    if (diffInDays === 0) return 'Hoy'
    if (diffInDays === 1) return 'Ayer'
    if (diffInDays < 30) return `Hace ${diffInDays} días`
    const diffInMonths = Math.floor(diffInDays / 30)
    return `Hace ${diffInMonths} ${diffInMonths === 1 ? 'mes' : 'meses'}`
  }

  const renderStars = (rating, size = 'sm') => {
    const sizeClass = size === 'lg' ? 'w-5 h-5' : 'w-4 h-4'
    return (
      <div className="flex items-center gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <svg
            key={star}
            className={`${sizeClass} ${star <= rating ? 'text-yellow-400 fill-current' : 'text-gray-300'}`}
            viewBox="0 0 20 20"
          >
            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
          </svg>
        ))}
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="bg-warm-camel/20 rounded h-8 w-48"></div>
          <div className="bg-warm-camel/20 rounded-lg h-32"></div>
          <div className="space-y-4">
            {[...Array(3)].map((_, index) => (
              <div key={index} className="bg-warm-camel/20 rounded-lg h-24"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-8 ${className}`}>

      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
            Reseñas y Calificaciones
          </h2>
          <p className="text-warm-camel text-sm">
            {reviewStats.total} reseñas verificadas
          </p>
        </div>

        {showWriteReview && (
          <AnimatedButton
            variant="primary"
            size="sm"
            onClick={() => setIsWritingReview(true)}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15.232 5.232l3.536 3.536m-2.036-5.036a2.5 2.5 0 113.536 3.536L6.5 21.036H3v-3.572L16.732 3.732z" />
              </svg>
            }
          >
            Escribir Reseña
          </AnimatedButton>
        )}
      </div>

      {/* Review Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
      >
        <Card variant="glass">
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">

              {/* Overall Rating */}
              <div className="text-center">
                <div className="text-4xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                  {reviewStats.average}
                </div>
                {renderStars(Math.round(reviewStats.average), 'lg')}
                <p className="text-warm-camel text-sm mt-2">
                  Basado en {reviewStats.total} reseñas
                </p>
              </div>

              {/* Rating Distribution */}
              <div className="space-y-2">
                {[5, 4, 3, 2, 1].map((rating) => (
                  <div key={rating} className="flex items-center gap-3">
                    <span className="text-sm text-forest-emerald dark:text-light-cloud-gray w-8">
                      {rating} ★
                    </span>
                    <div className="flex-1 bg-warm-camel/20 rounded-full h-2">
                      <div
                        className="bg-yellow-400 h-2 rounded-full transition-all duration-500"
                        style={{
                          width: `${(reviewStats.distribution[rating] / reviewStats.total) * 100}%`
                        }}
                      />
                    </div>
                    <span className="text-sm text-warm-camel w-8">
                      {reviewStats.distribution[rating]}
                    </span>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Filters and Sorting */}
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex gap-2">
          <select
            value={filterBy}
            onChange={(e) => setFilterBy(e.target.value)}
            className="px-3 py-2 rounded-lg border border-warm-camel/30 bg-white/10 backdrop-blur-sm text-forest-emerald dark:text-light-cloud-gray text-sm focus:outline-none focus:ring-2 focus:ring-rich-gold/50"
          >
            <option value="all">Todas las calificaciones</option>
            <option value="5">5 estrellas</option>
            <option value="4">4 estrellas</option>
            <option value="3">3 estrellas</option>
            <option value="2">2 estrellas</option>
            <option value="1">1 estrella</option>
          </select>
        </div>

        <div className="flex gap-2">
          <select
            value={sortBy}
            onChange={(e) => setSortBy(e.target.value)}
            className="px-3 py-2 rounded-lg border border-warm-camel/30 bg-white/10 backdrop-blur-sm text-forest-emerald dark:text-light-cloud-gray text-sm focus:outline-none focus:ring-2 focus:ring-rich-gold/50"
          >
            <option value="newest">Más recientes</option>
            <option value="oldest">Más antiguas</option>
            <option value="highest">Calificación más alta</option>
            <option value="lowest">Calificación más baja</option>
            <option value="helpful">Más útiles</option>
          </select>
        </div>
      </div>

      {/* Reviews List */}
      <div className="space-y-6">
        {reviews.map((review, index) => (
          <motion.div
            key={review.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card variant="default">
              <CardContent className="p-6">

                {/* Review Header */}
                <div className="flex items-start gap-4 mb-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-rich-gold to-warm-camel rounded-full flex items-center justify-center text-lg">
                    {review.user.avatar}
                  </div>

                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                        {review.user.name}
                      </h4>
                      {review.user.verified && (
                        <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                        </svg>
                      )}
                      {review.verified && (
                        <Badge variant="success" size="sm">Compra verificada</Badge>
                      )}
                    </div>

                    <div className="flex items-center gap-3 text-sm text-warm-camel">
                      <span>{review.user.username}</span>
                      <span>•</span>
                      <span>{formatTimeAgo(review.createdAt)}</span>
                      <span>•</span>
                      <span>{review.user.reviewCount} reseñas</span>
                    </div>
                  </div>

                  <div className="text-right">
                    {renderStars(review.rating)}
                    <p className="text-xs text-warm-camel mt-1">
                      Talla {review.size}
                    </p>
                  </div>
                </div>

                {/* Review Content */}
                <div className="mb-4">
                  <h5 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    {review.title}
                  </h5>
                  <p className="text-forest-emerald dark:text-light-cloud-gray text-sm leading-relaxed">
                    {review.content}
                  </p>
                </div>

                {/* Review Details */}
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4 p-4 bg-warm-camel/5 rounded-lg">
                  <div className="text-center">
                    <div className="text-xs text-warm-camel mb-1">Ajuste</div>
                    <div className={`text-sm font-medium ${fitOptions[review.fit].color}`}>
                      {fitOptions[review.fit].label}
                    </div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-warm-camel mb-1">Comodidad</div>
                    <div className="text-sm">{renderStars(review.comfort)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-warm-camel mb-1">Calidad</div>
                    <div className="text-sm">{renderStars(review.quality)}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-xs text-warm-camel mb-1">Estilo</div>
                    <div className="text-sm">{renderStars(review.style)}</div>
                  </div>
                </div>

                {/* Review Images */}
                {review.images.length > 0 && (
                  <div className="flex gap-2 mb-4">
                    {review.images.map((image, imgIndex) => (
                      <div key={imgIndex} className="w-16 h-16 bg-warm-camel/10 rounded-lg flex items-center justify-center">
                        <span className="text-sm">📷</span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Review Actions */}
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <button
                      onClick={() => handleHelpful(review.id, true)}
                      className="flex items-center gap-1 text-warm-camel hover:text-green-500 transition-colors"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 10h4.764a2 2 0 011.789 2.894l-3.5 7A2 2 0 0115.263 21h-4.017c-.163 0-.326-.02-.485-.06L7 20m7-10V5a2 2 0 00-2-2h-.095c-.5 0-.905.405-.905.905 0 .714-.211 1.412-.608 2.006L7 11v9m7-10h-2M7 20H5a2 2 0 01-2-2v-6a2 2 0 012-2h2.5" />
                      </svg>
                      <span className="text-xs">Útil ({review.helpful})</span>
                    </button>

                    <button
                      onClick={() => handleLike(review.id)}
                      className="flex items-center gap-1 text-warm-camel hover:text-red-500 transition-colors"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                      </svg>
                      <span className="text-xs">{review.likes}</span>
                    </button>
                  </div>

                  {review.replies.length > 0 && (
                    <button
                      onClick={() => setExpandedReview(expandedReview === review.id ? null : review.id)}
                      className="text-warm-camel hover:text-rich-gold transition-colors text-xs"
                    >
                      {expandedReview === review.id ? 'Ocultar' : 'Ver'} respuestas ({review.replies.length})
                    </button>
                  )}
                </div>

                {/* Replies */}
                <AnimatePresence>
                  {expandedReview === review.id && review.replies.length > 0 && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="mt-4 pl-4 border-l-2 border-warm-camel/20"
                    >
                      {review.replies.map((reply) => (
                        <div key={reply.id} className="flex items-start gap-3 py-3">
                          <div className="w-8 h-8 bg-gradient-to-br from-rich-gold to-warm-camel rounded-full flex items-center justify-center text-sm">
                            {reply.user.avatar}
                          </div>
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-1">
                              <span className="font-medium text-forest-emerald dark:text-light-cloud-gray text-sm">
                                {reply.user.name}
                              </span>
                              {reply.user.official && (
                                <Badge variant="primary" size="sm">Oficial</Badge>
                              )}
                            </div>
                            <p className="text-forest-emerald dark:text-light-cloud-gray text-sm">
                              {reply.content}
                            </p>
                            <p className="text-warm-camel text-xs mt-1">
                              {formatTimeAgo(reply.createdAt)}
                            </p>
                          </div>
                        </div>
                      ))}
                    </motion.div>
                  )}
                </AnimatePresence>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Load More */}
      {reviews.length >= maxReviews && (
        <div className="text-center">
          <AnimatedButton
            variant="secondary"
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            }
          >
            Ver Más Reseñas
          </AnimatedButton>
        </div>
      )}
    </div>
  )
}