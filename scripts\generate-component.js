#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// Get component name from command line arguments
const componentName = process.argv[2]

if (!componentName) {
  console.error('❌ Please provide a component name')
  console.log('Usage: npm run generate:component ComponentName')
  process.exit(1)
}

// Validate component name (PascalCase)
if (!/^[A-Z][a-zA-Z0-9]*$/.test(componentName)) {
  console.error('❌ Component name must be in PascalCase (e.g., MyComponent)')
  process.exit(1)
}

// Component template
const componentTemplate = `'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'

export default function ${componentName}() {
  const [isLoading, setIsLoading] = useState(false)

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="p-4"
    >
      <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
        ${componentName}
      </h2>
      
      <div className="space-y-4">
        <p className="text-warm-camel">
          This is the ${componentName} component.
        </p>
        
        {/* Add your component content here */}
      </div>
    </motion.div>
  )
}
`

// Test template
const testTemplate = `import { render, screen } from '@testing-library/react'
import ${componentName} from './${componentName}'

describe('${componentName}', () => {
  it('renders without crashing', () => {
    render(<${componentName} />)
    expect(screen.getByText('${componentName}')).toBeInTheDocument()
  })

  it('displays the component title', () => {
    render(<${componentName} />)
    expect(screen.getByRole('heading', { level: 2 })).toHaveTextContent('${componentName}')
  })
})
`

// Story template for Storybook
const storyTemplate = `import type { Meta, StoryObj } from '@storybook/react'
import ${componentName} from './${componentName}'

const meta: Meta<typeof ${componentName}> = {
  title: 'Components/${componentName}',
  component: ${componentName},
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}

export const Loading: Story = {
  args: {},
}
`

// Determine component directory based on type
const componentTypes = {
  ui: 'components/ui',
  features: 'components/features',
  layout: 'components/layout',
  account: 'components/account'
}

// Ask user for component type
console.log('🎨 Select component type:')
console.log('1. UI Component (components/ui)')
console.log('2. Feature Component (components/features)')
console.log('3. Layout Component (components/layout)')
console.log('4. Account Component (components/account)')

const readline = require('readline')
const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
})

rl.question('Enter choice (1-4): ', (choice) => {
  let componentDir
  
  switch (choice) {
    case '1':
      componentDir = componentTypes.ui
      break
    case '2':
      componentDir = componentTypes.features
      break
    case '3':
      componentDir = componentTypes.layout
      break
    case '4':
      componentDir = componentTypes.account
      break
    default:
      componentDir = componentTypes.ui
      console.log('⚠️  Invalid choice, defaulting to UI component')
  }

  const componentPath = path.join(componentDir, `${componentName}.jsx`)
  const testPath = path.join(componentDir, `${componentName}.test.jsx`)
  const storyPath = path.join(componentDir, `${componentName}.stories.js`)

  // Create component file
  try {
    // Ensure directory exists
    fs.mkdirSync(path.dirname(componentPath), { recursive: true })
    
    // Check if component already exists
    if (fs.existsSync(componentPath)) {
      console.error(`❌ Component ${componentName} already exists at ${componentPath}`)
      rl.close()
      return
    }

    // Write component file
    fs.writeFileSync(componentPath, componentTemplate)
    console.log(`✅ Created component: ${componentPath}`)

    // Write test file
    fs.writeFileSync(testPath, testTemplate)
    console.log(`✅ Created test: ${testPath}`)

    // Write story file
    fs.writeFileSync(storyPath, storyTemplate)
    console.log(`✅ Created story: ${storyPath}`)

    console.log(`\n🎉 Component ${componentName} created successfully!`)
    console.log(`\n📝 Next steps:`)
    console.log(`1. Import and use your component: import ${componentName} from '@/${componentPath.replace('.jsx', '')}'`)
    console.log(`2. Run tests: npm test ${componentName}`)
    console.log(`3. View in Storybook: npm run storybook`)

  } catch (error) {
    console.error(`❌ Error creating component: ${error.message}`)
  }

  rl.close()
})
