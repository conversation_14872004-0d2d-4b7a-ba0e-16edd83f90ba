#!/usr/bin/env node

/**
 * MULTI-VARIANT PRODUCT SCANNER
 * Handles products with multiple colors, models, and variants under same SKU
 * Integrates videos and description files
 */

const fs = require('fs')
const path = require('path')

function scanMultiVariantProducts() {
  const cytteBasePath = path.join(process.cwd(), '--materials', 'shoes', '2. CYTTE')
  
  if (!fs.existsSync(cytteBasePath)) {
    console.log('❌ CYTTE folder not found at:', cytteBasePath)
    return []
  }
  
  console.log('🔍 SCANNING MULTI-VARIANT PRODUCTS...')
  console.log('Base path:', cytteBasePath)
  
  const multiVariantProducts = []
  let foldersScanned = 0
  let variantsFound = 0
  
  // Recursive function to scan all folders
  function scanFolderRecursively(folderPath, pathComponents = []) {
    foldersScanned++
    
    if (foldersScanned % 50 === 0) {
      console.log(`📁 Scanned ${foldersScanned} folders, found ${variantsFound} variants...`)
    }
    
    try {
      const items = fs.readdirSync(folderPath, { withFileTypes: true })
      
      // Check if this folder contains product files (images, videos, description)
      const imageFiles = items.filter(item => 
        item.isFile() && /\.(webp|jpg|jpeg|png)$/i.test(item.name)
      ).map(item => item.name)
      
      const videoFiles = items.filter(item => 
        item.isFile() && /\.(mp4|mov|avi|webm)$/i.test(item.name)
      ).map(item => item.name)
      
      const descriptionFiles = items.filter(item => 
        item.isFile() && item.name.toLowerCase().includes('description')
      ).map(item => item.name)
      
      if (imageFiles.length > 0 || videoFiles.length > 0 || descriptionFiles.length > 0) {
        // This is a product variant folder!
        const variant = createVariantFromPath(folderPath, pathComponents, {
          images: imageFiles,
          videos: videoFiles,
          descriptions: descriptionFiles
        })
        
        if (variant) {
          // Check if we already have a product with this base SKU
          const existingProductIndex = multiVariantProducts.findIndex(p => p.baseSku === variant.baseSku)
          
          if (existingProductIndex >= 0) {
            // Add this as a variant to existing product
            multiVariantProducts[existingProductIndex].variants.push(variant)
          } else {
            // Create new multi-variant product
            const multiVariantProduct = createMultiVariantProduct(variant)
            multiVariantProducts.push(multiVariantProduct)
          }
          
          variantsFound++
        }
        return // Don't scan deeper if we found product files
      }
      
      // Continue scanning subdirectories
      const subFolders = items.filter(item => item.isDirectory())
      
      subFolders.forEach(subFolder => {
        const subFolderPath = path.join(folderPath, subFolder.name)
        const newPathComponents = [...pathComponents, subFolder.name]
        scanFolderRecursively(subFolderPath, newPathComponents)
      })
      
    } catch (error) {
      console.log(`⚠️  Error scanning ${folderPath}:`, error.message)
    }
  }
  
  // Start recursive scan
  scanFolderRecursively(cytteBasePath, [])
  
  console.log(`\n📈 MULTI-VARIANT SCAN COMPLETE`)
  console.log(`Total folders scanned: ${foldersScanned}`)
  console.log(`Total variants found: ${variantsFound}`)
  console.log(`Multi-variant products: ${multiVariantProducts.length}`)
  
  return multiVariantProducts
}

function createVariantFromPath(folderPath, pathComponents, files) {
  try {
    const relativePath = folderPath.replace(process.cwd(), '').replace(/\\/g, '/')
    const pathParts = pathComponents.filter(part => part.trim() !== '')
    
    if (pathParts.length < 2) {
      return null
    }
    
    const lastFolder = pathParts[pathParts.length - 1]
    
    // Parse SKU and variant name from folder name
    const skuMatch = lastFolder.match(/^([A-Z0-9\-]+)(?:\s*--\s*(.+))?$/i)
    const fullSku = skuMatch ? skuMatch[1] : generateSKU()
    const variantName = skuMatch ? (skuMatch[2] || 'Standard') : 'Standard'
    
    // Extract base SKU (remove any suffix after last dash)
    const baseSku = fullSku.split('-').slice(0, -1).join('-') || fullSku
    
    // Read description if available
    let description = ''
    if (files.descriptions.length > 0) {
      try {
        const descPath = path.join(folderPath, files.descriptions[0])
        description = fs.readFileSync(descPath, 'utf8').trim()
      } catch (error) {
        console.log(`⚠️  Could not read description from ${files.descriptions[0]}`)
      }
    }
    
    // Determine hierarchy from path
    const style = pathParts[0] || '1. SNEAKERS'
    const brand = pathParts[1] || 'Unknown'
    const gender = determineGender(pathParts)
    const modelFamily = determineModelFamily(pathParts)
    const collaborator = detectCollaborationFromPath(pathParts, variantName)
    
    // Generate image and video URLs
    const imageUrls = files.images.map(img => `${relativePath}/${img}`)
    const videoUrls = files.videos.map(vid => `${relativePath}/${vid}`)
    
    // Detect color from variant name or images
    const colors = detectColors(variantName, files.images)
    
    return {
      fullSku,
      baseSku,
      variantName,
      variantId: generateVariantId(baseSku, variantName),
      description,
      colors,
      images: imageUrls,
      videos: videoUrls,
      imageCount: files.images.length,
      videoCount: files.videos.length,
      hasDescription: files.descriptions.length > 0,
      
      // Hierarchy info
      style,
      brand,
      gender,
      modelFamily,
      collaborator,
      isCollaboration: !!collaborator,
      
      // Path info
      folderPath: relativePath,
      pathComponents: pathParts,
      
      // Metadata
      createdAt: new Date().toISOString()
    }
    
  } catch (error) {
    console.log(`⚠️  Error creating variant from path ${folderPath}:`, error.message)
    return null
  }
}

function createMultiVariantProduct(firstVariant) {
  const styleInfo = mapStyle(firstVariant.style)
  const brandInfo = mapBrand(firstVariant.brand)
  const genderInfo = mapGender(firstVariant.gender)
  const modelInfo = mapModelFamily(firstVariant.modelFamily)
  
  // Generate main product info from first variant
  const productId = `${styleInfo.id}-${brandInfo.id}-${genderInfo.id}-${modelInfo.id}-${firstVariant.baseSku.toLowerCase()}`
  
  // Aggregate all colors from variants
  const allColors = [...new Set(firstVariant.colors)]
  
  // Count total media
  const totalImages = firstVariant.imageCount
  const totalVideos = firstVariant.videoCount
  
  return {
    id: productId,
    baseSku: firstVariant.baseSku,
    name: generateProductName(brandInfo.name, modelInfo.display, firstVariant.baseSku, firstVariant.collaborator),
    description: firstVariant.description || generateProductDescription(brandInfo.name, modelInfo.display, firstVariant.baseSku, firstVariant.isCollaboration),
    
    // Hierarchy (6-level CYTTE structure)
    style: styleInfo.id,
    styleDisplay: styleInfo.display,
    styleCytteId: firstVariant.style,
    
    brand: brandInfo.name,
    brandId: brandInfo.id,
    brandType: brandInfo.type,
    brandCytteId: firstVariant.brand,
    
    gender: genderInfo.id.toUpperCase(),
    genderDisplay: genderInfo.display,
    genderPath: genderInfo.path,
    
    modelFamily: modelInfo.id,
    modelFamilyDisplay: modelInfo.display,
    modelVariant: modelInfo.id,
    modelCytteId: firstVariant.modelFamily,
    
    isCollaboration: firstVariant.isCollaboration,
    collaborationType: firstVariant.isCollaboration ? 'brand-x-brand' : null,
    collaborator: firstVariant.collaborator,
    collaboratorDisplay: firstVariant.collaborator ? `${firstVariant.collaborator} x ${brandInfo.name}` : null,
    
    // Multi-variant specific data
    isMultiVariant: true,
    variantCount: 1, // Will be updated as variants are added
    variants: [firstVariant],
    
    // Aggregated data
    colors: allColors,
    totalImages,
    totalVideos,
    hasVideos: totalVideos > 0,
    
    // Default main product data (from first variant)
    images: firstVariant.images.slice(0, 4), // Use first 4 images as main product images
    videos: firstVariant.videos,
    
    // Product details
    type: getProductType(styleInfo.id),
    subType: getProductSubType(modelInfo.display),
    
    // Pricing
    price: generatePrice(brandInfo.type, firstVariant.isCollaboration),
    originalPrice: null,
    currency: 'MXN',
    
    // Product classification
    sizes: generateSizes(genderInfo.id),
    materials: generateMaterials(brandInfo.type),
    
    isLimited: firstVariant.isCollaboration || brandInfo.type === 'luxury',
    isExclusive: brandInfo.type === 'luxury',
    isVip: brandInfo.type === 'luxury' || firstVariant.isCollaboration,
    isNew: Math.random() > 0.7,
    
    // Inventory
    stock: Math.floor(Math.random() * 20) + 1,
    availability: 'in-stock',
    releaseDate: generateReleaseDate(),
    rating: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10,
    reviews: Math.floor(Math.random() * 500) + 10,
    
    // SEO
    tags: generateTags(brandInfo.name, modelInfo.display, firstVariant.collaborator, styleInfo.id),
    keywords: generateKeywords(brandInfo.name, modelInfo.display, firstVariant.baseSku),
    searchTerms: generateSearchTerms(brandInfo.name, modelInfo.display, firstVariant.collaborator),
    
    // Metadata
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
}

// Helper functions
function determineGender(pathParts) {
  for (const part of pathParts) {
    if (part.includes('WOMEN') || part.includes('2. WOMEN')) return 'WOMEN'
    if (part.includes('MEN') || part.includes('3. MEN') || part.includes('1. MEN')) return 'MEN'
    if (part.includes('MIXTE') || part.includes('1. MIXTE')) return 'MIXTE'
  }
  return 'MIXTE'
}

function determineModelFamily(pathParts) {
  const modelKeywords = {
    'JORDAN': 'jordan',
    'AIR FORCE': 'air-force',
    'DUNK': 'dunk',
    'BLAZER': 'blazer',
    'CORTEZ': 'cortez',
    'AIR MAX': 'air-max',
    'ACE': 'ace',
    'SCREENER': 'screener',
    'RYTHON': 'rython',
    'HORSEBIT': 'horsebit'
  }
  
  for (const part of pathParts) {
    const upperPart = part.toUpperCase()
    for (const [keyword, family] of Object.entries(modelKeywords)) {
      if (upperPart.includes(keyword)) {
        return family
      }
    }
  }
  
  return 'standard'
}

function detectCollaborationFromPath(pathParts, variantName) {
  const collabKeywords = {
    'OFF': 'Off-White',
    'WHITE': 'Off-White',
    'SACAI': 'Sacai',
    'TRAVIS': 'Travis Scott',
    'VIRGIL': 'Virgil Abloh',
    'UNION': 'Union LA',
    'BODEGA': 'Bodega',
    'CLOT': 'CLOT',
    'DIOR': 'Dior',
    'GORETEX': 'Gore-Tex',
    'THOMAS CAMPBELL': 'Thomas Campbell'
  }
  
  const searchText = (pathParts.join(' ') + ' ' + variantName).toUpperCase()
  
  for (const [keyword, collaborator] of Object.entries(collabKeywords)) {
    if (searchText.includes(keyword)) {
      return collaborator
    }
  }
  
  return null
}

function detectColors(variantName, imageFiles) {
  const colorKeywords = {
    'BLACK': 'Negro',
    'WHITE': 'Blanco',
    'RED': 'Rojo',
    'BLUE': 'Azul',
    'GREEN': 'Verde',
    'YELLOW': 'Amarillo',
    'PINK': 'Rosa',
    'PURPLE': 'Morado',
    'ORANGE': 'Naranja',
    'BROWN': 'Marrón',
    'GRAY': 'Gris',
    'GREY': 'Gris',
    'GOLD': 'Dorado',
    'SILVER': 'Plateado',
    'SPACE': 'Espacial',
    'JEANS': 'Denim',
    'VERNIS': 'Charol',
    'GOLDIE': 'Dorado'
  }
  
  const colors = []
  const searchText = variantName.toUpperCase()
  
  for (const [keyword, color] of Object.entries(colorKeywords)) {
    if (searchText.includes(keyword)) {
      colors.push(color)
    }
  }
  
  // If no colors detected, try to infer from variant name
  if (colors.length === 0) {
    if (variantName.toLowerCase().includes('black')) colors.push('Negro')
    else if (variantName.toLowerCase().includes('white')) colors.push('Blanco')
    else colors.push('Multicolor')
  }
  
  return colors
}

function generateVariantId(baseSku, variantName) {
  return `${baseSku}-${variantName.toLowerCase().replace(/[^a-z0-9]/g, '-')}`
}

function generateSKU() {
  const letters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ'
  const numbers = '0123456789'
  
  let sku = ''
  for (let i = 0; i < 3; i++) {
    sku += letters[Math.floor(Math.random() * letters.length)]
  }
  for (let i = 0; i < 3; i++) {
    sku += numbers[Math.floor(Math.random() * numbers.length)]
  }
  sku += '-'
  for (let i = 0; i < 3; i++) {
    sku += letters[Math.floor(Math.random() * letters.length)]
  }
  
  return sku
}

module.exports = { 
  scanMultiVariantProducts, 
  createVariantFromPath, 
  createMultiVariantProduct 
}
