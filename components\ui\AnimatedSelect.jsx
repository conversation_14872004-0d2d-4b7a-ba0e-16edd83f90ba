'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence, useSpring } from 'framer-motion'

export default function AnimatedSelect({
  options = [],
  value = '',
  onChange,
  placeholder = 'Seleccionar...',
  label = '',
  error = '',
  success = false,
  disabled = false,
  required = false,
  className = '',
  ...props
}) {
  const [isOpen, setIsOpen] = useState(false)
  const [isFocused, setIsFocused] = useState(false)
  const [selectedOption, setSelectedOption] = useState(null)
  const selectRef = useRef(null)
  const dropdownRef = useRef(null)
  
  // Spring animations
  const springConfig = { stiffness: 300, damping: 30 }
  const scale = useSpring(1, springConfig)
  const borderOpacity = useSpring(0.3, springConfig)
  const labelY = useSpring(0, springConfig)
  const labelScale = useSpring(1, springConfig)
  const chevronRotation = useSpring(0, springConfig)

  useEffect(() => {
    const option = options.find(opt => opt.value === value)
    setSelectedOption(option)
  }, [value, options])

  useEffect(() => {
    if (isFocused || selectedOption) {
      labelY.set(-24)
      labelScale.set(0.85)
    } else {
      labelY.set(0)
      labelScale.set(1)
    }
  }, [isFocused, selectedOption, labelY, labelScale])

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (selectRef.current && !selectRef.current.contains(event.target)) {
        setIsOpen(false)
        setIsFocused(false)
        borderOpacity.set(0.3)
        chevronRotation.set(0)
        scale.set(1)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [borderOpacity, chevronRotation, scale])

  const handleToggle = () => {
    if (disabled) return
    
    const newIsOpen = !isOpen
    setIsOpen(newIsOpen)
    setIsFocused(newIsOpen)
    
    if (newIsOpen) {
      scale.set(1.02)
      borderOpacity.set(1)
      chevronRotation.set(180)
    } else {
      scale.set(1)
      borderOpacity.set(0.3)
      chevronRotation.set(0)
    }
  }

  const handleOptionSelect = (option) => {
    setSelectedOption(option)
    setIsOpen(false)
    setIsFocused(false)
    
    scale.set(0.98)
    setTimeout(() => scale.set(1), 100)
    
    borderOpacity.set(0.3)
    chevronRotation.set(0)
    
    onChange?.(option.value)
  }

  const getStateColors = () => {
    if (error) {
      return {
        border: 'border-warm-camel',
        focus: 'border-warm-camel ring-warm-camel/20',
        label: 'text-warm-camel',
        bg: 'bg-warm-camel/5'
      }
    }
    if (success) {
      return {
        border: 'border-forest-emerald',
        focus: 'border-forest-emerald ring-forest-emerald/20',
        label: 'text-forest-emerald',
        bg: 'bg-forest-emerald/5'
      }
    }
    return {
      border: 'border-soft-steel-gray dark:border-warm-camel/30',
      focus: 'border-rich-gold ring-rich-gold/20',
      label: 'text-warm-camel',
      bg: 'bg-light-cloud-gray/50 dark:bg-deep-pine/50'
    }
  }

  const stateColors = getStateColors()

  return (
    <div className={`relative ${className}`} ref={selectRef}>
      {/* Select Container */}
      <motion.div
        style={{ scale }}
        className="relative"
      >
        {/* Background with breathing effect */}
        <motion.div
          className={`absolute inset-0 rounded-lg ${stateColors.bg} transition-colors duration-300`}
          animate={{
            scale: isFocused ? [1, 1.01, 1] : 1
          }}
          transition={{
            duration: 2,
            repeat: isFocused ? Infinity : 0,
            ease: "easeInOut"
          }}
        />

        {/* Select Button */}
        <motion.button
          type="button"
          onClick={handleToggle}
          disabled={disabled}
          className={`
            w-full px-4 py-3 
            bg-transparent
            border-2 ${stateColors.border}
            rounded-lg
            text-left
            text-forest-emerald dark:text-light-cloud-gray
            transition-all duration-300
            focus:outline-none focus:ring-2 ${stateColors.focus}
            disabled:opacity-50 disabled:cursor-not-allowed
            relative z-10
            flex items-center justify-between
          `}
          whileTap={{ scale: 0.98 }}
        >
          {/* Selected Value */}
          <span className={selectedOption ? '' : 'text-warm-camel/60'}>
            {selectedOption ? selectedOption.label : placeholder}
          </span>
          
          {/* Chevron Icon */}
          <motion.div
            style={{ rotate: chevronRotation }}
            className="text-warm-camel"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </motion.div>
        </motion.button>

        {/* Floating Label */}
        {label && (
          <motion.label
            style={{
              y: labelY,
              scale: labelScale
            }}
            className={`
              absolute left-4 pointer-events-none
              ${stateColors.label}
              font-medium
              transition-colors duration-300
              origin-left
              z-20
            `}
          >
            {label}
            {required && (
              <motion.span
                className="text-warm-camel ml-1"
                animate={{
                  scale: isFocused ? [1, 1.2, 1] : 1
                }}
                transition={{
                  duration: 0.5,
                  repeat: isFocused ? Infinity : 0,
                  repeatDelay: 2
                }}
              >
                *
              </motion.span>
            )}
          </motion.label>
        )}

        {/* Success/Error Icons */}
        <motion.div
          className="absolute right-12 top-1/2 transform -translate-y-1/2"
          initial={{ scale: 0, rotate: -180 }}
          animate={{
            scale: (success || error) ? 1 : 0,
            rotate: (success || error) ? 0 : -180
          }}
          transition={{ type: "spring", stiffness: 300, damping: 30 }}
        >
          {success && (
            <div className="text-forest-emerald">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
            </div>
          )}
          {error && (
            <div className="text-warm-camel">
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
          )}
        </motion.div>

        {/* Animated Border Glow */}
        <motion.div
          className="absolute inset-0 rounded-lg border-2 border-rich-gold pointer-events-none"
          style={{
            opacity: borderOpacity
          }}
          animate={{
            boxShadow: isFocused 
              ? [
                  '0 0 0 0 rgba(255, 215, 0, 0)',
                  '0 0 0 4px rgba(255, 215, 0, 0.1)',
                  '0 0 0 0 rgba(255, 215, 0, 0)'
                ]
              : '0 0 0 0 rgba(255, 215, 0, 0)'
          }}
          transition={{
            duration: 2,
            repeat: isFocused ? Infinity : 0,
            ease: "easeInOut"
          }}
        />
      </motion.div>

      {/* Dropdown Options */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            ref={dropdownRef}
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.2, ease: "easeOut" }}
            className="absolute top-full left-0 right-0 mt-2 z-50"
          >
            <motion.div
              className="glass-card rounded-lg border border-warm-camel/20 shadow-xl max-h-60 overflow-y-auto"
              initial={{ backdropFilter: "blur(0px)" }}
              animate={{ backdropFilter: "blur(12px)" }}
            >
              {options.map((option, index) => (
                <motion.button
                  key={option.value}
                  type="button"
                  onClick={() => handleOptionSelect(option)}
                  className={`
                    w-full px-4 py-3 text-left
                    text-forest-emerald dark:text-light-cloud-gray
                    hover:bg-rich-gold/10
                    transition-colors duration-200
                    first:rounded-t-lg last:rounded-b-lg
                    ${selectedOption?.value === option.value ? 'bg-rich-gold/20' : ''}
                  `}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.05 }}
                  whileHover={{ 
                    x: 4,
                    backgroundColor: "rgba(255, 215, 0, 0.1)"
                  }}
                  whileTap={{ scale: 0.98 }}
                >
                  <div className="flex items-center justify-between">
                    <span>{option.label}</span>
                    {selectedOption?.value === option.value && (
                      <motion.div
                        initial={{ scale: 0, rotate: -180 }}
                        animate={{ scale: 1, rotate: 0 }}
                        className="text-rich-gold"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                        </svg>
                      </motion.div>
                    )}
                  </div>
                  
                  {option.description && (
                    <div className="text-sm text-warm-camel mt-1">
                      {option.description}
                    </div>
                  )}
                </motion.button>
              ))}
              
              {options.length === 0 && (
                <div className="px-4 py-3 text-warm-camel text-center">
                  No hay opciones disponibles
                </div>
              )}
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Message */}
      <motion.div
        initial={{ opacity: 0, y: -10, height: 0 }}
        animate={{
          opacity: error ? 1 : 0,
          y: error ? 0 : -10,
          height: error ? 'auto' : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        {error && (
          <motion.p
            className="text-sm text-warm-camel mt-2 flex items-center gap-2"
            initial={{ x: -10 }}
            animate={{ x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <motion.span
              animate={{
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 0.5,
                repeat: Infinity,
                repeatDelay: 2
              }}
            >
              ⚠️
            </motion.span>
            {error}
          </motion.p>
        )}
      </motion.div>

      {/* Success Message */}
      <motion.div
        initial={{ opacity: 0, y: -10, height: 0 }}
        animate={{
          opacity: success && !error ? 1 : 0,
          y: success && !error ? 0 : -10,
          height: success && !error ? 'auto' : 0
        }}
        transition={{ duration: 0.3 }}
        className="overflow-hidden"
      >
        {success && !error && (
          <motion.p
            className="text-sm text-forest-emerald mt-2 flex items-center gap-2"
            initial={{ x: -10 }}
            animate={{ x: 0 }}
            transition={{ delay: 0.1 }}
          >
            <motion.span
              animate={{
                rotate: [0, 360]
              }}
              transition={{
                duration: 1,
                ease: "easeInOut"
              }}
            >
              ✅
            </motion.span>
            Selección válida
          </motion.p>
        )}
      </motion.div>
    </div>
  )
}
