#!/usr/bin/env node

/**
 * TWL Product Database Population Script
 * Populates Supabase database with real products from CYTTE supplier
 * Converts our indexed product data to database format
 */

const fs = require('fs').promises;
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

class ProductPopulator {
  constructor() {
    this.processedCount = 0;
    this.errorCount = 0;
    this.skippedCount = 0;
    this.brandMap = new Map();
    this.categoryMap = new Map();
    this.supplierMap = new Map();
  }

  async loadMappings() {
    console.log('📋 Loading brand, category, and supplier mappings...');

    // Load brands
    const { data: brands, error: brandsError } = await supabase
      .from('brands')
      .select('id, name, slug');

    if (brandsError) throw brandsError;

    brands.forEach(brand => {
      this.brandMap.set(brand.name.toLowerCase(), brand.id);
      this.brandMap.set(brand.slug, brand.id);
    });

    // Load categories
    const { data: categories, error: categoriesError } = await supabase
      .from('categories')
      .select('id, name, slug');

    if (categoriesError) throw categoriesError;

    categories.forEach(category => {
      this.categoryMap.set(category.name.toLowerCase(), category.id);
      this.categoryMap.set(category.slug, category.id);
    });

    // Load suppliers
    const { data: suppliers, error: suppliersError } = await supabase
      .from('suppliers')
      .select('id, name, code');

    if (suppliersError) throw suppliersError;

    suppliers.forEach(supplier => {
      this.supplierMap.set(supplier.code, supplier.id);
      this.supplierMap.set(supplier.name.toLowerCase(), supplier.id);
    });

    console.log(`✅ Loaded ${brands.length} brands, ${categories.length} categories, ${suppliers.length} suppliers`);
  }

  getBrandId(brandName) {
    const normalized = brandName.toLowerCase();
    
    // Direct mapping
    if (this.brandMap.has(normalized)) {
      return this.brandMap.get(normalized);
    }

    // Fuzzy matching for common variations
    const brandMappings = {
      'lv': 'louis-vuitton',
      'louis vuitton': 'louis-vuitton',
      'nb': 'new-balance',
      'new balance': 'new-balance',
      'golden goose': 'golden-goose',
      'bottega veneta': 'bottega-veneta',
      'miu miu': 'miu-miu',
      'off white': 'off-white',
      'off-white': 'off-white',
      'fear of god': 'fear-of-god',
      'stone island': 'stone-island',
      'a bathing ape': 'bape',
      'bape': 'bape'
    };

    const mappedBrand = brandMappings[normalized];
    if (mappedBrand && this.brandMap.has(mappedBrand)) {
      return this.brandMap.get(mappedBrand);
    }

    return null;
  }

  getCategoryId(categoryName) {
    const normalized = categoryName.toLowerCase();
    
    // Direct mapping
    if (this.categoryMap.has(normalized)) {
      return this.categoryMap.get(normalized);
    }

    // Category mappings
    const categoryMappings = {
      'sneaker': 'sneakers',
      'sneakers': 'sneakers',
      'sandal': 'sandals',
      'sandalias': 'sandals',
      'slides': 'sandals',
      'formal': 'formal-shoes',
      'dress': 'formal-shoes',
      'boot': 'boots',
      'botas': 'boots',
      'casual': 'casual-shoes'
    };

    const mappedCategory = categoryMappings[normalized];
    if (mappedCategory && this.categoryMap.has(mappedCategory)) {
      return this.categoryMap.get(mappedCategory);
    }

    // Default to sneakers if no match
    return this.categoryMap.get('sneakers');
  }

  generateSlug(name) {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9\s-]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  }

  estimatePrice(brand, category) {
    const brandPricing = {
      'gucci': { min: 8000, max: 25000 },
      'louis-vuitton': { min: 12000, max: 35000 },
      'dior': { min: 15000, max: 40000 },
      'balenciaga': { min: 10000, max: 30000 },
      'prada': { min: 8000, max: 22000 },
      'bottega-veneta': { min: 12000, max: 28000 },
      'valentino': { min: 9000, max: 25000 },
      'golden-goose': { min: 7000, max: 18000 },
      'off-white': { min: 6000, max: 15000 },
      'nike': { min: 2000, max: 8000 },
      'jordan': { min: 3000, max: 12000 },
      'adidas': { min: 1800, max: 7000 },
      'new-balance': { min: 1500, max: 5000 }
    };

    const brandKey = brand.toLowerCase().replace(/\s+/g, '-');
    const pricing = brandPricing[brandKey] || { min: 2000, max: 8000 };
    
    // Add category multiplier
    const categoryMultipliers = {
      'formal-shoes': 1.3,
      'boots': 1.2,
      'sneakers': 1.0,
      'sandals': 0.8,
      'casual-shoes': 0.9
    };

    const multiplier = categoryMultipliers[category] || 1.0;
    const basePrice = Math.floor(Math.random() * (pricing.max - pricing.min) + pricing.min);
    
    return Math.floor(basePrice * multiplier);
  }

  async processProductsFromIndex() {
    console.log('📦 Processing products from CYTTE index...');

    try {
      // Read the product index file
      const indexPath = path.join(process.cwd(), 'output', 'cytte-product-index.json');
      const indexData = await fs.readFile(indexPath, 'utf8');
      const productIndex = JSON.parse(indexData);

      console.log(`📊 Found ${productIndex.products.length} products in index`);

      // Process products in batches
      const batchSize = 50;
      const batches = [];
      
      for (let i = 0; i < productIndex.products.length; i += batchSize) {
        batches.push(productIndex.products.slice(i, i + batchSize));
      }

      console.log(`🔄 Processing ${batches.length} batches of ${batchSize} products each...`);

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        const batch = batches[batchIndex];
        console.log(`\n📦 Processing batch ${batchIndex + 1}/${batches.length}...`);

        const productData = [];

        for (const indexProduct of batch) {
          try {
            const brandId = this.getBrandId(indexProduct.brand);
            const categoryId = this.getCategoryId(indexProduct.category);
            const supplierId = this.supplierMap.get('CYTTE');

            if (!brandId) {
              console.log(`⚠️ Skipping ${indexProduct.name} - brand not found: ${indexProduct.brand}`);
              this.skippedCount++;
              continue;
            }

            const price = this.estimatePrice(indexProduct.brand, indexProduct.category);
            const originalPrice = Math.floor(price * 1.2); // 20% markup

            const product = {
              sku: indexProduct.sku,
              supplier_id: supplierId,
              supplier_sku: indexProduct.supplier_sku,
              brand_id: brandId,
              category_id: categoryId,
              name: indexProduct.name,
              slug: this.generateSlug(indexProduct.name),
              description: indexProduct.description || `Luxury ${indexProduct.brand} ${indexProduct.category} with premium craftsmanship and exceptional design.`,
              short_description: `Premium ${indexProduct.brand} ${indexProduct.category}`,
              price: price * 100, // Convert to cents
              original_price: originalPrice * 100, // Convert to cents
              currency: 'MXN',
              images: indexProduct.images || [],
              sizes: this.generateSizes(indexProduct.category, indexProduct.gender),
              colors: this.extractColors(indexProduct.name),
              features: this.generateFeatures(indexProduct.brand, indexProduct.category),
              metadata: {
                supplier: 'CYTTE',
                source_path: indexProduct.source_path,
                image_format: indexProduct.image_format,
                indexed_at: new Date().toISOString()
              },
              stock_quantity: Math.floor(Math.random() * 20) + 5, // Random stock 5-25
              in_stock: true,
              status: 'active',
              featured: indexProduct.featured || false,
              is_new: Math.random() > 0.8, // 20% chance of being new
              gender: indexProduct.gender || 'unisex',
              meta_title: `${indexProduct.name} - ${indexProduct.brand} | The White Laces`,
              meta_description: `Compra ${indexProduct.name} de ${indexProduct.brand} en The White Laces. Calzado de lujo con envío gratis a México.`
            };

            productData.push(product);
            this.processedCount++;

          } catch (error) {
            console.error(`❌ Error processing product ${indexProduct.name}:`, error.message);
            this.errorCount++;
          }
        }

        // Insert batch to database
        if (productData.length > 0) {
          const { error } = await supabase
            .from('products')
            .insert(productData);

          if (error) {
            console.error(`❌ Database error for batch ${batchIndex + 1}:`, error);
            this.errorCount += productData.length;
          } else {
            console.log(`✅ Successfully inserted batch ${batchIndex + 1} (${productData.length} products)`);
          }
        }

        // Small delay between batches to avoid rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }

    } catch (error) {
      console.error('❌ Error processing products:', error);
      throw error;
    }
  }

  generateSizes(category, gender) {
    const sizeMappings = {
      'sneakers': ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44', '45'],
      'sandals': ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44'],
      'formal-shoes': ['36', '37', '38', '39', '40', '41', '42', '43', '44', '45'],
      'boots': ['36', '37', '38', '39', '40', '41', '42', '43', '44', '45'],
      'casual-shoes': ['35', '36', '37', '38', '39', '40', '41', '42', '43', '44']
    };

    return sizeMappings[category] || sizeMappings['sneakers'];
  }

  extractColors(productName) {
    const colorKeywords = {
      'white': 'Blanco',
      'black': 'Negro',
      'red': 'Rojo',
      'blue': 'Azul',
      'green': 'Verde',
      'yellow': 'Amarillo',
      'pink': 'Rosa',
      'purple': 'Morado',
      'brown': 'Café',
      'gray': 'Gris',
      'grey': 'Gris',
      'beige': 'Beige',
      'cream': 'Crema',
      'gold': 'Dorado',
      'silver': 'Plateado'
    };

    const colors = [];
    const nameLower = productName.toLowerCase();

    Object.entries(colorKeywords).forEach(([english, spanish]) => {
      if (nameLower.includes(english)) {
        colors.push(spanish);
      }
    });

    return colors.length > 0 ? colors : ['Multicolor'];
  }

  generateFeatures(brand, category) {
    const brandFeatures = {
      'gucci': ['Artesanía Italiana', 'Cuero Premium', 'Diseño Icónico'],
      'louis-vuitton': ['Lujo Francés', 'Savoir-faire', 'Materiales Excepcionales'],
      'dior': ['Alta Costura', 'Elegancia Francesa', 'Diseño Exclusivo'],
      'balenciaga': ['Vanguardia', 'Diseño Disruptivo', 'Innovación'],
      'nike': ['Tecnología Air', 'Rendimiento Deportivo', 'Innovación'],
      'adidas': ['Tecnología Boost', 'Herencia Deportiva', 'Calidad Alemana']
    };

    const categoryFeatures = {
      'sneakers': ['Suela de Goma', 'Amortiguación', 'Transpirable'],
      'sandals': ['Comodidad', 'Diseño Abierto', 'Versatilidad'],
      'formal-shoes': ['Elegancia', 'Cuero Genuino', 'Construcción Goodyear'],
      'boots': ['Durabilidad', 'Protección', 'Estilo Robusto'],
      'casual-shoes': ['Comodidad Diaria', 'Versatilidad', 'Estilo Relajado']
    };

    const brandKey = brand.toLowerCase().replace(/\s+/g, '-');
    const features = [
      ...(brandFeatures[brandKey] || ['Calidad Premium', 'Diseño Exclusivo']),
      ...(categoryFeatures[category] || ['Comodidad', 'Estilo'])
    ];

    return features.slice(0, 4); // Limit to 4 features
  }

  async run() {
    try {
      console.log('🚀 Starting product database population...\n');

      await this.loadMappings();
      await this.processProductsFromIndex();

      console.log('\n🎉 Product population completed!');
      console.log(`📊 Summary:`);
      console.log(`   ✅ Processed: ${this.processedCount} products`);
      console.log(`   ❌ Errors: ${this.errorCount} products`);
      console.log(`   ⚠️ Skipped: ${this.skippedCount} products`);

    } catch (error) {
      console.error('❌ Population failed:', error);
      process.exit(1);
    }
  }
}

// Run the populator
const populator = new ProductPopulator();
populator.run().catch(error => {
  console.error('❌ Product populator failed:', error);
  process.exit(1);
});
