'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import Image from 'next/image'
import { useCart } from '@/contexts/CartContext'
import { useCartNotification } from '@/contexts/CartNotificationContext'
import { useWishlist } from '@/contexts/WishlistContext'
import { cn } from '@/lib/utils'
import { cleanProductName } from '@/lib/real-products-loader'

export default function EnhancedProductCard({
  product,
  index = 0,
  variant = 'default',
  className = '',
  showQuickActions = true,
  priority = false
}) {
  const router = useRouter()
  const { addItem } = useCart()
  const { showCartSuccess } = useCartNotification()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()

  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [isHovered, setIsHovered] = useState(false)

  const isWishlisted = isInWishlist(product.id)
  const hasDiscount = product.originalPrice && product.originalPrice > product.price

  // Navigate to product page
  const handleProductClick = () => {
    router.push(`/product/${product.slug || product.id}`)
  }

  // Add to cart with loading state
  const handleAddToCart = async (e) => {
    e.stopPropagation()
    setIsAddingToCart(true)

    try {
      await new Promise(resolve => setTimeout(resolve, 600))
      addItem(product.id, 'M', 1)
      showCartSuccess(product, 'M', 1)
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setIsAddingToCart(false)
    }
  }

  // Toggle wishlist
  const handleWishlistToggle = (e) => {
    e.stopPropagation()
    if (isWishlisted) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product.id)
    }
  }

  // Get category badge info (from CleanProductCard)
  const getCategoryBadge = () => {
    const category = product.category?.toLowerCase() || 'general'
    const badges = {
      'formal': { text: 'FORMAL', color: 'bg-purple-500' },
      'sandals': { text: 'SANDAL', color: 'bg-amber-500' },
      'casual': { text: 'CASUAL', color: 'bg-green-500' },
      'sneakers': { text: 'TENNIS', color: 'bg-blue-500' },
      'boots': { text: 'BOTAS', color: 'bg-red-500' },
      'heels': { text: 'TACONES', color: 'bg-pink-500' },
      'general': { text: 'NUEVO', color: 'bg-gray-800' }
    }
    return badges[category] || badges.general
  }

  const categoryBadge = getCategoryBadge()

  // Get variant-specific classes
  const getVariantClasses = () => {
    switch (variant) {
      case 'masonry':
        return {
          container: 'h-full flex flex-col',
          image: 'relative aspect-[4/3] bg-light-gray dark:bg-neutral-700 overflow-hidden',
          info: 'p-4 flex-1 flex flex-col justify-between'
        }
      case 'shop-look':
        return {
          container: 'h-full flex flex-col',
          image: 'relative aspect-[4/3] bg-light-gray dark:bg-neutral-700 overflow-hidden',
          info: 'p-4 flex-1 flex flex-col justify-between'
        }
      default:
        return {
          container: 'h-full flex flex-col',
          image: 'relative aspect-[4/3] bg-light-gray dark:bg-neutral-700 overflow-hidden',
          info: 'p-4 flex-1 flex flex-col justify-between'
        }
    }
  }

  const variantClasses = getVariantClasses()

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.05, duration: 0.4 }}
      className={cn(
        'group cursor-pointer',
        'bg-pure-white dark:bg-neutral-800 rounded-2xl overflow-hidden',
        'shadow-sm hover:shadow-xl transition-all duration-500',
        'transform hover:scale-[1.02] hover:-translate-y-1',
        variantClasses.container,
        className
      )}
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      onClick={handleProductClick}
    >
      {/* Product Image Container */}
      <div className={variantClasses.image}>
        {/* First Image - Default */}
        <ProductImage
          src={product.image || product.images?.[0] || "/placeholder.jpg"}
          alt={product.name}
          className={`absolute inset-0 object-cover transition-all duration-500 ease-out ${
            isHovered && (product.images?.[1] || product.image2)
              ? 'opacity-0 scale-110'
              : 'opacity-100 group-hover:scale-110'
          }`}
          priority={priority || index < 4} // Load first 4 images immediately
        />

        {/* Second Image - Hover Effect */}
        {(product.images?.[1] || product.image2) && (
          <ProductImage
            src={product.images?.[1] || product.image2}
            alt={`${product.name} - Vista alternativa`}
            className={`absolute inset-0 object-cover transition-all duration-500 ease-out z-10 ${
              isHovered
                ? 'opacity-100 scale-110'
                : 'opacity-0 scale-100'
            }`}
          />
        )}

        {/* NUEVO Badge - Top Left */}
        {product.isNew && (
          <div className="absolute top-3 left-3 z-20">
            <div className="bg-gray-800 text-white text-xs px-3 py-1 rounded-md font-poppins font-semibold">
              NUEVO
            </div>
          </div>
        )}

        {/* Wishlist Heart - Top Right */}
        <button
          onClick={handleWishlistToggle}
          className="absolute top-3 right-3 w-8 h-8 flex items-center justify-center transition-all duration-300 transform hover:scale-110 z-30"
        >
          <svg
            className={`w-6 h-6 transition-colors duration-300 ${
              isWishlisted
                ? 'text-lime-500 fill-current'
                : 'text-gray-300 hover:text-lime-500'
            }`}
            viewBox="0 0 24 24"
            fill={isWishlisted ? "currentColor" : "none"}
            stroke="currentColor"
            strokeWidth={isWishlisted ? 0 : 1.5}
          >
            <path strokeLinecap="round" strokeLinejoin="round" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>

        {/* Quick View Button - Bottom Left to avoid overlap */}
        {showQuickActions && (
          <button
            onClick={handleQuickView}
            className={cn(
              'absolute bottom-3 left-3 w-10 h-10 rounded-full z-30',
              'bg-white/90 hover:bg-white text-gray-700 hover:text-black',
              'flex items-center justify-center transition-all duration-300',
              'shadow-lg hover:shadow-xl cursor-pointer',
              'opacity-0 group-hover:opacity-100',
              'transform translate-y-2 group-hover:translate-y-0 group-hover:scale-110'
            )}
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
            </svg>
          </button>
        )}

        {/* Add to Cart Button - Bottom Right Circle */}
        <ButtonLoading
          onClick={handleAddToCart}
          isLoading={isAddingToCart}
          className="absolute bottom-3 right-3 w-12 h-12 bg-lime-500 hover:bg-gray-800 hover:text-white text-black rounded-full flex items-center justify-center transition-all duration-300 opacity-0 group-hover:opacity-100 transform translate-y-2 group-hover:translate-y-0 group-hover:scale-110 shadow-lg hover:shadow-xl z-30"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5 6m0 0h9" />
          </svg>
        </ButtonLoading>
      </div>

      {/* Product Info */}
      <div className={variantClasses.info}>
        {/* Product Name and Category Badge */}
        <div className="flex items-start justify-between mb-2">
          <h3 className="font-poppins font-bold text-pure-black dark:text-pure-white text-base leading-tight flex-1 pr-2">
            {cleanProductName(product.name)}
          </h3>
          <div className={`text-xs px-2 py-1 rounded text-white font-poppins font-semibold ${categoryBadge.color} flex-shrink-0`}>
            {categoryBadge.text}
          </div>
        </div>

        {/* Brand Name */}
        <p className="font-poppins text-sm text-text-gray dark:text-neutral-400 uppercase tracking-wide mb-1">
          {cleanProductName(product.brand)}
        </p>

        {/* Gender */}
        <p className="font-poppins text-xs text-text-gray dark:text-neutral-400 uppercase mb-3">
          {product.gender?.toUpperCase() || 'UNISEX'}
        </p>

        {/* Price */}
        <div className="flex items-center gap-2">
          {hasDiscount && (
            <span className="font-poppins text-sm text-text-gray dark:text-neutral-400 line-through">
              ${product.originalPrice?.toLocaleString()} MXN
            </span>
          )}
          <span className="font-poppins font-bold text-xl text-pure-black dark:text-pure-white">
            ${product.price?.toLocaleString()} MXN
          </span>
        </div>
      </div>

      {/* Quick View Modal */}
      <QuickViewModal
        product={product}
        isOpen={showQuickView}
        onClose={() => setShowQuickView(false)}
      />
    </motion.div>
  )
}
