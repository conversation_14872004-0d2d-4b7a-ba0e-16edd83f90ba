'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { useTheme } from '@/components/theme/ThemeProvider'
import Button from '@/components/ui/Button'

export default function AnimatedNavigation() {
  const { theme, toggleTheme } = useTheme()
  const [activeItem, setActiveItem] = useState('/')
  const [isScrolled, setIsScrolled] = useState(false)

  const navigation = [
    { name: 'Inicio', href: '/', icon: '🏠' },
    { name: 'Tienda', href: '/shop', icon: '🛍️' },
    { name: '<PERSON><PERSON>', href: '/brands', icon: '✨' },
    { name: 'Limitadas', href: '/limited', icon: '💎' },
    { name: 'Comunidad', href: '/community', icon: '👥' },
  ]

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20)
    }
    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  return (
    <motion.nav
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-500 ${
        isScrolled 
          ? 'glass backdrop-blur-xl border-b border-rich-gold/20' 
          : 'bg-transparent'
      }`}
      initial={{ y: -100 }}
      animate={{ y: 0 }}
      transition={{ duration: 0.6, type: "spring", stiffness: 100 }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16 lg:h-20">
          
          {/* Animated Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <Link href="/" className="flex items-center space-x-3">
              <motion.div 
                className="w-10 h-10 bg-gradient-natural rounded-xl flex items-center justify-center relative overflow-hidden"
                whileHover={{ rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <span className="text-forest-emerald font-bold text-xl relative z-10">W</span>
                
                {/* Animated background */}
                <motion.div
                  className="absolute inset-0 bg-gradient-to-r from-rich-gold to-warm-camel"
                  initial={{ scale: 0 }}
                  whileHover={{ scale: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </motion.div>
              
              <motion.span 
                className="text-xl font-playfair font-bold text-gradient hidden sm:block"
                animate={{
                  backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"]
                }}
                transition={{ duration: 8, repeat: Infinity, ease: "linear" }}
                style={{ backgroundSize: "200% 200%" }}
              >
                The White Laces
              </motion.span>
            </Link>
          </motion.div>

          {/* Desktop Navigation */}
          <div className="hidden lg:flex items-center space-x-1">
            {navigation.map((item, index) => (
              <motion.div
                key={item.href}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1, duration: 0.5 }}
              >
                <Link
                  href={item.href}
                  className="relative px-4 py-2 rounded-lg transition-colors duration-300"
                  onMouseEnter={() => setActiveItem(item.href)}
                >
                  <motion.span
                    className="relative z-10 text-forest-emerald dark:text-light-cloud-gray hover:text-rich-gold transition-colors duration-300 font-medium flex items-center gap-2"
                    whileHover={{ scale: 1.05 }}
                  >
                    <span className="text-sm">{item.icon}</span>
                    {item.name}
                  </motion.span>
                  
                  {/* Animated background */}
                  <AnimatePresence>
                    {activeItem === item.href && (
                      <motion.div
                        className="absolute inset-0 bg-rich-gold/10 rounded-lg"
                        layoutId="activeBackground"
                        initial={{ opacity: 0, scale: 0.8 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.8 }}
                        transition={{ duration: 0.3, type: "spring", stiffness: 300 }}
                      />
                    )}
                  </AnimatePresence>
                  
                  {/* Hover ripple effect */}
                  <motion.div
                    className="absolute inset-0 rounded-lg"
                    whileHover={{
                      boxShadow: [
                        "0 0 0 0 rgba(255, 215, 0, 0.3)",
                        "0 0 0 8px rgba(255, 215, 0, 0.1)",
                        "0 0 0 16px rgba(255, 215, 0, 0)"
                      ]
                    }}
                    transition={{ duration: 0.6 }}
                  />
                </Link>
              </motion.div>
            ))}
          </div>

          {/* Right Side Actions */}
          <div className="flex items-center space-x-3">
            
            {/* Theme Toggle */}
            <motion.div
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={toggleTheme}
                className="relative overflow-hidden"
              >
                <motion.div
                  animate={{ rotate: theme === 'dark' ? 0 : 180 }}
                  transition={{ duration: 0.5, type: "spring", stiffness: 200 }}
                >
                  {theme === 'dark' ? (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z" />
                    </svg>
                  ) : (
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z" />
                    </svg>
                  )}
                </motion.div>
                
                {/* Glow effect */}
                <motion.div
                  className="absolute inset-0 rounded-lg bg-rich-gold/20"
                  initial={{ scale: 0, opacity: 0 }}
                  whileHover={{ scale: 1, opacity: 1 }}
                  transition={{ duration: 0.3 }}
                />
              </Button>
            </motion.div>

            {/* Cart Button */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button variant="primary" size="icon" className="relative">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                
                {/* Animated badge */}
                <motion.div
                  className="absolute -top-1 -right-1 h-5 w-5 bg-warm-camel text-forest-emerald rounded-full flex items-center justify-center text-xs font-bold"
                  animate={{
                    scale: [1, 1.2, 1],
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    ease: "easeInOut"
                  }}
                >
                  2
                </motion.div>
              </Button>
            </motion.div>
          </div>
        </div>
      </div>

      {/* Mobile Navigation Overlay */}
      <AnimatePresence>
        {/* Mobile menu would go here */}
      </AnimatePresence>
    </motion.nav>
  )
}
