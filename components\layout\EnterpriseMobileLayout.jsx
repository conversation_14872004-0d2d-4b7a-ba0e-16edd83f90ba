'use client'

/**
 * 🏢 ENTERPRISE-GRADE MOBILE LAYOUT SYSTEM
 * 
 * Comprehensive mobile-first layout with advanced optimizations
 * Built for production-ready luxury e-commerce experiences
 * 
 * Features:
 * - Advanced gesture recognition and swipe navigation
 * - Smart navigation hiding with scroll detection
 * - Pull-to-refresh functionality
 * - Haptic feedback integration
 * - Performance monitoring and optimization
 * - Safe area support for all devices
 * - Enterprise-grade error handling
 */

import { useEffect, useState, useCallback, useRef } from 'react'
import { motion, AnimatePresence, useScroll, useMotionValueEvent } from 'framer-motion'
import { useRouter, usePathname } from 'next/navigation'
import LuxuryMobileHeader from './LuxuryMobileHeader'
import MobileSwipeEngine from '../mobile/MobileSwipeEngine'
import { HapticEngine, DeviceCapabilities } from '@/lib/mobile-gestures'
import { PerformanceOptimizer } from '@/lib/mobile-performance'

export default function EnterpriseMobileLayout({ 
  children, 
  showBottomPadding = true,
  enableSwipeNavigation = true,
  enablePullToRefresh = true,
  enableSmartHiding = true,
  enablePerformanceMonitoring = true
}) {
  const router = useRouter()
  const pathname = usePathname()
  const { scrollY } = useScroll()
  
  // Device and layout state
  const [isMobile, setIsMobile] = useState(false)
  const [isKeyboardOpen, setIsKeyboardOpen] = useState(false)
  const [viewportHeight, setViewportHeight] = useState(0)
  const [orientation, setOrientation] = useState('portrait')
  const [deviceInfo, setDeviceInfo] = useState({})
  const [isStandalone, setIsStandalone] = useState(false)

  // Navigation state
  const [isNavVisible, setIsNavVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [isAtTop, setIsAtTop] = useState(true)
  const [currentNavIndex, setCurrentNavIndex] = useState(0)

  // Pull to refresh state
  const [isPulling, setIsPulling] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [touchStartY, setTouchStartY] = useState(0)

  // Performance monitoring
  const performanceOptimizer = useRef(null)
  const refreshThreshold = 80

  // Navigation items
  const navItems = [
    {
      id: 'home',
      name: 'Inicio',
      href: '/',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      )
    },
    {
      id: 'shop',
      name: 'Tienda',
      href: '/tienda',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      )
    },
    {
      id: 'ai',
      name: 'IA',
      href: '/ia',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    },
    {
      id: 'social',
      name: 'Social',
      href: '/social',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      )
    },
    {
      id: 'profile',
      name: 'Perfil',
      href: '/profile',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      )
    }
  ]

  // Enhanced mobile detection with device capabilities
  const checkMobile = useCallback(() => {
    const width = window.innerWidth
    const height = window.innerHeight
    const userAgent = navigator.userAgent
    
    const isMobileDevice = width < 1024 || 
                          DeviceCapabilities.isTouchDevice() || 
                          /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)
    
    setIsMobile(isMobileDevice)
    setViewportHeight(height)
    setOrientation(width > height ? 'landscape' : 'portrait')
    
    // Get device information
    setDeviceInfo(DeviceCapabilities.getScreenInfo())
    
    // Check if running as PWA
    setIsStandalone(
      window.matchMedia('(display-mode: standalone)').matches ||
      window.navigator.standalone === true
    )
  }, [])

  // Enhanced keyboard detection
  const handleResize = useCallback(() => {
    checkMobile()

    if (window.innerWidth < 1024) {
      const currentHeight = window.innerHeight
      const heightDifference = viewportHeight - currentHeight
      const keyboardThreshold = window.innerHeight * 0.25
      
      setIsKeyboardOpen(heightDifference > keyboardThreshold)
    }
  }, [viewportHeight, checkMobile])

  // Smart navigation hiding based on scroll
  useMotionValueEvent(scrollY, 'change', (latest) => {
    if (!enableSmartHiding) return
    
    const direction = latest > lastScrollY ? 'down' : 'up'
    const isScrollingFast = Math.abs(latest - lastScrollY) > 5
    
    setIsAtTop(latest < 10)
    
    if (isScrollingFast) {
      setIsNavVisible(direction === 'up' || latest < 100)
    }
    
    setLastScrollY(latest)
  })

  // Get current navigation index
  useEffect(() => {
    const currentIndex = navItems.findIndex(item => {
      if (item.href === '/') {
        return pathname === '/'
      }
      return pathname.startsWith(item.href)
    })
    setCurrentNavIndex(Math.max(0, currentIndex))
  }, [pathname])

  // Initialize performance monitoring
  useEffect(() => {
    if (enablePerformanceMonitoring && isMobile) {
      performanceOptimizer.current = new PerformanceOptimizer({
        targetFPS: 60,
        memoryThreshold: 50,
        batteryThreshold: 0.2,
        enableLazyLoading: true
      })
      
      performanceOptimizer.current.initialize()
    }

    return () => {
      if (performanceOptimizer.current) {
        performanceOptimizer.current.cleanup()
      }
    }
  }, [enablePerformanceMonitoring, isMobile])

  // Setup event listeners
  useEffect(() => {
    checkMobile()

    const handleResizeEvent = () => handleResize()
    const handleOrientationChange = () => setTimeout(checkMobile, 100)

    window.addEventListener('resize', handleResizeEvent, { passive: true })
    window.addEventListener('orientationchange', handleOrientationChange, { passive: true })

    // Visual viewport API for better keyboard detection
    if ('visualViewport' in window) {
      const visualViewport = window.visualViewport
      const handleViewportChange = () => {
        const heightDiff = window.innerHeight - visualViewport.height
        setIsKeyboardOpen(heightDiff > 150)
      }
      
      visualViewport.addEventListener('resize', handleViewportChange, { passive: true })
      
      return () => {
        window.removeEventListener('resize', handleResizeEvent)
        window.removeEventListener('orientationchange', handleOrientationChange)
        visualViewport.removeEventListener('resize', handleViewportChange)
      }
    }

    return () => {
      window.removeEventListener('resize', handleResizeEvent)
      window.removeEventListener('orientationchange', handleOrientationChange)
    }
  }, [handleResize, checkMobile])

  // Swipe navigation handlers
  const handleSwipeLeft = useCallback(() => {
    if (!enableSwipeNavigation) return
    
    const nextIndex = (currentNavIndex + 1) % navItems.length
    const nextItem = navItems[nextIndex]
    
    HapticEngine.trigger('medium')
    router.push(nextItem.href)
  }, [currentNavIndex, router, enableSwipeNavigation])

  const handleSwipeRight = useCallback(() => {
    if (!enableSwipeNavigation) return
    
    const prevIndex = currentNavIndex === 0 ? navItems.length - 1 : currentNavIndex - 1
    const prevItem = navItems[prevIndex]
    
    HapticEngine.trigger('medium')
    router.push(prevItem.href)
  }, [currentNavIndex, router, enableSwipeNavigation])

  // Pull to refresh handlers
  const handleTouchStart = useCallback((e) => {
    if (!enablePullToRefresh || !isAtTop) return
    setTouchStartY(e.touches[0].clientY)
  }, [enablePullToRefresh, isAtTop])

  const handleTouchMove = useCallback((e) => {
    if (!enablePullToRefresh || !isAtTop || touchStartY === 0) return
    
    const currentY = e.touches[0].clientY
    const distance = currentY - touchStartY
    
    if (distance > 0 && distance < refreshThreshold * 2) {
      setPullDistance(distance)
      setIsPulling(distance > 20)
      
      if (distance > refreshThreshold && !isPulling) {
        HapticEngine.trigger('light')
      }
    }
  }, [enablePullToRefresh, isAtTop, touchStartY, refreshThreshold, isPulling])

  const handleTouchEnd = useCallback(() => {
    if (!enablePullToRefresh) return
    
    if (pullDistance > refreshThreshold) {
      setIsRefreshing(true)
      HapticEngine.trigger('heavy')
      
      setTimeout(() => {
        setIsRefreshing(false)
        window.location.reload()
      }, 1500)
    }
    
    setIsPulling(false)
    setPullDistance(0)
    setTouchStartY(0)
  }, [enablePullToRefresh, pullDistance, refreshThreshold])

  // Navigation item click handler
  const handleNavClick = useCallback((item) => {
    HapticEngine.trigger('medium')
    router.push(item.href)
  }, [router])

  // Check if nav item is active
  const isActive = useCallback((item) => {
    if (item.href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(item.href)
  }, [pathname])

  // Advanced constraint-based styles for mobile optimization
  const constrainedStyles = {
    maxWidth: '100vw',
    overflowX: 'hidden',
    WebkitTextSizeAdjust: '100%',
    textSizeAdjust: '100%',
    WebkitTapHighlightColor: 'transparent',
    WebkitTouchCallout: 'none',
    touchAction: 'manipulation'
  }

  // Desktop fallback
  if (!isMobile) {
    return (
      <div className="min-h-screen bg-white dark:bg-black">
        {children}
      </div>
    )
  }

  return (
    <div
      className="min-h-screen bg-white dark:bg-black relative"
      style={constrainedStyles}
    >
      {/* Pull to refresh indicator */}
      <AnimatePresence>
        {isPulling && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-0 left-0 right-0 z-50 flex justify-center pt-safe-area-inset-top"
          >
            <div className="bg-white/90 dark:bg-black/90 backdrop-blur-xl rounded-full px-4 py-2 mt-4 shadow-lg border border-black/10 dark:border-white/10">
              <div className="flex items-center space-x-2">
                <motion.div
                  animate={{ rotate: isRefreshing ? 360 : 0 }}
                  transition={{ duration: 1, repeat: isRefreshing ? Infinity : 0, ease: 'linear' }}
                  className="w-4 h-4 border-2 border-lime-green border-t-transparent rounded-full"
                />
                <span className="text-sm font-medium text-black dark:text-white">
                  {isRefreshing ? 'Actualizando...' : pullDistance > refreshThreshold ? 'Suelta para actualizar' : 'Desliza para actualizar'}
                </span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main content with swipe navigation */}
      <MobileSwipeEngine
        config={{
          threshold: 100,
          velocityThreshold: 0.5,
          enableHaptics: true,
          enableMomentum: true,
          directions: enableSwipeNavigation ? ['left', 'right'] : []
        }}
        callbacks={{
          onSwipeLeft: handleSwipeLeft,
          onSwipeRight: handleSwipeRight
        }}
        className="min-h-screen"
      >
        <div
          className="relative"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{
            transform: `translateY(${Math.min(pullDistance * 0.5, 40)}px)`,
            transition: isPulling ? 'none' : 'transform 0.3s ease-out'
          }}
        >
          {/* Mobile Header handled by main Header component */}

          {/* Main Content */}
          <main
            className={`
              ${isStandalone ? 'pt-safe-area-inset-top' : 'pt-16'}
              ${showBottomPadding && !isKeyboardOpen ? 'pb-24' : 'pb-4'}
              ${orientation === 'landscape' ? 'pt-12 pb-16' : ''}
              transition-all duration-300 ease-out
              min-h-screen
              relative
              overflow-x-hidden
              touch-pan-y
            `}
            style={{
              paddingBottom: isKeyboardOpen ? '1rem' : showBottomPadding ? '6rem' : '1rem'
            }}
          >
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, ease: 'easeOut' }}
              className="relative z-10"
            >
              {children}
            </motion.div>
          </main>
        </div>
      </MobileSwipeEngine>

      {/* Advanced Bottom Navigation */}
      <AnimatePresence>
        {isNavVisible && !isKeyboardOpen && (
          <motion.nav
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ 
              type: 'spring', 
              stiffness: 300, 
              damping: 30,
              mass: 0.8
            }}
            className="fixed bottom-0 left-0 right-0 z-50"
          >
            <div className="bg-white/95 dark:bg-black/95 backdrop-blur-xl border-t border-black/10 dark:border-white/10 shadow-2xl">
              <div className="px-2 pt-2 pb-safe-area-inset-bottom">
                <div className="flex items-center justify-around">
                  {navItems.map((item, index) => {
                    const active = isActive(item)
                    
                    return (
                      <motion.button
                        key={item.id}
                        onClick={() => handleNavClick(item)}
                        className={`
                          relative flex flex-col items-center justify-center p-3 rounded-2xl 
                          transition-all duration-300 min-w-[60px] min-h-[60px]
                          ${active 
                            ? 'bg-lime-green/10 text-lime-green' 
                            : 'text-black/60 dark:text-white/60 hover:text-black dark:hover:text-white hover:bg-black/5 dark:hover:bg-white/5'
                          }
                        `}
                        whileTap={{ scale: 0.9 }}
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ 
                          delay: index * 0.1, 
                          duration: 0.3,
                          type: 'spring',
                          stiffness: 400,
                          damping: 25
                        }}
                      >
                        {/* Active indicator background */}
                        {active && (
                          <motion.div
                            layoutId="activeNavTab"
                            className="absolute inset-0 bg-lime-green/10 rounded-2xl"
                            transition={{ 
                              type: 'spring', 
                              bounce: 0.2, 
                              duration: 0.6 
                            }}
                          />
                        )}
                        
                        {/* Icon */}
                        <motion.div
                          className="relative z-10 mb-1"
                          animate={{ 
                            scale: active ? 1.1 : 1,
                            y: active ? -2 : 0
                          }}
                          transition={{ 
                            duration: 0.2,
                            type: 'spring',
                            stiffness: 400
                          }}
                        >
                          {item.icon(active)}
                        </motion.div>
                        
                        {/* Label */}
                        <span className={`
                          text-xs font-medium relative z-10 transition-all duration-300
                          ${active ? 'text-lime-green font-semibold' : ''}
                        `}>
                          {item.name}
                        </span>
                        
                        {/* Active dot indicator */}
                        {active && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute -top-1 w-1 h-1 bg-lime-green rounded-full"
                          />
                        )}
                      </motion.button>
                    )
                  })}
                </div>
              </div>
            </div>
          </motion.nav>
        )}
      </AnimatePresence>

      {/* Swipe navigation hints */}
      {enableSwipeNavigation && (
        <div className="fixed bottom-20 left-4 right-4 z-40 pointer-events-none">
          <div className="flex justify-between items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 0.3, x: 0 }}
              className="text-xs text-black/40 dark:text-white/40 flex items-center"
            >
              <span>←</span>
              <span className="ml-1">Desliza</span>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 0.3, x: 0 }}
              className="text-xs text-black/40 dark:text-white/40 flex items-center"
            >
              <span className="mr-1">Desliza</span>
              <span>→</span>
            </motion.div>
          </div>
        </div>
      )}

      {/* Advanced Mobile Optimizations */}
      <style jsx global>{`
        @media (max-width: 1024px) {
          /* Enhanced touch interactions */
          * {
            touch-action: manipulation;
            -webkit-tap-highlight-color: transparent;
          }

          input, textarea, select, [contenteditable] {
            touch-action: auto;
            -webkit-user-select: text;
            user-select: text;
          }

          /* Prevent horizontal scroll and zoom */
          html, body {
            overflow-x: hidden;
            max-width: 100vw;
            -webkit-text-size-adjust: 100%;
            text-size-adjust: 100%;
          }

          /* iOS-specific optimizations */
          html {
            scroll-behavior: smooth;
            -webkit-overflow-scrolling: touch;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
          }

          /* Enhanced button interactions */
          button, [role="button"], .touch-target {
            min-height: 44px;
            min-width: 44px;
            cursor: pointer;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            user-select: none;
          }

          /* Improved form elements */
          input[type="text"], 
          input[type="email"], 
          input[type="password"], 
          input[type="search"],
          textarea {
            font-size: 16px; /* Prevent zoom on iOS */
            border-radius: 8px;
            -webkit-appearance: none;
            appearance: none;
          }

          /* Momentum scrolling for all scrollable elements */
          .scroll-smooth {
            -webkit-overflow-scrolling: touch;
            scroll-behavior: smooth;
          }

          /* PWA optimizations */
          @media (display-mode: standalone) {
            body {
              padding-top: env(safe-area-inset-top);
              padding-bottom: env(safe-area-inset-bottom);
              padding-left: env(safe-area-inset-left);
              padding-right: env(safe-area-inset-right);
            }
          }
        }
      `}</style>
    </div>
  )
}
