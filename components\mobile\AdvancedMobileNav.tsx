'use client'

/**
 * 🚀 ADVANCED MOBILE NAVIGATION - CUTTING-EDGE MOBILE UX
 * 
 * Features:
 * - Gesture-based navigation with swipe detection
 * - Dynamic bottom nav with smart hiding/showing
 * - Floating action buttons with haptic feedback
 * - Pull-to-refresh functionality
 * - Smart scroll behavior detection
 * - iOS-style momentum scrolling
 * - Advanced touch target optimization
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { motion, AnimatePresence, useScroll, useMotionValueEvent } from 'framer-motion'
import { useRouter, usePathname } from 'next/navigation'
import MobileSwipeEngine from './MobileSwipeEngine'

interface NavItem {
  id: string
  name: string
  href: string
  icon: (isActive: boolean) => React.ReactNode
  badge?: number
  swipeAction?: () => void
}

interface AdvancedMobileNavProps {
  children: React.ReactNode
  enableSwipeNavigation?: boolean
  enablePullToRefresh?: boolean
  enableSmartHiding?: boolean
  customNavItems?: NavItem[]
}

export default function AdvancedMobileNav({
  children,
  enableSwipeNavigation = true,
  enablePullToRefresh = true,
  enableSmartHiding = true,
  customNavItems
}: AdvancedMobileNavProps) {
  const router = useRouter()
  const pathname = usePathname()
  const { scrollY } = useScroll()
  
  // Navigation state
  const [isNavVisible, setIsNavVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [isScrollingDown, setIsScrollingDown] = useState(false)
  const [currentNavIndex, setCurrentNavIndex] = useState(0)
  
  // Pull to refresh state
  const [isPulling, setIsPulling] = useState(false)
  const [pullDistance, setPullDistance] = useState(0)
  const [isRefreshing, setIsRefreshing] = useState(false)
  
  // Touch and gesture state
  const [touchStartY, setTouchStartY] = useState(0)
  const [isAtTop, setIsAtTop] = useState(true)
  
  // Refs
  const navContainerRef = useRef<HTMLDivElement>(null)
  const refreshThreshold = 80

  // Default navigation items
  const defaultNavItems: NavItem[] = [
    {
      id: 'home',
      name: 'Inicio',
      href: '/',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      )
    },
    {
      id: 'shop',
      name: 'Tienda',
      href: '/shop',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      )
    },
    {
      id: 'search',
      name: 'Buscar',
      href: '/search',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      )
    },
    {
      id: 'wishlist',
      name: 'Favoritos',
      href: '/wishlist',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      )
    },
    {
      id: 'profile',
      name: 'Perfil',
      href: '/profile',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      )
    }
  ]

  const navItems = customNavItems || defaultNavItems

  // Haptic feedback utility
  const triggerHaptic = useCallback((intensity: 'light' | 'medium' | 'heavy' = 'medium') => {
    try {
      if ('vibrate' in navigator) {
        const patterns = {
          light: [10],
          medium: [20],
          heavy: [30, 10, 30]
        }
        navigator.vibrate(patterns[intensity])
      }
    } catch (error) {
      // Silently fail
    }
  }, [])

  // Smart navigation hiding based on scroll
  useMotionValueEvent(scrollY, 'change', (latest) => {
    if (!enableSmartHiding) return
    
    const direction = latest > lastScrollY ? 'down' : 'up'
    const isScrollingFast = Math.abs(latest - lastScrollY) > 5
    
    setIsScrollingDown(direction === 'down')
    setIsAtTop(latest < 10)
    
    if (isScrollingFast) {
      setIsNavVisible(direction === 'up' || latest < 100)
    }
    
    setLastScrollY(latest)
  })

  // Get current navigation index
  useEffect(() => {
    const currentIndex = navItems.findIndex(item => {
      if (item.href === '/') {
        return pathname === '/'
      }
      return pathname.startsWith(item.href)
    })
    setCurrentNavIndex(Math.max(0, currentIndex))
  }, [pathname, navItems])

  // Swipe navigation handlers
  const handleSwipeLeft = useCallback(() => {
    if (!enableSwipeNavigation) return
    
    const nextIndex = (currentNavIndex + 1) % navItems.length
    const nextItem = navItems[nextIndex]
    
    triggerHaptic('medium')
    router.push(nextItem.href)
  }, [currentNavIndex, navItems, router, triggerHaptic, enableSwipeNavigation])

  const handleSwipeRight = useCallback(() => {
    if (!enableSwipeNavigation) return
    
    const prevIndex = currentNavIndex === 0 ? navItems.length - 1 : currentNavIndex - 1
    const prevItem = navItems[prevIndex]
    
    triggerHaptic('medium')
    router.push(prevItem.href)
  }, [currentNavIndex, navItems, router, triggerHaptic, enableSwipeNavigation])

  // Pull to refresh handlers
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (!enablePullToRefresh || !isAtTop) return
    setTouchStartY(e.touches[0].clientY)
  }, [enablePullToRefresh, isAtTop])

  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (!enablePullToRefresh || !isAtTop || touchStartY === 0) return
    
    const currentY = e.touches[0].clientY
    const distance = currentY - touchStartY
    
    if (distance > 0 && distance < refreshThreshold * 2) {
      setPullDistance(distance)
      setIsPulling(distance > 20)
      
      // Haptic feedback at threshold
      if (distance > refreshThreshold && !isPulling) {
        triggerHaptic('light')
      }
    }
  }, [enablePullToRefresh, isAtTop, touchStartY, refreshThreshold, isPulling, triggerHaptic])

  const handleTouchEnd = useCallback(() => {
    if (!enablePullToRefresh) return
    
    if (pullDistance > refreshThreshold) {
      setIsRefreshing(true)
      triggerHaptic('heavy')
      
      // Simulate refresh
      setTimeout(() => {
        setIsRefreshing(false)
        window.location.reload()
      }, 1500)
    }
    
    setIsPulling(false)
    setPullDistance(0)
    setTouchStartY(0)
  }, [enablePullToRefresh, pullDistance, refreshThreshold, triggerHaptic])

  // Navigation item click handler
  const handleNavClick = useCallback((item: NavItem) => {
    triggerHaptic('medium')
    router.push(item.href)
  }, [router, triggerHaptic])

  // Check if nav item is active
  const isActive = useCallback((item: NavItem) => {
    if (item.href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(item.href)
  }, [pathname])

  return (
    <div className="relative min-h-screen bg-white dark:bg-black">
      {/* Pull to refresh indicator */}
      <AnimatePresence>
        {isPulling && (
          <motion.div
            initial={{ opacity: 0, y: -50 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -50 }}
            className="fixed top-0 left-0 right-0 z-50 flex justify-center pt-safe-area-inset-top"
          >
            <div className="bg-white/90 dark:bg-black/90 backdrop-blur-xl rounded-full px-4 py-2 mt-4 shadow-lg border border-black/10 dark:border-white/10">
              <div className="flex items-center space-x-2">
                <motion.div
                  animate={{ rotate: isRefreshing ? 360 : 0 }}
                  transition={{ duration: 1, repeat: isRefreshing ? Infinity : 0, ease: 'linear' }}
                  className="w-4 h-4 border-2 border-lime-green border-t-transparent rounded-full"
                />
                <span className="text-sm font-medium text-black dark:text-white">
                  {isRefreshing ? 'Actualizando...' : pullDistance > refreshThreshold ? 'Suelta para actualizar' : 'Desliza para actualizar'}
                </span>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Main content with swipe navigation */}
      <MobileSwipeEngine
        config={{
          threshold: 100,
          velocityThreshold: 0.5,
          enableHaptics: true,
          enableMomentum: true,
          directions: enableSwipeNavigation ? ['left', 'right'] : []
        }}
        callbacks={{
          onSwipeLeft: handleSwipeLeft,
          onSwipeRight: handleSwipeRight
        }}
        className="min-h-screen"
      >
        <div
          className="relative"
          onTouchStart={handleTouchStart}
          onTouchMove={handleTouchMove}
          onTouchEnd={handleTouchEnd}
          style={{
            transform: `translateY(${Math.min(pullDistance * 0.5, 40)}px)`,
            transition: isPulling ? 'none' : 'transform 0.3s ease-out'
          }}
        >
          {children}
        </div>
      </MobileSwipeEngine>

      {/* Advanced Bottom Navigation */}
      <AnimatePresence>
        {isNavVisible && (
          <motion.nav
            ref={navContainerRef}
            initial={{ y: 100, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: 100, opacity: 0 }}
            transition={{ 
              type: 'spring', 
              stiffness: 300, 
              damping: 30,
              mass: 0.8
            }}
            className="fixed bottom-0 left-0 right-0 z-50 lg:hidden"
          >
            {/* Glassmorphic background */}
            <div className="bg-white/95 dark:bg-black/95 backdrop-blur-xl border-t border-black/10 dark:border-white/10 shadow-2xl">
              {/* Navigation items */}
              <div className="px-2 pt-2 pb-safe-area-inset-bottom">
                <div className="flex items-center justify-around">
                  {navItems.map((item, index) => {
                    const active = isActive(item)
                    
                    return (
                      <motion.button
                        key={item.id}
                        onClick={() => handleNavClick(item)}
                        className={`
                          relative flex flex-col items-center justify-center p-3 rounded-2xl 
                          transition-all duration-300 min-w-[60px] min-h-[60px]
                          ${active 
                            ? 'bg-lime-green/10 text-lime-green' 
                            : 'text-black/60 dark:text-white/60 hover:text-black dark:hover:text-white hover:bg-black/5 dark:hover:bg-white/5'
                          }
                        `}
                        whileTap={{ scale: 0.9 }}
                        initial={{ y: 20, opacity: 0 }}
                        animate={{ y: 0, opacity: 1 }}
                        transition={{ 
                          delay: index * 0.1, 
                          duration: 0.3,
                          type: 'spring',
                          stiffness: 400,
                          damping: 25
                        }}
                      >
                        {/* Active indicator background */}
                        {active && (
                          <motion.div
                            layoutId="activeNavTab"
                            className="absolute inset-0 bg-lime-green/10 rounded-2xl"
                            transition={{ 
                              type: 'spring', 
                              bounce: 0.2, 
                              duration: 0.6 
                            }}
                          />
                        )}
                        
                        {/* Icon with micro-interactions */}
                        <motion.div
                          className="relative z-10 mb-1"
                          animate={{ 
                            scale: active ? 1.1 : 1,
                            y: active ? -2 : 0
                          }}
                          transition={{ 
                            duration: 0.2,
                            type: 'spring',
                            stiffness: 400
                          }}
                        >
                          {item.icon(active)}
                          
                          {/* Badge for notifications */}
                          {item.badge && item.badge > 0 && (
                            <motion.div
                              initial={{ scale: 0 }}
                              animate={{ scale: 1 }}
                              className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full min-w-[18px] h-[18px] flex items-center justify-center font-bold"
                            >
                              {item.badge > 99 ? '99+' : item.badge}
                            </motion.div>
                          )}
                        </motion.div>
                        
                        {/* Label */}
                        <span className={`
                          text-xs font-medium relative z-10 transition-all duration-300
                          ${active ? 'text-lime-green font-semibold' : ''}
                        `}>
                          {item.name}
                        </span>
                        
                        {/* Active dot indicator */}
                        {active && (
                          <motion.div
                            initial={{ scale: 0 }}
                            animate={{ scale: 1 }}
                            className="absolute -top-1 w-1 h-1 bg-lime-green rounded-full"
                          />
                        )}
                      </motion.button>
                    )
                  })}
                </div>
              </div>
            </div>
          </motion.nav>
        )}
      </AnimatePresence>

      {/* Swipe navigation hints */}
      {enableSwipeNavigation && (
        <div className="fixed bottom-20 left-4 right-4 z-40 pointer-events-none lg:hidden">
          <div className="flex justify-between items-center">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 0.3, x: 0 }}
              className="text-xs text-black/40 dark:text-white/40 flex items-center"
            >
              <span>←</span>
              <span className="ml-1">Desliza</span>
            </motion.div>
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 0.3, x: 0 }}
              className="text-xs text-black/40 dark:text-white/40 flex items-center"
            >
              <span className="mr-1">Desliza</span>
              <span>→</span>
            </motion.div>
          </div>
        </div>
      )}
    </div>
  )
}
