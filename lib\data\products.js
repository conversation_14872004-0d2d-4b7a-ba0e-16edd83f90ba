// Mock product data for The White Laces
// This would typically come from a database or API

export const brands = [
  { id: 'nike', name: 'Nike', logo: '/brands/nike.svg', featured: true },
  { id: 'adidas', name: 'Adidas', logo: '/brands/adidas.svg', featured: true },
  { id: 'gucci', name: 'G<PERSON>', logo: '/brands/gucci.svg', featured: true },
  { id: 'dior', name: '<PERSON><PERSON>', logo: '/brands/dior.svg', featured: true },
  { id: 'balenciaga', name: '<PERSON><PERSON>cia<PERSON>', logo: '/brands/balenciaga.svg', featured: true },
  { id: 'off-white', name: 'Off-White', logo: '/brands/off-white.svg', featured: true },
  { id: 'jordan', name: '<PERSON>', logo: '/brands/jordan.svg', featured: true },
  { id: 'yeezy', name: 'Yeezy', logo: '/brands/yeezy.svg', featured: true },
]

export const categories = [
  { id: 'sneakers', name: 'Sneakers', slug: 'sneakers', icon: '👟' },
  { id: 'luxury', name: '<PERSON><PERSON><PERSON>', slug: 'luxury', icon: '💎' },
  { id: 'limited', name: 'Edición Limitada', slug: 'limited', icon: '⭐' },
  { id: 'men', name: 'Hombre', slug: 'men', icon: '👨' },
  { id: 'women', name: 'Mujer', slug: 'women', icon: '👩' },
  { id: 'kids', name: 'Niños', slug: 'kids', icon: '👶' },
]

export const mockProducts = [
  {
    id: 'nike-air-jordan-1-retro-high',
    name: 'Air Jordan 1 Retro High OG "Chicago"',
    brand: 'Jordan',
    brandId: 'jordan',
    category: 'sneakers',
    gender: 'unisex',
    price: 4500,
    originalPrice: 5200,
    currency: 'MXN',
    images: [
      '/products/jordan-1-chicago-1.jpg',
      '/products/jordan-1-chicago-2.jpg',
      '/products/jordan-1-chicago-3.jpg',
      '/products/jordan-1-chicago-4.jpg',
    ],
    colors: ['Rojo/Blanco/Negro', 'Negro/Blanco'],
    sizes: ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
    description: 'El icónico Air Jordan 1 en su colorway más legendario. Una pieza de historia del basketball que nunca pasa de moda.',
    features: [
      'Cuero premium de alta calidad',
      'Suela de goma con tracción superior',
      'Diseño clásico de 1985',
      'Swoosh de Nike bordado',
      'Logo Wings en el tobillo'
    ],
    isLimited: true,
    isNew: false,
    isVip: true,
    isExclusive: false,
    stock: 15,
    rating: 4.9,
    reviews: 1247,
    releaseDate: '2024-01-15',
    sku: 'TWL-JOR-001',
    tags: ['basketball', 'retro', 'chicago', 'og', 'high-top']
  },
  {
    id: 'gucci-ace-sneaker-bee',
    name: 'Ace Sneaker with Bee Embroidery',
    brand: 'Gucci',
    brandId: 'gucci',
    category: 'luxury',
    gender: 'unisex',
    price: 18500,
    originalPrice: null,
    currency: 'MXN',
    images: [
      '/products/gucci-ace-bee-1.jpg',
      '/products/gucci-ace-bee-2.jpg',
      '/products/gucci-ace-bee-3.jpg',
    ],
    colors: ['Blanco/Oro', 'Negro/Oro'],
    sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
    description: 'Sneaker de lujo con bordado de abeja dorada, símbolo icónico de la casa Gucci.',
    features: [
      'Cuero italiano premium',
      'Bordado de abeja dorado',
      'Suela de goma blanca',
      'Forro de piel',
      'Made in Italy'
    ],
    isLimited: false,
    isNew: true,
    isVip: true,
    isExclusive: true,
    stock: 8,
    rating: 4.7,
    reviews: 89,
    releaseDate: '2024-02-01',
    sku: 'TWL-GUC-001',
    tags: ['luxury', 'italian', 'bee', 'embroidery', 'designer']
  },
  {
    id: 'nike-dunk-low-panda',
    name: 'Dunk Low "Panda"',
    brand: 'Nike',
    brandId: 'nike',
    category: 'sneakers',
    gender: 'unisex',
    price: 2800,
    originalPrice: 3200,
    currency: 'MXN',
    images: [
      '/products/nike-dunk-panda-1.jpg',
      '/products/nike-dunk-panda-2.jpg',
      '/products/nike-dunk-panda-3.jpg',
    ],
    colors: ['Blanco/Negro'],
    sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
    description: 'El colorway más popular del Nike Dunk Low. Perfecto para cualquier outfit.',
    features: [
      'Cuero sintético duradero',
      'Suela de goma con tracción',
      'Diseño clásico de basketball',
      'Swoosh contrastante',
      'Collar acolchado'
    ],
    isLimited: false,
    isNew: false,
    isVip: false,
    isExclusive: false,
    stock: 45,
    rating: 4.6,
    reviews: 2156,
    releaseDate: '2023-08-15',
    sku: 'TWL-NIK-001',
    tags: ['basketball', 'panda', 'classic', 'versatile', 'popular']
  },
  {
    id: 'yeezy-boost-350-v2-zebra',
    name: 'Yeezy Boost 350 V2 "Zebra"',
    brand: 'Yeezy',
    brandId: 'yeezy',
    category: 'limited',
    gender: 'unisex',
    price: 8900,
    originalPrice: 10500,
    currency: 'MXN',
    images: [
      '/products/yeezy-zebra-1.jpg',
      '/products/yeezy-zebra-2.jpg',
      '/products/yeezy-zebra-3.jpg',
    ],
    colors: ['Blanco/Negro/Rojo'],
    sizes: ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'],
    description: 'Uno de los colorways más icónicos de Yeezy. Diseño de Kanye West con tecnología Boost.',
    features: [
      'Tecnología Boost de Adidas',
      'Upper de Primeknit',
      'Patrón zebra único',
      'Suela translúcida',
      'Fit ceñido'
    ],
    isLimited: true,
    isNew: false,
    isVip: true,
    isExclusive: true,
    stock: 6,
    rating: 4.8,
    reviews: 892,
    releaseDate: '2023-12-10',
    sku: 'TWL-YEE-001',
    tags: ['yeezy', 'kanye', 'boost', 'zebra', 'limited', 'hype']
  },
  {
    id: 'dior-b23-high-top',
    name: 'B23 High-Top Sneaker Oblique',
    brand: 'Dior',
    brandId: 'dior',
    category: 'luxury',
    gender: 'men',
    price: 32000,
    originalPrice: null,
    currency: 'MXN',
    images: [
      '/products/dior-b23-1.jpg',
      '/products/dior-b23-2.jpg',
      '/products/dior-b23-3.jpg',
    ],
    colors: ['Azul Oblique', 'Negro Oblique'],
    sizes: ['7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
    description: 'Sneaker de alta gama con el icónico patrón Oblique de Dior. Lujo francés en cada detalle.',
    features: [
      'Patrón Oblique de Dior',
      'Cuero italiano premium',
      'Suela de goma técnica',
      'Forro de piel de becerro',
      'Made in Italy',
      'Caja de lujo incluida'
    ],
    isLimited: true,
    isNew: true,
    isVip: true,
    isExclusive: true,
    stock: 3,
    rating: 4.9,
    reviews: 34,
    releaseDate: '2024-01-20',
    sku: 'TWL-DIO-001',
    tags: ['dior', 'luxury', 'oblique', 'french', 'high-top', 'premium']
  },
  {
    id: 'off-white-out-of-office',
    name: 'Out Of Office "For Walking"',
    brand: 'Off-White',
    brandId: 'off-white',
    category: 'limited',
    gender: 'unisex',
    price: 12500,
    originalPrice: 14000,
    currency: 'MXN',
    images: [
      '/products/off-white-ooo-1.jpg',
      '/products/off-white-ooo-2.jpg',
      '/products/off-white-ooo-3.jpg',
    ],
    colors: ['Blanco/Negro', 'Negro/Blanco'],
    sizes: ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11'],
    description: 'Diseño deconstructivista de Virgil Abloh. Streetwear de lujo con estética industrial.',
    features: [
      'Diseño deconstructivista',
      'Texto "For Walking"',
      'Suela chunky',
      'Materiales mixtos',
      'Estética industrial',
      'Firma de Virgil Abloh'
    ],
    isLimited: true,
    isNew: false,
    isVip: true,
    isExclusive: true,
    stock: 12,
    rating: 4.7,
    reviews: 156,
    releaseDate: '2023-11-05',
    sku: 'TWL-OFF-001',
    tags: ['off-white', 'virgil', 'deconstructed', 'streetwear', 'chunky', 'industrial']
  }
]

// Utility functions
export const getProductById = (id) => {
  return mockProducts.find(product => product.id === id)
}

export const getProductsByBrand = (brandId) => {
  return mockProducts.filter(product => product.brandId === brandId)
}

export const getProductsByCategory = (category) => {
  return mockProducts.filter(product => product.category === category)
}

export const getFeaturedProducts = () => {
  return mockProducts.filter(product => product.isVip || product.isLimited)
}

export const getNewProducts = () => {
  return mockProducts.filter(product => product.isNew)
}

export const searchProducts = (query) => {
  const lowercaseQuery = query.toLowerCase()
  return mockProducts.filter(product => 
    product.name.toLowerCase().includes(lowercaseQuery) ||
    product.brand.toLowerCase().includes(lowercaseQuery) ||
    product.tags.some(tag => tag.toLowerCase().includes(lowercaseQuery))
  )
}

export const filterProducts = (filters) => {
  let filtered = [...mockProducts]
  
  if (filters.brand) {
    filtered = filtered.filter(product => product.brandId === filters.brand)
  }
  
  if (filters.category) {
    filtered = filtered.filter(product => product.category === filters.category)
  }
  
  if (filters.gender) {
    filtered = filtered.filter(product => 
      product.gender === filters.gender || product.gender === 'unisex'
    )
  }
  
  if (filters.priceRange) {
    filtered = filtered.filter(product => 
      product.price >= filters.priceRange.min && 
      product.price <= filters.priceRange.max
    )
  }
  
  if (filters.size) {
    filtered = filtered.filter(product => 
      product.sizes.includes(filters.size)
    )
  }
  
  if (filters.isLimited) {
    filtered = filtered.filter(product => product.isLimited)
  }
  
  if (filters.isNew) {
    filtered = filtered.filter(product => product.isNew)
  }
  
  return filtered
}

export const sortProducts = (products, sortBy) => {
  const sorted = [...products]
  
  switch (sortBy) {
    case 'price-low':
      return sorted.sort((a, b) => a.price - b.price)
    case 'price-high':
      return sorted.sort((a, b) => b.price - a.price)
    case 'name':
      return sorted.sort((a, b) => a.name.localeCompare(b.name))
    case 'brand':
      return sorted.sort((a, b) => a.brand.localeCompare(b.brand))
    case 'newest':
      return sorted.sort((a, b) => new Date(b.releaseDate) - new Date(a.releaseDate))
    case 'rating':
      return sorted.sort((a, b) => b.rating - a.rating)
    default:
      return sorted
  }
}
