'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
// Mock products data
const products = [
  { id: 1, name: 'Nike Air Force 1', brand: 'Nike', price: 2500, category: 'sneakers', tags: ['casual', 'white'], rating: 4.5, reviews: 120 },
  { id: 2, name: 'Adidas Stan Smith', brand: 'Adidas', price: 2200, category: 'sneakers', tags: ['casual', 'white'], rating: 4.3, reviews: 95 },
  { id: 3, name: 'Gucci Ace Sneakers', brand: 'Gucci', price: 15000, category: 'luxury', tags: ['designer', 'white'], rating: 4.8, reviews: 45 },
  { id: 4, name: 'Nike Air Max 90', brand: 'Nike', price: 2800, category: 'sneakers', tags: ['athletic', 'colorful'], rating: 4.4, reviews: 88 },
  { id: 5, name: 'Converse Chuck <PERSON>', brand: 'Converse', price: 1800, category: 'casual', tags: ['classic', 'canvas'], rating: 4.2, reviews: 156 },
  { id: 6, name: 'Vans Old Skool', brand: 'Vans', price: 1900, category: 'casual', tags: ['skate', 'classic'], rating: 4.1, reviews: 78 }
]
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

export default function SmartRecommendations({ 
  currentProduct = null, 
  type = 'personalized', 
  limit = 6,
  title = 'Recomendaciones para Ti'
}) {
  const { stylePreferences, recentlyViewed, matchesPreferences } = useUserPreferences()
  const { addToCart } = useCart()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()
  const [recommendations, setRecommendations] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    generateRecommendations()
  }, [currentProduct, type, stylePreferences, recentlyViewed])

  const generateRecommendations = async () => {
    setIsLoading(true)
    
    // Simulate AI processing delay
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    let recommendedProducts = []
    
    switch (type) {
      case 'personalized':
        recommendedProducts = getPersonalizedRecommendations()
        break
      case 'similar':
        recommendedProducts = getSimilarProducts()
        break
      case 'trending':
        recommendedProducts = getTrendingProducts()
        break
      case 'recently-viewed':
        recommendedProducts = getRecentlyViewedSimilar()
        break
      case 'complete-look':
        recommendedProducts = getCompleteLookRecommendations()
        break
      default:
        recommendedProducts = getPersonalizedRecommendations()
    }
    
    setRecommendations(recommendedProducts.slice(0, limit))
    setIsLoading(false)
  }

  const getPersonalizedRecommendations = () => {
    const { favoritebrands, preferredStyles, preferredColors, priceRange } = stylePreferences
    
    return products
      .filter(product => currentProduct ? product.id !== currentProduct.id : true)
      .map(product => ({
        ...product,
        score: calculatePersonalizationScore(product)
      }))
      .sort((a, b) => b.score - a.score)
  }

  const calculatePersonalizationScore = (product) => {
    let score = 0
    const { favoritebrands, preferredStyles, preferredColors, priceRange } = stylePreferences
    
    // Brand preference (30% weight)
    if (favoritebrands.includes(product.brand.toLowerCase())) {
      score += 30
    }
    
    // Style preference (25% weight)
    if (preferredStyles.some(style => 
      product.category?.toLowerCase().includes(style.toLowerCase()) ||
      product.name?.toLowerCase().includes(style.toLowerCase())
    )) {
      score += 25
    }
    
    // Color preference (20% weight)
    if (preferredColors.some(color => 
      product.name?.toLowerCase().includes(color.toLowerCase())
    )) {
      score += 20
    }
    
    // Price range (15% weight)
    if (product.price >= priceRange.min && product.price <= priceRange.max) {
      score += 15
    }
    
    // Recently viewed brands (10% weight)
    const recentBrands = recentlyViewed.map(p => p.brand.toLowerCase())
    if (recentBrands.includes(product.brand.toLowerCase())) {
      score += 10
    }
    
    return score
  }

  const getSimilarProducts = () => {
    if (!currentProduct) return products.slice(0, limit)
    
    return products
      .filter(product => product.id !== currentProduct.id)
      .map(product => ({
        ...product,
        similarity: calculateSimilarity(product, currentProduct)
      }))
      .sort((a, b) => b.similarity - a.similarity)
  }

  const calculateSimilarity = (product1, product2) => {
    let similarity = 0
    
    // Same brand (40% weight)
    if (product1.brand === product2.brand) similarity += 40
    
    // Same category (30% weight)
    if (product1.category === product2.category) similarity += 30
    
    // Similar price range (20% weight)
    const priceDiff = Math.abs(product1.price - product2.price)
    const maxPrice = Math.max(product1.price, product2.price)
    const priceScore = Math.max(0, 20 - (priceDiff / maxPrice) * 20)
    similarity += priceScore
    
    // Similar tags (10% weight)
    const commonTags = product1.tags?.filter(tag => product2.tags?.includes(tag)) || []
    similarity += (commonTags.length / Math.max(product1.tags?.length || 1, product2.tags?.length || 1)) * 10
    
    return similarity
  }

  const getTrendingProducts = () => {
    // Mock trending algorithm based on views, ratings, and recency
    return products
      .map(product => ({
        ...product,
        trendingScore: (product.rating * 20) + (product.reviews * 0.1) + (Math.random() * 10)
      }))
      .sort((a, b) => b.trendingScore - a.trendingScore)
  }

  const getRecentlyViewedSimilar = () => {
    if (recentlyViewed.length === 0) return getTrendingProducts()
    
    const recentProduct = recentlyViewed[0]
    return getSimilarProducts().map(product => ({
      ...product,
      reason: `Similar a ${recentProduct.name}`
    }))
  }

  const getCompleteLookRecommendations = () => {
    if (!currentProduct) return products.slice(0, limit)
    
    // Recommend complementary items
    const complementaryCategories = {
      'sneakers': ['casual', 'athletic'],
      'formal': ['dress', 'business'],
      'casual': ['sneakers', 'lifestyle']
    }
    
    const currentCategory = currentProduct.category?.toLowerCase() || 'sneakers'
    const targetCategories = complementaryCategories[currentCategory] || ['casual']
    
    return products
      .filter(product => 
        product.id !== currentProduct.id &&
        targetCategories.some(cat => product.category?.toLowerCase().includes(cat))
      )
      .slice(0, limit)
  }

  const handleAddToCart = (product) => {
    const defaultSize = product.sizes?.[0] || 'M'
    addToCart(product, defaultSize, 1)
  }

  const handleWishlistToggle = (product) => {
    if (isInWishlist(product.id)) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product)
    }
  }

  const getRecommendationReason = (product, index) => {
    switch (type) {
      case 'personalized':
        if (stylePreferences.favoritebrands.includes(product.brand.toLowerCase())) {
          return `Tu marca favorita: ${product.brand}`
        }
        if (product.score > 50) {
          return 'Perfecto para tu estilo'
        }
        return 'Recomendado para ti'
      
      case 'similar':
        return `Similar a ${currentProduct?.name}`
      
      case 'trending':
        return `Tendencia #${index + 1}`
      
      case 'recently-viewed':
        return product.reason || 'Basado en tu historial'
      
      case 'complete-look':
        return 'Completa tu look'
      
      default:
        return 'Recomendado'
    }
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
          {title}
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(limit)].map((_, index) => (
            <Card key={index} variant="glass" className="animate-pulse">
              <CardContent className="p-4">
                <div className="aspect-square bg-warm-camel/20 rounded-lg mb-4" />
                <div className="space-y-2">
                  <div className="h-4 bg-warm-camel/20 rounded w-3/4" />
                  <div className="h-3 bg-warm-camel/20 rounded w-1/2" />
                  <div className="h-4 bg-warm-camel/20 rounded w-1/3" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (recommendations.length === 0) {
    return null
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
          {title}
        </h2>
        <div className="flex items-center gap-2 text-sm text-warm-camel">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          Powered by IA
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {recommendations.map((product, index) => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card variant="glass" className="group hover:shadow-lg transition-all duration-300">
              <CardContent className="p-4">
                
                {/* Recommendation Reason */}
                <div className="mb-3">
                  <span className="text-xs bg-rich-gold/20 text-rich-gold px-2 py-1 rounded-full">
                    {getRecommendationReason(product, index)}
                  </span>
                </div>
                
                {/* Product Image */}
                <div className="relative mb-4">
                  <TransitionLink href={`/product/${product.id}`}>
                    <div className="aspect-square bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray rounded-lg flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                      <span className="text-warm-camel font-medium text-center px-4">
                        {product.name}
                      </span>
                    </div>
                  </TransitionLink>
                  
                  {/* Wishlist Button */}
                  <motion.button
                    onClick={() => handleWishlistToggle(product)}
                    className={`absolute top-2 right-2 p-2 rounded-full backdrop-blur-sm transition-all duration-200 ${
                      isInWishlist(product.id)
                        ? 'bg-rich-gold text-forest-emerald'
                        : 'bg-white/20 text-white hover:bg-white/30'
                    }`}
                    whileHover={{ scale: 1.1 }}
                    whileTap={{ scale: 0.9 }}
                  >
                    <svg className="w-4 h-4" fill={isInWishlist(product.id) ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </motion.button>
                </div>

                {/* Product Info */}
                <div className="space-y-2">
                  <TransitionLink href={`/product/${product.id}`}>
                    <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray group-hover:text-rich-gold transition-colors duration-200 line-clamp-2">
                      {product.name}
                    </h3>
                  </TransitionLink>
                  
                  <p className="text-warm-camel text-sm">{product.brand}</p>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-lg font-bold text-forest-emerald dark:text-light-cloud-gray">
                        ${product.price.toLocaleString()} MXN
                      </p>
                      {product.originalPrice && product.originalPrice > product.price && (
                        <p className="text-sm text-warm-camel line-through">
                          ${product.originalPrice.toLocaleString()} MXN
                        </p>
                      )}
                    </div>
                    
                    {/* Match Score */}
                    {type === 'personalized' && product.score > 0 && (
                      <div className="text-xs text-rich-gold font-medium">
                        {Math.round(product.score)}% match
                      </div>
                    )}
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex gap-2 mt-4">
                  <TransitionLink href={`/product/${product.id}`} className="flex-1">
                    <AnimatedButton
                      variant="outline"
                      size="sm"
                      className="w-full"
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                      }
                    >
                      Ver
                    </AnimatedButton>
                  </TransitionLink>

                  <AnimatedButton
                    variant="primary"
                    size="sm"
                    onClick={() => handleAddToCart(product)}
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    }
                  >
                    Agregar
                  </AnimatedButton>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
