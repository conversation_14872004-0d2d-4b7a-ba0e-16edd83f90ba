/**
 * TWL Security Audit Script
 * Comprehensive security analysis for enterprise-grade applications
 */

const fs = require('fs')
const path = require('path')

async function runSecurityAudit() {
  console.log('🔒 TWL SECURITY AUDIT')
  console.log('=' .repeat(60))

  const auditResults = {
    secretsCheck: [],
    dependencyCheck: [],
    codeSecurityCheck: [],
    configurationCheck: [],
    errors: [],
    warnings: [],
    securityScore: 0
  }

  try {
    // Test 1: Check for exposed secrets
    console.log('\n1. 🔐 Checking for Exposed Secrets...')
    await checkExposedSecrets(auditResults)

    // Test 2: Check dependencies for vulnerabilities
    console.log('\n2. 📦 Checking Dependencies...')
    await checkDependencies(auditResults)

    // Test 3: Code security analysis
    console.log('\n3. 🛡️ Code Security Analysis...')
    await checkCodeSecurity(auditResults)

    // Test 4: Configuration security
    console.log('\n4. ⚙️ Configuration Security...')
    await checkConfiguration(auditResults)

    // Generate security report
    generateSecurityReport(auditResults)

  } catch (error) {
    console.error('❌ Security audit failed:', error)
    auditResults.errors.push(`Security audit error: ${error.message}`)
  }

  return auditResults
}

async function checkExposedSecrets(auditResults) {
  const secretPatterns = [
    { name: 'API Keys', pattern: /api[_-]?key[_-]?[=:]\s*['"]\w+['"]/, severity: 'HIGH' },
    { name: 'Database URLs', pattern: /database[_-]?url[_-]?[=:]\s*['"]\w+['"]/, severity: 'HIGH' },
    { name: 'JWT Secrets', pattern: /jwt[_-]?secret[_-]?[=:]\s*['"]\w+['"]/, severity: 'HIGH' },
    { name: 'Private Keys', pattern: /-----BEGIN\s+(RSA\s+)?PRIVATE\s+KEY-----/, severity: 'CRITICAL' },
    { name: 'AWS Keys', pattern: /AKIA[0-9A-Z]{16}/, severity: 'HIGH' },
    { name: 'Stripe Keys', pattern: /sk_live_[0-9a-zA-Z]{24}/, severity: 'HIGH' },
    { name: 'Supabase Keys', pattern: /eyJ[A-Za-z0-9_-]*\.eyJ[A-Za-z0-9_-]*\.[A-Za-z0-9_-]*/, severity: 'MEDIUM' }
  ]

  const filesToCheck = [
    '.env',
    '.env.local',
    '.env.production',
    'next.config.js',
    'package.json'
  ]

  for (const file of filesToCheck) {
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8')
        
        for (const pattern of secretPatterns) {
          const matches = content.match(pattern.pattern)
          if (matches) {
            const result = {
              file,
              type: pattern.name,
              severity: pattern.severity,
              found: matches.length,
              safe: file.includes('.env') // .env files are expected to have secrets
            }
            
            auditResults.secretsCheck.push(result)
            
            if (!result.safe) {
              console.log(`    ⚠️ ${pattern.name} found in ${file} (${pattern.severity})`)
              auditResults.warnings.push(`Potential secret exposure: ${pattern.name} in ${file}`)
            } else {
              console.log(`    ✅ ${pattern.name} properly stored in ${file}`)
            }
          }
        }
      } else {
        console.log(`    ℹ️ ${file} not found (OK)`)
      }
    } catch (error) {
      auditResults.errors.push(`Secret check error for ${file}: ${error.message}`)
    }
  }
}

async function checkDependencies(auditResults) {
  try {
    if (fs.existsSync('package.json')) {
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'))
      
      // Check for known vulnerable packages
      const vulnerablePackages = [
        { name: 'lodash', versions: ['<4.17.21'], issue: 'Prototype pollution' },
        { name: 'axios', versions: ['<0.21.1'], issue: 'SSRF vulnerability' },
        { name: 'node-fetch', versions: ['<2.6.7'], issue: 'Information exposure' },
        { name: 'minimist', versions: ['<1.2.6'], issue: 'Prototype pollution' }
      ]

      const allDeps = {
        ...packageJson.dependencies || {},
        ...packageJson.devDependencies || {}
      }

      for (const [depName, depVersion] of Object.entries(allDeps)) {
        const vulnerable = vulnerablePackages.find(pkg => pkg.name === depName)
        
        if (vulnerable) {
          const result = {
            package: depName,
            currentVersion: depVersion,
            vulnerableVersions: vulnerable.versions,
            issue: vulnerable.issue,
            severity: 'MEDIUM'
          }
          
          auditResults.dependencyCheck.push(result)
          console.log(`    ⚠️ ${depName}@${depVersion} - ${vulnerable.issue}`)
        }
      }

      // Check for outdated Next.js
      if (allDeps['next']) {
        const nextVersion = allDeps['next'].replace(/[^0-9.]/g, '')
        const majorVersion = parseInt(nextVersion.split('.')[0])
        
        if (majorVersion < 14) {
          auditResults.dependencyCheck.push({
            package: 'next',
            currentVersion: allDeps['next'],
            issue: 'Outdated Next.js version',
            severity: 'MEDIUM'
          })
          console.log(`    ⚠️ Next.js version ${allDeps['next']} is outdated`)
        } else {
          console.log(`    ✅ Next.js version ${allDeps['next']} is current`)
        }
      }

    } else {
      auditResults.errors.push('package.json not found')
    }
  } catch (error) {
    auditResults.errors.push(`Dependency check error: ${error.message}`)
  }
}

async function checkCodeSecurity(auditResults) {
  const securityIssues = [
    { name: 'eval() usage', pattern: /\beval\s*\(/, severity: 'HIGH', description: 'eval() can execute arbitrary code' },
    { name: 'innerHTML usage', pattern: /\.innerHTML\s*=/, severity: 'MEDIUM', description: 'innerHTML can lead to XSS' },
    { name: 'document.write', pattern: /document\.write\s*\(/, severity: 'MEDIUM', description: 'document.write can be unsafe' },
    { name: 'Hardcoded passwords', pattern: /password\s*[=:]\s*['"][^'"]+['"]/, severity: 'HIGH', description: 'Hardcoded credentials' },
    { name: 'SQL injection risk', pattern: /query\s*\+\s*['"]/, severity: 'HIGH', description: 'Potential SQL injection' },
    { name: 'Unsafe regex', pattern: /new\s+RegExp\s*\(\s*[^)]*\+/, severity: 'MEDIUM', description: 'Dynamic regex can cause ReDoS' }
  ]

  const filesToScan = []
  
  // Recursively find JS/TS files
  function findFiles(dir, extensions = ['.js', '.jsx', '.ts', '.tsx']) {
    try {
      const items = fs.readdirSync(dir, { withFileTypes: true })
      
      for (const item of items) {
        const fullPath = path.join(dir, item.name)
        
        if (item.isDirectory() && !item.name.startsWith('.') && item.name !== 'node_modules') {
          findFiles(fullPath, extensions)
        } else if (item.isFile() && extensions.some(ext => item.name.endsWith(ext))) {
          filesToScan.push(fullPath)
        }
      }
    } catch (error) {
      // Skip directories that can't be read
    }
  }

  findFiles('app')
  findFiles('components')
  findFiles('lib')

  for (const file of filesToScan.slice(0, 50)) { // Limit to first 50 files for performance
    try {
      const content = fs.readFileSync(file, 'utf8')
      
      for (const issue of securityIssues) {
        const matches = content.match(new RegExp(issue.pattern, 'g'))
        if (matches) {
          const result = {
            file: file.replace(process.cwd(), ''),
            issue: issue.name,
            severity: issue.severity,
            description: issue.description,
            occurrences: matches.length
          }
          
          auditResults.codeSecurityCheck.push(result)
          console.log(`    ⚠️ ${issue.name} in ${result.file} (${matches.length} occurrences)`)
        }
      }
    } catch (error) {
      // Skip files that can't be read
    }
  }

  if (auditResults.codeSecurityCheck.length === 0) {
    console.log('    ✅ No obvious security issues found in code')
  }
}

async function checkConfiguration(auditResults) {
  const configChecks = [
    {
      name: 'Next.js Security Headers',
      file: 'next.config.js',
      check: (content) => content.includes('headers') && content.includes('X-Frame-Options'),
      severity: 'MEDIUM'
    },
    {
      name: 'HTTPS Enforcement',
      file: 'next.config.js',
      check: (content) => content.includes('redirects') || content.includes('https'),
      severity: 'LOW'
    },
    {
      name: 'Environment Variables',
      file: '.env.example',
      check: () => fs.existsSync('.env.example'),
      severity: 'LOW'
    },
    {
      name: 'Git Ignore',
      file: '.gitignore',
      check: (content) => content.includes('.env') && content.includes('node_modules'),
      severity: 'MEDIUM'
    }
  ]

  for (const check of configChecks) {
    try {
      let passed = false
      let content = ''
      
      if (check.file && fs.existsSync(check.file)) {
        content = fs.readFileSync(check.file, 'utf8')
        passed = check.check(content)
      } else if (check.check) {
        passed = check.check()
      }

      const result = {
        name: check.name,
        file: check.file,
        passed,
        severity: check.severity
      }

      auditResults.configurationCheck.push(result)
      
      if (passed) {
        console.log(`    ✅ ${check.name}`)
      } else {
        console.log(`    ⚠️ ${check.name} - Not configured`)
        auditResults.warnings.push(`Configuration issue: ${check.name}`)
      }
    } catch (error) {
      auditResults.errors.push(`Configuration check error for ${check.name}: ${error.message}`)
    }
  }
}

function generateSecurityReport(auditResults) {
  console.log('\n🔒 SECURITY AUDIT REPORT')
  console.log('=' .repeat(60))

  // Calculate security score
  let totalChecks = 0
  let passedChecks = 0

  // Secrets check
  const safeSecrets = auditResults.secretsCheck.filter(s => s.safe).length
  const unsafeSecrets = auditResults.secretsCheck.filter(s => !s.safe).length
  totalChecks += auditResults.secretsCheck.length || 1
  passedChecks += safeSecrets

  // Dependencies check
  totalChecks += 1
  if (auditResults.dependencyCheck.length === 0) passedChecks += 1

  // Code security check
  totalChecks += 1
  if (auditResults.codeSecurityCheck.length === 0) passedChecks += 1

  // Configuration check
  const configPassed = auditResults.configurationCheck.filter(c => c.passed).length
  totalChecks += auditResults.configurationCheck.length
  passedChecks += configPassed

  auditResults.securityScore = totalChecks > 0 ? ((passedChecks / totalChecks) * 100).toFixed(1) : 0

  console.log(`\n📊 Security Summary:`)
  console.log(`  Security Score: ${auditResults.securityScore}%`)
  console.log(`  Secrets Check: ${safeSecrets} safe, ${unsafeSecrets} unsafe`)
  console.log(`  Dependencies: ${auditResults.dependencyCheck.length} issues found`)
  console.log(`  Code Security: ${auditResults.codeSecurityCheck.length} issues found`)
  console.log(`  Configuration: ${configPassed}/${auditResults.configurationCheck.length} checks passed`)
  console.log(`  Errors: ${auditResults.errors.length}`)
  console.log(`  Warnings: ${auditResults.warnings.length}`)

  const grade = auditResults.securityScore >= 90 ? '🛡️ EXCELLENT' :
                auditResults.securityScore >= 75 ? '✅ GOOD' :
                auditResults.securityScore >= 60 ? '⚠️ NEEDS IMPROVEMENT' : '❌ CRITICAL ISSUES'

  console.log(`\n🎯 Security Grade: ${grade}`)

  if (auditResults.securityScore >= 75) {
    console.log('\n🚀 Security posture is good for production deployment!')
  } else {
    console.log('\n⚠️ Address security issues before production deployment')
  }

  // Top recommendations
  console.log('\n💡 Security Recommendations:')
  if (unsafeSecrets > 0) {
    console.log('  - Move all secrets to environment variables')
  }
  if (auditResults.dependencyCheck.length > 0) {
    console.log('  - Update vulnerable dependencies')
  }
  if (auditResults.codeSecurityCheck.length > 0) {
    console.log('  - Review and fix code security issues')
  }
  console.log('  - Implement Content Security Policy (CSP)')
  console.log('  - Enable security headers in Next.js config')
  console.log('  - Set up automated security scanning in CI/CD')

  return auditResults.securityScore
}

// Run audit if this file is executed directly
if (require.main === module) {
  runSecurityAudit()
    .then(results => {
      const success = results.securityScore >= 75
      console.log(`\n${success ? '🛡️' : '⚠️'} Security audit completed`)
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('❌ Security audit execution failed:', error)
      process.exit(1)
    })
}

module.exports = { runSecurityAudit }
