'use client'

import { motion } from 'framer-motion'

// Base Skeleton Component
function Skeleton({ className = '', animate = true, ...props }) {
  return (
    <div
      className={`bg-gradient-to-r from-light-gray via-neutral-200 to-light-gray dark:from-neutral-700 dark:via-neutral-600 dark:to-neutral-700 ${
        animate ? 'animate-pulse' : ''
      } ${className}`}
      {...props}
    />
  )
}

// Enhanced Skeleton with shimmer effect
function ShimmerSkeleton({ className = '', ...props }) {
  return (
    <div className={`relative overflow-hidden ${className}`} {...props}>
      <div className="absolute inset-0 bg-light-gray dark:bg-neutral-700" />
      <motion.div
        className="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 dark:via-white/10 to-transparent"
        animate={{
          x: ['-100%', '100%']
        }}
        transition={{
          duration: 1.5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </div>
  )
}

// Product Card Skeleton
export function ProductCardSkeleton({ className = '' }) {
  return (
    <div className={`bg-pure-white dark:bg-neutral-800 rounded-2xl overflow-hidden shadow-sm ${className}`}>
      {/* Image Skeleton */}
      <div className="relative aspect-[4/3]">
        <ShimmerSkeleton className="w-full h-full" />
        
        {/* Heart icon skeleton */}
        <div className="absolute top-3 right-3">
          <Skeleton className="w-8 h-8 rounded-full" />
        </div>
        
        {/* Add to cart button skeleton */}
        <div className="absolute bottom-3 right-3">
          <Skeleton className="w-12 h-12 rounded-full" />
        </div>
      </div>
      
      {/* Content Skeleton */}
      <div className="p-4 space-y-3">
        {/* Title and badge */}
        <div className="flex items-start justify-between">
          <Skeleton className="h-4 w-3/4 rounded" />
          <Skeleton className="h-6 w-16 rounded" />
        </div>
        
        {/* Brand */}
        <Skeleton className="h-3 w-1/2 rounded" />
        
        {/* Gender */}
        <Skeleton className="h-3 w-1/3 rounded" />
        
        {/* Price */}
        <div className="flex items-center gap-2">
          <Skeleton className="h-5 w-16 rounded" />
          <Skeleton className="h-4 w-12 rounded" />
        </div>
      </div>
    </div>
  )
}

// Product Grid Skeleton
export function ProductGridSkeleton({ count = 8, columns = 5 }) {
  const gridCols = {
    2: 'grid-cols-2',
    3: 'grid-cols-2 md:grid-cols-3',
    4: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4',
    5: 'grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'
  }

  return (
    <div className={`grid gap-4 sm:gap-6 lg:gap-8 xl:gap-10 ${gridCols[columns] || gridCols[5]}`}>
      {[...Array(count)].map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: index * 0.1 }}
        >
          <ProductCardSkeleton />
        </motion.div>
      ))}
    </div>
  )
}

// Wishlist Card Skeleton
export function WishlistCardSkeleton() {
  return (
    <div className="bg-pure-white dark:bg-neutral-800 rounded-2xl overflow-hidden shadow-sm">
      {/* Preview Images Grid Skeleton */}
      <div className="relative h-48">
        <div className="grid grid-cols-2 h-full">
          {[...Array(4)].map((_, idx) => (
            <ShimmerSkeleton key={idx} className="w-full h-full" />
          ))}
        </div>
        
        {/* Badge skeleton */}
        <div className="absolute top-3 right-3">
          <Skeleton className="h-6 w-20 rounded-full" />
        </div>
      </div>
      
      {/* Content Skeleton */}
      <div className="p-4 space-y-3">
        <div className="flex items-start justify-between">
          <Skeleton className="h-5 w-3/4 rounded" />
          <Skeleton className="w-4 h-4 rounded" />
        </div>
        
        <Skeleton className="h-3 w-full rounded" />
        <Skeleton className="h-3 w-2/3 rounded" />
        
        <div className="flex items-center justify-between">
          <Skeleton className="h-3 w-16 rounded" />
          <Skeleton className="h-3 w-20 rounded" />
        </div>
      </div>
    </div>
  )
}

// Cart Item Skeleton
export function CartItemSkeleton() {
  return (
    <div className="bg-pure-white dark:bg-neutral-800 rounded-xl p-4">
      <div className="flex gap-4">
        {/* Image skeleton */}
        <ShimmerSkeleton className="w-20 h-20 rounded-lg flex-shrink-0" />
        
        {/* Content skeleton */}
        <div className="flex-1 space-y-2">
          <Skeleton className="h-4 w-3/4 rounded" />
          <Skeleton className="h-3 w-1/2 rounded" />
          <div className="flex items-center gap-2">
            <Skeleton className="h-4 w-16 rounded" />
            <Skeleton className="h-3 w-12 rounded" />
          </div>
        </div>
        
        {/* Controls skeleton */}
        <div className="flex flex-col items-end gap-2">
          <Skeleton className="w-6 h-6 rounded" />
          <div className="flex items-center gap-2">
            <Skeleton className="w-8 h-8 rounded" />
            <Skeleton className="w-8 h-4 rounded" />
            <Skeleton className="w-8 h-8 rounded" />
          </div>
        </div>
      </div>
    </div>
  )
}

// Search Results Skeleton
export function SearchResultsSkeleton({ count = 5 }) {
  return (
    <div className="space-y-4">
      {[...Array(count)].map((_, index) => (
        <motion.div
          key={index}
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          transition={{ delay: index * 0.1 }}
          className="flex items-center space-x-4 p-3"
        >
          <ShimmerSkeleton className="w-16 h-16 rounded-lg flex-shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 w-3/4 rounded" />
            <Skeleton className="h-3 w-1/2 rounded" />
            <Skeleton className="h-3 w-1/4 rounded" />
          </div>
        </motion.div>
      ))}
    </div>
  )
}

// Hero Section Skeleton
export function HeroSkeleton() {
  return (
    <div className="min-h-screen pt-32 bg-pure-white dark:bg-dark-gray flex items-center justify-center">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Left content skeleton */}
          <div className="space-y-8 text-center lg:text-left">
            <div className="space-y-4">
              <Skeleton className="h-16 w-full rounded" />
              <Skeleton className="h-12 w-3/4 rounded" />
            </div>
            <Skeleton className="h-6 w-full rounded" />
            <div className="flex flex-col sm:flex-row gap-6 justify-center lg:justify-start">
              <Skeleton className="h-12 w-48 rounded-xl" />
              <Skeleton className="h-12 w-48 rounded-xl" />
            </div>
          </div>
          
          {/* Right content skeleton */}
          <div className="relative">
            <ShimmerSkeleton className="w-full h-[450px] rounded-2xl" />
          </div>
        </div>
      </div>
    </div>
  )
}

// Page Header Skeleton
export function PageHeaderSkeleton() {
  return (
    <div className="py-16 text-center">
      <div className="max-w-3xl mx-auto space-y-4">
        <Skeleton className="h-12 w-3/4 mx-auto rounded" />
        <Skeleton className="h-6 w-1/2 mx-auto rounded" />
      </div>
    </div>
  )
}

// Navigation Skeleton
export function NavigationSkeleton() {
  return (
    <div className="flex items-center justify-between p-4">
      <Skeleton className="h-8 w-32 rounded" />
      <div className="flex items-center gap-4">
        <Skeleton className="h-8 w-20 rounded" />
        <Skeleton className="h-8 w-20 rounded" />
        <Skeleton className="h-8 w-8 rounded-full" />
      </div>
    </div>
  )
}

export default Skeleton
