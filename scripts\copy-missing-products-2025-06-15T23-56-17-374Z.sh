#!/bin/bash
# ENTERPRISE MISSING PRODUCTS COPY SCRIPT
# Generated by Missing Products Scanner

echo "🚀 Copying missing products from CYTTE materials..."
echo "=================================================="

mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\BD7700-222 -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\BD7700-222 -- <PERSON><PERSON>"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\JGD212-EJD -- GUCCI" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\JGD212-EJD -- GUCCI"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\JGD212-ZZF -- GUCCI" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\JGD212-ZZF -- GUCCI"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED212-EDR -- GUCCI" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED212-EDR -- GUCCI"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED212-EJD -- GUCCI" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED212-EJD -- GUCCI"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED212-JJG -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED212-JJG -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED482-JDK -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED482-JDK -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED482-JJJ -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\1. GUCCI\ZED482-JJJ -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\10. NOCTA Drake" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\10. NOCTA Drake\NO0224-027 -- Nocta Drake" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\10. NOCTA Drake\NO0224-027 -- Nocta Drake"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\10. NOCTA Drake" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\10. NOCTA Drake\NO0224-028 -- Nocta Drake" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\10. NOCTA Drake\NO0224-028 -- Nocta Drake"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\11. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\11. OFF WHITE\AO4606-001 -- OW" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\11. OFF WHITE\AO4606-001 -- OW"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\11. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\11. OFF WHITE\JSD121-EDJ -- OW" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\11. OFF WHITE\JSD121-EDJ -- OW"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY\EDD515-KDZ -- Tiffany" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY\EDD515-KDZ -- Tiffany"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY\ZED212-KZF -- TIFFANY" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY\ZED212-KZF -- TIFFANY"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY\ZFD212-KDF -- Tiffany" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\12. TIFFANY\ZFD212-KDF -- Tiffany"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\DM7926-300 -- Billie Ellish x Nike" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\DM7926-300 -- Billie Ellish x Nike"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\JGD212-EDS -- Keep Fresh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\JGD212-EDS -- Keep Fresh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\JGD212-JJZ -- Lakers" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\JGD212-JJZ -- Lakers"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\JGD482-EJR -- Squidward Tentacles" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\JGD482-EJR -- Squidward Tentacles"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\JGD482-ZJR -- Loading" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\JGD482-ZJR -- Loading"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZDD577-EJK -- Peace Minus one" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZDD577-EJK -- Peace Minus one"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZED212-JJK -- Milk" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZED212-JJK -- Milk"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZED482-EJK -- Molly" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZED482-EJK -- Molly"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZED482-EJS -- Garfield" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZED482-EJS -- Garfield"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZED482-JJD -- Doraemon" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZED482-JJD -- Doraemon"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZJD212-EJJ -- Ideas worth spreading" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\13. OTHERS\ZJD212-EJJ -- Ideas worth spreading"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\20214673875005 -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\20214673875005 -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JGD212-EJD -- Stassy - LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JGD212-EJD -- Stassy - LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JGD212-EJK -- LV- Stassy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JGD212-EJK -- LV- Stassy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JGD212-EJR -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JGD212-EJR -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JGD534-ZJJ -- LV Collab Undefeated" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JGD534-ZJJ -- LV Collab Undefeated"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JZD474-EJH -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\JZD474-EJH -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\ZED212-ZZS -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\2. LV\ZED212-ZZS -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\JGD212-EJR -- Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\JGD212-EJR -- Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\JGD212-EJS -- Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\JGD212-EJS -- Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\ZDD577-EJK -- Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\ZDD577-EJK -- Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\ZDD577-EJZ -- Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\ZDD577-EJZ -- Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\ZED212-EDS -- Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\ZED212-EDS -- Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\ZED212-JJK -- Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\3. SUPREME\ZED212-JJK -- Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\JGD212-EZD -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\JGD212-EZD -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZDD101-EJD -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZDD101-EJD -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-EJE -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-EJE -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-EJF -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-EJF -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-EJJ -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-EJJ -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-EJZ -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-EJZ -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-ZJZ -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\4. STUSSY\ZED212-ZJZ -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\JGD212-EJJ -- The North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\JGD212-EJJ -- The North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZDD101-EJS -- North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZDD101-EJS -- North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED212-EJF -- The North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED212-EJF -- The North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED212-EJZ -- The North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED212-EJZ -- The North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED212-JJK -- Supreme - The North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED212-JJK -- Supreme - The North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED212-ZZS -- The North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED212-ZZS -- The North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED482-EDF -- The North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\5. THE NORTH FACE\ZED482-EDF -- The North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\DQ1098-349 -- BAPE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\DQ1098-349 -- BAPE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\JGD482-ZJR -- Bape" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\JGD482-ZJR -- Bape"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\ZED212-EDJ -- BAPE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\ZED212-EDJ -- BAPE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\ZED212-ZZE -- BAPE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\ZED212-ZZE -- BAPE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\ZED212-ZZH -- BAPE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\6. BAPE\ZED212-ZZH -- BAPE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\13872628583005 -- Dior" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\13872628583005 -- Dior"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\JGD482-ZJR -- Dior" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\JGD482-ZJR -- Dior"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\JZD101-ZZH -- Dior" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\JZD101-ZZH -- Dior"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\ZED212-EJE -- DIOR" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\ZED212-EJE -- DIOR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\ZED482-EDK -- DIOR" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\ZED482-EDK -- DIOR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\ZED482-JDG -- Dior" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\7. DIOR\ZED482-JDG -- Dior"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED\13832628583005 -- Undefeated" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED\13832628583005 -- Undefeated"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED\JGD212-EJK -- Undefeated" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED\JGD212-EJK -- Undefeated"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED\JSD212-EDJ -- Undefeated" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED\JSD212-EDJ -- Undefeated"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED\ZED212-ZZK -- Undefeated" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\8. UNDEFEATED\ZED212-ZZK -- Undefeated"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\9. LEVIS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\9. LEVIS\ZED482-EDK -- LEVIS" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\9. LEVIS\ZED482-EDK -- LEVIS"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\9. LEVIS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\9. LEVIS\ZED482-JDK -- LEVIS" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\9. LEVIS\ZED482-JDK -- LEVIS"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\JGD212-EJK -- Balenciaga" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\JGD212-EJK -- Balenciaga"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\ZJD482-EZJ -- Hand made" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\1. MIXTE\ZJD482-EZJ -- Hand made"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\2. WOMEN\DR7883-101 -- SACAI Shadow" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\2. WOMEN\DR7883-101 -- SACAI Shadow"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\2. WOMEN\JKD212-EDR -- WMNS" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\1. AIR FORCE\2. WOMEN\JKD212-EDR -- WMNS"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\10. BLAZER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\10. BLAZER\JGD534-AZJ -- Nike x Sacai Blazer Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\10. BLAZER\JGD534-AZJ -- Nike x Sacai Blazer Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\10. BLAZER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\10. BLAZER\JGD534-QDH -- Nike x Sacai Blazer Mid" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\10. BLAZER\JGD534-QDH -- Nike x Sacai Blazer Mid"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE\DD1875-700 -- Sacai - Vapor Waffle" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE\DD1875-700 -- Sacai - Vapor Waffle"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE\EFD557-PDK -- Sacai - Regasus Vaporrly" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE\EFD557-PDK -- Sacai - Regasus Vaporrly"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE\JZD538-QDK -- Jean Paul Gaultier x Sacai - Vapor Waffle" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\11. VAPOR WAFFLE\JZD538-QDK -- Jean Paul Gaultier x Sacai - Vapor Waffle"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\CQ9329-012 -- Lebron Witness V Zoom" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\CQ9329-012 -- Lebron Witness V Zoom"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\CQ9329-300 -- Lebron Ambassador XIII" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\CQ9329-300 -- Lebron Ambassador XIII"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\CV7564-401 -- Lebron XVIII Low EP" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\CV7564-401 -- Lebron XVIII Low EP"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\DM1123-400 -- Lebron Witness" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\DM1123-400 -- Lebron Witness"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\DM1123-501 -- Lebron Witness" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\DM1123-501 -- Lebron Witness"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\DRD575-RDJ -- Lebron XX EP" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\DRD575-RDJ -- Lebron XX EP"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FB2239-004 -- Lebron Witness 8" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FB2239-004 -- Lebron Witness 8"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FD0209-001 -- Lebron Witness" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FD0209-001 -- Lebron Witness"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FN0941-001 -- Lebron XX ASW EP Battleknit" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FN0941-001 -- Lebron XX ASW EP Battleknit"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FV2346-800 -- Lebron XXI EP Zoom Air" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FV2346-800 -- Lebron XXI EP Zoom Air"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FZ1095-105 -- Lebron XXI" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\FZ1095-105 -- Lebron XXI"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\HD397-DJE -- Lebron XXI" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\HD397-DJE -- Lebron XXI"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\JGD494-KDF -- Lebron Limited EP" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\JGD494-KDF -- Lebron Limited EP"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\JHD397-EJS -- Lebron XX" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\JHD397-EJS -- Lebron XX"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\ZED397-KDZ -- Lebron Witness VII" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\12. LEBRON JAMES\1. MEN\ZED397-KDZ -- Lebron Witness VII"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\CH6336-017 -- Dior Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\CH6336-017 -- Dior Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\CH6336-019 -- Gucci Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\CH6336-019 -- Gucci Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JDF556-EJF -- LV Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JDF556-EJF -- LV Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JED170-EZD -- Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JED170-EZD -- Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JED575-ZZH -- Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JED575-ZZH -- Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JFD069-EZJ -- Burberry Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JFD069-EZJ -- Burberry Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JJD556-EJR -- White Party Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JJD556-EJR -- White Party Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JKD575-EJK -- Givenchy Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\JKD575-EJK -- Givenchy Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\SJ2068-130 -- Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\SJ2068-130 -- Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-001 -- Gucci Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-001 -- Gucci Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-004 -- Gucci Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-004 -- Gucci Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-022 -- Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-022 -- Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-024 -- Travis Scott XX" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-024 -- Travis Scott XX"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-026 -- Gucci Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-026 -- Gucci Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-032 -- Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-032 -- Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-040 -- Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-040 -- Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-042 -- Dior Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS2024-042 -- Dior Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS7089-526 -- Violent Bear - Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\XS7089-526 -- Violent Bear - Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\ZKD271-EDF -- Undefeated Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\ZKD271-EDF -- Undefeated Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\ZZD577-EDR -- Balenciaga Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\1. TRAVIS SCOTT\ZZD577-EDR -- Balenciaga Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\JKD117-FJE -- Virgil Abloh x Futura" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\JKD117-FJE -- Virgil Abloh x Futura"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\LX1988 -- LV collab" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\LX1988 -- LV collab"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\SJ9950-056 -- 2025 year of the Snake" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\SJ9950-056 -- 2025 year of the Snake"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\XS2024 -- The North Face collab" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\XS2024 -- The North Face collab"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\XZ6188 -- Stussy collab" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\XZ6188 -- Stussy collab"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\ZDD474-FJS -- Goat Lows - OFF WHITE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\1. JORDAN 1 LOW\ZDD474-FJS -- Goat Lows - OFF WHITE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\33172898443003 -- Black & Black" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\33172898443003 -- Black & Black"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JHD101-QJZ -- Bodega" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JHD101-QJZ -- Bodega"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JHD101-QJZ -- Comics Tay" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JHD101-QJZ -- Comics Tay"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JHD101-QJZ -- Union LA" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JHD101-QJZ -- Union LA"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JHD170-JJK -- Space" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JHD170-JJK -- Space"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JJD553-KDE -- OFF WHITE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JJD553-KDE -- OFF WHITE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JJD553-KDE -- OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JJD553-KDE -- OFF WHITE\webp" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JJD553-KDE -- OFF WHITE\webp"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD170-SED -- Jeans" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD170-SED -- Jeans"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD170-SED -- Jordan 1 x XX" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD170-SED -- Jordan 1 x XX"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD435-AZH -- Wear Me" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD435-AZH -- Wear Me"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD474-EJR -- Air Dior" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD474-EJR -- Air Dior"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD474-EJR -- Air Dior" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD474-EJR -- Air Dior\webp" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD474-EJR -- Air Dior\webp"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-DZZ" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-DZZ"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-EJS -- Vernis" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-EJS -- Vernis"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-HDG -- Goldie" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-HDG -- Goldie"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-HDG -- Goldie" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-HDG -- Goldie\webp" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD556-HDG -- Goldie\webp"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD577-ADS -- Union LA" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JKD577-ADS -- Union LA"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JSD170-EZR -- CLOT" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JSD170-EZR -- CLOT"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JSD170-RZZ -- Vodoo" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JSD170-RZZ -- Vodoo"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JSD254-JDZ -- Goretex" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JSD254-JDZ -- Goretex"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JSD538-SZS -- Thomas Campbell - What the Dunk" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JSD538-SZS -- Thomas Campbell - What the Dunk"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JZD556-FJZ -- Moonshot" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\JZD556-FJZ -- Moonshot"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\ZDD170-SDJ" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\ZDD170-SDJ"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\ZZD535-EJF -- Golden boy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\2. JORDAN 1 HIGH\ZZD535-EJF -- Golden boy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\FQ7939 003 Nigel Sylvester" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\FQ7939 003 Nigel Sylvester"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\JKD262-FZD -- Virgil Abloh x Off White Nike SB" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\JKD262-FZD -- Virgil Abloh x Off White Nike SB"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\JSD068-QJE -- Nina Chanel x Jordan 2" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\2. AIR JORDAN\1. MIXTE\JSD068-QJE -- Nina Chanel x Jordan 2"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\CJ6106-100 -- Stranger Things" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\CJ6106-100 -- Stranger Things"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DRD474-ZJE -- Oatmeal" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DRD474-ZJE -- Oatmeal"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DZ3238-001 -- Bruce Lee Yin Yang" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DZ3238-001 -- Bruce Lee Yin Yang"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DZ3239-002 -- Yin Yang" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DZ3239-002 -- Yin Yang"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DZ3239-100 -- Yin Yang" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DZ3239-100 -- Yin Yang"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DZ3239-400 -- Yin Yang" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\DZ3239-400 -- Yin Yang"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\JJF474-ZZE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\JJF474-ZZE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\JKD243-EJH -- All petals united" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\JKD243-EJH -- All petals united"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\JZD129-EJS -- Union LA" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\JZD129-EJS -- Union LA"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\JZD489-SJS -- Air Zoom Cortez Forrest Gump" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\1. MIXTE\JZD489-SJS -- Air Zoom Cortez Forrest Gump"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\2. WOMEN\17787845454082" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\3. CORTEZ\2. WOMEN\17787845454082"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\FC1688-133 -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\FC1688-133 -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\JDD103-ZZG -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\JDD103-ZZG -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\JED259-EJD -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\JED259-EJD -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\LX1988 200  Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\LX1988 200  Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZDD230-ZZD -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZDD230-ZZD -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZKD270-EDZ -- LV Virgil" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZKD270-EDZ -- LV Virgil"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZKD271-EDG -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZKD271-EDG -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZKD271-EDH -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZKD271-EDH -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZKD271-EDK -- LV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\1. LV\ZKD271-EDK -- LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\CH8311-285 -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\CH8311-285 -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\DJ2024-130 -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\DJ2024-130 -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\JDD103-ZZG -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\JDD103-ZZG -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\JFD101-EZD -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\JFD101-EZD -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\KK1688-091 -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\KK1688-091 -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\LW1818-112 -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\LW1818-112 -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\XY1688-003 -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\XY1688-003 -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\ZKD563-FDH -- Gucci" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\2. GUCCI\ZKD563-FDH -- Gucci"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME\JKD474-EJZ -- Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME\JKD474-EJZ -- Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME\KK1333-032 -- Kaws Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME\KK1333-032 -- Kaws Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME\XD6188-035 -- BAPE Supreme" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME\XD6188-035 -- BAPE Supreme"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME\ZZD577-ZZZ -- Supreme Dragon year 2025" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\3. SUPREME\ZZD577-ZZZ -- Supreme Dragon year 2025"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\4. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\4. DIOR\KK1333-033 -- DIOR" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\4. DIOR\KK1333-033 -- DIOR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\4. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\4. DIOR\ZKD271-EDJ -- DIOR" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\4. DIOR\ZKD271-EDJ -- DIOR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY\JED575-ZZH -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY\JED575-ZZH -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY\JFD270-ZZZ -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY\JFD270-ZZZ -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY\KK1333-031 -- Stussy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\5. STUSSY\KK1333-031 -- Stussy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\6. The North Face" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\6. The North Face\DJ2024-127 -- The North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\6. The North Face\DJ2024-127 -- The North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\6. The North Face" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\6. The North Face\JKD271-EJR -- The North Face" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\6. The North Face\JKD271-EJR -- The North Face"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\JED242-ZJJ -- Off White" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\JED242-ZJJ -- Off White"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\JFD103-QJD -- Off White" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\JFD103-QJD -- Off White"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\JZD103-ZJR -- Off White" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\JZD103-ZJR -- Off White"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\JZD264-PJE -- Off white" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\JZD264-PJE -- Off white"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\ZGD271-EJJ -- Off White" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\7. OFF WHITE\ZGD271-EJJ -- Off White"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\8. HERMES" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\8. HERMES\XD6188-038 -- Hermes" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\8. HERMES\XD6188-038 -- Hermes"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\8. HERMES" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\8. HERMES\XD6188-040 -- HERMES" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\8. HERMES\XD6188-040 -- HERMES"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DH8009-600 -- Lebron James Fruitty Pebbles" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DH8009-600 -- Lebron James Fruitty Pebbles"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DJ6188-002 -- Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DJ6188-002 -- Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DM6868-039 -- FENDI" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DM6868-039 -- FENDI"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DR9704 200 -- Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DR9704 200 -- Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DRD101-HDR -- Parra" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DRD101-HDR -- Parra"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DRD212-EJF -- Olympics" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DRD212-EJF -- Olympics"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DV0831-101 -- Steam Puppet" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DV0831-101 -- Steam Puppet"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DV1024-010 -- Futura x Nike" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\DV1024-010 -- Futura x Nike"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\FD0860-001 -- Jarritos Mexico" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\FD0860-001 -- Jarritos Mexico"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\FN0316-999 -- CLOT" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\FN0316-999 -- CLOT"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\GF2409-169 -- YSL" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\GF2409-169 -- YSL"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\JED575-ZZH -- Bape" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\JED575-ZZH -- Bape"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\JEF212-ZJG -- Doaf" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\JEF212-ZJG -- Doaf"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\JSD534-EDJ -- Travis Scott" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\JSD534-EDJ -- Travis Scott"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\LW1818-110 -- Balenciaga" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\LW1818-110 -- Balenciaga"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\MU0232-363 -- Daniel Arsham" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\4. DUNK LOW\1. MIXTE\9. OTHERS\MU0232-363 -- Daniel Arsham"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\5. AIR MAX 1\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\5. AIR MAX 1\1. MIXTE\BV1977-200 -- Panthera" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\5. AIR MAX 1\1. MIXTE\BV1977-200 -- Panthera"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\5. AIR MAX 1\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\5. AIR MAX 1\1. MIXTE\JKD556-EJK -- Bandana" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\5. AIR MAX 1\1. MIXTE\JKD556-EJK -- Bandana"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\6. AIR MAX 97\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\6. AIR MAX 97\1. MIXTE\JDD170-KJF -- Grey sail" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\6. AIR MAX 97\1. MIXTE\JDD170-KJF -- Grey sail"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\7. FOG" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\7. FOG\AT8087-001 -- FOG" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\7. FOG\AT8087-001 -- FOG"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\7. FOG" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\7. FOG\AT9915-002 -- FOG" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\7. FOG\AT9915-002 -- FOG"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\AA7293-200 -- Air Max 90 - Desert Ore" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\AA7293-200 -- Air Max 90 - Desert Ore"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\DQ1615-200 -- Terra Forma" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\DQ1615-200 -- Terra Forma"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\DSD117-JDR -- Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\DSD117-JDR -- Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\JHD103-ZJZ -- Off White Dunk Pine" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\JHD103-ZJZ -- Off White Dunk Pine"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\JZD170-FJS -- Off White - Blazer Mid" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\JZD170-FJS -- Off White - Blazer Mid"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\ZJD101-EEJ -- Nike React Hyperdunk" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\ZJD101-EEJ -- Nike React Hyperdunk"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\ZZD372-FJJ -- OFF WHITE-Blazer low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\8. OFF WHITE\ZZD372-FJJ -- OFF WHITE-Blazer low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\9. JACQUEMUS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\9. JACQUEMUS\DR0424-100 -- J Force 1 Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\9. JACQUEMUS\DR0424-100 -- J Force 1 Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\9. JACQUEMUS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\9. JACQUEMUS\ZFD562-DJK -- Humara" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\9. JACQUEMUS\ZFD562-DJK -- Humara"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\FQ6891-001 -- Bode x Nike Astro Grabber" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\FQ6891-001 -- Bode x Nike Astro Grabber"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\1. NIKE Limited Edition\FQ6892-100 -- Bode x Nike Astro Grabber" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\1. NIKE Limited Edition\FQ6892-100 -- Bode x Nike Astro Grabber"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\10. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\10. OFF WHITE\13412745668006 -- Out of Office-Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\10. OFF WHITE\13412745668006 -- Out of Office-Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\10. OFF WHITE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\10. OFF WHITE\JZD212-EZD -- Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\10. OFF WHITE\JZD212-EZD -- Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\11. GIVENCHY\1. MEN\27752198733044" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN\27752198733044"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\11. GIVENCHY\1. MEN\305376245664025" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN\305376245664025"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\11. GIVENCHY\1. MEN\JFD233-QDE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN\JFD233-QDE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\11. GIVENCHY\1. MEN\JGD233-PJS" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN\JGD233-PJS"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\11. GIVENCHY\1. MEN\JHF233-SJF" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\1. MEN\JHF233-SJF"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\11. GIVENCHY\2. MIXTE\13482965127007" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\2. MIXTE\13482965127007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\11. GIVENCHY\2. MIXTE\JGD538-RDE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\11. GIVENCHY\2. MIXTE\JGD538-RDE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\12. Maison MARGIELA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\12. Maison MARGIELA\13432565128046 -- Yasuhiro Mihara" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\12. Maison MARGIELA\13432565128046 -- Yasuhiro Mihara"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\12. Maison MARGIELA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\12. Maison MARGIELA\13462075877007 -- 22 Classic" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\12. Maison MARGIELA\13462075877007 -- 22 Classic"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\12. Maison MARGIELA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\12. Maison MARGIELA\ZZD029-SJD -- MM6" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\12. Maison MARGIELA\ZZD029-SJD -- MM6"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\12. Maison MARGIELA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\12. Maison MARGIELA\ZZD029-SJJ -- MM6" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\12. Maison MARGIELA\ZZD029-SJJ -- MM6"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\13. VALENTINO\2. MIXTE\13408295730067" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\2. MIXTE\13408295730067"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\13. VALENTINO\2. MIXTE\13438945660048 -- VL7N" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\2. MIXTE\13438945660048 -- VL7N"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\13. VALENTINO\2. MIXTE\13458235040067" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\2. MIXTE\13458235040067"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\13. VALENTINO\2. MIXTE\KFD048-FZH" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\2. MIXTE\KFD048-FZH"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\13. VALENTINO\3. MEN\13482995737048" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\3. MEN\13482995737048"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\13. VALENTINO\3. MEN\13488995440048" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\13. VALENTINO\3. MEN\13488995440048"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\14. PRADA\1. MIXTE\13402145668007" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\1. MIXTE\13402145668007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\14. PRADA\1. MIXTE\13414768832009" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\1. MIXTE\13414768832009"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\14. PRADA\1. MIXTE\35763994443006 -- CloudBust Thunder" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\1. MIXTE\35763994443006 -- CloudBust Thunder"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\14. PRADA\3. MEN\11432968123004" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN\11432968123004"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\14. PRADA\3. MEN\13412565127048 -- Downtown Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN\13412565127048 -- Downtown Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\14. PRADA\3. MEN\13452195737048" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN\13452195737048"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\14. PRADA\3. MEN\13458875870008" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN\13458875870008"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\14. PRADA\3. MEN\13462775577048" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\14. PRADA\3. MEN\13462775577048"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\1. MIXTE\13458095440007 -- Bella Lily" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\1. MIXTE\13458095440007 -- Bella Lily"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\1. MIXTE\19217775874004 -- x NB" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\1. MIXTE\19217775874004 -- x NB"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\1. MIXTE\JFD474-QZG -- x NB - 530 SL" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\1. MIXTE\JFD474-QZG -- x NB - 530 SL"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\2. WOMEN\13413524583046 -- Youth Jump" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN\13413524583046 -- Youth Jump"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\2. WOMEN\13432925588007 -- Collab New Balance" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN\13432925588007 -- Collab New Balance"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\2. WOMEN\13433064833046" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN\13433064833046"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\2. WOMEN\13453824583007 -- Tyre" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN\13453824583007 -- Tyre"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\2. WOMEN\30564993735084" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN\30564993735084"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\15. MIU MIU\2. WOMEN\3059306484025 -- x NB" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\15. MIU MIU\2. WOMEN\3059306484025 -- x NB"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\16. BOTTEGA VENETA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\16. BOTTEGA VENETA\1. MIXTE\13403264833046 -- BV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\16. BOTTEGA VENETA\1. MIXTE\13403264833046 -- BV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\16. BOTTEGA VENETA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\16. BOTTEGA VENETA\1. MIXTE\EFD534-DDH -- BV" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\16. BOTTEGA VENETA\1. MIXTE\EFD534-DDH -- BV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\17. BURBERRY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\17. BURBERRY\1. MEN\27752148663063" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\17. BURBERRY\1. MEN\27752148663063"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\17. BURBERRY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\17. BURBERRY\1. MEN\JFD233-EZF" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\17. BURBERRY\1. MEN\JFD233-EZF"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\17. BURBERRY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\17. BURBERRY\1. MEN\JFD233-EZF -- v2" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\17. BURBERRY\1. MEN\JFD233-EZF -- v2"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\17. BURBERRY\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\17. BURBERRY\1. MEN\JKD233-EZF" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\17. BURBERRY\1. MEN\JKD233-EZF"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE\35752935238007 -- Marathon" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE\35752935238007 -- Marathon"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE\JFD575-KDH -- Marathon" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE\JFD575-KDH -- Marathon"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE\ZED230-ZJS -- GGDB" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\1. MIXTE\ZED230-ZJS -- GGDB"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\18. GOLDEN GOOSE\2. WOMEN\13432628583024 -- Super Star" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\2. WOMEN\13432628583024 -- Super Star"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\18. GOLDEN GOOSE\2. WOMEN\33558295440067 -- 2023 SS" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\18. GOLDEN GOOSE\2. WOMEN\33558295440067 -- 2023 SS"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE\JSD101-AZG -- City Verse" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE\JSD101-AZG -- City Verse"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE\JSD101-QDK -- City Verse" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE\JSD101-QDK -- City Verse"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE\JSD101-ZZD -- Bliss feel run" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE\JSD101-ZZD -- Bliss feel run"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE\JZD243-HDF -- Bliss" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\19. GAMA NORMAL\1. LULU LEMON\1. MIXTE\JZD243-HDF -- Bliss"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\13442465838006 -- Originals" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\13442465838006 -- Originals"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\17417645454003 -- Gazelle Ezquisitas" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\17417645454003 -- Gazelle Ezquisitas"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\17417645454003 -- Gazelle Ezquisitas" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\17417645454003 -- Gazelle Ezquisitas\webp" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\17417645454003 -- Gazelle Ezquisitas\webp"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\DRF145-QJH -- Gazelle INdoor" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\DRF145-QJH -- Gazelle INdoor"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\JHD577-ZJR -- Gucci Superstar" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\JHD577-ZJR -- Gucci Superstar"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\JJD103-EZJ -- Kanye West Yeezy" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\1. GUCCI\JJD103-EZJ -- Kanye West Yeezy"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\2. BALENCIAGA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\2. BALENCIAGA\ZED474-RZF -- Triple S Balenciaga" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\2. BALENCIAGA\ZED474-RZF -- Triple S Balenciaga"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God\10562978873004 -- Fear of God" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God\10562978873004 -- Fear of God"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God\19437875874003 -- FOG" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God\19437875874003 -- FOG"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God\EGD243-KDH -- FOG" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\3. Fear Of God\EGD243-KDH -- FOG"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\19332658393063 -- Samba Memorial Day" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\19332658393063 -- Samba Memorial Day"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\32147475874082 -- Atmos Tuxedo" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\32147475874082 -- Atmos Tuxedo"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\DSD230-HZR -- Samba x Wales Bonner" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\DSD230-HZR -- Samba x Wales Bonner"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\GY1702 -- Song for the mute" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\GY1702 -- Song for the mute"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JDD129-KDK -- Samba x Wales Bonner" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JDD129-KDK -- Samba x Wales Bonner"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JHD254-FJH -- Samba Jeans" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JHD254-FJH -- Samba Jeans"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JJD562-QED -- Samba Wales Bonner" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JJD562-QED -- Samba Wales Bonner"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JJD562-QJK -- Samba x Wales Bonner" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JJD562-QJK -- Samba x Wales Bonner"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JR1256 -- Samba Cow Women" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JR1256 -- Samba Cow Women"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JZD562-QZJ -- Samba pantera" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\1. SAMBA\JZD562-QZJ -- Samba pantera"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\27612538233046 -- Sean Wotherspoon" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\27612538233046 -- Sean Wotherspoon"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\GW3966-- Lego Superstar" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\GW3966-- Lego Superstar"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\GZ1774 -- Simpson" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\GZ1774 -- Simpson"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\JJD170-GJG -- CLOT" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\JJD170-GJG -- CLOT"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\JKD562-DZK -- Rabbit Hole Superstar" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\JKD562-DZK -- Rabbit Hole Superstar"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\JZD268-RDK -- Melting Sadness✖Adidas" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\2. SUPERSTAR\JZD268-RDK -- Melting Sadness✖Adidas"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\17717595444004 -- CLOT" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\17717595444004 -- CLOT"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\17797045664044 -- CLOT" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\17797045664044 -- CLOT"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JFD562-EZE -- Bold women" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JFD562-EZE -- Bold women"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JHD242-QZH -- Clot" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JHD242-QZH -- Clot"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JJD262-ZZS -- Hello Kitty" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JJD262-ZZS -- Hello Kitty"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JKD372-GZH" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JKD372-GZH"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JZD103-DZK" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\3. GAZELLE\JZD103-DZK"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\32152098443063 -- Puffylette" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\32152098443063 -- Puffylette"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\EDD515-JDH -- Kanye West - Sulfur" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\EDD515-JDH -- Kanye West - Sulfur"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\GW8852 -- Balenciaga" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\GW8852 -- Balenciaga"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\IH5632 -- Bad Bunny - Adizero SL72" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\IH5632 -- Bad Bunny - Adizero SL72"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JFD562-QZS -- Brain Dead Stan Smith" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JFD562-QZS -- Brain Dead Stan Smith"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JGD101-RJG -- Dragon Ball" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JGD101-RJG -- Dragon Ball"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JJD489-EJK -- LEGO - Neo Grandcourt" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JJD489-EJK -- LEGO - Neo Grandcourt"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JKD193-EED -- Adizero SL72 COW" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JKD193-EED -- Adizero SL72 COW"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JKD242-EZE -- Tokyo Pack" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JKD242-EZE -- Tokyo Pack"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JKD242-ZJH -- Tokyo Pack" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JKD242-ZJH -- Tokyo Pack"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JZD553-DZE -- BAPE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\2. ADIDAS Limited Edition\4. OTHERS\JZD553-DZE -- BAPE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\3. HERMES\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\3. HERMES\1. MIXTE\13468095730007 35-45" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\3. HERMES\1. MIXTE\13468095730007 35-45"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\3. HERMES\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\3. HERMES\2. WOMEN\JED129-KZE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\3. HERMES\2. WOMEN\JED129-KZE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13412565127048" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13412565127048"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13413534043065" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13413534043065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13432545668006 -- Screener" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13432545668006 -- Screener"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13432825588046" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13432825588046"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13433534043065" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13433534043065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13438975870007 -- Chunky B Screener" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13438975870007 -- Chunky B Screener"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13443434043065" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13443434043065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13458855390069 -- Rython trainer" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13458855390069 -- Rython trainer"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\13462895737048 -- Rython Vintage Trainer" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\13462895737048 -- Rython Vintage Trainer"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\JFD233-ADS -- ACE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\JFD233-ADS -- ACE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\JFD538-JJJ -- ACE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\JFD538-JJJ -- ACE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\JFD538-JJJ ACE-2" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\JFD538-JJJ ACE-2"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\JKD129-DDK -- Tennis 1977" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\JKD129-DDK -- Tennis 1977"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\JKD129-EJZ" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\JKD129-EJZ"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\JKD170-DZF -- Ace Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\JKD170-DZF -- Ace Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\JZD129-EJJ -- Tennis 1977" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\JZD129-EJJ -- Tennis 1977"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\1. MIXTE\ZDD145-ZZJ -- Chunky B" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\1. MIXTE\ZDD145-ZZJ -- Chunky B"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\2. WOMEN\13403164833067 -- Rython Vintage" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN\13403164833067 -- Rython Vintage"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\2. WOMEN\13418565120X01 -- Rython Trainer" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN\13418565120X01 -- Rython Trainer"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\2. WOMEN\13432025588046 -- Screener hightop" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN\13432025588046 -- Screener hightop"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\2. WOMEN\13432065128065" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN\13432065128065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\2. WOMEN\JGD170-AZG -- Rython" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\2. WOMEN\JGD170-AZG -- Rython"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\3. MEN\13444066122007" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\3. MEN\13444066122007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\3. MEN\JKD233-EDS -- Gypsophila Diamond" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\3. MEN\JKD233-EDS -- Gypsophila Diamond"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\4. GUCCI\3. MEN\ZGD213-EZF" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\4. GUCCI\3. MEN\ZGD213-EZF"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\1. MIXTE\13432645668007" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\1. MIXTE\13432645668007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\1. MIXTE\26067095734042 -- Collab Vans slip-on LX" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\1. MIXTE\26067095734042 -- Collab Vans slip-on LX"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\1. MIXTE\B27" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\1. MIXTE\B27\1334463125007 -- B27 Galaxy high" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\1. MIXTE\B27\1334463125007 -- B27 Galaxy high"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\2. WOMEN\22792028583024" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\2. WOMEN\22792028583024"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\2. WOMEN\22792028583065" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\2. WOMEN\22792028583065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\2. WOMEN\JHD170-HEJ -- Walk'in" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\2. WOMEN\JHD170-HEJ -- Walk'in"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\2. WOMEN\ZKD538-HZK -- B23 Oblique" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\2. WOMEN\ZKD538-HZK -- B23 Oblique"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\3. MEN\B33" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\3. MEN\B33\33508265830067 -- B33 collab Denim Tears" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\3. MEN\B33\33508265830067 -- B33 collab Denim Tears"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\3. MEN\B33" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\5. DIOR\3. MEN\B33\33538665830067 -- B33" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\5. DIOR\3. MEN\B33\33538665830067 -- B33"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\2. MEN\1. TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\2. MEN\1. TRAINER\13443068124X11 -- Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\2. MEN\1. TRAINER\13443068124X11 -- Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\2. MEN\1. TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\2. MEN\1. TRAINER\JJF129-EJR Trainer Sneaker Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\2. MEN\1. TRAINER\JJF129-EJR Trainer Sneaker Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\2. MEN\1. TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\2. MEN\1. TRAINER\JKD129-EJK Trainer Sneaker Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\2. MEN\1. TRAINER\JKD129-EJK Trainer Sneaker Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\2. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\2. MEN\13432595448027" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\2. MEN\13432595448027"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\13453968124X11 -- Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\13453968124X11 -- Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\13463948454x11 LV Trainer sneaker low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\13463948454x11 LV Trainer sneaker low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\13473768124X11 -- Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\13473768124X11 -- Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\19008135230067 -- LV Trainer Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\19008135230067 -- LV Trainer Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\35614626582067 -- Virgil Abloh Trainer" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\35614626582067 -- Virgil Abloh Trainer"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\FDD259-PDZ -- OFF White - Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\FDD259-PDZ -- OFF White - Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\JFD355-EJD LV Trainer sneaker Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\JFD355-EJD LV Trainer sneaker Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\JFD355-ZZK -- LV Trainer sneaker Low" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\1. LV TRAINER\JFD355-ZZK -- LV Trainer sneaker Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\FDD272-EJZ" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\FDD272-EJZ"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\6. LV\3. MIXTE\JSD268-KZZ -- Virgil Abloh" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\6. LV\3. MIXTE\JSD268-KZZ -- Virgil Abloh"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\12213644453069 -- Phantom Sneaker Balenciaga" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\12213644453069 -- Phantom Sneaker Balenciaga"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\12248355390069 -- Phantom sneaker" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\12248355390069 -- Phantom sneaker"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\13453244663007 -- Phantom Sneaker" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\13453244663007 -- Phantom Sneaker"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\134649768720611 -- Snow Boots" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\134649768720611 -- Snow Boots"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\17398065830046 -- Circuit sneaker" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\17398065830046 -- Circuit sneaker"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\17464063835084 -- Circuit Sneaker" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\17464063835084 -- Circuit Sneaker"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\35287825584023 -- Balenciaga" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\35287825584023 -- Balenciaga"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\EFD538-DZZ -- Track Trainer" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\EFD538-DZZ -- Track Trainer"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\EKD538-SJF -- Triple S" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\EKD538-SJF -- Triple S"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\ZFD268-AJD -- 10XL" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\ZFD268-AJD -- 10XL"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\ZJD538-SJF -- Gucci x Balenciaga Triple S" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\ZJD538-SJF -- Gucci x Balenciaga Triple S"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\ZRD538-ZZK -- Tess S Balenciaga" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\1. MIXTE\ZRD538-ZZK -- Tess S Balenciaga"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\2. WOMEN\EDD474-EJR" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\2. WOMEN\EDD474-EJR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\2. WOMEN\ZRD538-SED -- Triple S Clear sole" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\2. WOMEN\ZRD538-SED -- Triple S Clear sole"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\2. WOMEN\ZZD372-JDG -- Phantom Sneaker" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\2. WOMEN\ZZD372-JDG -- Phantom Sneaker"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\7. BALENCIAGA\3. MEN\122528350480401 -- Defender" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\7. BALENCIAGA\3. MEN\122528350480401 -- Defender"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\13448535040007" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\13448535040007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\13451055395007" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\13451055395007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\13453164123046" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\13453164123046"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\13462045458007" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\13462045458007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\13468945450007" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\13468945450007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\14332625588067 -- Cruise" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\14332625588067 -- Cruise"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\19737865124063" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\19737865124063"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\30533854393065" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\30533854393065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\30534963835025 -- Kung fu Panda" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\30534963835025 -- Kung fu Panda"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\33553964833084 -- Chanel 23SS" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\33553964833084 -- Chanel 23SS"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\33564726582069" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\33564726582069"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\JHD259-FDR" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\JHD259-FDR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\JZD029-HJR" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\JZD029-HJR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\ZGD398-RDG" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\ZGD398-RDG"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\ZSD029-DDH" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\ZSD029-DDH"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\8. CHANEL\1. WOMEN\ZSD477-RJR" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\8. CHANEL\1. WOMEN\ZSD477-RJR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\9. LOUBOUTIN\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\9. LOUBOUTIN\1. MEN\ZGD233-QJE" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\9. LOUBOUTIN\1. MEN\ZGD233-QJE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\Common Project" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\1. SNEAKERS\Common Project\13402148663024" "C:\2.MY_APP\TWL\V2\public\products\1. SNEAKERS\Common Project\13402148663024"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\1. NIKE Collabs\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\1. NIKE Collabs\1. MIXTE\11247045454022 -- Offcourt" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\1. NIKE Collabs\1. MIXTE\11247045454022 -- Offcourt"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\1. NIKE Collabs\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\1. NIKE Collabs\1. MIXTE\DGD170-KED -- Calm Mule" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\1. NIKE Collabs\1. MIXTE\DGD170-KED -- Calm Mule"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\1. NIKE Collabs\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\1. NIKE Collabs\1. MIXTE\DGF489-GDK -- Billie Ellish x Jordan" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\1. NIKE Collabs\1. MIXTE\DGF489-GDK -- Billie Ellish x Jordan"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\1. NIKE Collabs\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\1. NIKE Collabs\1. MIXTE\DJ5058-011 -- Legacy Mule" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\1. NIKE Collabs\1. MIXTE\DJ5058-011 -- Legacy Mule"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\10. MIU MIU\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\10. MIU MIU\1. WOMEN\13413664833004" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\10. MIU MIU\1. WOMEN\13413664833004"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\10. MIU MIU\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\10. MIU MIU\1. WOMEN\JZD170-KZK -- SNGG" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\10. MIU MIU\1. WOMEN\JZD170-KZK -- SNGG"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\10. MIU MIU\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\10. MIU MIU\2. MIXTE\JSD243-EZG -- MiuMiu x New Balance" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\10. MIU MIU\2. MIXTE\JSD243-EZG -- MiuMiu x New Balance"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\11. PRADA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\11. PRADA\32777795734042 -- Prada Slides" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\11. PRADA\32777795734042 -- Prada Slides"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\12. HERMES" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\12. HERMES\13432498733023 -- Chypre" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\12. HERMES\13432498733023 -- Chypre"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\13. CROCS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\13. CROCS\19937925584023 -- Bubble Crush Clog" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\13. CROCS\19937925584023 -- Bubble Crush Clog"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\13. CROCS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\13. CROCS\DSD474-KZH -- Bae Clog" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\13. CROCS\DSD474-KZH -- Bae Clog"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\13. CROCS" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\13. CROCS\JED361-RZK -- Cars x Crocs" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\13. CROCS\JED361-RZK -- Cars x Crocs"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\14. BOTTEGA VENETA\1. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\14. BOTTEGA VENETA\1. MEN\13493074573084" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\14. BOTTEGA VENETA\1. MEN\13493074573084"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\15. BIRKENSTOCK" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\15. BIRKENSTOCK\33484963835063 -- Boston Suede" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\15. BIRKENSTOCK\33484963835063 -- Boston Suede"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\15. BIRKENSTOCK" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\15. BIRKENSTOCK\JFD262-FZG" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\15. BIRKENSTOCK\JFD262-FZG"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\2. GUCCI\1. MIXTE\13432528583022" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\1. MIXTE\13432528583022"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\2. GUCCI\1. MIXTE\13454133045063 -- Horse bit" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\1. MIXTE\13454133045063 -- Horse bit"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\2. GUCCI\1. MIXTE\JKD553-KJF" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\1. MIXTE\JKD553-KJF"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\2. GUCCI\2. WOMEN\13407335044042 -- Horsebit" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\2. WOMEN\13407335044042 -- Horsebit"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\2. GUCCI\2. WOMEN\JSD029-GZE" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\2. GUCCI\2. WOMEN\JSD029-GZE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\3. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\3. DIOR\13462975577009 -- Collab Birkenstock" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\3. DIOR\13462975577009 -- Collab Birkenstock"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\3. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\3. DIOR\33514773575023 -- Revolution Slides" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\3. DIOR\33514773575023 -- Revolution Slides"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\3. DIOR" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\3. DIOR\ZDD538-GJD -- D-Wander" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\3. DIOR\ZDD538-GJD -- D-Wander"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\13433624583025 -- Pool Pillow by Murakami" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\13433624583025 -- Pool Pillow by Murakami"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\13433674573044 -- Yayoi Kusama x LV" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\13433674573044 -- Yayoi Kusama x LV"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\13434923585063 -- Love" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\13434923585063 -- Love"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\JDD489-EZG -- Mami Mule" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\JDD489-EZG -- Mami Mule"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\JGD170-EZJ -- Mami Mule" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\JGD170-EZJ -- Mami Mule"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\JZD559-KZR -- Waterfront" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\JZD559-KZR -- Waterfront"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\JZF170-EZJ -- Comfort Pool" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\JZF170-EZJ -- Comfort Pool"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\JZF170-FJJ -- Waterfront" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\JZF170-FJJ -- Waterfront"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\1. MIXTE\ZKD398-GZH" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\1. MIXTE\ZKD398-GZH"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\2. WOMEN\33514663125084 -- Comfort Pool Pillow" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\2. WOMEN\33514663125084 -- Comfort Pool Pillow"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\4. LV\2. WOMEN\33542458393043 -- Pool Pillow" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\4. LV\2. WOMEN\33542458393043 -- Pool Pillow"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\1. MIXTE\12232675578067 -- Triple S Mule" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE\12232675578067 -- Triple S Mule"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\1. MIXTE\13433694733044 -- Sunday Mule" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE\13433694733044 -- Sunday Mule"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\1. MIXTE\JFD538-FJH -- Mule Speed" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE\JFD538-FJH -- Mule Speed"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\1. MIXTE\JZD489-KDZ -- Mold Thong" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE\JZD489-KDZ -- Mold Thong"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\1. MIXTE\ZDD538-SDE -- Paris Low" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE\ZDD538-SDE -- Paris Low"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\1. MIXTE\ZED445-EEJ -- Speed 2.0" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE\ZED445-EEJ -- Speed 2.0"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\1. MIXTE\ZHD538-SZZ -- Track Mule" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\1. MIXTE\ZHD538-SZZ -- Track Mule"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\2. WOMEN\32767845664022 -- Mold Thong" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\2. WOMEN\32767845664022 -- Mold Thong"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\2. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\5. BALENCIAGA\2. WOMEN\JZD445-EEJ -- Chunky" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\5. BALENCIAGA\2. WOMEN\JZD445-EEJ -- Chunky"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\6. CHANEL\13414773575044" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL\13414773575044"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\6. CHANEL\13483894733044" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL\13483894733044"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\6. CHANEL\26162968123025" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL\26162968123025"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\6. CHANEL\DSD538-KJH" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL\DSD538-KJH"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\6. CHANEL\JHD029-FED" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL\JHD029-FED"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\6. CHANEL\JJD029-SZD --" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL\JJD029-SZD --"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\6. CHANEL\JKD029-HZK" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL\JKD029-HZK"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\6. CHANEL\JSD029-GJE" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\6. CHANEL\JSD029-GJE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\7. MAISON MARGIELA" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\7. MAISON MARGIELA\JFD538-GJR -- Tabi" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\7. MAISON MARGIELA\JFD538-GJR -- Tabi"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\8. GIVENCHY" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\8. GIVENCHY\JFD445-EJD -- Mashmallow slider wedge" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\8. GIVENCHY\JFD445-EJD -- Mashmallow slider wedge"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\9. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\9. UGG\1. WOMEN\13453074573065 -- Classic Coquette" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\9. UGG\1. WOMEN\13453074573065 -- Classic Coquette"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\9. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\2. SANDALS\9. UGG\1. WOMEN\35237475874062 -- Angla baby" "C:\2.MY_APP\TWL\V2\public\products\2. SANDALS\9. UGG\1. WOMEN\35237475874062 -- Angla baby"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\3. FORMAL\1. CHANEL\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\3. FORMAL\1. CHANEL\1. WOMEN\JHD259-EJZ" "C:\2.MY_APP\TWL\V2\public\products\3. FORMAL\1. CHANEL\1. WOMEN\JHD259-EJZ"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\3. FORMAL\3. GUCCI\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\3. FORMAL\3. GUCCI\1. WOMEN\JSD029-GED" "C:\2.MY_APP\TWL\V2\public\products\3. FORMAL\3. GUCCI\1. WOMEN\JSD029-GED"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\12573764833084" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\12573764833084"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\13443594443025" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\13443594443025"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\13453264123006" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\13453264123006"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\13483964833006" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\13483964833006"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\14314593445044" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\14314593445044"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\34253094443004" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\34253094443004"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\34272875878007" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\34272875878007"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\34273874873046" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\34273874873046"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JED233-DZE -- Newar Elastic" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JED233-DZE -- Newar Elastic"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JHD101-QJE -- UGG Pure" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JHD101-QJE -- UGG Pure"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JHD101-RZZ -- Casa slide" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JHD101-RZZ -- Casa slide"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JJD474-JDE -- Newar suela baja" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JJD474-JDE -- Newar suela baja"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JKD101-DEJ -- Newar" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JKD101-DEJ -- Newar"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JKD254-ADE" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JKD254-ADE"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JKD474-ADG" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JKD474-ADG"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JSD569-DZH -- Botas" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JSD569-DZH -- Botas"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JZD264-QZD -- Casa slide" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JZD264-QZD -- Casa slide"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\JZD474-DDK -- Doble knot" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\JZD474-DDK -- Doble knot"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\ZDD569-DZH -- Single knot" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\ZDD569-DZH -- Single knot"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\1. WOMEN\ZZD474-ADG -- UGG Pure" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\1. WOMEN\ZZD474-ADG -- UGG Pure"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\2. MIXTE\13463874873065" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\2. MIXTE\13463874873065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\2. MIXTE\34273874873046" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\2. MIXTE\34273874873046"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\2. MIXTE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\2. MIXTE\JDD553-ADR" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\2. MIXTE\JDD553-ADR"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\3. MEN\14837495444082" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\3. MEN\14837495444082"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\3. MEN\ZKD233-RZR -- strap" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\3. MEN\ZKD233-RZR -- strap"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\3. MEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\1. UGG\3. MEN\ZSD233-RZR -- laces" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\1. UGG\3. MEN\ZSD233-RZR -- laces"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\2. LV" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\2. LV\13442435047048 Mocassins" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\2. LV\13442435047048 Mocassins"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\3. MIU MIU\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\3. MIU MIU\1. WOMEN\13433564123065" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\3. MIU MIU\1. WOMEN\13433564123065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\3. MIU MIU\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\3. MIU MIU\1. WOMEN\13474863835084" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\3. MIU MIU\1. WOMEN\13474863835084"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\4. PRADA\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\4. PRADA\1. WOMEN\13438645450067" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\4. PRADA\1. WOMEN\13438645450067"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\4. PRADA\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\4. PRADA\1. WOMEN\13452075877009" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\4. PRADA\1. WOMEN\13452075877009"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\5. BOTTEGA VENETA\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\5. BOTTEGA VENETA\1. WOMEN\13442575878007 -- Boots" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\5. BOTTEGA VENETA\1. WOMEN\13442575878007 -- Boots"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\6. GUCCI\1. WOMEN" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\6. GUCCI\1. WOMEN\13432065128065" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\6. GUCCI\1. WOMEN\13432065128065"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\7. Adidas" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\4. CASUAL\7. Adidas\GX0054 -- Yeezy Boots - Mixte" "C:\2.MY_APP\TWL\V2\public\products\4. CASUAL\7. Adidas\GX0054 -- Yeezy Boots - Mixte"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\5. KIDS\1. UGG" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\5. KIDS\1. UGG\28477695444082 -- Size" "C:\2.MY_APP\TWL\V2\public\products\5. KIDS\1. UGG\28477695444082 -- Size"
mkdir -p "C:\2.MY_APP\TWL\V2\public\products\5. KIDS\2. GOLDEN GOOSE" && cp -r "C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE\5. KIDS\2. GOLDEN GOOSE\28441445665042 -- Super Star Kids" "C:\2.MY_APP\TWL\V2\public\products\5. KIDS\2. GOLDEN GOOSE\28441445665042 -- Super Star Kids"

echo "✅ Missing products copy complete!"