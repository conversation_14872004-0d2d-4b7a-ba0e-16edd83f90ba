'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

// Mock orders data
const mockOrders = [
  {
    id: 'TWL-123456',
    date: '2024-01-15',
    status: 'delivered',
    total: 4500,
    items: [
      {
        id: 1,
        name: 'Nike Air Force 1 Low White',
        brand: 'Nike',
        color: 'Blanco',
        size: '9',
        quantity: 1,
        price: 2500,
        image: '/placeholder.jpg'
      },
      {
        id: 2,
        name: 'Adidas Stan Smith',
        brand: 'Adidas',
        color: 'Blanco/Verde',
        size: '9',
        quantity: 1,
        price: 2000,
        image: '/placeholder.jpg'
      }
    ],
    shipping: {
      method: 'Envío Express',
      address: 'Av. Reforma 123, Col. Centro, CDMX',
      trackingNumber: 'TWL-TRACK-789'
    }
  },
  {
    id: 'TWL-123457',
    date: '2024-01-20',
    status: 'shipped',
    total: 8500,
    items: [
      {
        id: 3,
        name: 'Yeezy Boost 350 V2 Zebra',
        brand: 'Adidas',
        color: 'Blanco/Negro',
        size: '10',
        quantity: 1,
        price: 8500,
        image: '/placeholder.jpg'
      }
    ],
    shipping: {
      method: 'Envío Estándar',
      address: 'Calle Insurgentes 456, Col. Roma, CDMX',
      trackingNumber: 'TWL-TRACK-790',
      estimatedDelivery: '2024-01-25'
    }
  },
  {
    id: 'TWL-123458',
    date: '2024-01-22',
    status: 'processing',
    total: 15000,
    items: [
      {
        id: 4,
        name: 'Gucci Ace Sneakers White',
        brand: 'Gucci',
        color: 'Blanco',
        size: '8.5',
        quantity: 1,
        price: 15000,
        image: '/placeholder.jpg'
      }
    ],
    shipping: {
      method: 'Envío Express',
      address: 'Av. Polanco 789, Col. Polanco, CDMX'
    }
  }
]

const getStatusBadge = (status) => {
  switch (status) {
    case 'delivered':
      return <Badge variant="success" size="sm">Entregado</Badge>
    case 'shipped':
      return <Badge variant="info" size="sm">Enviado</Badge>
    case 'processing':
      return <Badge variant="warning" size="sm">Procesando</Badge>
    case 'confirmed':
      return <Badge variant="primary" size="sm">Confirmado</Badge>
    case 'cancelled':
      return <Badge variant="danger" size="sm">Cancelado</Badge>
    default:
      return <Badge variant="default" size="sm">Pendiente</Badge>
  }
}

const getStatusIcon = (status) => {
  switch (status) {
    case 'delivered':
      return '✅'
    case 'shipped':
      return '🚚'
    case 'processing':
      return '⏳'
    case 'confirmed':
      return '📋'
    case 'cancelled':
      return '❌'
    default:
      return '📦'
  }
}

export default function OrdersPage() {
  const [orders, setOrders] = useState([])
  const [selectedOrder, setSelectedOrder] = useState(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    // Simulate loading orders
    const loadOrders = async () => {
      setIsLoading(true)
      await new Promise(resolve => setTimeout(resolve, 1000))
      setOrders(mockOrders)
      setIsLoading(false)
    }

    loadOrders()
  }, [])

  const handleOrderClick = (order) => {
    setSelectedOrder(selectedOrder?.id === order.id ? null : order)
  }

  const handleTrackOrder = (trackingNumber) => {
    // In a real app, this would open tracking page or modal
    alert(`Rastreando pedido: ${trackingNumber}`)
  }

  const handleReorder = (order) => {
    // In a real app, this would add items to cart
    alert(`Reordenando pedido: ${order.id}`)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <motion.div
              className="w-12 h-12 border-4 border-rich-gold border-t-transparent rounded-full mx-auto mb-4"
              animate={{ rotate: 360 }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "linear"
              }}
            />
            <p className="text-warm-camel">Cargando tus pedidos...</p>
          </div>
        </div>
      </div>
    )
  }

  if (orders.length === 0) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <Card variant="glass">
              <CardContent className="p-12">
                <div className="text-6xl mb-6">📦</div>

                <h1 className="text-3xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  No tienes pedidos aún
                </h1>

                <p className="text-warm-camel text-lg mb-8">
                  ¡Es hora de encontrar tus sneakers perfectos!
                </p>

                <TransitionLink href="/shop">
                  <AnimatedButton
                    variant="primary"
                    size="lg"
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    }
                  >
                    Explorar Productos
                  </AnimatedButton>
                </TransitionLink>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Mis Pedidos
          </h1>
          <p className="text-warm-camel">
            Revisa el estado de tus pedidos y realiza seguimiento de tus envíos
          </p>
        </motion.div>

        {/* Orders List */}
        <div className="space-y-6">
          {orders.map((order, index) => (
            <motion.div
              key={order.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card variant="glass" className="overflow-hidden">
                <CardContent className="p-0">

                  {/* Order Header */}
                  <div
                    className="p-6 cursor-pointer hover:bg-warm-camel/5 transition-colors"
                    onClick={() => handleOrderClick(order)}
                  >
                    <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
                      <div className="flex items-center gap-4">
                        <div className="text-3xl">
                          {getStatusIcon(order.status)}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray">
                            Pedido {order.id}
                          </h3>
                          <p className="text-warm-camel text-sm">
                            {new Date(order.date).toLocaleDateString('es-MX', {
                              year: 'numeric',
                              month: 'long',
                              day: 'numeric'
                            })}
                          </p>
                        </div>
                      </div>

                      <div className="flex items-center gap-4">
                        {getStatusBadge(order.status)}
                        <div className="text-right">
                          <div className="text-lg font-bold text-forest-emerald dark:text-light-cloud-gray">
                            ${order.total.toLocaleString()} MXN
                          </div>
                          <div className="text-sm text-warm-camel">
                            {order.items.length} {order.items.length === 1 ? 'artículo' : 'artículos'}
                          </div>
                        </div>
                        <svg
                          className={`w-5 h-5 text-warm-camel transition-transform ${
                            selectedOrder?.id === order.id ? 'rotate-180' : ''
                          }`}
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Order Details */}
                  {selectedOrder?.id === order.id && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      exit={{ opacity: 0, height: 0 }}
                      className="border-t border-warm-camel/20"
                    >
                      <div className="p-6 space-y-6">

                        {/* Items */}
                        <div>
                          <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                            Artículos del Pedido
                          </h4>
                          <div className="space-y-3">
                            {order.items.map((item) => (
                              <div key={`${item.id}-${item.size}-${item.color}`} className="flex gap-4">
                                <div className="w-16 h-16 bg-warm-camel/10 rounded-lg flex items-center justify-center">
                                  <span className="text-2xl">👟</span>
                                </div>
                                <div className="flex-1">
                                  <h5 className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                                    {item.name}
                                  </h5>
                                  <p className="text-warm-camel text-sm">
                                    {item.brand} • {item.color} • Talla {item.size}
                                  </p>
                                  <div className="flex justify-between items-center mt-1">
                                    <span className="text-warm-camel text-sm">
                                      Cantidad: {item.quantity}
                                    </span>
                                    <span className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                                      ${item.price.toLocaleString()} MXN
                                    </span>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>

                        {/* Shipping Info */}
                        <div>
                          <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                            Información de Envío
                          </h4>
                          <div className="bg-warm-camel/5 rounded-lg p-4">
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              <div>
                                <p className="text-sm text-warm-camel mb-1">Método de envío:</p>
                                <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                                  {order.shipping.method}
                                </p>
                              </div>
                              <div>
                                <p className="text-sm text-warm-camel mb-1">Dirección:</p>
                                <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                                  {order.shipping.address}
                                </p>
                              </div>
                              {order.shipping.trackingNumber && (
                                <div>
                                  <p className="text-sm text-warm-camel mb-1">Número de rastreo:</p>
                                  <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                                    {order.shipping.trackingNumber}
                                  </p>
                                </div>
                              )}
                              {order.shipping.estimatedDelivery && (
                                <div>
                                  <p className="text-sm text-warm-camel mb-1">Entrega estimada:</p>
                                  <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                                    {new Date(order.shipping.estimatedDelivery).toLocaleDateString('es-MX')}
                                  </p>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>

                        {/* Actions */}
                        <div className="flex flex-col sm:flex-row gap-3">
                          {order.shipping.trackingNumber && (
                            <AnimatedButton
                              variant="primary"
                              size="sm"
                              onClick={() => handleTrackOrder(order.shipping.trackingNumber)}
                              icon={
                                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                                </svg>
                              }
                            >
                              Rastrear Pedido
                            </AnimatedButton>
                          )}

                          <AnimatedButton
                            variant="secondary"
                            size="sm"
                            onClick={() => handleReorder(order)}
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                              </svg>
                            }
                          >
                            Reordenar
                          </AnimatedButton>

                          <AnimatedButton
                            variant="ghost"
                            size="sm"
                            icon={
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                              </svg>
                            }
                          >
                            Descargar Factura
                          </AnimatedButton>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* Continue Shopping */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="mt-12 text-center"
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                ¿Buscas algo más?
              </h2>
              <p className="text-warm-camel mb-6">
                Descubre nuestra colección completa de sneakers exclusivos
              </p>
              <TransitionLink href="/shop">
                <AnimatedButton
                  variant="primary"
                  size="lg"
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                    </svg>
                  }
                >
                  Seguir Comprando
                </AnimatedButton>
              </TransitionLink>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
