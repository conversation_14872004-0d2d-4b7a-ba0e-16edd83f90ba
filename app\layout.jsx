import { Inter, Playfair_Display, Fira_Code } from 'next/font/google'
import './globals.css'
import { ThemeProvider } from '@/components/theme/ThemeProvider'
import { AuthProvider } from '@/contexts/AuthContext'
import { CartProvider } from '@/contexts/CartContext'
import { WishlistProvider } from '@/contexts/WishlistContext'
import { RouteTransitionProvider, PageTransitionWrapper } from '@/components/transitions/RouteTransitionProvider'
import NavigationProgress from '@/components/transitions/NavigationProgress'
import { UserPreferencesProvider } from '@/contexts/UserPreferencesContext'
import Header from '@/components/layout/Header'
import Footer from '@/components/layout/Footer'
import FloatingActionMenu from '@/components/features/FloatingActionMenu'
import MobileLayout from '@/components/layout/MobileLayout'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap',
})

const firaCode = Fira_Code({
  subsets: ['latin'],
  variable: '--font-fira',
  display: 'swap',
})

export const metadata = {
  title: {
    default: 'The White Laces | Luxury Streetwear Footwear',
    template: '%s | The White Laces'
  },
  description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands. Mexico-first, LATAM-ready.',
  keywords: ['luxury sneakers', 'streetwear', 'limited edition', 'premium footwear', 'Mexico', 'LATAM'],
  authors: [{ name: 'The White Laces Team' }],
  creator: 'The White Laces',
  publisher: 'The White Laces',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://thewhitelaces.com'),
  alternates: {
    canonical: '/',
    languages: {
      'es-MX': '/es-MX',
      'en-US': '/en-US',
      'pt-BR': '/pt-BR',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'es_MX',
    url: 'https://thewhitelaces.com',
    title: 'The White Laces | Luxury Streetwear Footwear',
    description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands.',
    siteName: 'The White Laces',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'The White Laces - Luxury Streetwear Footwear',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The White Laces | Luxury Streetwear Footwear',
    description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands.',
    images: ['/og-image.jpg'],
    creator: '@thewhitelaces',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="es-MX" suppressHydrationWarning>
      <body 
        className={`${inter.variable} ${playfair.variable} ${firaCode.variable} font-inter antialiased`}
        suppressHydrationWarning
      >
        <ThemeProvider
          attribute="class"
          defaultTheme="dark"
          enableSystem
          disableTransitionOnChange={false}
        >
          <RouteTransitionProvider>
            <AuthProvider>
              <UserPreferencesProvider>
                <CartProvider>
                  <WishlistProvider>
                    <div className="min-h-screen bg-ice-white dark:bg-dark-grey text-text-primary dark:text-text-inverse transition-colors duration-300">
                      <NavigationProgress />

                      {/* Desktop Layout */}
                      <div className="hidden lg:block">
                        <Header />
                        <PageTransitionWrapper>
                          <main>
                            {children}
                          </main>
                        </PageTransitionWrapper>
                        <Footer />
                        <FloatingActionMenu />
                      </div>

                      {/* Mobile Layout */}
                      <div className="lg:hidden">
                        <MobileLayout>
                          <PageTransitionWrapper>
                            {children}
                          </PageTransitionWrapper>
                        </MobileLayout>
                      </div>
                    </div>
                  </WishlistProvider>
                </CartProvider>
              </UserPreferencesProvider>
            </AuthProvider>
          </RouteTransitionProvider>
        </ThemeProvider>
      </body>
    </html>
  )
}
