import { Inter, Playfair_Display, Fira_Code, Poppins } from 'next/font/google'
import localFont from 'next/font/local'
import './globals.css'
import { ThemeProvider } from '@/components/theme/ThemeProvider'
import { AuthProvider } from '@/contexts/AuthContext'
import { CartProvider } from '@/contexts/CartContext'
import { WishlistProvider } from '@/contexts/WishlistContext'
import { CartNotificationProvider } from '@/contexts/CartNotificationContext'
import { ToastProvider } from '@/components/ui/Toast'
import { PageErrorBoundary } from '@/components/ui/ErrorBoundary'
import { RouteTransitionProvider, PageTransitionWrapper } from '@/components/transitions/RouteTransitionProvider'
import NavigationProgress from '@/components/transitions/NavigationProgress'
import { UserPreferencesProvider } from '@/contexts/UserPreferencesContext'
import Header from '@/components/layout/NewHeader'
import Footer from '@/components/layout/Footer'
import AIFloatingButtons from '@/components/features/AIFloatingButtons'
import FloatingGamificationWidget from '@/components/features/FloatingGamificationWidget'
import HydrationProvider from '@/components/providers/HydrationProvider'

const inter = Inter({
  subsets: ['latin'],
  variable: '--font-inter',
  display: 'swap',
})

const playfair = Playfair_Display({
  subsets: ['latin'],
  variable: '--font-playfair',
  display: 'swap',
})

const firaCode = Fira_Code({
  subsets: ['latin'],
  variable: '--font-fira',
  display: 'swap',
})

const poppins = Poppins({
  subsets: ['latin'],
  variable: '--font-poppins',
  weight: ['300', '400', '500', '600', '700'],
  display: 'swap',
})

const godber = localFont({
  src: [
    {
      path: '../public/fonts/Godber-Regular.ttf',
      weight: '400',
      style: 'normal',
    },
  ],
  variable: '--font-godber',
  display: 'swap',
})

export const metadata = {
  title: {
    default: 'The White Laces | Luxury Streetwear Footwear',
    template: '%s | The White Laces'
  },
  description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands. Mexico-first, LATAM-ready.',
  keywords: ['luxury sneakers', 'streetwear', 'limited edition', 'premium footwear', 'Mexico', 'LATAM'],
  authors: [{ name: 'The White Laces Team' }],
  creator: 'The White Laces',
  publisher: 'The White Laces',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://thewhitelaces.com'),
  alternates: {
    canonical: '/',
    languages: {
      'es-MX': '/es-MX',
      'en-US': '/en-US',
      'pt-BR': '/pt-BR',
    },
  },
  openGraph: {
    type: 'website',
    locale: 'es_MX',
    url: 'https://thewhitelaces.com',
    title: 'The White Laces | Luxury Streetwear Footwear',
    description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands.',
    siteName: 'The White Laces',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'The White Laces - Luxury Streetwear Footwear',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'The White Laces | Luxury Streetwear Footwear',
    description: 'Discover luxury streetwear footwear at The White Laces. Premium sneakers, limited editions, and exclusive drops from top brands.',
    images: ['/og-image.jpg'],
    creator: '@thewhitelaces',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  verification: {
    google: 'your-google-verification-code',
    yandex: 'your-yandex-verification-code',
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="es-MX" suppressHydrationWarning>
      <body 
        className={`${inter.variable} ${playfair.variable} ${firaCode.variable} ${poppins.variable} ${godber.variable} font-poppins antialiased`}
        suppressHydrationWarning
      >
          <ThemeProvider
            attribute="class"
            defaultTheme="light"
            enableSystem
            disableTransitionOnChange={false}
          >
            <RouteTransitionProvider>
              <AuthProvider>
                <UserPreferencesProvider>
                  <CartProvider>
                    <CartNotificationProvider>
                      <WishlistProvider>
                        <ToastProvider>
                          <div className="min-h-screen bg-pure-white dark:bg-dark-gray text-text-primary dark:text-text-inverse transition-colors duration-300">
                            <NavigationProgress />

                            {/* Main Layout */}
                            <Header />
                            <PageTransitionWrapper>
                              <main>
                                <PageErrorBoundary>
                                  {children}
                                </PageErrorBoundary>
                              </main>
                            </PageTransitionWrapper>
                            <Footer />
                            <AIFloatingButtons />
                            <FloatingGamificationWidget />
                          </div>
                        </ToastProvider>
                      </WishlistProvider>
                    </CartNotificationProvider>
                  </CartProvider>
                </UserPreferencesProvider>
              </AuthProvider>
            </RouteTransitionProvider>
          </ThemeProvider>
      </body>
    </html>
  )
}
