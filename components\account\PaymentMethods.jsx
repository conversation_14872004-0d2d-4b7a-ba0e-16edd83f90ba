'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import AnimatedInput from '@/components/ui/AnimatedInput'
import AnimatedSelect from '@/components/ui/AnimatedSelect'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'

const cardTypes = [
  { value: 'visa', label: 'Visa' },
  { value: 'mastercard', label: 'Mastercard' },
  { value: 'amex', label: 'American Express' },
  { value: 'discover', label: 'Discover' }
]

const expiryMonths = Array.from({ length: 12 }, (_, i) => ({
  value: String(i + 1).padStart(2, '0'),
  label: String(i + 1).padStart(2, '0')
}))

const expiryYears = Array.from({ length: 10 }, (_, i) => {
  const year = new Date().getFullYear() + i
  return { value: String(year), label: String(year) }
})

const getCardIcon = (type) => {
  const icons = {
    visa: '💳',
    mastercard: '💳',
    amex: '💳',
    discover: '💳'
  }
  return icons[type] || '💳'
}

const maskCardNumber = (number) => {
  if (!number) return ''
  const cleaned = number.replace(/\D/g, '')
  return `**** **** **** ${cleaned.slice(-4)}`
}

const detectCardType = (number) => {
  const cleaned = number.replace(/\D/g, '')
  if (cleaned.startsWith('4')) return 'visa'
  if (cleaned.startsWith('5') || cleaned.startsWith('2')) return 'mastercard'
  if (cleaned.startsWith('3')) return 'amex'
  if (cleaned.startsWith('6')) return 'discover'
  return 'unknown'
}

export default function PaymentMethods() {
  const {
    paymentMethods,
    defaultPaymentId,
    addPaymentMethod,
    updatePaymentMethod,
    deletePaymentMethod,
    setDefaultPayment
  } = useUserPreferences()

  const [isAddingNew, setIsAddingNew] = useState(false)
  const [editingId, setEditingId] = useState(null)
  const [formData, setFormData] = useState({
    type: '',
    cardNumber: '',
    expiryMonth: '',
    expiryYear: '',
    cvv: '',
    cardholderName: '',
    billingAddress: {
      address1: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'MX'
    },
    isDefault: false
  })

  const resetForm = () => {
    setFormData({
      type: '',
      cardNumber: '',
      expiryMonth: '',
      expiryYear: '',
      cvv: '',
      cardholderName: '',
      billingAddress: {
        address1: '',
        city: '',
        state: '',
        postalCode: '',
        country: 'MX'
      },
      isDefault: false
    })
  }

  const handleInputChange = (field, value) => {
    if (field.startsWith('billingAddress.')) {
      const addressField = field.split('.')[1]
      setFormData(prev => ({
        ...prev,
        billingAddress: { ...prev.billingAddress, [addressField]: value }
      }))
    } else {
      setFormData(prev => ({ ...prev, [field]: value }))
      
      // Auto-detect card type
      if (field === 'cardNumber') {
        const detectedType = detectCardType(value)
        if (detectedType !== 'unknown') {
          setFormData(prev => ({ ...prev, type: detectedType }))
        }
      }
    }
  }

  const formatCardNumber = (value) => {
    const cleaned = value.replace(/\D/g, '')
    const formatted = cleaned.replace(/(\d{4})(?=\d)/g, '$1 ')
    return formatted.slice(0, 19) // Max 16 digits + 3 spaces
  }

  const handleCardNumberChange = (e) => {
    const formatted = formatCardNumber(e.target.value)
    handleInputChange('cardNumber', formatted)
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    // Create payment method with masked card number for storage
    const paymentData = {
      ...formData,
      cardNumber: formData.cardNumber.replace(/\D/g, ''), // Store clean number
      maskedNumber: maskCardNumber(formData.cardNumber)
    }
    
    if (editingId) {
      updatePaymentMethod(editingId, paymentData)
      if (formData.isDefault) {
        setDefaultPayment(editingId)
      }
      setEditingId(null)
    } else {
      const newId = addPaymentMethod(paymentData)
      if (formData.isDefault) {
        setDefaultPayment(newId)
      }
      setIsAddingNew(false)
    }
    
    resetForm()
  }

  const handleEdit = (payment) => {
    setFormData({
      ...payment,
      cardNumber: payment.maskedNumber || maskCardNumber(payment.cardNumber)
    })
    setEditingId(payment.id)
    setIsAddingNew(true)
  }

  const handleDelete = (id) => {
    if (confirm('¿Estás seguro de que quieres eliminar este método de pago?')) {
      deletePaymentMethod(id)
    }
  }

  const handleCancel = () => {
    setIsAddingNew(false)
    setEditingId(null)
    resetForm()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
            Métodos de Pago
          </h2>
          <p className="text-warm-camel mt-1">
            Gestiona tus tarjetas y métodos de pago
          </p>
        </div>
        
        {!isAddingNew && (
          <AnimatedButton
            variant="primary"
            onClick={() => setIsAddingNew(true)}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            }
          >
            Agregar Tarjeta
          </AnimatedButton>
        )}
      </div>

      {/* Add/Edit Form */}
      <AnimatePresence>
        {isAddingNew && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card variant="glass">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                  {editingId ? 'Editar Tarjeta' : 'Nueva Tarjeta'}
                </h3>
                
                <form onSubmit={handleSubmit} className="space-y-4">
                  <AnimatedInput
                    label="Nombre del Titular"
                    placeholder="Nombre como aparece en la tarjeta"
                    value={formData.cardholderName}
                    onChange={(e) => handleInputChange('cardholderName', e.target.value)}
                    required
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AnimatedInput
                      label="Número de Tarjeta"
                      placeholder="1234 5678 9012 3456"
                      value={formData.cardNumber}
                      onChange={handleCardNumberChange}
                      required
                      icon={<span className="text-lg">{getCardIcon(formData.type)}</span>}
                    />
                    
                    <AnimatedInput
                      label="CVV"
                      placeholder="123"
                      value={formData.cvv}
                      onChange={(e) => handleInputChange('cvv', e.target.value.slice(0, 4))}
                      required
                      type="password"
                    />
                  </div>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <AnimatedSelect
                      label="Mes de Expiración"
                      placeholder="Mes"
                      options={expiryMonths}
                      value={formData.expiryMonth}
                      onChange={(value) => handleInputChange('expiryMonth', value)}
                      required
                    />
                    
                    <AnimatedSelect
                      label="Año de Expiración"
                      placeholder="Año"
                      options={expiryYears}
                      value={formData.expiryYear}
                      onChange={(value) => handleInputChange('expiryYear', value)}
                      required
                    />
                  </div>
                  
                  <div className="border-t border-warm-camel/20 pt-4">
                    <h4 className="text-lg font-medium text-forest-emerald dark:text-light-cloud-gray mb-4">
                      Dirección de Facturación
                    </h4>
                    
                    <div className="space-y-4">
                      <AnimatedInput
                        label="Dirección"
                        placeholder="Calle y número"
                        value={formData.billingAddress.address1}
                        onChange={(e) => handleInputChange('billingAddress.address1', e.target.value)}
                        required
                      />
                      
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <AnimatedInput
                          label="Ciudad"
                          placeholder="Ciudad"
                          value={formData.billingAddress.city}
                          onChange={(e) => handleInputChange('billingAddress.city', e.target.value)}
                          required
                        />
                        
                        <AnimatedInput
                          label="Estado"
                          placeholder="Estado"
                          value={formData.billingAddress.state}
                          onChange={(e) => handleInputChange('billingAddress.state', e.target.value)}
                          required
                        />
                        
                        <AnimatedInput
                          label="Código Postal"
                          placeholder="12345"
                          value={formData.billingAddress.postalCode}
                          onChange={(e) => handleInputChange('billingAddress.postalCode', e.target.value)}
                          required
                        />
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="isDefault"
                      checked={formData.isDefault}
                      onChange={(e) => handleInputChange('isDefault', e.target.checked)}
                      className="w-4 h-4 text-rich-gold focus:ring-rich-gold border-warm-camel rounded"
                    />
                    <label htmlFor="isDefault" className="text-forest-emerald dark:text-light-cloud-gray">
                      Establecer como método de pago predeterminado
                    </label>
                  </div>
                  
                  <div className="flex gap-3 pt-4">
                    <AnimatedButton
                      type="submit"
                      variant="primary"
                      className="flex-1"
                    >
                      {editingId ? 'Actualizar Tarjeta' : 'Guardar Tarjeta'}
                    </AnimatedButton>
                    
                    <AnimatedButton
                      type="button"
                      variant="ghost"
                      onClick={handleCancel}
                      className="flex-1"
                    >
                      Cancelar
                    </AnimatedButton>
                  </div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Payment Methods List */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AnimatePresence>
          {paymentMethods.map((payment, index) => (
            <motion.div
              key={payment.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card 
                variant="glass" 
                className={`relative ${
                  payment.id === defaultPaymentId 
                    ? 'ring-2 ring-rich-gold shadow-lg' 
                    : ''
                }`}
              >
                <CardContent className="p-6">
                  {payment.id === defaultPaymentId && (
                    <div className="absolute top-3 right-3">
                      <span className="bg-rich-gold text-forest-emerald text-xs font-bold px-2 py-1 rounded-full">
                        Predeterminada
                      </span>
                    </div>
                  )}
                  
                  <div className="flex items-center gap-3 mb-4">
                    <span className="text-2xl">{getCardIcon(payment.type)}</span>
                    <div>
                      <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray capitalize">
                        {payment.type} {payment.maskedNumber}
                      </h3>
                      <p className="text-warm-camel text-sm">
                        Expira {payment.expiryMonth}/{payment.expiryYear}
                      </p>
                    </div>
                  </div>
                  
                  <div className="text-warm-camel text-sm mb-4">
                    <p className="font-medium">{payment.cardholderName}</p>
                    <p>{payment.billingAddress.address1}</p>
                    <p>
                      {payment.billingAddress.city}, {payment.billingAddress.state} {payment.billingAddress.postalCode}
                    </p>
                  </div>
                  
                  <div className="flex gap-2">
                    <AnimatedButton
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(payment)}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      }
                    >
                      Editar
                    </AnimatedButton>
                    
                    {payment.id !== defaultPaymentId && (
                      <AnimatedButton
                        variant="ghost"
                        size="sm"
                        onClick={() => setDefaultPayment(payment.id)}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        }
                      >
                        Predeterminada
                      </AnimatedButton>
                    )}
                    
                    <AnimatedButton
                      variant="danger"
                      size="sm"
                      onClick={() => handleDelete(payment.id)}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      }
                    >
                      Eliminar
                    </AnimatedButton>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {paymentMethods.length === 0 && !isAddingNew && (
        <Card variant="glass">
          <CardContent className="p-8 text-center">
            <div className="text-6xl mb-4">💳</div>
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
              No tienes métodos de pago guardados
            </h3>
            <p className="text-warm-camel mb-6">
              Agrega una tarjeta para acelerar el proceso de checkout
            </p>
            <AnimatedButton
              variant="primary"
              onClick={() => setIsAddingNew(true)}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              }
            >
              Agregar Primera Tarjeta
            </AnimatedButton>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
