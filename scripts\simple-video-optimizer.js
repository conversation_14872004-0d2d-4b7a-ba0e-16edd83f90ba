// Simple TWL Video Optimizer - Immediate execution
const fs = require('fs').promises
const path = require('path')
const { execSync } = require('child_process')

console.log('🎬 TWL SIMPLE VIDEO OPTIMIZER')
console.log('=============================')

async function optimizeVideos() {
  try {
    // Check FFmpeg
    console.log('🔍 Checking FFmpeg...')
    execSync('ffmpeg -version', { stdio: 'pipe' })
    console.log('✅ FFmpeg is available')

    // Scan for videos
    console.log('🔍 Scanning for videos...')
    const productsDir = path.join(process.cwd(), 'public', 'products')
    const videos = []
    
    await scanDirectory(productsDir, videos)
    
    console.log(`📊 Found ${videos.length} videos`)
    
    if (videos.length === 0) {
      console.log('ℹ️  No videos found')
      return
    }

    // Show first few videos as examples
    console.log('\n📹 Sample videos found:')
    videos.slice(0, 5).forEach((video, index) => {
      console.log(`${index + 1}. ${video.fileName} (${video.fileSizeMB}MB)`)
    })
    
    if (videos.length > 5) {
      console.log(`... and ${videos.length - 5} more videos`)
    }

    // Calculate total size
    const totalSizeMB = videos.reduce((sum, video) => sum + video.fileSizeMB, 0)
    console.log(`\n📦 Total size: ${Math.round(totalSizeMB * 100) / 100} MB`)

    // Optimize first video as test
    console.log('\n🧪 Testing optimization on first video...')
    const testVideo = videos[0]
    
    if (testVideo) {
      await optimizeVideo(testVideo)
    }

    console.log('\n✅ Video optimization test complete!')
    console.log('🎯 To optimize all videos, the script can be extended')
    
  } catch (error) {
    console.error('❌ Error:', error.message)
  }
}

async function scanDirectory(dirPath, videos) {
  try {
    const items = await fs.readdir(dirPath, { withFileTypes: true })
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item.name)
      
      if (item.isDirectory()) {
        await scanDirectory(fullPath, videos)
      } else if (isVideoFile(item.name)) {
        const stats = await fs.stat(fullPath)
        
        videos.push({
          sourcePath: fullPath,
          fileName: item.name,
          fileSize: stats.size,
          fileSizeMB: Math.round(stats.size / (1024 * 1024) * 100) / 100,
          relativePath: path.relative(path.join(process.cwd(), 'public', 'products'), fullPath)
        })
      }
    }
  } catch (error) {
    console.log(`⚠️  Could not scan ${dirPath}: ${error.message}`)
  }
}

function isVideoFile(fileName) {
  const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v']
  return videoExtensions.includes(path.extname(fileName).toLowerCase())
}

async function optimizeVideo(video) {
  try {
    console.log(`🔄 Optimizing: ${video.fileName}`)
    console.log(`📏 Original size: ${video.fileSizeMB} MB`)
    
    // Create backup directory
    const backupDir = path.join(process.cwd(), 'backup-videos')
    await fs.mkdir(backupDir, { recursive: true })
    
    // Create backup
    const backupPath = path.join(backupDir, video.fileName)
    await fs.copyFile(video.sourcePath, backupPath)
    console.log(`💾 Backup created: ${backupPath}`)
    
    // Create optimized version
    const tempPath = `${video.sourcePath}.optimized.mp4`
    
    const command = [
      'ffmpeg',
      '-i', `"${video.sourcePath}"`,
      '-c:v', 'libx264',
      '-crf', '28',
      '-preset', 'fast',
      '-vf', 'scale=\'min(1280,iw)\':\'min(720,ih)\':force_original_aspect_ratio=decrease',
      '-r', '24',
      '-c:a', 'aac',
      '-b:a', '96k',
      '-movflags', '+faststart',
      '-y', `"${tempPath}"`
    ].join(' ')
    
    console.log('⚙️  Running FFmpeg optimization...')
    execSync(command, { stdio: 'pipe', timeout: 180000 })
    
    // Check result
    const stats = await fs.stat(tempPath)
    const newSizeMB = Math.round(stats.size / (1024 * 1024) * 100) / 100
    const reduction = video.fileSizeMB - newSizeMB
    const reductionPercent = (reduction / video.fileSizeMB) * 100
    
    console.log(`📏 New size: ${newSizeMB} MB`)
    console.log(`📉 Reduction: ${Math.round(reduction * 100) / 100} MB (${Math.round(reductionPercent)}%)`)
    
    if (reductionPercent > 10) {
      // Replace original with optimized version
      await fs.rename(tempPath, video.sourcePath)
      console.log('✅ Optimization successful - original replaced')
    } else {
      // Remove optimized version if savings are minimal
      await fs.unlink(tempPath)
      console.log('⚠️  Minimal savings - keeping original')
    }
    
  } catch (error) {
    console.error(`❌ Failed to optimize ${video.fileName}:`, error.message)
  }
}

// Run the optimizer
optimizeVideos()
