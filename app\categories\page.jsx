'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { motion } from 'framer-motion'
import { getAllRealProducts } from '@/lib/real-products-loader'
import { MobileContainer, MobileSection } from '@/components/mobile/MobileContainer'

// CYTTE Category Configuration
const CATEGORIES = [
  {
    id: 'sneakers',
    name: 'SNEAKERS',
    description: 'Sneakers de lujo de las mejores marcas',
    icon: '👟',
    color: 'from-blue-500 to-blue-600',
    cytteFolder: '1. SNEAKERS',
    href: '/categories/sneakers'
  },
  {
    id: 'sandals',
    name: 'SANDALIAS', 
    description: 'Sandalias premium para cada ocasión',
    icon: '🩴',
    color: 'from-orange-500 to-orange-600',
    cytteFolder: '2. SANDALS',
    href: '/categories/sandals'
  },
  {
    id: 'formal',
    name: 'FORMAL',
    description: 'Calzado formal de alta gama',
    icon: '👔',
    color: 'from-gray-700 to-gray-800',
    cytteFolder: '3. FORMAL',
    href: '/categories/formal'
  },
  {
    id: 'casual',
    name: 'CASU<PERSON>',
    description: 'Estilo casual con toque de lujo',
    icon: '👞',
    color: 'from-green-500 to-green-600',
    cytteFolder: '4. CASUAL',
    href: '/categories/casual'
  },
  {
    id: 'kids',
    name: 'NIÑOS',
    description: 'Calzado de lujo para los más pequeños',
    icon: '👶',
    color: 'from-pink-500 to-pink-600',
    cytteFolder: '5. KIDS',
    href: '/categories/kids'
  }
]

export default function CategoriesPage() {
  const router = useRouter()
  const [categoryStats, setCategoryStats] = useState({})
  const [isLoading, setIsLoading] = useState(true)
  const [isMobile, setIsMobile] = useState(false)

  // Mobile detection
  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 1024)
    }
    checkMobile()
    window.addEventListener('resize', checkMobile)
    return () => window.removeEventListener('resize', checkMobile)
  }, [])

  // Load category statistics
  useEffect(() => {
    const loadCategoryStats = async () => {
      setIsLoading(true)
      try {
        const allProducts = getAllRealProducts()
        
        const stats = {}
        CATEGORIES.forEach(category => {
          const categoryProducts = allProducts.filter(product => {
            const productCategory = product.type || product.category
            return productCategory?.toLowerCase() === category.id.toLowerCase()
          })
          stats[category.id] = {
            count: categoryProducts.length,
            brands: [...new Set(categoryProducts.map(p => p.brand))].length,
            priceRange: {
              min: Math.min(...categoryProducts.map(p => p.price || 0)),
              max: Math.max(...categoryProducts.map(p => p.price || 0))
            }
          }
        })
        
        setCategoryStats(stats)
      } catch (error) {
        console.error('Error loading category stats:', error)
      } finally {
        setIsLoading(false)
      }
    }

    loadCategoryStats()
  }, [])

  const handleCategoryClick = (categoryHref) => {
    router.push(categoryHref)
  }

  // Mobile Layout
  if (isMobile) {
    return (
      <div className="min-h-screen bg-white dark:bg-gray-900 pt-16">
        <MobileContainer>
          <MobileSection
            title="Categorías"
            subtitle="Explora nuestra colección por categorías"
            className="pt-4"
          >
            <div className="space-y-4">
              {CATEGORIES.map((category, index) => (
                <motion.div
                  key={category.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  onClick={() => handleCategoryClick(category.href)}
                  className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm border border-gray-200 dark:border-gray-700 cursor-pointer hover:shadow-md transition-all duration-300"
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 rounded-full bg-gradient-to-r ${category.color} flex items-center justify-center text-white text-xl`}>
                        {category.icon}
                      </div>
                      <div>
                        <h3 className="text-lg font-bold text-black dark:text-white">
                          {category.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {category.description}
                        </p>
                        {!isLoading && categoryStats[category.id] && (
                          <p className="text-xs text-lime-green-dark font-medium mt-1">
                            {categoryStats[category.id].count} productos
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="text-gray-400">
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                      </svg>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          </MobileSection>
        </MobileContainer>
      </div>
    )
  }

  // Desktop Layout
  return (
    <div className="min-h-screen bg-white dark:bg-gray-900 pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-bold text-black dark:text-white mb-4">
            Explora por Categorías
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Descubre nuestra colección organizada por categorías. Desde sneakers de lujo hasta calzado formal de alta gama.
          </p>
        </motion.div>

        {/* Categories Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {CATEGORIES.map((category, index) => (
            <motion.div
              key={category.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
              onClick={() => handleCategoryClick(category.href)}
              className="group cursor-pointer"
            >
              <div className="bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-sm border border-gray-200 dark:border-gray-700 hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                
                {/* Category Icon */}
                <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${category.color} flex items-center justify-center text-white text-2xl mb-6 group-hover:scale-110 transition-transform duration-300`}>
                  {category.icon}
                </div>

                {/* Category Info */}
                <h3 className="text-2xl font-bold text-black dark:text-white mb-3 group-hover:text-lime-green-dark transition-colors">
                  {category.name}
                </h3>
                
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  {category.description}
                </p>

                {/* Category Stats */}
                {!isLoading && categoryStats[category.id] && (
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Productos:</span>
                      <span className="font-semibold text-lime-green-dark">
                        {categoryStats[category.id].count}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-gray-500">Marcas:</span>
                      <span className="font-semibold text-lime-green-dark">
                        {categoryStats[category.id].brands}
                      </span>
                    </div>
                    {categoryStats[category.id].priceRange.max > 0 && (
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-500">Desde:</span>
                        <span className="font-semibold text-lime-green-dark">
                          ${categoryStats[category.id].priceRange.min.toLocaleString('es-MX')} MXN
                        </span>
                      </div>
                    )}
                  </div>
                )}

                {/* Loading State */}
                {isLoading && (
                  <div className="space-y-2">
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-3/4"></div>
                    <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse w-1/2"></div>
                  </div>
                )}

                {/* Arrow Icon */}
                <div className="flex justify-end mt-6">
                  <div className="w-8 h-8 rounded-full bg-lime-green/10 flex items-center justify-center group-hover:bg-lime-green group-hover:text-black transition-all duration-300">
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="text-center mt-16"
        >
          <div className="bg-gradient-to-r from-lime-green/10 to-lime-green/5 rounded-2xl p-8">
            <h2 className="text-2xl font-bold text-black dark:text-white mb-4">
              ¿No encuentras lo que buscas?
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Explora toda nuestra colección o usa nuestras funciones de búsqueda avanzada
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button
                onClick={() => router.push('/shop')}
                className="bg-lime-green text-black px-8 py-4 rounded-lg hover:bg-lime-green/90 transition-colors font-semibold"
              >
                Ver Toda la Colección
              </button>
              <button
                onClick={() => router.push('/ai-features')}
                className="border border-lime-green text-lime-green-dark px-8 py-4 rounded-lg hover:bg-lime-green hover:text-black transition-colors font-semibold"
              >
                Búsqueda con IA
              </button>
            </div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
