'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import SocialShopping from '@/components/features/SocialShopping'
import UserGeneratedContent from '@/components/features/UserGeneratedContent'
import SocialWishlist from '@/components/features/SocialWishlist'
import GamificationSystem from '@/components/features/GamificationSystem'

const communityStats = {
  members: 12450,
  posts: 8920,
  reviews: 3240,
  outfits: 5680
}

const featuredMembers = [
  {
    id: 1,
    name: '<PERSON>',
    username: '@maria_sneaks',
    avatar: '👩🏻',
    level: 'Sneaker Expert',
    followers: 1250,
    posts: 89,
    badges: ['👑', '🔥', '⭐'],
    bio: 'Coleccionista de Jordan 1s y amante del streetwear'
  },
  {
    id: 2,
    name: '<PERSON>',
    username: '@carlos_style',
    avatar: '👨🏽',
    level: 'Style Influencer',
    followers: 3400,
    posts: 156,
    badges: ['🎨', '📸', '💎'],
    bio: 'Creador de contenido y consultor de moda urbana'
  },
  {
    id: 3,
    name: 'Ana Rodríguez',
    username: '@ana_kicks',
    avatar: '👩🏻‍🦱',
    level: 'Fashion Lover',
    followers: 890,
    posts: 67,
    badges: ['💖', '✨', '🌟'],
    bio: 'Apasionada por los sneakers femeninos y el color'
  }
]

const communityFeatures = [
  {
    id: 1,
    title: 'Feed Social',
    description: 'Comparte tus outfits, reseñas y descubrimientos',
    icon: '📱',
    color: 'from-blue-500 to-purple-600'
  },
  {
    id: 2,
    title: 'Reseñas Verificadas',
    description: 'Lee opiniones reales de otros sneakerheads',
    icon: '⭐',
    color: 'from-yellow-500 to-orange-600'
  },
  {
    id: 3,
    title: 'Outfit Inspiration',
    description: 'Descubre cómo combinar tus sneakers favoritos',
    icon: '👗',
    color: 'from-pink-500 to-rose-600'
  },
  {
    id: 4,
    title: 'Trending Now',
    description: 'Mantente al día con las últimas tendencias',
    icon: '📈',
    color: 'from-green-500 to-teal-600'
  },
  {
    id: 5,
    title: 'Gamificación',
    description: 'Gana puntos, badges y sube de nivel',
    icon: '🏆',
    color: 'from-orange-500 to-red-600'
  },
  {
    id: 6,
    title: 'Eventos Exclusivos',
    description: 'Acceso a drops y eventos solo para miembros',
    icon: '🎉',
    color: 'from-purple-500 to-indigo-600'
  }
]

const recentPosts = [
  {
    id: 1,
    user: 'María González',
    avatar: '👩🏻',
    content: '¡Acabo de recibir mis nuevos Jordan 1 Chicago! La calidad es increíble 🔥',
    likes: 89,
    comments: 23,
    time: '2h'
  },
  {
    id: 2,
    user: 'Carlos Mendoza',
    avatar: '👨🏽',
    content: 'Outfit del día: Yeezy 350 + jeans vintage. ¿Qué opinan del combo?',
    likes: 156,
    comments: 45,
    time: '4h'
  },
  {
    id: 3,
    user: 'Ana Rodríguez',
    avatar: '👩🏻‍🦱',
    content: '¿Alguien más está esperando el restock de los Dunk Low Panda? 🐼',
    likes: 67,
    comments: 34,
    time: '6h'
  }
]

const recentActivity = [
  {
    id: 1,
    user: 'María González',
    avatar: '👩🏻',
    action: 'compró Jordan 1 Chicago',
    type: 'purchase',
    time: '2h'
  },
  {
    id: 2,
    user: 'Carlos Mendoza',
    avatar: '👨🏽',
    action: 'escribió una reseña',
    type: 'review',
    time: '4h'
  },
  {
    id: 3,
    user: 'Ana Rodríguez',
    avatar: '👩🏻‍🦱',
    action: 'compartió en Instagram',
    type: 'social',
    time: '6h'
  }
]

export default function CommunityPage() {
  const [showSocialModal, setShowSocialModal] = useState(false)
  const [activeTab, setActiveTab] = useState('overview') // overview, ugc, wishlists, gamification

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Comunidad TWL
          </h1>
          <p className="text-warm-camel text-lg max-w-3xl mx-auto">
            Únete a la comunidad de sneakerheads más vibrante de México. 
            Comparte, descubre y conecta con otros apasionados del calzado.
          </p>
        </motion.div>

        {/* Community Stats */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-12"
        >
          <Card variant="glass">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                {communityStats.members.toLocaleString()}
              </div>
              <div className="text-sm text-warm-camel">Miembros</div>
            </CardContent>
          </Card>
          
          <Card variant="glass">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                {communityStats.posts.toLocaleString()}
              </div>
              <div className="text-sm text-warm-camel">Posts</div>
            </CardContent>
          </Card>
          
          <Card variant="glass">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                {communityStats.reviews.toLocaleString()}
              </div>
              <div className="text-sm text-warm-camel">Reseñas</div>
            </CardContent>
          </Card>
          
          <Card variant="glass">
            <CardContent className="p-6 text-center">
              <div className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-2">
                {communityStats.outfits.toLocaleString()}
              </div>
              <div className="text-sm text-warm-camel">Outfits</div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Tabs */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="mb-8"
        >
          <div className="flex flex-wrap gap-2 justify-center">
            {[
              { id: 'overview', label: 'Resumen', icon: '🏠' },
              { id: 'ugc', label: 'Contenido', icon: '📸' },
              { id: 'wishlists', label: 'Listas', icon: '💖' },
              { id: 'gamification', label: 'Progreso', icon: '🏆' }
            ].map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`px-6 py-3 rounded-lg font-medium transition-all duration-200 flex items-center gap-2 ${
                  activeTab === tab.id
                    ? 'bg-rich-gold text-forest-emerald shadow-lg'
                    : 'bg-warm-camel/10 text-warm-camel hover:bg-warm-camel/20'
                }`}
              >
                <span>{tab.icon}</span>
                <span>{tab.label}</span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Tab Content */}
        <AnimatePresence mode="wait">
          {activeTab === 'overview' && (
            <motion.div
              key="overview"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className="space-y-16"
            >
              {/* Featured Members */}
              <div>
                <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8">
                  Miembros Destacados
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {featuredMembers.map((member, index) => (
                    <motion.div
                      key={member.id}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: index * 0.1 }}
                    >
                      <Card variant="glass" className="group hover:shadow-lg transition-all duration-300">
                        <CardContent className="p-6 text-center">
                          <div className="w-20 h-20 mx-auto mb-4 bg-gradient-to-br from-rich-gold to-warm-camel rounded-full flex items-center justify-center text-3xl">
                            {member.avatar}
                          </div>

                          <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-1">
                            {member.name}
                          </h3>

                          <p className="text-warm-camel text-sm mb-2">{member.username}</p>

                          <Badge variant="primary" size="sm" className="mb-4">
                            {member.level}
                          </Badge>

                          <p className="text-warm-camel text-xs mb-4 line-clamp-2">
                            {member.bio}
                          </p>

                          <div className="flex justify-center gap-1 mb-4">
                            {member.badges.map((badge, i) => (
                              <span key={i} className="text-lg">{badge}</span>
                            ))}
                          </div>

                          <div className="grid grid-cols-2 gap-4 text-center">
                            <div>
                              <div className="text-lg font-bold text-forest-emerald dark:text-light-cloud-gray">
                                {member.followers.toLocaleString()}
                              </div>
                              <div className="text-xs text-warm-camel">Seguidores</div>
                            </div>
                            <div>
                              <div className="text-lg font-bold text-forest-emerald dark:text-light-cloud-gray">
                                {member.posts}
                              </div>
                              <div className="text-xs text-warm-camel">Posts</div>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </motion.div>
                  ))}
                </div>
              </div>

              {/* Community Features */}
              <div>
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8">
            Características de la Comunidad
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {communityFeatures.map((feature, index) => (
              <motion.div
                key={feature.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 1.1 + index * 0.1 }}
              >
                <Card variant="default" className="group hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${feature.color} flex items-center justify-center text-2xl`}>
                        {feature.icon}
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-1">
                          {feature.title}
                        </h3>
                        <p className="text-warm-camel text-sm">
                          {feature.description}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
                ))}
              </div>
              </div>
            </motion.div>
          )}

          {activeTab === 'ugc' && (
            <motion.div
              key="ugc"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <UserGeneratedContent />
            </motion.div>
          )}

          {activeTab === 'wishlists' && (
            <motion.div
              key="wishlists"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <SocialWishlist />
            </motion.div>
          )}

          {activeTab === 'gamification' && (
            <motion.div
              key="gamification"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <GamificationSystem />
            </motion.div>
          )}
        </AnimatePresence>

        {/* Join Community CTA */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.8 }}
          className="text-center"
        >
          <Card variant="glass">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
                ¿Listo para unirte?
              </h2>
              <p className="text-warm-camel mb-8 max-w-2xl mx-auto">
                Crea tu cuenta y forma parte de la comunidad de sneakerheads más 
                activa de México. Comparte tus pasiones y descubre nuevas tendencias.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <AnimatedButton
                  variant="primary"
                  size="lg"
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                  }
                >
                  Crear Cuenta
                </AnimatedButton>
                
                <AnimatedButton
                  variant="secondary"
                  size="lg"
                  onClick={() => setShowSocialModal(true)}
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                    </svg>
                  }
                >
                  Explorar Comunidad
                </AnimatedButton>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>

      {/* Social Shopping Modal */}
      <AnimatePresence>
        {showSocialModal && (
          <SocialShopping
            onClose={() => setShowSocialModal(false)}
          />
        )}
      </AnimatePresence>
    </div>
  )
}
