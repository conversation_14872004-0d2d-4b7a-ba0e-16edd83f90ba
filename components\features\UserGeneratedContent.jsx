'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'

// Mock UGC data
const mockUGCPosts = [
  {
    id: 1,
    user: {
      id: 'user1',
      name: '<PERSON>',
      username: '@maria_sneaks',
      avatar: '👩🏻',
      verified: true,
      followers: 1250
    },
    content: {
      text: '¡Mis nuevos Jordan 1 Chicago llegaron perfectos! La calidad es increíble y el servicio de TWL es de otro nivel 🔥 #TWLLook #Jordan1',
      images: ['/placeholder-ugc-1.jpg', '/placeholder-ugc-2.jpg'],
      products: [
        { id: 4, name: 'Jordan 1 Retro High Chicago', price: 4200 }
      ]
    },
    engagement: {
      likes: 89,
      comments: 23,
      shares: 12,
      saves: 45
    },
    timestamp: '2024-01-20T10:30:00Z',
    hashtags: ['#TWLLook', '#Jordan1', '#Sneakerhead'],
    location: 'Ciudad de México',
    featured: true
  },
  {
    id: 2,
    user: {
      id: 'user2',
      name: '<PERSON>',
      username: '@carlos_style',
      avatar: '👨🏽',
      verified: false,
      followers: 890
    },
    content: {
      text: 'Outfit del día con mis Yeezy 350 V2. Perfect combo para el street style mexicano ✨',
      images: ['/placeholder-ugc-3.jpg'],
      products: [
        { id: 2, name: 'Yeezy Boost 350 V2 Zebra', price: 8500 }
      ]
    },
    engagement: {
      likes: 156,
      comments: 34,
      shares: 8,
      saves: 67
    },
    timestamp: '2024-01-19T15:45:00Z',
    hashtags: ['#TWLLook', '#Yeezy', '#OOTD'],
    location: 'Guadalajara',
    featured: false
  },
  {
    id: 3,
    user: {
      id: 'user3',
      name: 'Ana Rodríguez',
      username: '@ana_kicks',
      avatar: '👩🏻‍🦱',
      verified: true,
      followers: 2100
    },
    content: {
      text: '¿Alguien más está obsesionado con los Air Force 1? Son perfectos para cualquier ocasión 💫',
      images: ['/placeholder-ugc-4.jpg', '/placeholder-ugc-5.jpg'],
      products: [
        { id: 1, name: 'Nike Air Force 1 Low White', price: 2500 }
      ]
    },
    engagement: {
      likes: 203,
      comments: 56,
      shares: 15,
      saves: 89
    },
    timestamp: '2024-01-18T12:20:00Z',
    hashtags: ['#TWLLook', '#AirForce1', '#Nike'],
    location: 'Monterrey',
    featured: true
  }
]

export default function UserGeneratedContent({ 
  className = '',
  showCreatePost = true,
  maxPosts = 6,
  featured = false 
}) {
  const [posts, setPosts] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedPost, setSelectedPost] = useState(null)
  const [filter, setFilter] = useState('all') // all, featured, following
  const [isCreatingPost, setIsCreatingPost] = useState(false)

  useEffect(() => {
    // Simulate loading posts
    const loadPosts = async () => {
      setIsLoading(true)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      let filteredPosts = mockUGCPosts
      if (featured) {
        filteredPosts = mockUGCPosts.filter(post => post.featured)
      }
      if (filter === 'featured') {
        filteredPosts = mockUGCPosts.filter(post => post.featured)
      }
      
      setPosts(filteredPosts.slice(0, maxPosts))
      setIsLoading(false)
    }
    
    loadPosts()
  }, [featured, filter, maxPosts])

  const handleLike = (postId) => {
    setPosts(posts.map(post => 
      post.id === postId 
        ? { ...post, engagement: { ...post.engagement, likes: post.engagement.likes + 1 } }
        : post
    ))
  }

  const handleShare = (post) => {
    // In a real app, this would open share modal
    if (navigator.share) {
      navigator.share({
        title: `${post.user.name} en TWL`,
        text: post.content.text,
        url: `${window.location.origin}/community/post/${post.id}`
      })
    } else {
      // Fallback to copy link
      navigator.clipboard.writeText(`${window.location.origin}/community/post/${post.id}`)
      alert('Link copiado al portapapeles')
    }
  }

  const formatTimeAgo = (timestamp) => {
    const now = new Date()
    const postTime = new Date(timestamp)
    const diffInHours = Math.floor((now - postTime) / (1000 * 60 * 60))
    
    if (diffInHours < 1) return 'Hace menos de 1h'
    if (diffInHours < 24) return `Hace ${diffInHours}h`
    const diffInDays = Math.floor(diffInHours / 24)
    return `Hace ${diffInDays}d`
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
            Comunidad TWL
          </h2>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, index) => (
            <div key={index} className="animate-pulse">
              <Card variant="default">
                <CardContent className="p-4">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-10 h-10 bg-warm-camel/20 rounded-full"></div>
                    <div className="flex-1">
                      <div className="bg-warm-camel/20 rounded h-4 mb-1"></div>
                      <div className="bg-warm-camel/20 rounded h-3 w-2/3"></div>
                    </div>
                  </div>
                  <div className="bg-warm-camel/20 rounded-lg h-48 mb-4"></div>
                  <div className="bg-warm-camel/20 rounded h-4 mb-2"></div>
                  <div className="bg-warm-camel/20 rounded h-3 w-3/4"></div>
                </CardContent>
              </Card>
            </div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-6 ${className}`}>
      
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
            {featured ? 'Posts Destacados' : 'Comunidad TWL'}
          </h2>
          <p className="text-warm-camel text-sm">
            Descubre cómo otros sneakerheads combinan sus TWL
          </p>
        </div>
        
        {showCreatePost && (
          <AnimatedButton
            variant="primary"
            size="sm"
            onClick={() => setIsCreatingPost(true)}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            }
          >
            Crear Post
          </AnimatedButton>
        )}
      </div>

      {/* Filters */}
      {!featured && (
        <div className="flex gap-2">
          {[
            { id: 'all', label: 'Todos' },
            { id: 'featured', label: 'Destacados' },
            { id: 'following', label: 'Siguiendo' }
          ].map((filterOption) => (
            <button
              key={filterOption.id}
              onClick={() => setFilter(filterOption.id)}
              className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                filter === filterOption.id
                  ? 'bg-rich-gold text-forest-emerald'
                  : 'bg-warm-camel/10 text-warm-camel hover:bg-warm-camel/20'
              }`}
            >
              {filterOption.label}
            </button>
          ))}
        </div>
      )}

      {/* Posts Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {posts.map((post, index) => (
          <motion.div
            key={post.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card variant="default" className="group hover:shadow-lg transition-all duration-300 overflow-hidden">
              <CardContent className="p-0">
                
                {/* User Header */}
                <div className="p-4 pb-0">
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-rich-gold to-warm-camel rounded-full flex items-center justify-center text-lg">
                      {post.user.avatar}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-1">
                        <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm">
                          {post.user.name}
                        </h4>
                        {post.user.verified && (
                          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                          </svg>
                        )}
                      </div>
                      <div className="flex items-center gap-2 text-xs text-warm-camel">
                        <span>{post.user.username}</span>
                        <span>•</span>
                        <span>{formatTimeAgo(post.timestamp)}</span>
                      </div>
                    </div>
                    
                    {post.featured && (
                      <Badge variant="primary" size="sm">Destacado</Badge>
                    )}
                  </div>
                </div>

                {/* Content */}
                <div className="p-4 pt-3">
                  <p className="text-forest-emerald dark:text-light-cloud-gray text-sm mb-3 line-clamp-3">
                    {post.content.text}
                  </p>
                  
                  {/* Hashtags */}
                  <div className="flex flex-wrap gap-1 mb-3">
                    {post.hashtags.slice(0, 3).map((hashtag) => (
                      <span key={hashtag} className="text-xs text-rich-gold hover:underline cursor-pointer">
                        {hashtag}
                      </span>
                    ))}
                  </div>
                </div>

                {/* Images */}
                <div className="relative">
                  <div className="aspect-square bg-warm-camel/10 flex items-center justify-center group-hover:scale-105 transition-transform duration-300">
                    <span className="text-4xl">📸</span>
                  </div>
                  
                  {post.content.images.length > 1 && (
                    <div className="absolute top-2 right-2 bg-black/50 text-white text-xs px-2 py-1 rounded-full">
                      +{post.content.images.length - 1}
                    </div>
                  )}
                </div>

                {/* Products */}
                {post.content.products.length > 0 && (
                  <div className="p-4 border-t border-warm-camel/20">
                    <div className="flex items-center gap-2 mb-2">
                      <svg className="w-4 h-4 text-warm-camel" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                      <span className="text-xs text-warm-camel font-medium">Productos en el post:</span>
                    </div>
                    {post.content.products.map((product) => (
                      <div key={product.id} className="flex items-center justify-between text-xs">
                        <span className="text-forest-emerald dark:text-light-cloud-gray line-clamp-1">
                          {product.name}
                        </span>
                        <span className="font-semibold text-rich-gold">
                          ${product.price.toLocaleString()}
                        </span>
                      </div>
                    ))}
                  </div>
                )}

                {/* Engagement */}
                <div className="p-4 pt-0">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center gap-4">
                      <button
                        onClick={() => handleLike(post.id)}
                        className="flex items-center gap-1 text-warm-camel hover:text-red-500 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                        <span className="text-xs">{post.engagement.likes}</span>
                      </button>
                      
                      <button className="flex items-center gap-1 text-warm-camel hover:text-blue-500 transition-colors">
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                        </svg>
                        <span className="text-xs">{post.engagement.comments}</span>
                      </button>
                    </div>
                    
                    <button
                      onClick={() => handleShare(post)}
                      className="flex items-center gap-1 text-warm-camel hover:text-green-500 transition-colors"
                    >
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                      </svg>
                      <span className="text-xs">{post.engagement.shares}</span>
                    </button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Load More */}
      {posts.length >= maxPosts && (
        <div className="text-center">
          <AnimatedButton
            variant="secondary"
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
            }
          >
            Ver Más Posts
          </AnimatedButton>
        </div>
      )}
    </div>
  )
}
