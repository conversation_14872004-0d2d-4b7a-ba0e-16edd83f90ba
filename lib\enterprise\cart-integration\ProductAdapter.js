/**
 * TWL Enterprise Product Adapter for Cart Integration
 * Bridges the enterprise product system with the existing cart implementation
 */

import { getTWLSystem } from '../TWLEnterpriseSystem'
import { getProductById } from '@/lib/data/products'
import { loadRealProduct } from '@/lib/real-products-loader'

class EnterpriseProductAdapter {
  constructor() {
    this.enterpriseSystem = null
    this.fallbackEnabled = true
    this.cacheEnabled = true
    this.performanceMetrics = {
      enterpriseHits: 0,
      fallbackHits: 0,
      errors: 0,
      averageResponseTime: 0
    }
  }

  /**
   * Initialize the enterprise system connection
   */
  async initialize() {
    try {
      this.enterpriseSystem = getTWLSystem()
      if (!this.enterpriseSystem) {
        console.warn('🔄 Enterprise system not initialized, using fallback mode')
        return false
      }
      
      const health = this.enterpriseSystem.getHealth()
      if (health.status !== 'healthy') {
        console.warn('⚠️ Enterprise system unhealthy, using fallback mode')
        return false
      }
      
      console.log('✅ Enterprise product adapter initialized successfully')
      return true
    } catch (error) {
      console.error('❌ Failed to initialize enterprise adapter:', error)
      return false
    }
  }

  /**
   * Get product data optimized for cart usage
   * @param {string} productId - Product identifier
   * @returns {Promise<Object>} Cart-optimized product data
   */
  async getProductForCart(productId) {
    const startTime = Date.now()
    
    try {
      // Try enterprise system first
      if (this.enterpriseSystem) {
        const enterpriseProduct = await this.enterpriseSystem.getProduct(productId)
        
        if (enterpriseProduct) {
          this.performanceMetrics.enterpriseHits++
          const cartProduct = this.transformToCartFormat(enterpriseProduct)
          
          this.updateMetrics(startTime)
          console.log(`🚀 Enterprise product loaded: ${productId} (${Date.now() - startTime}ms)`)
          
          return cartProduct
        }
      }

      // Fallback to existing product loaders
      if (this.fallbackEnabled) {
        return await this.loadWithFallback(productId, startTime)
      }

      throw new Error('Product not found and fallback disabled')

    } catch (error) {
      this.performanceMetrics.errors++
      console.error(`❌ Error loading product ${productId}:`, error)
      
      // Try fallback even if enterprise system failed
      if (this.fallbackEnabled) {
        return await this.loadWithFallback(productId, startTime)
      }
      
      throw error
    }
  }

  /**
   * Load product using fallback methods
   */
  async loadWithFallback(productId, startTime) {
    try {
      // Try loadRealProduct first (your current system)
      let product = await loadRealProduct(productId)
      
      if (product) {
        this.performanceMetrics.fallbackHits++
        this.updateMetrics(startTime)
        console.log(`🔄 Fallback product loaded: ${productId} (${Date.now() - startTime}ms)`)
        return product
      }

      // Final fallback to getProductById
      product = await getProductById(productId)
      
      if (product) {
        this.performanceMetrics.fallbackHits++
        this.updateMetrics(startTime)
        console.log(`🔄 Basic product loaded: ${productId} (${Date.now() - startTime}ms)`)
        return product
      }

      throw new Error(`Product ${productId} not found in any system`)

    } catch (fallbackError) {
      console.error(`❌ All fallback methods failed for ${productId}:`, fallbackError)
      throw fallbackError
    }
  }

  /**
   * Transform enterprise product data to cart-compatible format
   */
  transformToCartFormat(enterpriseProduct) {
    return {
      // Core cart fields (maintain compatibility)
      id: enterpriseProduct.id,
      name: enterpriseProduct.name,
      brand: enterpriseProduct.brand.name,
      price: enterpriseProduct.pricing.currentPrice,
      image: enterpriseProduct.media.primaryImage || enterpriseProduct.media.images[0]?.url,
      images: enterpriseProduct.media.images.map(img => img.url),
      
      // Enhanced enterprise fields
      originalPrice: enterpriseProduct.pricing.originalPrice,
      discountPercent: enterpriseProduct.pricing.discountPercent,
      isOnSale: enterpriseProduct.pricing.isOnSale,
      currency: enterpriseProduct.pricing.currency,
      
      // Product details
      description: enterpriseProduct.details.description,
      features: enterpriseProduct.details.features,
      isLimitedEdition: enterpriseProduct.details.isLimitedEdition,
      isCollaboration: enterpriseProduct.details.isCollaboration,
      collaborationPartner: enterpriseProduct.details.collaborationPartner,
      
      // Inventory information
      inStock: enterpriseProduct.inventory.inStock,
      stockLevel: enterpriseProduct.inventory.stockCount,
      sizes: enterpriseProduct.inventory.sizes,
      lowStock: enterpriseProduct.inventory.stockCount < 5,
      
      // Media
      videos: enterpriseProduct.media.videos.map(vid => vid.url),
      thumbnail: enterpriseProduct.media.thumbnail,
      
      // Metadata
      rating: enterpriseProduct.metadata.rating,
      reviewCount: enterpriseProduct.metadata.reviewCount,
      isPopular: enterpriseProduct.metadata.isPopular,
      isTrending: enterpriseProduct.metadata.isTrending,
      
      // Category and brand info
      category: {
        id: enterpriseProduct.category.id,
        name: enterpriseProduct.category.name
      },
      brandInfo: {
        id: enterpriseProduct.brand.id,
        name: enterpriseProduct.brand.name,
        isLuxury: enterpriseProduct.brand.isLuxury,
        tier: enterpriseProduct.brand.tier
      },
      
      // Enterprise metadata
      _source: 'enterprise',
      _loadedAt: new Date().toISOString(),
      _cached: true
    }
  }

  /**
   * Validate cart item against current product data
   */
  async validateCartItem(cartItem) {
    try {
      const currentProduct = await this.getProductForCart(cartItem.productId)
      
      return {
        isValid: currentProduct.inStock,
        hasStockIssue: !currentProduct.inStock || currentProduct.stockLevel < cartItem.quantity,
        hasPriceChange: currentProduct.price !== cartItem.price,
        currentPrice: currentProduct.price,
        originalPrice: cartItem.price,
        stockLevel: currentProduct.stockLevel,
        isLowStock: currentProduct.lowStock,
        product: currentProduct
      }
    } catch (error) {
      console.error(`❌ Error validating cart item ${cartItem.productId}:`, error)
      return {
        isValid: false,
        hasError: true,
        error: error.message
      }
    }
  }

  /**
   * Get product recommendations based on cart items
   */
  async getCartRecommendations(cartItems, limit = 4) {
    try {
      if (!this.enterpriseSystem) {
        return []
      }

      const productIds = cartItems.map(item => item.productId)
      const brands = [...new Set(cartItems.map(item => item.brand))]
      const categories = [...new Set(cartItems.map(item => item.category?.name).filter(Boolean))]

      // Search for similar products
      const recommendations = await this.enterpriseSystem.searchProducts('', {
        brands: brands.slice(0, 3), // Limit to top 3 brands
        categories: categories.slice(0, 2), // Limit to top 2 categories
        inStockOnly: true,
        sortBy: 'popularity',
        sortOrder: 'desc'
      }, 1, limit * 2) // Get more than needed to filter out cart items

      if (recommendations && recommendations.products) {
        // Filter out products already in cart
        const filtered = recommendations.products
          .filter(product => !productIds.includes(product.id))
          .slice(0, limit)
          .map(product => this.transformToCartFormat(product))

        return filtered
      }

      return []
    } catch (error) {
      console.error('❌ Error getting cart recommendations:', error)
      return []
    }
  }

  /**
   * Batch load multiple products for cart
   */
  async getProductsForCart(productIds) {
    const results = []
    const errors = []

    // Use Promise.allSettled to handle partial failures
    const promises = productIds.map(async (productId) => {
      try {
        const product = await this.getProductForCart(productId)
        return { productId, product, success: true }
      } catch (error) {
        return { productId, error: error.message, success: false }
      }
    })

    const settled = await Promise.allSettled(promises)

    settled.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        if (result.value.success) {
          results.push(result.value.product)
        } else {
          errors.push(result.value)
        }
      } else {
        errors.push({
          productId: productIds[index],
          error: result.reason.message,
          success: false
        })
      }
    })

    return { products: results, errors }
  }

  /**
   * Update performance metrics
   */
  updateMetrics(startTime) {
    const responseTime = Date.now() - startTime
    const totalRequests = this.performanceMetrics.enterpriseHits + this.performanceMetrics.fallbackHits
    
    this.performanceMetrics.averageResponseTime = 
      (this.performanceMetrics.averageResponseTime * (totalRequests - 1) + responseTime) / totalRequests
  }

  /**
   * Get adapter performance metrics
   */
  getMetrics() {
    const total = this.performanceMetrics.enterpriseHits + this.performanceMetrics.fallbackHits
    
    return {
      ...this.performanceMetrics,
      totalRequests: total,
      enterpriseHitRate: total > 0 ? (this.performanceMetrics.enterpriseHits / total * 100).toFixed(1) : 0,
      errorRate: total > 0 ? (this.performanceMetrics.errors / total * 100).toFixed(1) : 0
    }
  }

  /**
   * Reset performance metrics
   */
  resetMetrics() {
    this.performanceMetrics = {
      enterpriseHits: 0,
      fallbackHits: 0,
      errors: 0,
      averageResponseTime: 0
    }
  }
}

// Create singleton instance
const productAdapter = new EnterpriseProductAdapter()

// Initialize on first import
productAdapter.initialize().catch(console.error)

export default productAdapter
export { EnterpriseProductAdapter }
