This guide includes:

🎨 Color System
🔠 Typography
🧩 Component Naming Conventions
📐 Spacing & Layout Rules
🌗 Dark Mode Handling
🎞️ Animation Guidelines
💡 Best Practices
🛠️ The White Laces – Developer Style Guide
Glassmorphic | Minimalist Luxury | Streetwear Edge | Mobile-First
👟 Luxury Footwear Meets Gen Z Culture | E-commerce Platform 

1. 🎨 Color System
Base UI Colors

Name,               HEX / RGBA,                             Use Case
Fog Black,      #14161A,                                Main background (dark mode)
Mist Gray,      #1E2127,                                "Cards, overlays"
Frosted Overlay,    "rgba(255,255,255,0.08)",           Glassmorphism blur layer
Arctic White,   #FAFAFA,                                Light mode background
Soft Cloud,     #F3F4F6,                                "Light cards, input backgrounds"


🌈 Accent Colors (Luxury Streetwear Energy)

Name,                   HEX,                        Use
Neon Pulse,         #FF1C53,                    "Primary CTA buttons, limited tags"
Cyber Blue,         #00F9FF,                    "Hover states, links"
Gold Dust,          #FFD166,"VIP badges, early access"
Velvet Red,         #B91C3B,"Sale alerts, error states"
Mint Glow,          #7EE892,"Success messages, wishlists added"


🌤️ Gradient Variants (for hero sections, banners)

1. Sunset Pulse (Limited Editions)
linear-gradient(135deg, #FF1C53, #FFD166)

2. Cyber Flow (Sneakers / Tech Drops)
linear-gradient(135deg, #00F9FF, #1E2127)

3. Earth Luxe (Luxury Brands)
linear-gradient(135deg, #7EE892, #B91C3B)


2. 🔠 Typography
Font Pairing


Type,Font Family,Weights Used
Headings,Playfair Display", "Cinzel,"Bold, Regular"
Body Text,Inter", "Helvetica Neue,"Light, Regular, Medium"
Monospace,Fira Code", "JetBrains Mono,"Code snippets, console logs"


Font Sizes (Tailwind-compatible)

Size,px / rem,Use Case
xs,12px / 0.75rem,"Captions, labels"
sm,14px / 0.875rem,"Buttons, small text"
base,16px / 1rem,Body text
lg,18px / 1.125rem,Subheaders
xl,20px / 1.25rem,Section headers
2xl,24px / 1.5rem,Hero titles
3xl,30px / 1.875rem,Feature headlines


Font Weight Mapping

Weight,Tailwind Class,Use Case
Light,font-light,"Descriptions, subtle text"
Regular,font-normal,General body copy
Medium,font-medium,"Buttons, subheaders"
Bold,font-bold,"Titles, CTAs"
Black,font-black,Feature headlines


3. 📐 Spacing & Layout
Spacing Scale (Tailwind-compatible)

Class,Value (px/rem),Use Case
p-1,4px / 0.25rem,Small padding/margin
p-2,8px / 0.5rem,Button spacing
p-3,12px / 0.75rem,Card inner padding
p-4,16px / 1rem,Section padding
p-6,24px / 1.5rem,Modal content padding
p-8,32px / 2rem,Hero section padding


Border Radius

Class,Value,Use Case
rounded,0.25rem,Standard cards/buttons
rounded-lg,0.5rem,"Product images, banners"
rounded-xl,1rem,Floating action buttons
rounded-full,Full circle,"Profile icons, badges"


Shadow Levels

Class,Description
shadow-none,No shadow
shadow-sm,Slight elevation (buttons)
shadow,Default card shadows
shadow-md,Stronger depth (modals)
shadow-glass,Custom glassmorphic effect


4. 🌗 Dark Mode Handling

We use class-based dark mode via Tailwind’s dark: variant:
<div class="bg-fog-black text-white dark:bg-mist-gray">


Toggle Logic (React Example):
const toggleDarkMode = () => {
  document.documentElement.classList.toggle('dark');
};


Add this in your global styles (globals.css or _app.css):
.dark {
  color-scheme: dark;
}


5. 🎞️ Animation & Microinteractions
Transition Classes

Property,Tailwind Class,Description
Background,transition-colors,Smooth background changes
Opacity,transition-opacity,Fading elements
Transform,transition-transform,"Hover effects, scale animations"
All Properties,transition-all,For complex transitions


Animation Examples

Animation,Tailwind Class,Description
Fade-in,animate-fadeIn,Toast notifications
Glow Button,animate-glow,Interactive CTAs
Neon Pulse,animate-pulse-neon,Limited edition tags



Keyframes (from Tailwind config):

keyframes: {
  pulseNeon: {
    '0%': { boxShadow: '0 0 8px #FF1C53' },
    '100%': { boxShadow: '0 0 16px #FF1C53' }
  },
  glow: {
    '0%, 100%': { transform: 'scale(1)' },
    '50%': { transform: 'scale(1.03)' }
  },
  fadeIn: {
    '0%': { opacity: '0' },
    '100%': { opacity: '1' }
  }
}


6. 🧩 Component Styling Standards

✅ Class Structure Pattern
Use this pattern consistently across components:

<component-class>
  <background + blur + border>
  <spacing>
  <typography>
  <interaction-states>
</component-class>


Example Button:

<button class="
  w-full py-2 px-4
  bg-neon-pulse text-white rounded-lg
  hover:bg-[#e01647] transition-all
">
  Add to Cart
</button>


7. 🧱 Utility Patterns
Glassmorphic Container:

<div class="
  backdrop-blur-md 
  bg-mist-gray 
  border border-frosted-overlay 
  rounded-xl 
  p-4 
  shadow-glass
">
  <!-- Content -->
</div>


Responsive Grid:

<div class="grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4">
  <!-- Cards go here -->
</div>


Gradient Button:

<button class="bg-gradient-to-r from-gradient-start to-gradient-end text-white py-2 px-6 rounded-lg">
  Shop Now
</button>


8. 🧪 Accessibility Best Practices
Always use semantic HTML (<button>, <a>, <nav>)
Ensure all interactive elements are keyboard accessible
Provide alt text on all images
Use high contrast colors (WCAG AA minimum)
Avoid pure black/white for long reading
Animate only if it enhances experience


9. 🧩 Reusable Component Patterns
Glassmorphic Card

<div class="backdrop-blur-md bg-mist-gray border border-frosted-overlay rounded-xl p-4 shadow-glass">
  <img src="product.jpg" alt="Product" class="w-full h-auto rounded-lg mb-4" />
  <h3 class="text-white font-bold">Nike x Off-White Dunk</h3>
  <p class="text-sm text-cyber-blue">$180</p>
  <button class="mt-3 w-full py-2 bg-neon-pulse text-white rounded-lg hover:bg-[#e01647] transition-all">
    Add to Cart
  </button>
</div>


Toast Notification

<div class="fixed bottom-4 right-4 bg-mint-glow text-black px-4 py-2 rounded-lg shadow-glass backdrop-blur-sm animate-fade-in">
  Added to wishlist!
</div>


Form Input

<input type="text" placeholder="Email" class="
  w-full p-3
  bg-mist-gray
  border border-frosted-overlay
  rounded-lg
  text-white
  placeholder:text-gray-400
  focus:outline-none
  focus:border-neon-pulse
"/>



10. 📁 File Structure Recommendations

/components
  /ui
    Button.jsx
    Card.jsx
    Modal.jsx
    Badge.jsx
    Input.jsx
/styles
  globals.css
  tailwind.config.js
/pages
  /index.jsx
  /shop.jsx
  /product/[id].jsx
/lib
  /utils.js
  /theme.js
/public
  /images
  /icons



  11. 📦 Developer Tooling
Recommended Tools:
VSCode Extensions : Tailwind CSS IntelliSense, Prettier, Bracket Pair Colorizer
Linting : ESLint + Tailwind Sorter
Preview : Storybook (for component library)
Performance : Lighthouse, Web Vitals, Image Optimization (Cloudinary or ImageKit)


