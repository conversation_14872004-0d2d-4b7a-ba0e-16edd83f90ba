Below is a comprehensive AI Prompt Library for building the TWL Admin Dashboard (CMS/CRM) — your luxury streetwear e-commerce platform’s admin interface , built with Next.js , styled using Tailwind CSS , and featuring a glassmorphic UI .

This library includes AI-powered prompts that you can use with tools like Cursor , GPT-4 , or Claude 4 to generate:

✅ Reusable React components
✅ Theme-aware UI elements
✅ Localization-ready interfaces
✅ AI-powered admin features
Each prompt is structured to ensure consistency, performance, and alignment with your Mexico-first strategy , mobile-first design , and future LATAM expansion .

🧠 The White Laces – AI Prompt Library for Admin Components
Next.js | Tailwind CSS | Glassmorphism UI | Mexico-First Strategy
👟 Luxury Footwear Meets Gen Z Culture | E-commerce Platform 

📋 General Prompt Template
Use this format when asking AI tools to generate components:

Generate a [Component Name] for TWL Admin Dashboard using React and Tailwind CSS.
Support:
- Dark mode by default
- Light mode toggle
- Glassmorphism style (frosted overlay, soft blur)
Include:
- Necessary props
- Accessible ARIA attributes
Optional:
- TypeScript support
Return:
- JSX file
- Example usage


🧱 Atomic Components (/components/ui)
🧾 ProductCard (Editable)

Generate an editable ProductCard component for TWL Admin Dashboard using React and Tailwind CSS.
Include:
- Image upload area
- Name, brand, category inputs
- Price input with currency selector
- Limited Edition toggle
Style: Glassmorphic with Mist Gray background and Neon Pulse accents


🧾 OrderTable

Create a sortable OrderTable for TWL Admin Dashboard.
Show:
- Order ID
- Customer name
- Total
- Status (Pending, Processing, Delivered)
Add filters and export to CSV option.
Use Tailwind CSS with dark theme


🧠 UGCPostCard (Moderation View)

Design a UGCPostCard for TWL Admin Dashboard.
Include:
- Post image
- Caption
- Approve/Reject buttons
- Share to Instagram button
Style: Soft Cloud background with Frosted Overlay


🧾 CampaignForm

Build a CampaignForm component for TWL Admin Dashboard.
Fields:
- Title
- Message
- Target audience
- Send date
Include preview pane and send test button.
Use glassmorphic styling with soft blur


🧾 LanguageEditor

Create a language translation editor for TWL Admin Dashboard.
Allow editing JSON keys for:
- es-MX (Spanish - Mexico)
- en-US (English)
- pt-BR (Portuguese - Brazil)
Include search bar and auto-suggestions.
Support syntax highlighting


🖼️ MediaUploader

Write a drag-and-drop MediaUploader for TWL Admin Dashboard.
Features:
- Upload product images
- Preview thumbnails
- Delete uploaded files
- Auto-optimize for web
Use Tailwind CSS with backdrop blur effect


🧠 WishlistPreview

Generate a WishlistPreview component for TWL Admin Dashboard.
Show user's saved items in grid layout.
Include:
- Product image
- Brand
- Price
- Add to cart button
Support filtering by user


📢 NotificationBanner

Create a NotificationBanner for TWL Admin Dashboard.
Use cases:
- New drop alerts
- Low stock warnings
- UGC trending posts
Style: Cyber Blue glow with neon pulse animation


🧑‍🤝‍🧑 CreatorProfileCard

Design a CreatorProfileCard for TWL Admin Dashboard.
Include:
- Influencer avatar
- Bio
- Referral links
- Engagement stats
Style: VIP gold-dust background with frosted border


📈 StatsCard (Sales, Traffic, Engagement)

Implement a StatsCard for TWL Admin Dashboard.
Display metrics like:
- Sales
- User growth
- UGC engagement
Use gradient borders and animated number counters.
Support light/dark mode


🧭 SidebarNav (Admin Navigation)

Create a SidebarNav component for TWL Admin Dashboard.
Include:
- Products
- Orders
- Users
- UGC Wall
- Content
- Marketing
- Settings
Use active state highlight and smooth transitions.
Support icons from Lucide or Heroicons


🎨 Layout & Theme Logic
🌗 ThemeProvider (Dark/Light Toggle)

Write a ThemeProvider context for TWL Admin Dashboard.
Allow switching between dark and light themes.
Persist preference in localStorage.
Apply theme class to <html> tag.
Include useTheme hook


🌙 DarkModeToggle (Button)

Create a dark mode toggle component for TWL Admin Dashboard.
Use sun/moon icon.
Animate scale-up on press.
Save preference in localStorage.


🧭 AdminLayout Wrapper

Generate a layout wrapper for TWL Admin Dashboard.
Include:
- Sidebar navigation
- Top header with logo
- Main content area
- Footer with version info
Make it responsive and theme-aware


🛠️ Feature-Based Components (/components/features)
📦 ProductManagementPanel

Build a ProductManagementPanel for TWL Admin Dashboard.
Features:
- Add new product form
- Bulk edit via CSV
- Filter by brand, gender, availability
Include:
- Drag-and-drop image uploader
- SEO meta tags section
- Save draft/publish toggle


🧾 OrderManagementSystem

Create an OrderManagementSystem for TWL Admin Dashboard.
Features:
- Sort by status (pending, shipped, delivered)
- Export invoice as PDF
- Cancel/refund order
Include:
- Modal for updating shipping address
- Status badge with color indicators


🧠 UserManagementTool

Implement a UserManagementTool for TWL Admin Dashboard.
Features:
- View customer profiles
- See wishlist/favorites
- Block/spam user
Include:
- Search bar
- Export user list
- Filter by loyalty tier


🧪 UGCModerationPanel

Generate a UGC moderation panel for TWL Admin Dashboard.
Allow admins to:
- Approve/reject posts
- Highlight top looks
- Ban spam accounts
Include:
- Infinite scroll
- Batch actions
- Flagging system


🧩 LocalizationManager

Create a LocalizationManager for TWL Admin Dashboard.
Allow editing:
- Spanish (es-MX)
- English (en-US)
- Portuguese (pt-BR)
Include:
- Key-value editor
- Sync with live site
- Auto-detect missing translations


🧠 CampaignBuilder

Design a CampaignBuilder for TWL Admin Dashboard.
Features:
- Email/SMS templates
- Audience segmentation
- Schedule delivery
- Track open/click rates
Use glassmorphic card style with soft blur


🧮 LoyaltyProgramEditor

Build a LoyaltyProgramEditor for TWL Admin Dashboard.
Features:
- Manage tiers (Bronze, Silver, Gold)
- Edit badge icons
- Set point rewards
Include:
- Level-up animations
- Manual awarding of points


🧠 ChatbotDashboard

Implement a chatbot management dashboard for TWL.
Features:
- Train responses
- Monitor popular queries
- Integrate with NLP model
Include:
- Response editor
- Analytics chart
- Live query log


📊 AnalyticsDashboard

Generate an AnalyticsDashboard for TWL Admin Dashboard.
Include:
- Sales Overview (daily/weekly/monthly)
- Top Products
- UGC Engagement
- Campaign Performance
Use charts (Chart.js or ApexCharts).
Support export to CSV/PDF


🧰 API & Backend Integration Prompts
🔒 Authenticated API Call Hook

Write a custom hook for authenticated API calls in TWL Admin Dashboard.
Support:
- JWT tokens
- Firebase auth
- Error handling
- Loading states
Include example usage in component


📦 Product API Integration

Create a Next.js API route for managing products in TWL Admin Dashboard.
Support:
- GET /products
- POST /products
- PUT /products/:id
- DELETE /products/:id
Use PostgreSQL or Firebase Firestore


🧠 Order API Service

Write a service file for managing orders in TWL Admin Dashboard.
Include functions for:
- Fetch all orders
- Update order status
- Export to CSV
Use async/await and error boundaries


📣 Notification API Endpoint

Generate a notification endpoint for TWL Admin Dashboard.
Support:
- Get all notifications
- Mark as read
- Send push alert
Use Redis or Firebase Realtime DB


🧪 Testing & QA Prompts
✅ Jest Test for ProductCard

Write a Jest unit test for ProductCard in TWL Admin Dashboard.
Test:
- Renders correctly
- Handles image upload
- Updates metadata
Use React Testing Library


🧪 Cypress E2E Test for Order Table

Generate a Cypress E2E test for OrderTable in TWL Admin Dashboard.
Test:
- Filters work
- Pagination loads
- Invoice export works
Use fixtures for mock data


🧹 ESLint Rules for Admin Codebase

Create ESLint rules for TWL Admin Dashboard.
Enforce:
- No console.log
- Proper Tailwind ordering
- Alt text on images
- TypeScript types
Include Prettier integration


📄 Documentation & DevOps Prompts
📄 Storybook Entry for ProductCard

Generate a Storybook entry for ProductCard in TWL Admin Dashboard.
Include:
- Default card
- With limited edition badge
- In dark/light mode
Use MDX format for documentation


📝 Notion Page Template for Component Docs

Create a Notion page template for documenting admin components.
Sections:
- Purpose
- Props
- Code Sample
- Usage Notes
- Accessibility Info


📋 JSDoc for useTheme Hook

Add JSDoc comments to the useTheme hook in TWL Admin Dashboard.
Document:
- What the hook does
- Exposed methods
- Example usage
- TypeScript types


🧩 Bonus: AI-Powered Admin Features
🎙️ VoiceSearchLogViewer

Build a VoiceSearchLogViewer for TWL Admin Dashboard.
Display:
- Transcribed query
- Matched product
- Date/time
Include filters and export options.
Use Neon Pulse highlights


🖼️ VisualSearchImageGallery

Generate a VisualSearchImageGallery for TWL Admin Dashboard.
Allow uploading, tagging, and linking to shoe styles.
Include:
- Grid view
- Upload modal
- Tag suggestions
Style: Frosted cards with soft blur


🧠 SmartRecommendationTuner

Create a SmartRecommendationTuner for TWL Admin Dashboard.
Allow manually adjusting:
- Similar product matches
- Boost certain shoes
- Blacklist items
Use searchable dropdowns and drag-and-drop sorting


🧩 StyleMatchTrainingTool

Design a StyleMatchTrainingTool for TWL Admin Dashboard.
Allow uploading outfit photos and linking them to shoe styles.
Include:
- Image upload
- Shoe selector
- Save association
Use editorial layout with glassmorphic cards


📁 Folder Structure Suggestion

/components
├── /ui              # Atomic components
│   ├── Button.jsx
│   ├── Input.jsx
│   └── Badge.jsx
│
├── /features        # Feature-based components
│   ├── ProductCard.jsx
│   ├── OrderTable.jsx
│   └── UGCPostCard.jsx
│
├── /layout          # Layout wrappers
│   ├── Header.jsx
│   └── SidebarNav.jsx
│
├── /theme           # Theme logic
│   ├── ThemeProvider.jsx
│   └── useTheme.js
│
└── /shared          # Utilities
    ├── formatPrice.js
    └── slugify.js


