/**
 * TWL Minimal Integration Test Script
 * Tests the minimal cart enhancement integration
 */

const fs = require('fs')
const path = require('path')

async function testMinimalIntegration() {
  console.log('🧪 TWL Minimal Integration Test')
  console.log('=' .repeat(50))

  const testResults = {
    fileTests: [],
    integrationTests: [],
    errors: [],
    overallSuccess: false
  }

  try {
    // Test 1: Check if minimal integration files exist
    console.log('\n1. 📁 Testing File Structure...')
    await testFileStructure(testResults)

    // Test 2: Check cart page integration
    console.log('\n2. 🛒 Testing Cart Page Integration...')
    await testCartPageIntegration(testResults)

    // Test 3: Check API endpoints
    console.log('\n3. 🔌 Testing API Endpoints...')
    await testAPIEndpoints(testResults)

    // Generate report
    testResults.overallSuccess = generateMinimalReport(testResults)

  } catch (error) {
    console.error('❌ Test suite failed:', error)
    testResults.errors.push(`Test suite error: ${error.message}`)
  }

  return testResults
}

async function testFileStructure(testResults) {
  const requiredFiles = [
    'lib/enterprise/minimal/CartEnhancer.js',
    'lib/enterprise/minimal/useCartEnhancements.js',
    'app/api/enterprise/products/route.ts',
    'app/api/enterprise/products/[id]/route.ts',
    'app/api/enterprise/system/status/route.ts'
  ]

  for (const file of requiredFiles) {
    try {
      if (fs.existsSync(file)) {
        const content = fs.readFileSync(file, 'utf8')
        const hasContent = content.length > 100 // Basic content check
        
        testResults.fileTests.push({
          name: `File: ${file}`,
          success: hasContent,
          message: hasContent ? 'File exists and has content' : 'File exists but appears empty'
        })
        
        console.log(`  ${hasContent ? '✅' : '⚠️'} ${file}`)
      } else {
        testResults.fileTests.push({
          name: `File: ${file}`,
          success: false,
          message: 'File does not exist'
        })
        console.log(`  ❌ ${file} - Missing`)
      }
    } catch (error) {
      testResults.fileTests.push({
        name: `File: ${file}`,
        success: false,
        message: error.message
      })
      console.log(`  ❌ ${file} - Error: ${error.message}`)
    }
  }
}

async function testCartPageIntegration(testResults) {
  try {
    const cartPagePath = 'app/cart/page.jsx'
    
    if (fs.existsSync(cartPagePath)) {
      const content = fs.readFileSync(cartPagePath, 'utf8')
      
      // Check for minimal integration patterns
      const hasOriginalCart = content.includes('useCart') && content.includes('@/contexts/CartContext')
      const hasEnhancements = content.includes('useCartEnhancements')
      const hasOptionalFeatures = content.includes('enhancements.hasSavings') || content.includes('enhancements.hasRecommendations')
      const hasNonBreakingChanges = content.includes('getTotal()') // Original function still used
      
      testResults.integrationTests.push({
        name: 'Cart Page - Original Cart Hook',
        success: hasOriginalCart,
        message: hasOriginalCart ? 'Original cart hook preserved' : 'Original cart hook missing'
      })
      
      testResults.integrationTests.push({
        name: 'Cart Page - Enhancement Hook',
        success: hasEnhancements,
        message: hasEnhancements ? 'Enhancement hook added' : 'Enhancement hook missing'
      })
      
      testResults.integrationTests.push({
        name: 'Cart Page - Optional Features',
        success: hasOptionalFeatures,
        message: hasOptionalFeatures ? 'Optional enhancement features implemented' : 'Optional features missing'
      })
      
      testResults.integrationTests.push({
        name: 'Cart Page - Non-Breaking Changes',
        success: hasNonBreakingChanges,
        message: hasNonBreakingChanges ? 'Original functionality preserved' : 'Original functionality may be broken'
      })
      
      console.log(`  ${hasOriginalCart ? '✅' : '❌'} Original cart hook preserved`)
      console.log(`  ${hasEnhancements ? '✅' : '❌'} Enhancement hook added`)
      console.log(`  ${hasOptionalFeatures ? '✅' : '❌'} Optional features implemented`)
      console.log(`  ${hasNonBreakingChanges ? '✅' : '❌'} Non-breaking changes`)
      
    } else {
      testResults.integrationTests.push({
        name: 'Cart Page Integration',
        success: false,
        message: 'Cart page file not found'
      })
      console.log('  ❌ Cart page file not found')
    }
  } catch (error) {
    testResults.integrationTests.push({
      name: 'Cart Page Integration',
      success: false,
      message: error.message
    })
    console.log(`  ❌ Cart page integration test failed: ${error.message}`)
  }
}

async function testAPIEndpoints(testResults) {
  const apiFiles = [
    'app/api/enterprise/system/status/route.ts',
    'app/api/enterprise/products/route.ts',
    'app/api/enterprise/products/[id]/route.ts'
  ]

  for (const apiFile of apiFiles) {
    try {
      if (fs.existsSync(apiFile)) {
        const content = fs.readFileSync(apiFile, 'utf8')
        const hasGETHandler = content.includes('export async function GET')
        const hasErrorHandling = content.includes('try') && content.includes('catch')
        const hasNextResponse = content.includes('NextResponse')
        
        const isValid = hasGETHandler && hasErrorHandling && hasNextResponse
        
        testResults.integrationTests.push({
          name: `API: ${apiFile}`,
          success: isValid,
          message: isValid ? 'API endpoint properly structured' : 'API endpoint missing required components'
        })
        
        console.log(`  ${isValid ? '✅' : '❌'} ${apiFile}`)
        if (!isValid) {
          console.log(`    - GET handler: ${hasGETHandler ? '✅' : '❌'}`)
          console.log(`    - Error handling: ${hasErrorHandling ? '✅' : '❌'}`)
          console.log(`    - NextResponse: ${hasNextResponse ? '✅' : '❌'}`)
        }
      } else {
        testResults.integrationTests.push({
          name: `API: ${apiFile}`,
          success: false,
          message: 'API file does not exist'
        })
        console.log(`  ❌ ${apiFile} - Missing`)
      }
    } catch (error) {
      testResults.integrationTests.push({
        name: `API: ${apiFile}`,
        success: false,
        message: error.message
      })
      console.log(`  ❌ ${apiFile} - Error: ${error.message}`)
    }
  }
}

function generateMinimalReport(testResults) {
  console.log('\n📋 MINIMAL INTEGRATION TEST REPORT')
  console.log('=' .repeat(50))

  const allTests = [
    ...testResults.fileTests,
    ...testResults.integrationTests
  ]

  const totalTests = allTests.length
  const passedTests = allTests.filter(test => test.success).length
  const failedTests = totalTests - passedTests

  console.log(`\n📊 Summary:`)
  console.log(`  Total Tests: ${totalTests}`)
  console.log(`  Passed: ${passedTests} ✅`)
  console.log(`  Failed: ${failedTests} ${failedTests > 0 ? '❌' : '✅'}`)
  console.log(`  Success Rate: ${((passedTests / totalTests) * 100).toFixed(1)}%`)

  console.log(`\n📁 File Tests: ${testResults.fileTests.filter(t => t.success).length}/${testResults.fileTests.length}`)
  console.log(`🔧 Integration Tests: ${testResults.integrationTests.filter(t => t.success).length}/${testResults.integrationTests.length}`)

  if (testResults.errors.length > 0) {
    console.log(`\n❌ Errors:`)
    testResults.errors.forEach(error => console.log(`  - ${error}`))
  }

  const overallSuccess = failedTests === 0 && testResults.errors.length === 0
  const successRate = (passedTests / totalTests) * 100

  console.log(`\n🎯 Overall Result: ${overallSuccess ? '✅ SUCCESS' : successRate >= 80 ? '⚠️ MOSTLY SUCCESSFUL' : '❌ NEEDS ATTENTION'}`)

  if (overallSuccess) {
    console.log('\n🎉 Minimal integration completed successfully!')
    console.log('✅ All files created and properly structured')
    console.log('✅ Cart page enhanced without breaking changes')
    console.log('✅ API endpoints ready for enterprise features')
    console.log('\n🚀 Your cart now has optional enterprise enhancements!')
    console.log('💡 Features will automatically enable when enterprise API is available')
  } else if (successRate >= 80) {
    console.log('\n⚠️ Integration mostly successful with minor issues')
    console.log('🔧 Review failed tests and address any issues')
    console.log('🚀 Core functionality should be working')
  } else {
    console.log('\n❌ Integration needs attention')
    console.log('🔧 Please review and fix the failed tests')
  }

  return overallSuccess
}

// Run tests if this file is executed directly
if (require.main === module) {
  testMinimalIntegration()
    .then(results => {
      const success = results.overallSuccess
      console.log(`\n${success ? '🎉' : '⚠️'} Minimal integration test completed`)
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('❌ Test execution failed:', error)
      process.exit(1)
    })
}

module.exports = { testMinimalIntegration }
