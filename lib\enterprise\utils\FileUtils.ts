/**
 * TWL Enterprise File Utilities
 * File system operations and utilities for the product system
 */

import fs from 'fs'
import path from 'path'
import crypto from 'crypto'

export interface FileInfo {
  name: string
  path: string
  size: number
  extension: string
  mimeType: string
  lastModified: Date
  checksum: string
}

export interface DirectoryInfo {
  name: string
  path: string
  fileCount: number
  directoryCount: number
  totalSize: number
  lastModified: Date
}

export class FileUtils {
  /**
   * Get file information
   */
  static async getFileInfo(filePath: string): Promise<FileInfo | null> {
    try {
      const stats = await fs.promises.stat(filePath)
      
      if (!stats.isFile()) {
        return null
      }

      const name = path.basename(filePath)
      const extension = path.extname(filePath).toLowerCase().slice(1)
      const checksum = await this.calculateFileChecksum(filePath)

      return {
        name,
        path: filePath,
        size: stats.size,
        extension,
        mimeType: this.getMimeType(extension),
        lastModified: stats.mtime,
        checksum
      }
    } catch (error) {
      return null
    }
  }

  /**
   * Get directory information
   */
  static async getDirectoryInfo(dirPath: string): Promise<DirectoryInfo | null> {
    try {
      const stats = await fs.promises.stat(dirPath)
      
      if (!stats.isDirectory()) {
        return null
      }

      const items = await fs.promises.readdir(dirPath)
      let fileCount = 0
      let directoryCount = 0
      let totalSize = 0

      for (const item of items) {
        const itemPath = path.join(dirPath, item)
        const itemStats = await fs.promises.stat(itemPath)

        if (itemStats.isFile()) {
          fileCount++
          totalSize += itemStats.size
        } else if (itemStats.isDirectory()) {
          directoryCount++
        }
      }

      return {
        name: path.basename(dirPath),
        path: dirPath,
        fileCount,
        directoryCount,
        totalSize,
        lastModified: stats.mtime
      }
    } catch (error) {
      return null
    }
  }

  /**
   * Calculate file checksum
   */
  static async calculateFileChecksum(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const hash = crypto.createHash('md5')
      const stream = fs.createReadStream(filePath)

      stream.on('data', data => hash.update(data))
      stream.on('end', () => resolve(hash.digest('hex')))
      stream.on('error', reject)
    })
  }

  /**
   * Get MIME type from file extension
   */
  static getMimeType(extension: string): string {
    const mimeTypes: Record<string, string> = {
      // Images
      'jpg': 'image/jpeg',
      'jpeg': 'image/jpeg',
      'png': 'image/png',
      'webp': 'image/webp',
      'gif': 'image/gif',
      'svg': 'image/svg+xml',
      
      // Videos
      'mp4': 'video/mp4',
      'webm': 'video/webm',
      'mov': 'video/quicktime',
      'avi': 'video/x-msvideo',
      
      // Documents
      'txt': 'text/plain',
      'json': 'application/json',
      'xml': 'application/xml',
      'pdf': 'application/pdf',
      
      // Default
      '': 'application/octet-stream'
    }

    return mimeTypes[extension.toLowerCase()] || mimeTypes['']
  }

  /**
   * Check if file exists
   */
  static async fileExists(filePath: string): Promise<boolean> {
    try {
      await fs.promises.access(filePath, fs.constants.F_OK)
      return true
    } catch {
      return false
    }
  }

  /**
   * Check if directory exists
   */
  static async directoryExists(dirPath: string): Promise<boolean> {
    try {
      const stats = await fs.promises.stat(dirPath)
      return stats.isDirectory()
    } catch {
      return false
    }
  }

  /**
   * Create directory recursively
   */
  static async ensureDirectory(dirPath: string): Promise<void> {
    try {
      await fs.promises.mkdir(dirPath, { recursive: true })
    } catch (error) {
      throw new Error(`Failed to create directory ${dirPath}: ${error.message}`)
    }
  }

  /**
   * Read file as text
   */
  static async readTextFile(filePath: string): Promise<string | null> {
    try {
      return await fs.promises.readFile(filePath, 'utf-8')
    } catch {
      return null
    }
  }

  /**
   * Read file as JSON
   */
  static async readJsonFile<T = any>(filePath: string): Promise<T | null> {
    try {
      const content = await this.readTextFile(filePath)
      return content ? JSON.parse(content) : null
    } catch {
      return null
    }
  }

  /**
   * Write text file
   */
  static async writeTextFile(filePath: string, content: string): Promise<void> {
    try {
      await this.ensureDirectory(path.dirname(filePath))
      await fs.promises.writeFile(filePath, content, 'utf-8')
    } catch (error) {
      throw new Error(`Failed to write file ${filePath}: ${error.message}`)
    }
  }

  /**
   * Write JSON file
   */
  static async writeJsonFile(filePath: string, data: any, pretty: boolean = true): Promise<void> {
    try {
      const content = pretty ? JSON.stringify(data, null, 2) : JSON.stringify(data)
      await this.writeTextFile(filePath, content)
    } catch (error) {
      throw new Error(`Failed to write JSON file ${filePath}: ${error.message}`)
    }
  }

  /**
   * Copy file
   */
  static async copyFile(sourcePath: string, destinationPath: string): Promise<void> {
    try {
      await this.ensureDirectory(path.dirname(destinationPath))
      await fs.promises.copyFile(sourcePath, destinationPath)
    } catch (error) {
      throw new Error(`Failed to copy file from ${sourcePath} to ${destinationPath}: ${error.message}`)
    }
  }

  /**
   * Delete file
   */
  static async deleteFile(filePath: string): Promise<void> {
    try {
      await fs.promises.unlink(filePath)
    } catch (error) {
      throw new Error(`Failed to delete file ${filePath}: ${error.message}`)
    }
  }

  /**
   * Delete directory recursively
   */
  static async deleteDirectory(dirPath: string): Promise<void> {
    try {
      await fs.promises.rm(dirPath, { recursive: true, force: true })
    } catch (error) {
      throw new Error(`Failed to delete directory ${dirPath}: ${error.message}`)
    }
  }

  /**
   * Get files in directory with filter
   */
  static async getFilesInDirectory(
    dirPath: string, 
    options: {
      recursive?: boolean
      extensions?: string[]
      pattern?: RegExp
    } = {}
  ): Promise<string[]> {
    const { recursive = false, extensions, pattern } = options
    const files: string[] = []

    try {
      const items = await fs.promises.readdir(dirPath)

      for (const item of items) {
        const itemPath = path.join(dirPath, item)
        const stats = await fs.promises.stat(itemPath)

        if (stats.isFile()) {
          // Check extension filter
          if (extensions) {
            const ext = path.extname(item).toLowerCase().slice(1)
            if (!extensions.includes(ext)) continue
          }

          // Check pattern filter
          if (pattern && !pattern.test(item)) continue

          files.push(itemPath)
        } else if (stats.isDirectory() && recursive) {
          const subFiles = await this.getFilesInDirectory(itemPath, options)
          files.push(...subFiles)
        }
      }
    } catch (error) {
      throw new Error(`Failed to read directory ${dirPath}: ${error.message}`)
    }

    return files
  }

  /**
   * Get directory size recursively
   */
  static async getDirectorySize(dirPath: string): Promise<number> {
    let totalSize = 0

    try {
      const items = await fs.promises.readdir(dirPath)

      for (const item of items) {
        const itemPath = path.join(dirPath, item)
        const stats = await fs.promises.stat(itemPath)

        if (stats.isFile()) {
          totalSize += stats.size
        } else if (stats.isDirectory()) {
          totalSize += await this.getDirectorySize(itemPath)
        }
      }
    } catch (error) {
      // Ignore errors for inaccessible directories
    }

    return totalSize
  }

  /**
   * Format file size in human readable format
   */
  static formatFileSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB', 'TB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(1)} ${units[unitIndex]}`
  }

  /**
   * Clean filename (remove invalid characters)
   */
  static cleanFilename(filename: string): string {
    return filename
      .replace(/[<>:"/\\|?*]/g, '') // Remove invalid characters
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .replace(/_{2,}/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, '') // Remove leading/trailing underscores
  }

  /**
   * Get unique filename (add suffix if file exists)
   */
  static async getUniqueFilename(filePath: string): Promise<string> {
    if (!(await this.fileExists(filePath))) {
      return filePath
    }

    const dir = path.dirname(filePath)
    const ext = path.extname(filePath)
    const name = path.basename(filePath, ext)

    let counter = 1
    let uniquePath: string

    do {
      uniquePath = path.join(dir, `${name}_${counter}${ext}`)
      counter++
    } while (await this.fileExists(uniquePath))

    return uniquePath
  }
}

export default FileUtils
