-- 🌱 TWL SEED DATA
-- 🎯 Initial data for brands, categories, and collections

-- 🏷️ INSERT BRANDS
INSERT INTO brands (name, slug, description, country, founded_year, category, featured) VALUES
('Nike', 'nike', 'Just Do It - Global leader in athletic footwear and apparel', 'USA', 1964, 'Athletic', true),
('Adidas', 'adidas', 'Impossible is Nothing - German multinational corporation', 'Germany', 1949, 'Athletic', true),
('Gucci', 'gucci', 'Italian luxury fashion house founded in Florence', 'Italy', 1921, 'Luxury', true),
('Dior', 'dior', 'French luxury fashion house controlled by LVMH', 'France', 1946, 'Luxury', true),
('Louis Vuitton', 'louis-vuitton', 'French fashion house and luxury goods company', 'France', 1854, 'Luxury', true),
('Balenciaga', 'balenciaga', 'Spanish luxury fashion house founded by <PERSON><PERSON><PERSON><PERSON>', 'Spain', 1919, 'Luxury', true),
('<PERSON><PERSON>', 'chanel', 'French luxury fashion house founded by <PERSON><PERSON>', 'France', 1910, 'Luxury', true),
('Christian Louboutin', 'christian-louboutin', 'French fashion designer known for red-soled shoes', 'France', 1991, 'Luxury', true),
('Off-<PERSON>', 'off-white', 'Italian luxury fashion label founded by <PERSON> <PERSON><PERSON><PERSON>h', 'Italy', 2012, 'Streetwear', true),
('Givenchy', 'givenchy', 'French luxury fashion and perfume house', 'France', 1952, 'Luxury', false),
('Maison Margiela', 'maison-margiela', 'French luxury fashion house founded by Belgian designer <PERSON> Margiela', 'France', 1988, 'Luxury', false),
('Valentino', 'valentino', 'Italian luxury fashion house founded by Valentino Garavani', 'Italy', 1960, 'Luxury', false),
('Prada', 'prada', 'Italian luxury fashion house founded by Mario Prada', 'Italy', 1913, 'Luxury', false),
('Miu Miu', 'miu-miu', 'Italian high fashion womens clothing and accessory brand', 'Italy', 1993, 'Luxury', false),
('Bottega Veneta', 'bottega-veneta', 'Italian luxury fashion house known for leather goods', 'Italy', 1966, 'Luxury', false),
('Burberry', 'burberry', 'British luxury fashion house headquartered in London', 'UK', 1856, 'Luxury', false),
('Golden Goose', 'golden-goose', 'Italian luxury sneaker brand known for distressed designs', 'Italy', 2000, 'Luxury', false),
('UGG', 'ugg', 'American footwear company known for sheepskin boots', 'USA', 1978, 'Casual', false),
('Celine', 'celine', 'French luxury fashion house founded by Céline Vipiana', 'France', 1945, 'Luxury', false),
('Jordan', 'jordan', 'Basketball footwear and athletic clothing brand owned by Nike', 'USA', 1984, 'Athletic', true);

-- 📂 INSERT CATEGORIES
INSERT INTO categories (name, slug, description, parent_id, sort_order, active) VALUES
-- Main Categories
('Sneakers', 'sneakers', 'Athletic and casual sneakers for all occasions', NULL, 1, true),
('Sandals', 'sandals', 'Comfortable sandals and slides for warm weather', NULL, 2, true),
('Formal', 'formal', 'Dress shoes and formal footwear for special occasions', NULL, 3, true),
('Casual', 'casual', 'Everyday casual shoes and lifestyle footwear', NULL, 4, true),
('Kids', 'kids', 'Footwear designed specifically for children', NULL, 5, true);

-- Get category IDs for subcategories
DO $$
DECLARE
    sneakers_id UUID;
    sandals_id UUID;
    formal_id UUID;
    casual_id UUID;
    kids_id UUID;
BEGIN
    SELECT id INTO sneakers_id FROM categories WHERE slug = 'sneakers';
    SELECT id INTO sandals_id FROM categories WHERE slug = 'sandals';
    SELECT id INTO formal_id FROM categories WHERE slug = 'formal';
    SELECT id INTO casual_id FROM categories WHERE slug = 'casual';
    SELECT id INTO kids_id FROM categories WHERE slug = 'kids';

    -- Sneakers Subcategories
    INSERT INTO categories (name, slug, description, parent_id, sort_order, active) VALUES
    ('Basketball', 'basketball', 'High-performance basketball sneakers', sneakers_id, 1, true),
    ('Running', 'running', 'Running and athletic performance shoes', sneakers_id, 2, true),
    ('Lifestyle', 'lifestyle', 'Casual lifestyle and street sneakers', sneakers_id, 3, true),
    ('High-Top', 'high-top', 'High-top sneakers and boots', sneakers_id, 4, true),
    ('Low-Top', 'low-top', 'Low-top and minimalist sneakers', sneakers_id, 5, true);

    -- Sandals Subcategories
    INSERT INTO categories (name, slug, description, parent_id, sort_order, active) VALUES
    ('Slides', 'slides', 'Comfortable slides and slip-on sandals', sandals_id, 1, true),
    ('Flip Flops', 'flip-flops', 'Classic flip-flop style sandals', sandals_id, 2, true),
    ('Sport Sandals', 'sport-sandals', 'Athletic and outdoor sandals', sandals_id, 3, true),
    ('Luxury Sandals', 'luxury-sandals', 'Designer and luxury sandals', sandals_id, 4, true);

    -- Formal Subcategories
    INSERT INTO categories (name, slug, description, parent_id, sort_order, active) VALUES
    ('Oxford', 'oxford', 'Classic Oxford dress shoes', formal_id, 1, true),
    ('Loafers', 'loafers', 'Slip-on loafers and moccasins', formal_id, 2, true),
    ('Heels', 'heels', 'High heels and dress shoes for women', formal_id, 3, true),
    ('Boots', 'boots', 'Formal boots and ankle boots', formal_id, 4, true);

    -- Casual Subcategories
    INSERT INTO categories (name, slug, description, parent_id, sort_order, active) VALUES
    ('Canvas', 'canvas', 'Canvas shoes and casual sneakers', casual_id, 1, true),
    ('Slip-On', 'slip-on', 'Easy slip-on casual shoes', casual_id, 2, true),
    ('Boat Shoes', 'boat-shoes', 'Nautical-inspired casual shoes', casual_id, 3, true),
    ('Espadrilles', 'espadrilles', 'Rope-soled casual shoes', casual_id, 4, true);

    -- Kids Subcategories
    INSERT INTO categories (name, slug, description, parent_id, sort_order, active) VALUES
    ('Toddler', 'toddler', 'Shoes for toddlers (sizes 4-10)', kids_id, 1, true),
    ('Little Kid', 'little-kid', 'Shoes for little kids (sizes 10.5-3)', kids_id, 2, true),
    ('Big Kid', 'big-kid', 'Shoes for big kids (sizes 3.5-7)', kids_id, 3, true);
END $$;

-- 📚 INSERT COLLECTIONS
INSERT INTO collections (name, slug, description, featured, active, sort_order) VALUES
('Featured Drops', 'featured-drops', 'Our most exclusive and sought-after releases', true, true, 1),
('New Arrivals', 'new-arrivals', 'The latest additions to our collection', true, true, 2),
('Limited Edition', 'limited-edition', 'Rare and limited edition releases', true, true, 3),
('Best Sellers', 'best-sellers', 'Our most popular and loved products', true, true, 4),
('Luxury Collection', 'luxury-collection', 'Premium luxury footwear from top designers', true, true, 5),
('Streetwear Essentials', 'streetwear-essentials', 'Must-have streetwear and urban style shoes', false, true, 6),
('Athletic Performance', 'athletic-performance', 'High-performance athletic and sports footwear', false, true, 7),
('Summer Collection', 'summer-collection', 'Perfect footwear for the summer season', false, true, 8),
('Winter Collection', 'winter-collection', 'Warm and stylish footwear for cold weather', false, true, 9),
('Collaboration Drops', 'collaboration-drops', 'Exclusive brand collaborations and partnerships', false, true, 10);

-- 🔧 CREATE SEARCH VECTOR FUNCTION
CREATE OR REPLACE FUNCTION update_product_search_vector()
RETURNS TRIGGER AS $$
BEGIN
    NEW.search_vector := to_tsvector('english', 
        COALESCE(NEW.name, '') || ' ' || 
        COALESCE(NEW.description, '') || ' ' ||
        COALESCE((SELECT name FROM brands WHERE id = NEW.brand_id), '') || ' ' ||
        COALESCE((SELECT name FROM categories WHERE id = NEW.category_id), '')
    );
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Add search vector column to products
ALTER TABLE products ADD COLUMN search_vector tsvector;

-- Create trigger for search vector updates
CREATE TRIGGER update_products_search_vector
    BEFORE INSERT OR UPDATE ON products
    FOR EACH ROW EXECUTE FUNCTION update_product_search_vector();

-- Create index for full-text search
CREATE INDEX idx_products_search_vector ON products USING gin(search_vector);

-- 📊 CREATE ANALYTICS VIEWS
CREATE OR REPLACE VIEW product_analytics AS
SELECT 
    p.id,
    p.name,
    p.sku,
    b.name as brand_name,
    c.name as category_name,
    p.price,
    p.stock_quantity,
    p.rating,
    p.review_count,
    COALESCE(pv.view_count, 0) as view_count,
    COALESCE(wi.wishlist_count, 0) as wishlist_count,
    COALESCE(oi.order_count, 0) as order_count,
    p.created_at
FROM products p
LEFT JOIN brands b ON p.brand_id = b.id
LEFT JOIN categories c ON p.category_id = c.id
LEFT JOIN (
    SELECT product_id, COUNT(*) as view_count
    FROM product_views
    GROUP BY product_id
) pv ON p.id = pv.product_id
LEFT JOIN (
    SELECT product_id, COUNT(*) as wishlist_count
    FROM wishlist_items
    GROUP BY product_id
) wi ON p.id = wi.product_id
LEFT JOIN (
    SELECT product_id, COUNT(*) as order_count
    FROM order_items
    GROUP BY product_id
) oi ON p.id = oi.product_id;

-- 🔄 CREATE FUNCTIONS FOR COMMON OPERATIONS

-- Function to get product recommendations
CREATE OR REPLACE FUNCTION get_product_recommendations(target_product_id UUID, limit_count INTEGER DEFAULT 5)
RETURNS TABLE (
    id UUID,
    name VARCHAR,
    price DECIMAL,
    image_url TEXT,
    brand_name VARCHAR
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        p.id,
        p.name,
        p.price,
        (p.images->0->>'url')::TEXT as image_url,
        b.name as brand_name
    FROM products p
    JOIN brands b ON p.brand_id = b.id
    WHERE p.id != target_product_id
    AND p.status = 'active'
    AND p.in_stock = true
    AND (
        p.brand_id = (SELECT brand_id FROM products WHERE id = target_product_id)
        OR p.category_id = (SELECT category_id FROM products WHERE id = target_product_id)
    )
    ORDER BY p.rating DESC, p.created_at DESC
    LIMIT limit_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update product rating
CREATE OR REPLACE FUNCTION update_product_rating(product_id UUID)
RETURNS VOID AS $$
DECLARE
    avg_rating DECIMAL;
    total_reviews INTEGER;
BEGIN
    SELECT 
        ROUND(AVG(rating)::NUMERIC, 2),
        COUNT(*)
    INTO avg_rating, total_reviews
    FROM reviews 
    WHERE reviews.product_id = update_product_rating.product_id;
    
    UPDATE products 
    SET 
        rating = COALESCE(avg_rating, 0),
        review_count = total_reviews
    WHERE id = update_product_rating.product_id;
END;
$$ LANGUAGE plpgsql;

-- Trigger to update product rating when review is added/updated/deleted
CREATE OR REPLACE FUNCTION trigger_update_product_rating()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'DELETE' THEN
        PERFORM update_product_rating(OLD.product_id);
        RETURN OLD;
    ELSE
        PERFORM update_product_rating(NEW.product_id);
        RETURN NEW;
    END IF;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER update_product_rating_trigger
    AFTER INSERT OR UPDATE OR DELETE ON reviews
    FOR EACH ROW EXECUTE FUNCTION trigger_update_product_rating();
