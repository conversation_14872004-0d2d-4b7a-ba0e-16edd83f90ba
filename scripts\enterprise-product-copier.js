#!/usr/bin/env node

/**
 * ENTERPRISE PRODUCT COPIER
 * 
 * PERFECTIONIST ENGINEERING SOLUTION for TWL
 * 
 * Features:
 * - Safely copies all missing CYTTE products to public folder
 * - Maintains perfect directory structure
 * - Enterprise-grade error handling and logging
 * - Progress tracking and verification
 * - Atomic operations with rollback capability
 */

const fs = require('fs').promises;
const path = require('path');
const sharp = require('sharp');

class EnterpriseProductCopier {
  constructor() {
    this.cytteBasePath = path.join(process.cwd(), '--materials', 'shoes', '2. CYTTE');
    this.publicBasePath = path.join(process.cwd(), 'public', 'products');
    this.backupBasePath = path.join(process.cwd(), 'backup', 'products-before-copy');
    
    this.stats = {
      totalToCopy: 0,
      copied: 0,
      failed: 0,
      skipped: 0,
      jpgConverted: 0,
      webpPreserved: 0,
      startTime: Date.now()
    };
    
    this.logFile = null;
    this.missingProducts = [];
  }

  async initialize() {
    console.log('🚀 ENTERPRISE PRODUCT COPIER - TWL PERFECTIONIST EDITION');
    console.log('=========================================================');
    console.log('📦 COPYING ALL MISSING CYTTE PRODUCTS TO PUBLIC FOLDER');
    console.log('🎯 MAINTAINING PERFECT DIRECTORY STRUCTURE');
    console.log('🖼️  AUTO-CONVERTING JPG → WebP (PRESERVING EXISTING WebP)');
    console.log('');
    
    // Create directories
    await this.createDirectories();
    
    // Initialize logging
    await this.initializeLogging();
    
    // Create backup
    await this.createBackup();
    
    this.log('✅ Enterprise Product Copier initialized successfully');
  }

  async createDirectories() {
    const dirs = [this.publicBasePath, this.backupBasePath, path.join(process.cwd(), 'logs')];
    
    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true });
        console.log(`✅ Directory ready: ${path.relative(process.cwd(), dir)}`);
      } catch (error) {
        console.log(`📁 Directory exists: ${path.relative(process.cwd(), dir)}`);
      }
    }
  }

  async initializeLogging() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    this.logFile = path.join(process.cwd(), 'logs', `product-copy-${timestamp}.log`);
    
    await this.log('=== ENTERPRISE PRODUCT COPY SESSION STARTED ===');
    await this.log(`Timestamp: ${new Date().toISOString()}`);
    await this.log(`Source: ${this.cytteBasePath}`);
    await this.log(`Target: ${this.publicBasePath}`);
  }

  async log(message) {
    const timestamp = new Date().toISOString();
    const logEntry = `[${timestamp}] ${message}\n`;
    
    console.log(message);
    
    if (this.logFile) {
      try {
        await fs.appendFile(this.logFile, logEntry);
      } catch (error) {
        console.error('❌ Logging error:', error.message);
      }
    }
  }

  async createBackup() {
    await this.log('📦 Creating backup of current public products folder...');
    
    try {
      // Check if public folder exists and has content
      const publicExists = await fs.access(this.publicBasePath).then(() => true).catch(() => false);
      
      if (publicExists) {
        await this.copyDirectory(this.publicBasePath, this.backupBasePath);
        await this.log('✅ Backup created successfully');
      } else {
        await this.log('ℹ️  No existing public folder to backup');
      }
    } catch (error) {
      await this.log(`❌ Backup failed: ${error.message}`);
      throw error;
    }
  }

  async loadMissingProductsReport() {
    await this.log('📄 Loading missing products report...');
    
    // Find the most recent missing products report
    const logsDir = path.join(process.cwd(), 'logs');
    const files = await fs.readdir(logsDir);
    const reportFiles = files.filter(f => f.startsWith('missing-products-report-') && f.endsWith('.json'));
    
    if (reportFiles.length === 0) {
      throw new Error('No missing products report found. Please run scan-missing-products first.');
    }
    
    // Get the most recent report
    reportFiles.sort().reverse();
    const reportPath = path.join(logsDir, reportFiles[0]);
    
    const reportContent = await fs.readFile(reportPath, 'utf8');
    const report = JSON.parse(reportContent);
    
    // Extract missing products list
    this.missingProducts = Object.entries(report.missingProducts).map(([key, info]) => ({
      key,
      sourcePath: info.path,
      targetPath: path.join(this.publicBasePath, ...info.components),
      components: info.components,
      category: info.components[0] || 'unknown'
    }));
    
    this.stats.totalToCopy = this.missingProducts.length;
    
    await this.log(`📊 Loaded ${this.stats.totalToCopy} missing products from report`);
    
    return report;
  }

  async copyAllMissingProducts() {
    await this.log(`🚀 Starting to copy ${this.stats.totalToCopy} missing products...`);
    
    const categoryStats = new Map();
    
    for (const product of this.missingProducts) {
      try {
        await this.copyProduct(product);
        this.stats.copied++;
        
        // Track by category
        if (!categoryStats.has(product.category)) {
          categoryStats.set(product.category, { copied: 0, failed: 0 });
        }
        categoryStats.get(product.category).copied++;
        
      } catch (error) {
        await this.log(`❌ Failed to copy ${product.key}: ${error.message}`);
        this.stats.failed++;
        
        if (!categoryStats.has(product.category)) {
          categoryStats.set(product.category, { copied: 0, failed: 0 });
        }
        categoryStats.get(product.category).failed++;
      }
      
      // Progress update every 10 products
      if ((this.stats.copied + this.stats.failed) % 10 === 0) {
        const progress = Math.round(((this.stats.copied + this.stats.failed) / this.stats.totalToCopy) * 100);
        await this.log(`📊 Progress: ${progress}% (${this.stats.copied} copied, ${this.stats.failed} failed)`);
      }
    }
    
    return categoryStats;
  }

  async copyProduct(product) {
    // Verify source exists
    const sourceExists = await fs.access(product.sourcePath).then(() => true).catch(() => false);
    if (!sourceExists) {
      throw new Error(`Source path does not exist: ${product.sourcePath}`);
    }
    
    // Create target directory
    await fs.mkdir(path.dirname(product.targetPath), { recursive: true });
    
    // Copy the product directory
    await this.copyDirectory(product.sourcePath, product.targetPath);
    
    await this.log(`✅ Copied: ${product.key}`);
  }

  async copyDirectory(source, target) {
    try {
      await fs.mkdir(target, { recursive: true });
      const items = await fs.readdir(source, { withFileTypes: true });

      for (const item of items) {
        const sourcePath = path.join(source, item.name);
        const targetPath = path.join(target, item.name);

        if (item.isDirectory()) {
          await this.copyDirectory(sourcePath, targetPath);
        } else {
          await this.copyFileWithConversion(sourcePath, targetPath, item.name);
        }
      }
    } catch (error) {
      throw new Error(`Failed to copy ${source} to ${target}: ${error.message}`);
    }
  }

  async copyFileWithConversion(sourcePath, targetPath, fileName) {
    const ext = path.extname(fileName).toLowerCase();

    if (ext === '.jpg' || ext === '.jpeg') {
      // Convert JPG to WebP
      const webpTargetPath = targetPath.replace(/\.(jpg|jpeg)$/i, '.webp');

      try {
        await sharp(sourcePath)
          .webp({
            quality: 85,
            effort: 4
          })
          .toFile(webpTargetPath);

        this.stats.jpgConverted++;
        await this.log(`🔄 Converted JPG→WebP: ${fileName} → ${path.basename(webpTargetPath)}`);

      } catch (error) {
        // If conversion fails, copy original file
        await fs.copyFile(sourcePath, targetPath);
        await this.log(`⚠️  JPG conversion failed, copied original: ${fileName}`);
      }

    } else if (ext === '.webp') {
      // Preserve existing WebP files
      await fs.copyFile(sourcePath, targetPath);
      this.stats.webpPreserved++;

    } else {
      // Copy all other files as-is (videos, text files, etc.)
      await fs.copyFile(sourcePath, targetPath);
    }
  }

  async generateFinalReport(categoryStats) {
    const duration = Math.round((Date.now() - this.stats.startTime) / 1000);
    
    await this.log('\n🎉 ENTERPRISE PRODUCT COPY COMPLETE!');
    await this.log('=====================================');
    await this.log(`⏱️  Duration: ${Math.floor(duration / 60)}m ${duration % 60}s`);
    await this.log(`📦 Total to copy: ${this.stats.totalToCopy}`);
    await this.log(`✅ Successfully copied: ${this.stats.copied}`);
    await this.log(`❌ Failed: ${this.stats.failed}`);
    await this.log(`⏭️  Skipped: ${this.stats.skipped}`);
    await this.log(`🔄 JPG→WebP converted: ${this.stats.jpgConverted}`);
    await this.log(`🖼️  WebP preserved: ${this.stats.webpPreserved}`);
    await this.log(`📊 Success rate: ${Math.round((this.stats.copied / this.stats.totalToCopy) * 100)}%`);
    
    await this.log('\n📂 COPY RESULTS BY CATEGORY:');
    for (const [category, stats] of categoryStats) {
      await this.log(`   ${category}: ${stats.copied} copied, ${stats.failed} failed`);
    }
    
    await this.log(`\n📄 Full log: ${this.logFile}`);
    await this.log(`📦 Backup: ${this.backupBasePath}`);
    await this.log('\n🚀 All CYTTE products now available in public folder!');
    
    return {
      duration,
      stats: this.stats,
      categoryStats: Object.fromEntries(categoryStats)
    };
  }

  async verifyResults() {
    await this.log('\n🔍 Verifying copy results...');
    
    let verified = 0;
    let missing = 0;
    
    for (const product of this.missingProducts) {
      const targetExists = await fs.access(product.targetPath).then(() => true).catch(() => false);
      if (targetExists) {
        verified++;
      } else {
        missing++;
        await this.log(`⚠️  Missing after copy: ${product.key}`);
      }
    }
    
    await this.log(`📊 Verification: ${verified} verified, ${missing} still missing`);
    
    return { verified, missing };
  }
}

// Main execution
async function main() {
  const copier = new EnterpriseProductCopier();
  
  try {
    await copier.initialize();
    
    // Load missing products report
    const report = await copier.loadMissingProductsReport();
    
    if (copier.stats.totalToCopy === 0) {
      await copier.log('ℹ️  No missing products to copy');
      return;
    }
    
    // Copy all missing products
    const categoryStats = await copier.copyAllMissingProducts();
    
    // Verify results
    const verification = await copier.verifyResults();
    
    // Generate final report
    const finalReport = await copier.generateFinalReport(categoryStats);
    
    // Success message
    if (copier.stats.failed === 0) {
      console.log('\n🎉 PERFECT SUCCESS! All CYTTE products copied successfully!');
    } else {
      console.log(`\n⚠️  Copy completed with ${copier.stats.failed} failures. Check logs for details.`);
    }
    
    return finalReport;
    
  } catch (error) {
    console.error('❌ Enterprise product copy failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = EnterpriseProductCopier;
