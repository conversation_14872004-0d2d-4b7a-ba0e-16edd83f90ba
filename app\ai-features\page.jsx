'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import AISearchHub from '@/components/features/AISearchHub'
import SmartRecommendations from '@/components/features/SmartRecommendations'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

export default function AIFeaturesPage() {
  const [activeDemo, setActiveDemo] = useState(null)

  const aiFeatures = [
    {
      id: 'voice-search',
      title: 'Búsqueda por Voz Inteligente',
      description: 'Reconocimiento de voz avanzado con procesamiento de lenguaje natural en español y inglés.',
      icon: '🎤',
      gradient: 'from-blue-500 to-purple-600',
      capabilities: [
        'Reconocimiento de voz en tiempo real',
        'Procesamiento de lenguaje natural',
        'Búsqueda contextual inteligente',
        'Soporte multiidioma (ES/EN)',
        'Corrección automática de errores'
      ],
      metrics: {
        accuracy: '96%',
        responseTime: '< 1.5s',
        languages: '2',
        confidence: '94%'
      }
    },
    {
      id: 'visual-search',
      title: 'Búsqueda Visual con IA',
      description: 'Análisis de imágenes con inteligencia artificial para encontrar productos similares.',
      icon: '📸',
      gradient: 'from-green-500 to-teal-600',
      capabilities: [
        'Análisis de imagen con CNN',
        'Detección de características',
        'Reconocimiento de colores y texturas',
        'Búsqueda por similitud visual',
        'Clasificación automática de productos'
      ],
      metrics: {
        accuracy: '92%',
        responseTime: '< 2s',
        formats: '5+',
        similarity: '89%'
      }
    },
    {
      id: 'smart-recommendations',
      title: 'Recomendaciones Personalizadas',
      description: 'Sistema de recomendaciones basado en machine learning y análisis de comportamiento.',
      icon: '🧠',
      gradient: 'from-orange-500 to-red-600',
      capabilities: [
        'Aprendizaje automático adaptativo',
        'Análisis de comportamiento de usuario',
        'Filtrado colaborativo',
        'Personalización en tiempo real',
        'Predicción de tendencias'
      ],
      metrics: {
        accuracy: '94%',
        engagement: '+45%',
        conversion: '+32%',
        satisfaction: '4.8/5'
      }
    },
    {
      id: 'predictive-analytics',
      title: 'Análisis Predictivo',
      description: 'Predicción de tendencias y comportamiento de compra usando big data y IA.',
      icon: '📊',
      gradient: 'from-purple-500 to-pink-600',
      capabilities: [
        'Predicción de demanda',
        'Análisis de tendencias',
        'Optimización de inventario',
        'Segmentación inteligente',
        'Forecasting de ventas'
      ],
      metrics: {
        accuracy: '91%',
        prediction: '7 días',
        dataPoints: '1M+',
        models: '12'
      }
    }
  ]

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Hero Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-16"
        >
          <h1 className="text-5xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-6">
            🤖 Inteligencia Artificial
          </h1>
          <p className="text-xl text-warm-camel max-w-4xl mx-auto mb-8">
            Descubre el futuro del e-commerce con nuestras tecnologías de IA avanzadas. 
            Búsqueda inteligente, recomendaciones personalizadas y análisis predictivo.
          </p>
          
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <AnimatedButton
              variant="primary"
              size="lg"
              onClick={() => setActiveDemo('search-hub')}
              icon={
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              }
            >
              Probar IA Ahora
            </AnimatedButton>
            
            <TransitionLink href="/shop">
              <AnimatedButton
                variant="secondary"
                size="lg"
                icon={
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                }
              >
                Ver Productos
              </AnimatedButton>
            </TransitionLink>
          </div>
        </motion.div>

        {/* AI Features Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-16">
          {aiFeatures.map((feature, index) => (
            <motion.div
              key={feature.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card variant="glass" className="h-full hover:shadow-xl transition-all duration-300">
                <CardContent className="p-8">
                  
                  {/* Feature Header */}
                  <div className="flex items-center gap-4 mb-6">
                    <div className={`w-16 h-16 rounded-full bg-gradient-to-r ${feature.gradient} flex items-center justify-center text-2xl text-white`}>
                      {feature.icon}
                    </div>
                    <div>
                      <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                        {feature.title}
                      </h3>
                      <p className="text-warm-camel text-sm">
                        {feature.description}
                      </p>
                    </div>
                  </div>

                  {/* Capabilities */}
                  <div className="mb-6">
                    <h4 className="text-sm font-medium text-warm-camel mb-3">Capacidades:</h4>
                    <ul className="space-y-2">
                      {feature.capabilities.map((capability, capIndex) => (
                        <li key={capIndex} className="flex items-center gap-2 text-sm text-forest-emerald dark:text-light-cloud-gray">
                          <div className="w-1.5 h-1.5 bg-rich-gold rounded-full"></div>
                          {capability}
                        </li>
                      ))}
                    </ul>
                  </div>

                  {/* Metrics */}
                  <div className="grid grid-cols-2 gap-4 mb-6">
                    {Object.entries(feature.metrics).map(([key, value]) => (
                      <div key={key} className="text-center p-3 bg-warm-camel/5 rounded-lg">
                        <div className="text-lg font-bold text-forest-emerald dark:text-light-cloud-gray">
                          {value}
                        </div>
                        <div className="text-xs text-warm-camel capitalize">
                          {key.replace(/([A-Z])/g, ' $1').trim()}
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Action Button */}
                  <AnimatedButton
                    variant="primary"
                    onClick={() => setActiveDemo(feature.id)}
                    className="w-full"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                    }
                  >
                    Probar {feature.title}
                  </AnimatedButton>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </div>

        {/* AI Search Hub Demo */}
        {activeDemo === 'search-hub' && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="mb-16"
          >
            <AISearchHub showRecommendations={false} />
          </motion.div>
        )}

        {/* Technology Stack */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="mb-16"
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8 text-center">
                🔧 Stack Tecnológico de IA
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                {[
                  { name: 'TensorFlow', desc: 'Machine Learning', icon: '🧠' },
                  { name: 'OpenAI GPT', desc: 'Procesamiento de lenguaje', icon: '💬' },
                  { name: 'Computer Vision', desc: 'Análisis de imágenes', icon: '👁️' },
                  { name: 'BigQuery', desc: 'Análisis de datos', icon: '📊' },
                  { name: 'WebRTC', desc: 'Reconocimiento de voz', icon: '🎤' },
                  { name: 'PyTorch', desc: 'Deep Learning', icon: '🔥' },
                  { name: 'Elasticsearch', desc: 'Búsqueda inteligente', icon: '🔍' },
                  { name: 'Apache Kafka', desc: 'Streaming de datos', icon: '⚡' }
                ].map((tech, index) => (
                  <motion.div
                    key={tech.name}
                    initial={{ opacity: 0, scale: 0.8 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ delay: 0.7 + index * 0.1 }}
                    className="text-center p-4 bg-warm-camel/5 rounded-lg hover:bg-warm-camel/10 transition-colors"
                  >
                    <div className="text-2xl mb-2">{tech.icon}</div>
                    <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm">
                      {tech.name}
                    </h4>
                    <p className="text-xs text-warm-camel">{tech.desc}</p>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Performance Metrics */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="mb-16"
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8 text-center">
                📈 Métricas de Rendimiento
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                {[
                  { metric: '94%', label: 'Precisión promedio', icon: '🎯', color: 'text-green-500' },
                  { metric: '1.8s', label: 'Tiempo de respuesta', icon: '⚡', color: 'text-blue-500' },
                  { metric: '+45%', label: 'Engagement de usuarios', icon: '📊', color: 'text-purple-500' },
                  { metric: '99.9%', label: 'Disponibilidad del sistema', icon: '🔧', color: 'text-orange-500' }
                ].map((stat, index) => (
                  <motion.div
                    key={stat.label}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.9 + index * 0.1 }}
                    className="text-center"
                  >
                    <div className="text-3xl mb-2">{stat.icon}</div>
                    <div className={`text-3xl font-bold ${stat.color} mb-1`}>
                      {stat.metric}
                    </div>
                    <div className="text-sm text-warm-camel">
                      {stat.label}
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Smart Recommendations Demo */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
        >
          <SmartRecommendations 
            type="personalized"
            title="🤖 Demo: Recomendaciones Personalizadas"
            limit={6}
          />
        </motion.div>

        {/* CTA Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
          className="text-center mt-16"
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                🚀 Experimenta el Futuro del E-commerce
              </h2>
              <p className="text-warm-camel mb-6">
                Únete a miles de usuarios que ya disfrutan de una experiencia de compra potenciada por IA
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <TransitionLink href="/shop">
                  <AnimatedButton
                    variant="primary"
                    size="lg"
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    }
                  >
                    Comenzar a Comprar
                  </AnimatedButton>
                </TransitionLink>
                
                <TransitionLink href="/social">
                  <AnimatedButton
                    variant="secondary"
                    size="lg"
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    }
                  >
                    Unirse a la Comunidad
                  </AnimatedButton>
                </TransitionLink>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
