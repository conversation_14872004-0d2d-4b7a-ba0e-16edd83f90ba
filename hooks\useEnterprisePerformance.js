'use client'

import { useState, useEffect, useCallback, useRef } from 'react'

/**
 * ENTERPRISE-GRADE PERFORMANCE OPTIMIZATION HOOK
 * 
 * PERFECTIONIST ENGINEERING FEATURES:
 * - 60fps animation optimization
 * - Memory leak prevention
 * - Efficient event handling
 * - Mobile-first performance monitoring
 * - Battery-aware optimizations
 * - Network-aware loading strategies
 */

export function useEnterprisePerformance() {
  const [performanceMetrics, setPerformanceMetrics] = useState({
    fps: 60,
    memoryUsage: 0,
    networkSpeed: 'fast',
    batteryLevel: 1,
    isLowPowerMode: false
  })

  const frameCount = useRef(0)
  const lastTime = useRef(performance.now())
  const animationFrame = useRef(null)

  // FPS Monitoring for smooth animations
  const measureFPS = useCallback(() => {
    const now = performance.now()
    frameCount.current++

    if (now - lastTime.current >= 1000) {
      const fps = Math.round((frameCount.current * 1000) / (now - lastTime.current))
      setPerformanceMetrics(prev => ({ ...prev, fps }))
      frameCount.current = 0
      lastTime.current = now
    }

    animationFrame.current = requestAnimationFrame(measureFPS)
  }, [])

  // Memory Usage Monitoring
  const measureMemory = useCallback(() => {
    if ('memory' in performance) {
      const memoryInfo = performance.memory
      const memoryUsage = Math.round((memoryInfo.usedJSHeapSize / memoryInfo.jsHeapSizeLimit) * 100)
      setPerformanceMetrics(prev => ({ ...prev, memoryUsage }))
    }
  }, [])

  // Network Speed Detection
  const detectNetworkSpeed = useCallback(() => {
    if ('connection' in navigator) {
      const connection = navigator.connection
      const effectiveType = connection.effectiveType
      
      let networkSpeed = 'fast'
      if (effectiveType === 'slow-2g' || effectiveType === '2g') {
        networkSpeed = 'slow'
      } else if (effectiveType === '3g') {
        networkSpeed = 'medium'
      }
      
      setPerformanceMetrics(prev => ({ ...prev, networkSpeed }))
    }
  }, [])

  // Battery Level Detection
  const detectBatteryLevel = useCallback(async () => {
    if ('getBattery' in navigator) {
      try {
        const battery = await navigator.getBattery()
        setPerformanceMetrics(prev => ({
          ...prev,
          batteryLevel: battery.level,
          isLowPowerMode: battery.level < 0.2
        }))

        // Listen for battery changes
        battery.addEventListener('levelchange', () => {
          setPerformanceMetrics(prev => ({
            ...prev,
            batteryLevel: battery.level,
            isLowPowerMode: battery.level < 0.2
          }))
        })
      } catch (error) {
        console.warn('Battery API not supported')
      }
    }
  }, [])

  // Initialize performance monitoring
  useEffect(() => {
    measureFPS()
    measureMemory()
    detectNetworkSpeed()
    detectBatteryLevel()

    // Set up periodic monitoring
    const memoryInterval = setInterval(measureMemory, 5000)
    const networkInterval = setInterval(detectNetworkSpeed, 10000)

    return () => {
      if (animationFrame.current) {
        cancelAnimationFrame(animationFrame.current)
      }
      clearInterval(memoryInterval)
      clearInterval(networkInterval)
    }
  }, [measureFPS, measureMemory, detectNetworkSpeed, detectBatteryLevel])

  // Performance-aware animation settings
  const getAnimationSettings = useCallback(() => {
    const { fps, isLowPowerMode, networkSpeed } = performanceMetrics

    if (isLowPowerMode || fps < 30 || networkSpeed === 'slow') {
      return {
        duration: 0.2, // Faster animations
        ease: 'easeOut',
        reduceMotion: true,
        skipComplexAnimations: true
      }
    }

    if (fps < 45 || networkSpeed === 'medium') {
      return {
        duration: 0.3,
        ease: 'easeInOut',
        reduceMotion: false,
        skipComplexAnimations: false
      }
    }

    return {
      duration: 0.5, // Full animations
      ease: [0.25, 0.46, 0.45, 0.94],
      reduceMotion: false,
      skipComplexAnimations: false
    }
  }, [performanceMetrics])

  // Performance-aware image loading strategy
  const getImageLoadingStrategy = useCallback(() => {
    const { networkSpeed, isLowPowerMode } = performanceMetrics

    if (isLowPowerMode || networkSpeed === 'slow') {
      return {
        quality: 60,
        format: 'webp',
        lazy: true,
        placeholder: 'blur',
        priority: false
      }
    }

    if (networkSpeed === 'medium') {
      return {
        quality: 75,
        format: 'webp',
        lazy: true,
        placeholder: 'blur',
        priority: false
      }
    }

    return {
      quality: 85,
      format: 'webp',
      lazy: false,
      placeholder: 'blur',
      priority: true
    }
  }, [performanceMetrics])

  return {
    performanceMetrics,
    getAnimationSettings,
    getImageLoadingStrategy
  }
}

/**
 * ENTERPRISE-GRADE MOBILE TOUCH OPTIMIZATION HOOK
 */
export function useEnterpriseTouchOptimization() {
  const [touchMetrics, setTouchMetrics] = useState({
    isMobile: false,
    touchSupport: false,
    screenSize: 'desktop',
    orientation: 'portrait',
    pixelRatio: 1
  })

  const detectDeviceCapabilities = useCallback(() => {
    const isMobile = window.innerWidth < 1024 || 'ontouchstart' in window
    const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0
    const pixelRatio = window.devicePixelRatio || 1
    
    let screenSize = 'desktop'
    if (window.innerWidth < 640) screenSize = 'mobile'
    else if (window.innerWidth < 1024) screenSize = 'tablet'
    
    const orientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait'

    setTouchMetrics({
      isMobile,
      touchSupport,
      screenSize,
      orientation,
      pixelRatio
    })
  }, [])

  useEffect(() => {
    detectDeviceCapabilities()

    const handleResize = debounce(detectDeviceCapabilities, 150)
    const handleOrientationChange = () => {
      setTimeout(detectDeviceCapabilities, 100)
    }

    window.addEventListener('resize', handleResize, { passive: true })
    window.addEventListener('orientationchange', handleOrientationChange, { passive: true })

    return () => {
      window.removeEventListener('resize', handleResize)
      window.removeEventListener('orientationchange', handleOrientationChange)
    }
  }, [detectDeviceCapabilities])

  // Optimized touch event handlers
  const createTouchHandler = useCallback((callback) => {
    return (e) => {
      // Prevent default only when necessary
      if (e.cancelable) {
        e.preventDefault()
      }
      
      // Add haptic feedback for mobile
      if (touchMetrics.isMobile && navigator.vibrate) {
        navigator.vibrate(5)
      }
      
      callback(e)
    }
  }, [touchMetrics.isMobile])

  // Touch-optimized button props
  const getTouchOptimizedProps = useCallback((onClick) => {
    const baseProps = {
      onClick: createTouchHandler(onClick),
      style: {
        touchAction: 'manipulation',
        WebkitTapHighlightColor: 'transparent',
        userSelect: 'none'
      }
    }

    if (touchMetrics.touchSupport) {
      return {
        ...baseProps,
        onTouchStart: (e) => {
          e.currentTarget.style.transform = 'scale(0.98)'
        },
        onTouchEnd: (e) => {
          e.currentTarget.style.transform = 'scale(1)'
        }
      }
    }

    return baseProps
  }, [touchMetrics.touchSupport, createTouchHandler])

  return {
    touchMetrics,
    getTouchOptimizedProps,
    createTouchHandler
  }
}

/**
 * ENTERPRISE-GRADE SCROLL OPTIMIZATION HOOK
 */
export function useEnterpriseScrollOptimization() {
  const [scrollMetrics, setScrollMetrics] = useState({
    scrollY: 0,
    scrollDirection: 'up',
    isScrolling: false,
    scrollVelocity: 0
  })

  const lastScrollY = useRef(0)
  const scrollTimeout = useRef(null)
  const velocityHistory = useRef([])

  const handleScroll = useCallback(() => {
    const currentScrollY = window.scrollY
    const direction = currentScrollY > lastScrollY.current ? 'down' : 'up'
    const velocity = Math.abs(currentScrollY - lastScrollY.current)

    // Track velocity for momentum calculations
    velocityHistory.current.push(velocity)
    if (velocityHistory.current.length > 5) {
      velocityHistory.current.shift()
    }

    const avgVelocity = velocityHistory.current.reduce((a, b) => a + b, 0) / velocityHistory.current.length

    setScrollMetrics({
      scrollY: currentScrollY,
      scrollDirection: direction,
      isScrolling: true,
      scrollVelocity: avgVelocity
    })

    lastScrollY.current = currentScrollY

    // Clear scrolling state after scroll ends
    clearTimeout(scrollTimeout.current)
    scrollTimeout.current = setTimeout(() => {
      setScrollMetrics(prev => ({ ...prev, isScrolling: false, scrollVelocity: 0 }))
    }, 150)
  }, [])

  useEffect(() => {
    const throttledScroll = throttle(handleScroll, 16) // 60fps
    window.addEventListener('scroll', throttledScroll, { passive: true })

    return () => {
      window.removeEventListener('scroll', throttledScroll)
      clearTimeout(scrollTimeout.current)
    }
  }, [handleScroll])

  return scrollMetrics
}

// Utility functions
function debounce(func, wait) {
  let timeout
  return function executedFunction(...args) {
    const later = () => {
      clearTimeout(timeout)
      func(...args)
    }
    clearTimeout(timeout)
    timeout = setTimeout(later, wait)
  }
}

function throttle(func, limit) {
  let inThrottle
  return function() {
    const args = arguments
    const context = this
    if (!inThrottle) {
      func.apply(context, args)
      inThrottle = true
      setTimeout(() => inThrottle = false, limit)
    }
  }
}
