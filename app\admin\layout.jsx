// 🏢 TWL ADMIN DASHBOARD LAYOUT
// 🎯 Enterprise-grade admin interface with role-based access control

'use client'

import { useState, useEffect } from 'react'
import { useRouter, usePathname } from 'next/navigation'
import Link from 'next/link'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  HomeIcon, 
  ShoppingBagIcon, 
  UsersIcon, 
  ChartBarIcon,
  CogIcon,
  PhotoIcon,
  ChatBubbleLeftRightIcon,
  TruckIcon,
  CurrencyDollarIcon,
  BellIcon,
  MagnifyingGlassIcon,
  Bars3Icon,
  XMarkIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { useAuth } from '@/contexts/AuthContext'
import { supabase } from '@/lib/supabase'

const navigation = [
  {
    name: 'Dashboard',
    href: '/admin',
    icon: HomeIcon,
    description: 'Overview and key metrics'
  },
  {
    name: 'Products',
    href: '/admin/products',
    icon: ShoppingBagIcon,
    description: 'Product catalog management',
    children: [
      { name: 'All Products', href: '/admin/products' },
      { name: 'Add Product', href: '/admin/products/new' },
      { name: 'Categories', href: '/admin/products/categories' },
      { name: 'Brands', href: '/admin/products/brands' },
      { name: 'Inventory', href: '/admin/products/inventory' }
    ]
  },
  {
    name: 'Orders',
    href: '/admin/orders',
    icon: TruckIcon,
    description: 'Order management and fulfillment',
    children: [
      { name: 'All Orders', href: '/admin/orders' },
      { name: 'Pending', href: '/admin/orders?status=pending' },
      { name: 'Processing', href: '/admin/orders?status=processing' },
      { name: 'Shipped', href: '/admin/orders?status=shipped' },
      { name: 'Returns', href: '/admin/orders/returns' }
    ]
  },
  {
    name: 'Customers',
    href: '/admin/customers',
    icon: UsersIcon,
    description: 'Customer management and support',
    children: [
      { name: 'All Customers', href: '/admin/customers' },
      { name: 'VIP Customers', href: '/admin/customers?tier=vip' },
      { name: 'Support Tickets', href: '/admin/customers/support' },
      { name: 'Reviews', href: '/admin/customers/reviews' }
    ]
  },
  {
    name: 'Analytics',
    href: '/admin/analytics',
    icon: ChartBarIcon,
    description: 'Business intelligence and reports',
    children: [
      { name: 'Sales Report', href: '/admin/analytics/sales' },
      { name: 'Product Performance', href: '/admin/analytics/products' },
      { name: 'Customer Insights', href: '/admin/analytics/customers' },
      { name: 'Marketing ROI', href: '/admin/analytics/marketing' }
    ]
  },
  {
    name: 'Content',
    href: '/admin/content',
    icon: PhotoIcon,
    description: 'UGC and content moderation',
    children: [
      { name: 'UGC Posts', href: '/admin/content/ugc' },
      { name: 'Collections', href: '/admin/content/collections' },
      { name: 'Homepage', href: '/admin/content/homepage' },
      { name: 'Media Library', href: '/admin/content/media' }
    ]
  },
  {
    name: 'Marketing',
    href: '/admin/marketing',
    icon: ChatBubbleLeftRightIcon,
    description: 'Campaigns and promotions',
    children: [
      { name: 'Campaigns', href: '/admin/marketing/campaigns' },
      { name: 'Discounts', href: '/admin/marketing/discounts' },
      { name: 'Email Marketing', href: '/admin/marketing/email' },
      { name: 'Social Media', href: '/admin/marketing/social' }
    ]
  },
  {
    name: 'Finance',
    href: '/admin/finance',
    icon: CurrencyDollarIcon,
    description: 'Financial management and reporting',
    children: [
      { name: 'Revenue', href: '/admin/finance/revenue' },
      { name: 'Expenses', href: '/admin/finance/expenses' },
      { name: 'Suppliers', href: '/admin/finance/suppliers' },
      { name: 'Tax Reports', href: '/admin/finance/taxes' }
    ]
  },
  {
    name: 'Settings',
    href: '/admin/settings',
    icon: CogIcon,
    description: 'System configuration',
    children: [
      { name: 'General', href: '/admin/settings' },
      { name: 'Users & Roles', href: '/admin/settings/users' },
      { name: 'Payments', href: '/admin/settings/payments' },
      { name: 'Shipping', href: '/admin/settings/shipping' },
      { name: 'Integrations', href: '/admin/settings/integrations' }
    ]
  }
]

export default function AdminLayout({ children }) {
  const [sidebarOpen, setSidebarOpen] = useState(false)
  const [expandedItems, setExpandedItems] = useState(new Set())
  const [notifications, setNotifications] = useState([])
  const { user, signOut } = useAuth()
  const router = useRouter()
  const pathname = usePathname()

  // Check admin access
  useEffect(() => {
    if (!user) {
      router.push('/auth/login?redirect=/admin')
      return
    }

    // Check if user has admin role
    const isAdmin = user.user_metadata?.role === 'admin' || 
                   user.app_metadata?.role === 'admin'
    
    if (!isAdmin) {
      router.push('/')
      return
    }
  }, [user, router])

  // Load notifications
  useEffect(() => {
    loadNotifications()
  }, [])

  const loadNotifications = async () => {
    try {
      // Get recent orders, low stock alerts, etc.
      const { data: recentOrders } = await supabase
        .from('orders')
        .select('id, order_number, status, created_at')
        .order('created_at', { ascending: false })
        .limit(5)

      const { data: lowStockProducts } = await supabase
        .from('products')
        .select('id, name, stock_quantity')
        .lt('stock_quantity', 10)
        .eq('status', 'active')
        .limit(5)

      const notifs = []
      
      if (recentOrders) {
        recentOrders.forEach(order => {
          notifs.push({
            id: `order-${order.id}`,
            type: 'order',
            title: `New Order ${order.order_number}`,
            message: `Status: ${order.status}`,
            time: order.created_at,
            href: `/admin/orders/${order.id}`
          })
        })
      }

      if (lowStockProducts) {
        lowStockProducts.forEach(product => {
          notifs.push({
            id: `stock-${product.id}`,
            type: 'warning',
            title: 'Low Stock Alert',
            message: `${product.name} - ${product.stock_quantity} left`,
            time: new Date().toISOString(),
            href: `/admin/products/${product.id}`
          })
        })
      }

      setNotifications(notifs.slice(0, 10))
    } catch (error) {
      console.error('Error loading notifications:', error)
    }
  }

  const toggleExpanded = (itemName) => {
    const newExpanded = new Set(expandedItems)
    if (newExpanded.has(itemName)) {
      newExpanded.delete(itemName)
    } else {
      newExpanded.add(itemName)
    }
    setExpandedItems(newExpanded)
  }

  const handleSignOut = async () => {
    await signOut()
    router.push('/')
  }

  if (!user) {
    return (
      <div className="min-h-screen bg-gray-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-lime-green"></div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Mobile sidebar */}
      <AnimatePresence>
        {sidebarOpen && (
          <>
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 bg-gray-600 bg-opacity-75 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            />
            <motion.div
              initial={{ x: -300 }}
              animate={{ x: 0 }}
              exit={{ x: -300 }}
              className="fixed inset-y-0 left-0 z-50 w-64 bg-white dark:bg-gray-800 shadow-xl lg:hidden"
            >
              <SidebarContent 
                navigation={navigation}
                pathname={pathname}
                expandedItems={expandedItems}
                toggleExpanded={toggleExpanded}
                onClose={() => setSidebarOpen(false)}
              />
            </motion.div>
          </>
        )}
      </AnimatePresence>

      {/* Desktop sidebar */}
      <div className="hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col">
        <div className="flex min-h-0 flex-1 flex-col bg-white dark:bg-gray-800 shadow-xl">
          <SidebarContent 
            navigation={navigation}
            pathname={pathname}
            expandedItems={expandedItems}
            toggleExpanded={toggleExpanded}
          />
        </div>
      </div>

      {/* Main content */}
      <div className="lg:pl-64 flex flex-col flex-1">
        {/* Top navigation */}
        <div className="sticky top-0 z-10 bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
          <div className="px-4 sm:px-6 lg:px-8">
            <div className="flex h-16 justify-between items-center">
              <div className="flex items-center">
                <button
                  type="button"
                  className="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-lime-green"
                  onClick={() => setSidebarOpen(true)}
                >
                  <Bars3Icon className="h-6 w-6" />
                </button>
                
                <div className="flex-1 px-4 flex justify-between">
                  <div className="flex-1 flex">
                    <div className="w-full flex md:ml-0">
                      <div className="relative w-full text-gray-400 focus-within:text-gray-600">
                        <div className="absolute inset-y-0 left-0 flex items-center pointer-events-none">
                          <MagnifyingGlassIcon className="h-5 w-5" />
                        </div>
                        <input
                          className="block w-full h-full pl-8 pr-3 py-2 border-transparent text-gray-900 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-0 focus:border-transparent bg-gray-100 dark:bg-gray-700 dark:text-white rounded-md"
                          placeholder="Search products, orders, customers..."
                          type="search"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div className="ml-4 flex items-center md:ml-6">
                {/* Notifications */}
                <NotificationDropdown notifications={notifications} />
                
                {/* User menu */}
                <div className="ml-3 relative">
                  <div className="flex items-center space-x-3">
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                      {user.user_metadata?.first_name || user.email}
                    </span>
                    <button
                      onClick={handleSignOut}
                      className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
                    >
                      <ArrowRightOnRectangleIcon className="h-5 w-5" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Page content */}
        <main className="flex-1">
          <div className="py-6">
            {children}
          </div>
        </main>
      </div>
    </div>
  )
}

// Sidebar content component
function SidebarContent({ navigation, pathname, expandedItems, toggleExpanded, onClose }) {
  return (
    <>
      {/* Logo */}
      <div className="flex items-center h-16 flex-shrink-0 px-4 bg-gray-900">
        <Link href="/admin" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-lime-green rounded-lg flex items-center justify-center">
            <span className="text-black font-bold text-lg">T</span>
          </div>
          <span className="text-white font-bold text-xl">TWL Admin</span>
        </Link>
        {onClose && (
          <button
            onClick={onClose}
            className="ml-auto lg:hidden p-2 rounded-md text-gray-400 hover:text-white"
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        )}
      </div>

      {/* Navigation */}
      <nav className="mt-5 flex-1 px-2 space-y-1 overflow-y-auto">
        {navigation.map((item) => (
          <div key={item.name}>
            <Link
              href={item.href}
              className={`group flex items-center px-2 py-2 text-sm font-medium rounded-md transition-colors ${
                pathname === item.href
                  ? 'bg-lime-green text-black'
                  : 'text-gray-600 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700'
              }`}
              onClick={() => {
                if (item.children) {
                  toggleExpanded(item.name)
                }
                if (onClose && !item.children) {
                  onClose()
                }
              }}
            >
              <item.icon className="mr-3 h-5 w-5 flex-shrink-0" />
              {item.name}
              {item.children && (
                <motion.div
                  animate={{ rotate: expandedItems.has(item.name) ? 90 : 0 }}
                  className="ml-auto"
                >
                  <svg className="h-4 w-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                  </svg>
                </motion.div>
              )}
            </Link>
            
            {/* Submenu */}
            <AnimatePresence>
              {item.children && expandedItems.has(item.name) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: 'auto', opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  className="ml-6 mt-1 space-y-1 overflow-hidden"
                >
                  {item.children.map((child) => (
                    <Link
                      key={child.name}
                      href={child.href}
                      className={`group flex items-center px-2 py-2 text-sm rounded-md transition-colors ${
                        pathname === child.href
                          ? 'bg-lime-green text-black'
                          : 'text-gray-500 dark:text-gray-400 hover:bg-gray-50 dark:hover:bg-gray-700'
                      }`}
                      onClick={onClose}
                    >
                      {child.name}
                    </Link>
                  ))}
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        ))}
      </nav>
    </>
  )
}

// Notification dropdown component
function NotificationDropdown({ notifications }) {
  const [isOpen, setIsOpen] = useState(false)

  return (
    <div className="relative">
      <button
        onClick={() => setIsOpen(!isOpen)}
        className="p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700 relative"
      >
        <BellIcon className="h-6 w-6" />
        {notifications.length > 0 && (
          <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
            {notifications.length}
          </span>
        )}
      </button>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, scale: 0.95 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.95 }}
            className="absolute right-0 mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 z-50"
          >
            <div className="p-4">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white">Notifications</h3>
              <div className="mt-3 space-y-3 max-h-64 overflow-y-auto">
                {notifications.length === 0 ? (
                  <p className="text-gray-500 dark:text-gray-400 text-sm">No new notifications</p>
                ) : (
                  notifications.map((notification) => (
                    <div key={notification.id} className="flex items-start space-x-3">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 dark:text-white">
                          {notification.title}
                        </p>
                        <p className="text-sm text-gray-500 dark:text-gray-400">
                          {notification.message}
                        </p>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
