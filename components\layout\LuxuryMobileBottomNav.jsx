'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter, usePathname } from 'next/navigation'

export default function LuxuryMobileBottomNav() {
  const router = useRouter()
  const pathname = usePathname()
  const [isVisible, setIsVisible] = useState(true)
  const [lastScrollY, setLastScrollY] = useState(0)
  const [scrollDirection, setScrollDirection] = useState('up')

  // Smart hide/show based on scroll direction
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      const direction = currentScrollY > lastScrollY ? 'down' : 'up'
      
      // Show nav when scrolling up or at top, hide when scrolling down
      setIsVisible(direction === 'up' || currentScrollY < 100)
      setScrollDirection(direction)
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  // Haptic feedback for luxury interactions
  const hapticFeedback = (type = 'light') => {
    if (navigator.vibrate) {
      const patterns = {
        light: [5],
        medium: [10],
        heavy: [15]
      }
      navigator.vibrate(patterns[type])
    }
  }

  const navigationItems = [
    {
      name: 'Inicio',
      href: '/',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
        </svg>
      )
    },
    {
      name: 'Tienda',
      href: '/shop',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
        </svg>
      )
    },
    {
      name: 'IA',
      href: '/ai-features',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
        </svg>
      )
    },
    {
      name: 'Social',
      href: '/social',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
        </svg>
      )
    },
    {
      name: 'Perfil',
      href: '/profile',
      icon: (isActive) => (
        <svg className={`w-6 h-6 ${isActive ? 'fill-current' : ''}`} fill={isActive ? 'currentColor' : 'none'} stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={isActive ? 0 : 2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
        </svg>
      )
    }
  ]

  const handleNavigation = (href, name) => {
    hapticFeedback('medium')
    router.push(href)
  }

  const isActive = (href) => {
    if (href === '/') {
      return pathname === '/'
    }
    return pathname.startsWith(href)
  }

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.nav
          initial={{ y: 100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: 100, opacity: 0 }}
          transition={{ duration: 0.3, ease: 'easeInOut' }}
          className="fixed bottom-0 left-0 right-0 z-50 lg:hidden"
        >
          {/* Luxury Bottom Navigation */}
          <div className="bg-white/95 dark:bg-black/95 backdrop-blur-xl border-t border-black/10 dark:border-white/10 shadow-2xl">
            {/* Safe area padding for iOS */}
            <div className="px-2 pt-2 pb-safe-area-inset-bottom">
              <div className="flex items-center justify-around">
                {navigationItems.map((item, index) => {
                  const active = isActive(item.href)
                  
                  return (
                    <motion.button
                      key={item.name}
                      onClick={() => handleNavigation(item.href, item.name)}
                      className={`flex flex-col items-center justify-center p-3 rounded-2xl transition-all duration-300 min-w-[60px] relative ${
                        active 
                          ? 'bg-lime-green/10 text-lime-green' 
                          : 'text-black/60 dark:text-white/60 hover:text-black dark:hover:text-white hover:bg-black/5 dark:hover:bg-white/5'
                      }`}
                      whileTap={{ scale: 0.9 }}
                      initial={{ y: 20, opacity: 0 }}
                      animate={{ y: 0, opacity: 1 }}
                      transition={{ delay: index * 0.1, duration: 0.3 }}
                    >
                      {/* Active indicator */}
                      {active && (
                        <motion.div
                          layoutId="activeTab"
                          className="absolute inset-0 bg-lime-green/10 rounded-2xl"
                          transition={{ type: 'spring', bounce: 0.2, duration: 0.6 }}
                        />
                      )}
                      
                      {/* Icon */}
                      <motion.div
                        className="relative z-10 mb-1"
                        animate={{ 
                          scale: active ? 1.1 : 1,
                          y: active ? -2 : 0
                        }}
                        transition={{ duration: 0.2 }}
                      >
                        {item.icon(active)}
                      </motion.div>
                      
                      {/* Label */}
                      <span className={`text-xs font-medium relative z-10 transition-all duration-300 ${
                        active ? 'text-lime-green font-semibold' : ''
                      }`}>
                        {item.name}
                      </span>
                      
                      {/* Active dot */}
                      {active && (
                        <motion.div
                          initial={{ scale: 0 }}
                          animate={{ scale: 1 }}
                          className="absolute -top-1 w-1 h-1 bg-lime-green rounded-full"
                        />
                      )}
                    </motion.button>
                  )
                })}
              </div>
            </div>
          </div>
        </motion.nav>
      )}
    </AnimatePresence>
  )
}
