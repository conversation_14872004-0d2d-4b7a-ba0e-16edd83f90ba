'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useCart } from '@/contexts/CartContext'
import { useAuth } from '@/contexts/AuthContext'
import { formatPrice } from '@/lib/utils'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'
import Badge from '@/components/ui/Badge'
import { Card, CardContent } from '@/components/ui/Card'
import CheckoutFlow from './CheckoutFlow'
import AuthModal from './AuthModal'

export default function ShoppingCart({ isOpen, onClose }) {
  const { items, summary, updateQuantity, removeItem, applyDiscount, removeDiscount, cart } = useCart()
  const { isAuthenticated } = useAuth()
  const [discountCode, setDiscountCode] = useState('')
  const [isApplyingDiscount, setIsApplyingDiscount] = useState(false)
  const [isCheckoutOpen, setIsCheckoutOpen] = useState(false)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)

  const handleApplyDiscount = async () => {
    setIsApplyingDiscount(true)
    
    // Mock discount validation
    const validCodes = {
      'WELCOME10': { type: 'percentage', value: 10 },
      'SAVE500': { type: 'fixed', value: 500 },
      'VIP20': { type: 'percentage', value: 20 }
    }
    
    setTimeout(() => {
      if (validCodes[discountCode.toUpperCase()]) {
        applyDiscount(discountCode.toUpperCase(), validCodes[discountCode.toUpperCase()])
        setDiscountCode('')
      } else {
        // Show error (in a real app, you'd use a toast notification)
        alert('Código de descuento inválido')
      }
      setIsApplyingDiscount(false)
    }, 1000)
  }

  const handleCheckout = () => {
    if (!isAuthenticated) {
      setIsAuthModalOpen(true)
      return
    }
    setIsCheckoutOpen(true)
  }

  const handleCheckoutSuccess = (order) => {
    // Handle successful checkout
    console.log('Order placed:', order)
    onClose()
  }

  if (!isOpen) return null

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.9, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.9, y: 20 }}
          className="glass-card rounded-2xl max-w-2xl w-full max-h-[90vh] overflow-hidden"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-warm-camel/20">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-natural rounded-lg flex items-center justify-center">
                <svg className="w-5 h-5 text-forest-emerald" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
              </div>
              <div>
                <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                  Carrito de Compras
                </h2>
                <p className="text-sm text-warm-camel">
                  {summary.itemsCount} {summary.itemsCount === 1 ? 'artículo' : 'artículos'}
                </p>
              </div>
            </div>
            
            <Button variant="ghost" size="icon" onClick={onClose}>
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </Button>
          </div>

          {/* Content */}
          <div className="flex flex-col h-full max-h-[calc(90vh-80px)]">
            {items.length === 0 ? (
              /* Empty Cart */
              <div className="flex-1 flex flex-col items-center justify-center p-8">
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
                  className="text-6xl mb-4"
                >
                  🛍️
                </motion.div>
                <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                  Tu carrito está vacío
                </h3>
                <p className="text-warm-camel text-center mb-6">
                  Descubre nuestra increíble colección de sneakers de lujo
                </p>
                <Button variant="primary" onClick={onClose}>
                  Continuar Comprando
                </Button>
              </div>
            ) : (
              <>
                {/* Cart Items */}
                <div className="flex-1 overflow-y-auto p-6 space-y-4">
                  <AnimatePresence mode="popLayout">
                    {items.map((item, index) => (
                      <motion.div
                        key={item.id}
                        layout
                        initial={{ opacity: 0, x: -20 }}
                        animate={{ opacity: 1, x: 0 }}
                        exit={{ opacity: 0, x: 20 }}
                        transition={{ delay: index * 0.1 }}
                      >
                        <Card variant="default" className="overflow-hidden">
                          <CardContent className="p-4">
                            <div className="flex gap-4">
                              {/* Product Image */}
                              <div className="w-20 h-20 bg-gradient-to-br from-soft-steel-gray to-light-cloud-gray rounded-lg flex items-center justify-center flex-shrink-0">
                                <span className="text-warm-camel text-xs font-medium text-center">
                                  {item.product?.name?.slice(0, 10)}...
                                </span>
                              </div>

                              {/* Product Details */}
                              <div className="flex-1 min-w-0">
                                <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray line-clamp-1">
                                  {item.product?.name}
                                </h4>
                                <p className="text-sm text-warm-camel">
                                  {item.product?.brand} • Talla {item.size}
                                </p>
                                <div className="flex items-center gap-2 mt-2">
                                  <span className="text-lg font-bold text-rich-gold">
                                    {formatPrice(item.price, 'MXN', 'es-MX')}
                                  </span>
                                  {item.product?.originalPrice && (
                                    <span className="text-sm text-warm-camel line-through">
                                      {formatPrice(item.product.originalPrice, 'MXN', 'es-MX')}
                                    </span>
                                  )}
                                </div>
                              </div>

                              {/* Quantity Controls */}
                              <div className="flex flex-col items-end gap-2">
                                <Button
                                  variant="ghost"
                                  size="icon"
                                  onClick={() => removeItem(item.id)}
                                  className="h-6 w-6 text-warm-camel hover:text-forest-emerald"
                                >
                                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                  </svg>
                                </Button>
                                
                                <div className="flex items-center gap-2">
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => updateQuantity(item.id, item.quantity - 1)}
                                    className="h-8 w-8"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                  </Button>
                                  
                                  <span className="w-8 text-center font-medium text-forest-emerald dark:text-light-cloud-gray">
                                    {item.quantity}
                                  </span>
                                  
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => updateQuantity(item.id, item.quantity + 1)}
                                    className="h-8 w-8"
                                  >
                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                                    </svg>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      </motion.div>
                    ))}
                  </AnimatePresence>
                </div>

                {/* Discount Code */}
                <div className="px-6 py-4 border-t border-warm-camel/20">
                  {cart.discount ? (
                    <div className="flex items-center justify-between p-3 bg-rich-gold/10 rounded-lg">
                      <div className="flex items-center gap-2">
                        <Badge variant="primary" size="sm">
                          {cart.discount.code}
                        </Badge>
                        <span className="text-sm text-forest-emerald dark:text-light-cloud-gray">
                          -{formatPrice(summary.discount, 'MXN', 'es-MX')}
                        </span>
                      </div>
                      <Button variant="ghost" size="sm" onClick={removeDiscount}>
                        Remover
                      </Button>
                    </div>
                  ) : (
                    <div className="flex gap-2">
                      <Input
                        placeholder="Código de descuento"
                        value={discountCode}
                        onChange={(e) => setDiscountCode(e.target.value)}
                        className="flex-1"
                      />
                      <Button
                        variant="secondary"
                        onClick={handleApplyDiscount}
                        disabled={!discountCode || isApplyingDiscount}
                      >
                        {isApplyingDiscount ? 'Aplicando...' : 'Aplicar'}
                      </Button>
                    </div>
                  )}
                </div>

                {/* Summary */}
                <div className="px-6 py-4 border-t border-warm-camel/20 space-y-3">
                  <div className="flex justify-between text-sm">
                    <span className="text-warm-camel">Subtotal</span>
                    <span className="text-forest-emerald dark:text-light-cloud-gray">
                      {formatPrice(summary.subtotal, 'MXN', 'es-MX')}
                    </span>
                  </div>
                  
                  {summary.discount > 0 && (
                    <div className="flex justify-between text-sm">
                      <span className="text-warm-camel">Descuento</span>
                      <span className="text-rich-gold">
                        -{formatPrice(summary.discount, 'MXN', 'es-MX')}
                      </span>
                    </div>
                  )}
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-warm-camel">Envío</span>
                    <span className="text-forest-emerald dark:text-light-cloud-gray">
                      {summary.shipping === 0 ? 'Gratis' : formatPrice(summary.shipping, 'MXN', 'es-MX')}
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-sm">
                    <span className="text-warm-camel">IVA (16%)</span>
                    <span className="text-forest-emerald dark:text-light-cloud-gray">
                      {formatPrice(summary.tax, 'MXN', 'es-MX')}
                    </span>
                  </div>
                  
                  <div className="flex justify-between text-lg font-bold pt-2 border-t border-warm-camel/20">
                    <span className="text-forest-emerald dark:text-light-cloud-gray">Total</span>
                    <span className="text-rich-gold">
                      {formatPrice(summary.total, 'MXN', 'es-MX')}
                    </span>
                  </div>
                </div>

                {/* Actions */}
                <div className="p-6 border-t border-warm-camel/20 space-y-3">
                  <Button
                    variant="primary"
                    className="w-full"
                    size="lg"
                    onClick={handleCheckout}
                  >
                    {isAuthenticated ? 'Proceder al Checkout' : 'Iniciar Sesión para Comprar'}
                  </Button>
                  <Button variant="ghost" className="w-full" onClick={onClose}>
                    Continuar Comprando
                  </Button>
                </div>
              </>
            )}
          </div>
        </motion.div>
      </motion.div>

      {/* Checkout Flow */}
      <CheckoutFlow
        isOpen={isCheckoutOpen}
        onClose={() => setIsCheckoutOpen(false)}
        onSuccess={handleCheckoutSuccess}
      />

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </AnimatePresence>
  )
}
