#!/usr/bin/env node

/**
 * ENTERPRISE MISSING PRODUCTS SCANNER
 * 
 * PERFECTIONIST ENGINEERING SOLUTION for TWL
 * 
 * Features:
 * - Compares CYTTE materials vs public products
 * - Identifies missing products by category
 * - Generates comprehensive missing products report
 * - Creates copy commands for missing products
 * - Enterprise-grade logging and verification
 */

const fs = require('fs').promises;
const path = require('path');

class MissingProductsScanner {
  constructor() {
    this.cytteBasePath = path.join(process.cwd(), '--materials', 'shoes', '2. CYTTE');
    this.publicBasePath = path.join(process.cwd(), 'public', 'products');
    this.organizedBasePath = path.join(process.cwd(), 'public', 'products-organized');
    
    this.cytteProducts = new Map();
    this.publicProducts = new Map();
    this.missingProducts = new Map();
    
    this.stats = {
      cytteTotal: 0,
      publicTotal: 0,
      missingTotal: 0,
      categoriesMissing: 0,
      brandsMissing: 0
    };
  }

  async initialize() {
    console.log('🔍 ENTERPRISE MISSING PRODUCTS SCANNER');
    console.log('=====================================');
    console.log('🎯 PERFECTIONIST PRODUCT VERIFICATION SYSTEM');
    console.log('📊 COMPARING CYTTE vs PUBLIC PRODUCTS');
    console.log('');
  }

  async scanCytteProducts() {
    console.log('📁 Scanning CYTTE materials folder...');
    await this.scanDirectoryRecursive(this.cytteBasePath, [], 'CYTTE');
    this.stats.cytteTotal = this.cytteProducts.size;
    console.log(`✅ CYTTE scan complete: ${this.stats.cytteTotal} products found`);
  }

  async scanPublicProducts() {
    console.log('📁 Scanning public products folder...');
    await this.scanDirectoryRecursive(this.publicBasePath, [], 'PUBLIC');
    this.stats.publicTotal = this.publicProducts.size;
    console.log(`✅ Public scan complete: ${this.stats.publicTotal} products found`);
  }

  async scanDirectoryRecursive(dirPath, pathComponents, source) {
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true });
      const directories = items.filter(item => item.isDirectory());
      const files = items.filter(item => item.isFile());
      
      // Check if this is a product folder (contains images/videos)
      const hasProductFiles = files.some(file => 
        /\.(jpg|jpeg|png|webp|mp4|mov)$/i.test(file.name)
      );
      
      if (hasProductFiles) {
        const productKey = pathComponents.join('/');
        const productInfo = {
          path: dirPath,
          components: [...pathComponents],
          files: files.map(f => f.name),
          source
        };
        
        if (source === 'CYTTE') {
          this.cytteProducts.set(productKey, productInfo);
        } else {
          this.publicProducts.set(productKey, productInfo);
        }
      }
      
      // Recursively scan subdirectories
      for (const dir of directories) {
        const subPath = path.join(dirPath, dir.name);
        const newComponents = [...pathComponents, dir.name];
        await this.scanDirectoryRecursive(subPath, newComponents, source);
      }
      
    } catch (error) {
      // Directory might not exist or be accessible
      if (error.code !== 'ENOENT') {
        console.log(`⚠️  Error scanning ${dirPath}: ${error.message}`);
      }
    }
  }

  analyzeMissingProducts() {
    console.log('\n🔍 Analyzing missing products...');
    
    const categoryStats = new Map();
    
    for (const [productKey, productInfo] of this.cytteProducts) {
      if (!this.publicProducts.has(productKey)) {
        this.missingProducts.set(productKey, productInfo);
        
        // Analyze by category
        const category = productInfo.components[0] || 'unknown';
        if (!categoryStats.has(category)) {
          categoryStats.set(category, { count: 0, products: [] });
        }
        categoryStats.get(category).count++;
        categoryStats.get(category).products.push(productKey);
      }
    }
    
    this.stats.missingTotal = this.missingProducts.size;
    this.stats.categoriesMissing = categoryStats.size;
    
    console.log(`📊 Analysis complete: ${this.stats.missingTotal} missing products found`);
    
    return categoryStats;
  }

  generateMissingProductsReport(categoryStats) {
    console.log('\n📋 MISSING PRODUCTS REPORT');
    console.log('==========================');
    
    console.log('\n📊 SUMMARY:');
    console.log(`📁 CYTTE Total: ${this.stats.cytteTotal} products`);
    console.log(`📁 Public Total: ${this.stats.publicTotal} products`);
    console.log(`❌ Missing Total: ${this.stats.missingTotal} products`);
    console.log(`📂 Missing Categories: ${this.stats.categoriesMissing}`);
    
    console.log('\n🚨 MISSING BY CATEGORY:');
    for (const [category, data] of categoryStats) {
      console.log(`\n📂 ${category.toUpperCase()}: ${data.count} missing products`);
      
      // Group by brand
      const brandGroups = new Map();
      for (const productKey of data.products) {
        const components = productKey.split('/');
        const brand = components[1] || 'unknown';
        if (!brandGroups.has(brand)) {
          brandGroups.set(brand, []);
        }
        brandGroups.get(brand).push(productKey);
      }
      
      for (const [brand, products] of brandGroups) {
        console.log(`   🏢 ${brand}: ${products.length} products`);
        // Show first few examples
        products.slice(0, 3).forEach(product => {
          console.log(`      - ${product}`);
        });
        if (products.length > 3) {
          console.log(`      ... and ${products.length - 3} more`);
        }
      }
    }
    
    return {
      summary: {
        cytteTotal: this.stats.cytteTotal,
        publicTotal: this.stats.publicTotal,
        missingTotal: this.stats.missingTotal,
        categoriesMissing: this.stats.categoriesMissing
      },
      missingByCategory: Object.fromEntries(categoryStats),
      missingProducts: Object.fromEntries(this.missingProducts)
    };
  }

  generateCopyCommands(categoryStats) {
    console.log('\n🚀 GENERATING COPY COMMANDS...');
    
    const commands = [];
    
    for (const [productKey, productInfo] of this.missingProducts) {
      const sourcePath = productInfo.path;
      const targetPath = path.join(this.publicBasePath, ...productInfo.components);
      
      commands.push({
        source: sourcePath,
        target: targetPath,
        category: productInfo.components[0],
        brand: productInfo.components[1] || 'unknown',
        command: `mkdir -p "${path.dirname(targetPath)}" && cp -r "${sourcePath}" "${targetPath}"`
      });
    }
    
    console.log(`✅ Generated ${commands.length} copy commands`);
    
    return commands;
  }

  async saveMissingProductsReport(report, commands) {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    
    // Save detailed report
    const reportPath = path.join(process.cwd(), 'logs', `missing-products-report-${timestamp}.json`);
    await fs.writeFile(reportPath, JSON.stringify(report, null, 2));
    
    // Save copy commands script
    const scriptPath = path.join(process.cwd(), 'scripts', `copy-missing-products-${timestamp}.sh`);
    const scriptContent = [
      '#!/bin/bash',
      '# ENTERPRISE MISSING PRODUCTS COPY SCRIPT',
      '# Generated by Missing Products Scanner',
      '',
      'echo "🚀 Copying missing products from CYTTE materials..."',
      'echo "=================================================="',
      '',
      ...commands.map(cmd => cmd.command),
      '',
      'echo "✅ Missing products copy complete!"'
    ].join('\n');
    
    await fs.writeFile(scriptPath, scriptContent);
    
    console.log(`\n💾 Reports saved:`);
    console.log(`   📄 Detailed report: ${reportPath}`);
    console.log(`   📜 Copy script: ${scriptPath}`);
    
    return { reportPath, scriptPath };
  }

  async executeCopyCommands(commands) {
    console.log('\n🚀 EXECUTING COPY COMMANDS...');
    console.log('==============================');
    
    let copied = 0;
    let failed = 0;
    
    for (const cmd of commands) {
      try {
        // Create target directory
        await fs.mkdir(path.dirname(cmd.target), { recursive: true });
        
        // Copy directory recursively
        await this.copyDirectory(cmd.source, cmd.target);
        
        copied++;
        console.log(`✅ Copied: ${cmd.category}/${cmd.brand}`);
        
      } catch (error) {
        failed++;
        console.log(`❌ Failed: ${cmd.category}/${cmd.brand} - ${error.message}`);
      }
    }
    
    console.log(`\n📊 Copy Results:`);
    console.log(`✅ Copied: ${copied} products`);
    console.log(`❌ Failed: ${failed} products`);
    console.log(`📊 Success Rate: ${Math.round((copied / commands.length) * 100)}%`);
    
    return { copied, failed };
  }

  async copyDirectory(source, target) {
    try {
      await fs.mkdir(target, { recursive: true });
      const items = await fs.readdir(source, { withFileTypes: true });
      
      for (const item of items) {
        const sourcePath = path.join(source, item.name);
        const targetPath = path.join(target, item.name);
        
        if (item.isDirectory()) {
          await this.copyDirectory(sourcePath, targetPath);
        } else {
          await fs.copyFile(sourcePath, targetPath);
        }
      }
    } catch (error) {
      throw new Error(`Failed to copy ${source} to ${target}: ${error.message}`);
    }
  }
}

// Main execution
async function main() {
  const scanner = new MissingProductsScanner();
  
  try {
    await scanner.initialize();
    
    // Scan both directories
    await scanner.scanCytteProducts();
    await scanner.scanPublicProducts();
    
    // Analyze missing products
    const categoryStats = scanner.analyzeMissingProducts();
    
    // Generate report
    const report = scanner.generateMissingProductsReport(categoryStats);
    
    // Generate copy commands
    const commands = scanner.generateCopyCommands(categoryStats);
    
    // Save reports
    await scanner.saveMissingProductsReport(report, commands);
    
    // Ask user if they want to execute copy commands
    console.log('\n🤔 Would you like to copy the missing products now?');
    console.log('   This will copy all missing products from CYTTE materials to public folder.');
    
    // For now, just show the commands without executing
    console.log('\n📋 To copy missing products, run the generated script or use the copy commands.');
    
    return report;
    
  } catch (error) {
    console.error('❌ Missing products scan failed:', error);
    process.exit(1);
  }
}

// Run the script
if (require.main === module) {
  main();
}

module.exports = MissingProductsScanner;
