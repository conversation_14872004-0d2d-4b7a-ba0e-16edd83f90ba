/**
 * CYTTE PRODUCT CONVERTER
 * Converts CYTTE supplier structure to TWL multi-variant products
 */

import { 
  mapStyle, 
  mapBrand, 
  mapGender, 
  mapModelFamily,
  generateProductName,
  generateProductDescription,
  getProductType,
  getProductSubType,
  generatePrice,
  generateSizes,
  generateMaterials,
  generateReleaseDate,
  generateTags,
  generateKeywords,
  generateSearchTerms
} from '../scripts/product-helpers.js'

/**
 * Convert CYTTE multi-variant product to TWL format
 */
export function convertCytteToTWLProduct(cytteMultiVariantProduct) {
  if (!cytteMultiVariantProduct || !cytteMultiVariantProduct.variants || cytteMultiVariantProduct.variants.length === 0) {
    throw new Error('Invalid CYTTE multi-variant product data')
  }

  const firstVariant = cytteMultiVariantProduct.variants[0]
  
  // Map hierarchy data
  const styleInfo = mapStyle(firstVariant.style)
  const brandInfo = mapBrand(firstVariant.brand)
  const genderInfo = mapGender(firstVariant.gender)
  const modelInfo = mapModelFamily(firstVariant.modelFamily)
  
  // Generate product ID
  const productId = `${styleInfo.id}-${brandInfo.id}-${genderInfo.id}-${modelInfo.id}-${cytteMultiVariantProduct.baseSku.toLowerCase()}`
  
  // Process all variants
  const processedVariants = cytteMultiVariantProduct.variants.map(variant => ({
    ...variant,
    // Convert image paths to web-accessible URLs
    images: variant.images.map(imagePath => convertImagePath(imagePath)),
    videos: variant.videos.map(videoPath => convertVideoPath(videoPath)),
    // Ensure price is set
    price: variant.price || generatePrice(brandInfo.type, variant.isCollaboration),
    // Add additional variant metadata
    slug: generateVariantSlug(variant.variantName),
    isAvailable: true,
    stock: Math.floor(Math.random() * 20) + 1
  }))
  
  // Aggregate data from all variants
  const allColors = [...new Set(processedVariants.flatMap(v => v.colors))]
  const totalImages = processedVariants.reduce((sum, v) => sum + v.imageCount, 0)
  const totalVideos = processedVariants.reduce((sum, v) => sum + v.videoCount, 0)
  const hasVideos = totalVideos > 0
  
  // Determine if this is a collaboration product
  const isCollaboration = processedVariants.some(v => v.isCollaboration)
  const collaborators = [...new Set(processedVariants.filter(v => v.collaborator).map(v => v.collaborator))]
  
  // Generate main product data
  const twlProduct = {
    // Basic info
    id: productId,
    baseSku: cytteMultiVariantProduct.baseSku,
    name: generateProductName(
      brandInfo.name, 
      modelInfo.display, 
      cytteMultiVariantProduct.baseSku,
      collaborators.length === 1 ? collaborators[0] : (collaborators.length > 1 ? 'Multiple' : null)
    ),
    description: generateProductDescription(
      brandInfo.name, 
      modelInfo.display, 
      cytteMultiVariantProduct.baseSku, 
      isCollaboration
    ),
    slug: generateProductSlug(brandInfo.name, modelInfo.display, cytteMultiVariantProduct.baseSku),
    
    // Hierarchy
    style: styleInfo.id,
    styleDisplay: styleInfo.display,
    styleCytteId: firstVariant.style,
    
    brand: brandInfo.name,
    brandId: brandInfo.id,
    brandType: brandInfo.type,
    brandCytteId: firstVariant.brand,
    
    gender: genderInfo.id.toUpperCase(),
    genderDisplay: genderInfo.display,
    genderPath: genderInfo.path,
    
    modelFamily: modelInfo.id,
    modelFamilyDisplay: modelInfo.display,
    modelVariant: modelInfo.id,
    modelCytteId: firstVariant.modelFamily,
    
    // Collaboration info
    isCollaboration,
    collaborationType: isCollaboration ? (collaborators.length > 1 ? 'multiple-brand' : 'brand-x-brand') : null,
    collaborator: collaborators.length === 1 ? collaborators[0] : (collaborators.length > 1 ? 'Multiple' : null),
    collaboratorDisplay: isCollaboration ? 
      (collaborators.length === 1 ? `${collaborators[0]} x ${brandInfo.name}` : 'Colaboraciones Múltiples') : 
      null,
    
    // Multi-variant data
    isMultiVariant: processedVariants.length > 1,
    variantCount: processedVariants.length,
    variants: processedVariants,
    
    // Aggregated media
    colors: allColors,
    totalImages,
    totalVideos,
    hasVideos,
    
    // Main product media (from first variant)
    images: processedVariants[0].images.slice(0, 4),
    videos: processedVariants[0].videos,
    
    // Product classification
    type: getProductType(styleInfo.id),
    subType: getProductSubType(modelInfo.display),
    
    // Pricing (base price from first variant)
    price: processedVariants[0].price,
    originalPrice: Math.floor(processedVariants[0].price * 1.3), // 30% markup for original price
    currency: 'MXN',
    
    // Product details
    sizes: generateSizes(genderInfo.id),
    materials: generateMaterials(brandInfo.type),
    
    // Product flags
    isLimited: isCollaboration || brandInfo.type === 'luxury',
    isExclusive: brandInfo.type === 'luxury',
    isVip: brandInfo.type === 'luxury' || isCollaboration,
    isNew: Math.random() > 0.7,
    
    // Inventory
    stock: processedVariants.reduce((sum, v) => sum + v.stock, 0),
    availability: 'in-stock',
    releaseDate: generateReleaseDate(),
    rating: Math.round((Math.random() * 1.5 + 3.5) * 10) / 10,
    reviews: Math.floor(Math.random() * 500) + 10,
    
    // SEO
    tags: generateTags(brandInfo.name, modelInfo.display, collaborators[0], styleInfo.id),
    keywords: generateKeywords(brandInfo.name, modelInfo.display, cytteMultiVariantProduct.baseSku),
    searchTerms: generateSearchTerms(brandInfo.name, modelInfo.display, collaborators[0]),
    
    // Metadata
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    
    // CYTTE source info (for reference)
    cytteSource: {
      basePath: firstVariant.folderPath,
      variantPaths: processedVariants.map(v => v.folderPath),
      lastScanned: new Date().toISOString()
    }
  }
  
  return twlProduct
}

/**
 * Convert image path from CYTTE structure to web-accessible URL
 */
function convertImagePath(cyttePath) {
  // Remove the --materials prefix and convert to web path
  const webPath = cyttePath
    .replace(/^\/--materials\/shoes\/2\. CYTTE/, '/products')
    .replace(/\\/g, '/')
    .replace(/\s+/g, '-')
    .toLowerCase()
  
  return webPath
}

/**
 * Convert video path from CYTTE structure to web-accessible URL
 */
function convertVideoPath(cyttePath) {
  // Similar to image path conversion
  const webPath = cyttePath
    .replace(/^\/--materials\/shoes\/2\. CYTTE/, '/products')
    .replace(/\\/g, '/')
    .replace(/\s+/g, '-')
    .toLowerCase()
  
  return webPath
}

/**
 * Generate product slug for URL
 */
function generateProductSlug(brand, model, sku) {
  return `${brand}-${model}-${sku}`
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

/**
 * Generate variant slug
 */
function generateVariantSlug(variantName) {
  return variantName
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/^-+|-+$/g, '')
}

/**
 * Batch convert multiple CYTTE products
 */
export function batchConvertCytteProducts(cytteProducts) {
  const twlProducts = []
  const errors = []
  
  for (const cytteProduct of cytteProducts) {
    try {
      const twlProduct = convertCytteToTWLProduct(cytteProduct)
      twlProducts.push(twlProduct)
    } catch (error) {
      errors.push({
        baseSku: cytteProduct?.baseSku || 'unknown',
        error: error.message
      })
    }
  }
  
  return {
    products: twlProducts,
    errors,
    stats: {
      total: cytteProducts.length,
      converted: twlProducts.length,
      failed: errors.length,
      multiVariant: twlProducts.filter(p => p.isMultiVariant).length,
      collaborations: twlProducts.filter(p => p.isCollaboration).length
    }
  }
}

/**
 * Filter products by criteria
 */
export function filterProducts(products, criteria = {}) {
  return products.filter(product => {
    // Filter by brand
    if (criteria.brand && product.brandId !== criteria.brand) {
      return false
    }
    
    // Filter by style
    if (criteria.style && product.style !== criteria.style) {
      return false
    }
    
    // Filter by gender
    if (criteria.gender && product.gender !== criteria.gender.toUpperCase()) {
      return false
    }
    
    // Filter by collaboration
    if (criteria.isCollaboration !== undefined && product.isCollaboration !== criteria.isCollaboration) {
      return false
    }
    
    // Filter by multi-variant
    if (criteria.isMultiVariant !== undefined && product.isMultiVariant !== criteria.isMultiVariant) {
      return false
    }
    
    // Filter by price range
    if (criteria.minPrice && product.price < criteria.minPrice) {
      return false
    }
    if (criteria.maxPrice && product.price > criteria.maxPrice) {
      return false
    }
    
    // Filter by availability
    if (criteria.availability && product.availability !== criteria.availability) {
      return false
    }
    
    return true
  })
}

/**
 * Sort products by criteria
 */
export function sortProducts(products, sortBy = 'name', order = 'asc') {
  return [...products].sort((a, b) => {
    let aValue, bValue
    
    switch (sortBy) {
      case 'price':
        aValue = a.price
        bValue = b.price
        break
      case 'rating':
        aValue = a.rating
        bValue = b.rating
        break
      case 'reviews':
        aValue = a.reviews
        bValue = b.reviews
        break
      case 'releaseDate':
        aValue = new Date(a.releaseDate)
        bValue = new Date(b.releaseDate)
        break
      case 'name':
      default:
        aValue = a.name.toLowerCase()
        bValue = b.name.toLowerCase()
        break
    }
    
    if (order === 'desc') {
      return aValue < bValue ? 1 : aValue > bValue ? -1 : 0
    } else {
      return aValue > bValue ? 1 : aValue < bValue ? -1 : 0
    }
  })
}
