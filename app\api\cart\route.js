// TWL Cart API - Shopping cart management
import { NextResponse } from 'next/server'
import { supabase, dbHelpers } from '@/lib/supabase'

// GET /api/cart - Get user's cart
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const sessionId = searchParams.get('sessionId')

    if (!userId && !sessionId) {
      return NextResponse.json(
        { error: 'User ID or session ID required' },
        { status: 400 }
      )
    }

    // Get cart from database
    let cart = null
    
    if (userId) {
      cart = await dbHelpers.getUserCart(userId)
    } else {
      // For guest users, use session-based cart
      const { data, error } = await supabase
        .from('carts')
        .select('*')
        .eq('session_id', sessionId)
        .single()
      
      if (error && error.code !== 'PGRST116') {
        throw error
      }
      cart = data
    }

    if (!cart) {
      // Return empty cart
      return NextResponse.json({
        cart: {
          id: null,
          items: [],
          total_amount: 0,
          currency: 'MXN',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        }
      })
    }

    // Enrich cart items with current product data
    const enrichedItems = await Promise.all(
      cart.items.map(async (item) => {
        try {
          const { data: product } = await supabase
            .from('products')
            .select(`
              id,
              name,
              slug,
              price,
              original_price,
              currency,
              images,
              stock_quantity,
              in_stock,
              brands:brand_id(name, slug)
            `)
            .eq('id', item.product_id)
            .single()

          return {
            ...item,
            product: product ? {
              ...product,
              price: product.price / 100, // Convert from cents
              original_price: product.original_price ? product.original_price / 100 : null
            } : null
          }
        } catch (error) {
          console.error(`Error fetching product ${item.product_id}:`, error)
          return {
            ...item,
            product: null,
            error: 'Product not found'
          }
        }
      })
    )

    // Filter out items with missing products
    const validItems = enrichedItems.filter(item => item.product !== null)

    // Calculate totals
    const subtotal = validItems.reduce((sum, item) => {
      return sum + (item.product.price * item.quantity)
    }, 0)

    const response = {
      cart: {
        id: cart.id,
        items: validItems,
        subtotal,
        total_amount: cart.total_amount / 100, // Convert from cents
        currency: cart.currency,
        created_at: cart.created_at,
        updated_at: cart.updated_at
      }
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Cart GET API Error:', error)
    return NextResponse.json(
      { 
        error: 'Error fetching cart',
        message: error.message 
      },
      { status: 500 }
    )
  }
}

// POST /api/cart - Add item to cart
export async function POST(request) {
  try {
    const body = await request.json()
    const { userId, sessionId, productId, size, color, quantity = 1 } = body

    if (!userId && !sessionId) {
      return NextResponse.json(
        { error: 'User ID or session ID required' },
        { status: 400 }
      )
    }

    if (!productId || !size) {
      return NextResponse.json(
        { error: 'Product ID and size are required' },
        { status: 400 }
      )
    }

    // Verify product exists and is available
    const { data: product, error: productError } = await supabase
      .from('products')
      .select('id, name, price, stock_quantity, in_stock, status')
      .eq('id', productId)
      .eq('status', 'active')
      .single()

    if (productError || !product) {
      return NextResponse.json(
        { error: 'Product not found or unavailable' },
        { status: 404 }
      )
    }

    if (!product.in_stock || product.stock_quantity < quantity) {
      return NextResponse.json(
        { error: 'Product out of stock' },
        { status: 400 }
      )
    }

    // Get or create cart
    let cart = null
    
    if (userId) {
      cart = await dbHelpers.getUserCart(userId)
    } else {
      const { data, error } = await supabase
        .from('carts')
        .select('*')
        .eq('session_id', sessionId)
        .single()
      
      if (error && error.code !== 'PGRST116') {
        throw error
      }
      cart = data
    }

    const cartItems = cart?.items || []
    
    // Check if item already exists in cart
    const existingItemIndex = cartItems.findIndex(item => 
      item.product_id === productId && 
      item.size === size && 
      item.color === color
    )

    let updatedItems
    if (existingItemIndex >= 0) {
      // Update existing item quantity
      updatedItems = [...cartItems]
      updatedItems[existingItemIndex].quantity += quantity
    } else {
      // Add new item
      const newItem = {
        product_id: productId,
        size,
        color,
        quantity,
        unit_price: product.price, // Price in cents
        added_at: new Date().toISOString()
      }
      updatedItems = [...cartItems, newItem]
    }

    // Calculate total amount
    const totalAmount = updatedItems.reduce((sum, item) => {
      return sum + (item.unit_price * item.quantity)
    }, 0)

    // Update or create cart
    const cartData = {
      items: updatedItems,
      total_amount: totalAmount,
      currency: 'MXN',
      updated_at: new Date().toISOString()
    }

    if (userId) {
      cartData.user_id = userId
    } else {
      cartData.session_id = sessionId
    }

    let updatedCart
    if (cart) {
      // Update existing cart
      const { data, error } = await supabase
        .from('carts')
        .update(cartData)
        .eq('id', cart.id)
        .select()
        .single()

      if (error) throw error
      updatedCart = data
    } else {
      // Create new cart
      cartData.expires_at = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString() // 30 days
      
      const { data, error } = await supabase
        .from('carts')
        .insert(cartData)
        .select()
        .single()

      if (error) throw error
      updatedCart = data
    }

    return NextResponse.json({
      message: 'Item added to cart successfully',
      cart: {
        ...updatedCart,
        total_amount: updatedCart.total_amount / 100 // Convert from cents
      }
    })

  } catch (error) {
    console.error('Cart POST API Error:', error)
    return NextResponse.json(
      { 
        error: 'Error adding item to cart',
        message: error.message 
      },
      { status: 500 }
    )
  }
}

// PUT /api/cart - Update cart item quantity
export async function PUT(request) {
  try {
    const body = await request.json()
    const { userId, sessionId, productId, size, color, quantity } = body

    if (!userId && !sessionId) {
      return NextResponse.json(
        { error: 'User ID or session ID required' },
        { status: 400 }
      )
    }

    if (quantity < 0) {
      return NextResponse.json(
        { error: 'Quantity must be positive' },
        { status: 400 }
      )
    }

    // Get cart
    let cart = null
    
    if (userId) {
      cart = await dbHelpers.getUserCart(userId)
    } else {
      const { data, error } = await supabase
        .from('carts')
        .select('*')
        .eq('session_id', sessionId)
        .single()
      
      if (error) throw error
      cart = data
    }

    if (!cart) {
      return NextResponse.json(
        { error: 'Cart not found' },
        { status: 404 }
      )
    }

    // Update item quantity or remove if quantity is 0
    let updatedItems = cart.items.filter(item =>
      !(item.product_id === productId && item.size === size && item.color === color)
    )

    if (quantity > 0) {
      const updatedItem = cart.items.find(item =>
        item.product_id === productId && item.size === size && item.color === color
      )

      if (updatedItem) {
        updatedItems = [...updatedItems, {
          ...updatedItem,
          quantity
        }]
      }
    }

    // Calculate total amount
    const totalAmount = updatedItems.reduce((sum, item) => {
      return sum + (item.unit_price * item.quantity)
    }, 0)

    // Update cart
    const { data: updatedCart, error } = await supabase
      .from('carts')
      .update({
        items: updatedItems,
        total_amount: totalAmount,
        updated_at: new Date().toISOString()
      })
      .eq('id', cart.id)
      .select()
      .single()

    if (error) throw error

    return NextResponse.json({
      message: 'Cart updated successfully',
      cart: {
        ...updatedCart,
        total_amount: updatedCart.total_amount / 100 // Convert from cents
      }
    })

  } catch (error) {
    console.error('Cart PUT API Error:', error)
    return NextResponse.json(
      { 
        error: 'Error updating cart',
        message: error.message 
      },
      { status: 500 }
    )
  }
}

// DELETE /api/cart - Clear cart or remove item
export async function DELETE(request) {
  try {
    const { searchParams } = new URL(request.url)
    const userId = searchParams.get('userId')
    const sessionId = searchParams.get('sessionId')
    const productId = searchParams.get('productId')
    const size = searchParams.get('size')
    const color = searchParams.get('color')

    if (!userId && !sessionId) {
      return NextResponse.json(
        { error: 'User ID or session ID required' },
        { status: 400 }
      )
    }

    // Get cart
    let cart = null
    
    if (userId) {
      cart = await dbHelpers.getUserCart(userId)
    } else {
      const { data, error } = await supabase
        .from('carts')
        .select('*')
        .eq('session_id', sessionId)
        .single()
      
      if (error) throw error
      cart = data
    }

    if (!cart) {
      return NextResponse.json(
        { error: 'Cart not found' },
        { status: 404 }
      )
    }

    let updatedItems
    if (productId) {
      // Remove specific item
      updatedItems = cart.items.filter(item => 
        !(item.product_id === productId && 
          (!size || item.size === size) && 
          (!color || item.color === color))
      )
    } else {
      // Clear entire cart
      updatedItems = []
    }

    // Calculate total amount
    const totalAmount = updatedItems.reduce((sum, item) => {
      return sum + (item.unit_price * item.quantity)
    }, 0)

    // Update cart
    const { data: updatedCart, error } = await supabase
      .from('carts')
      .update({
        items: updatedItems,
        total_amount: totalAmount,
        updated_at: new Date().toISOString()
      })
      .eq('id', cart.id)
      .select()
      .single()

    if (error) throw error

    return NextResponse.json({
      message: productId ? 'Item removed from cart' : 'Cart cleared',
      cart: {
        ...updatedCart,
        total_amount: updatedCart.total_amount / 100 // Convert from cents
      }
    })

  } catch (error) {
    console.error('Cart DELETE API Error:', error)
    return NextResponse.json(
      { 
        error: 'Error updating cart',
        message: error.message 
      },
      { status: 500 }
    )
  }
}
