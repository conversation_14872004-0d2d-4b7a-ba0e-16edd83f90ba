# TWL Enterprise Developer Guide

**👨‍💻 Complete Developer Documentation for Integration and Extension**

This guide provides comprehensive information for developers working with the TWL Enterprise Product System, including integration patterns, code examples, and best practices.

## 📋 Table of Contents

- [Getting Started](#-getting-started)
- [Integration Patterns](#-integration-patterns)
- [Code Examples](#-code-examples)
- [API Integration](#-api-integration)
- [Custom Extensions](#-custom-extensions)
- [Testing Guidelines](#-testing-guidelines)
- [Best Practices](#-best-practices)
- [Development Workflow](#-development-workflow)

## 🚀 Getting Started

### **Development Environment Setup**

```bash
# Clone the project
git clone https://github.com/your-org/twl-enterprise.git
cd twl-enterprise

# Install dependencies
npm install

# Set up environment
cp .env.example .env.local

# Start development server
npm run dev

# Test enterprise system
npm run test:enterprise
```

### **Basic Integration**

```typescript
// lib/enterprise/init.ts
import { initializeTWLSystem } from './TWLEnterpriseSystem'

export async function setupEnterpriseSystem() {
  const system = await initializeTWLSystem({
    productsBasePath: 'public/products',
    enableCache: true,
    enableAutoScan: true,
    environment: 'development',
    logLevel: 'debug'
  })

  console.log('✅ Enterprise system ready')
  return system
}
```

### **Next.js Integration**

```typescript
// app/layout.tsx
import { setupEnterpriseSystem } from '@/lib/enterprise/init'

export default async function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  // Initialize enterprise system on server startup
  if (typeof window === 'undefined') {
    await setupEnterpriseSystem()
  }

  return (
    <html lang="en">
      <body>{children}</body>
    </html>
  )
}
```

## 🔧 Integration Patterns

### **1. React Hook Integration**

```typescript
// hooks/useProduct.ts
import { useState, useEffect } from 'react'
import { TWLProduct } from '@/lib/enterprise/models/Product'

export function useProduct(productId: string) {
  const [product, setProduct] = useState<TWLProduct | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (!productId) return

    const fetchProduct = async () => {
      try {
        setLoading(true)
        setError(null)

        const response = await fetch(`/api/enterprise/products/${productId}`)
        
        if (!response.ok) {
          throw new Error(`Failed to fetch product: ${response.statusText}`)
        }

        const data = await response.json()
        
        if (data.success) {
          setProduct(data.data)
        } else {
          throw new Error(data.error?.message || 'Unknown error')
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error')
        setProduct(null)
      } finally {
        setLoading(false)
      }
    }

    fetchProduct()
  }, [productId])

  return { product, loading, error }
}
```

### **2. Search Hook with Filters**

```typescript
// hooks/useProductSearch.ts
import { useState, useEffect, useCallback } from 'react'
import { TWLProductSearchResult, TWLProductFilters } from '@/lib/enterprise/models/Product'

export function useProductSearch() {
  const [results, setResults] = useState<TWLProductSearchResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  const search = useCallback(async (
    query: string,
    filters: TWLProductFilters = {},
    page: number = 1,
    pageSize: number = 20
  ) => {
    try {
      setLoading(true)
      setError(null)

      const params = new URLSearchParams({
        q: query,
        page: page.toString(),
        pageSize: pageSize.toString(),
        ...Object.fromEntries(
          Object.entries(filters).map(([key, value]) => [
            key,
            Array.isArray(value) ? value.join(',') : String(value)
          ])
        )
      })

      const response = await fetch(`/api/enterprise/products?${params}`)
      
      if (!response.ok) {
        throw new Error(`Search failed: ${response.statusText}`)
      }

      const data = await response.json()
      
      if (data.success) {
        setResults(data.data)
      } else {
        throw new Error(data.error?.message || 'Search failed')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Search failed')
      setResults(null)
    } finally {
      setLoading(false)
    }
  }, [])

  return { results, loading, error, search }
}
```

### **3. Context Provider Pattern**

```typescript
// contexts/EnterpriseContext.tsx
import React, { createContext, useContext, useEffect, useState } from 'react'
import { SystemHealth } from '@/lib/enterprise/models/Product'

interface EnterpriseContextType {
  systemHealth: SystemHealth | null
  isSystemReady: boolean
  refreshHealth: () => Promise<void>
}

const EnterpriseContext = createContext<EnterpriseContextType | undefined>(undefined)

export function EnterpriseProvider({ children }: { children: React.ReactNode }) {
  const [systemHealth, setSystemHealth] = useState<SystemHealth | null>(null)
  const [isSystemReady, setIsSystemReady] = useState(false)

  const refreshHealth = async () => {
    try {
      const response = await fetch('/api/enterprise/system/status')
      const data = await response.json()
      
      if (data.success) {
        setSystemHealth(data.data)
        setIsSystemReady(data.data.status === 'healthy')
      }
    } catch (error) {
      console.error('Failed to fetch system health:', error)
      setIsSystemReady(false)
    }
  }

  useEffect(() => {
    refreshHealth()
    
    // Refresh health every 30 seconds
    const interval = setInterval(refreshHealth, 30000)
    return () => clearInterval(interval)
  }, [])

  return (
    <EnterpriseContext.Provider value={{
      systemHealth,
      isSystemReady,
      refreshHealth
    }}>
      {children}
    </EnterpriseContext.Provider>
  )
}

export function useEnterprise() {
  const context = useContext(EnterpriseContext)
  if (context === undefined) {
    throw new Error('useEnterprise must be used within an EnterpriseProvider')
  }
  return context
}
```

## 💻 Code Examples

### **Product Display Component**

```typescript
// components/ProductCard.tsx
import React from 'react'
import Image from 'next/image'
import { TWLProduct } from '@/lib/enterprise/models/Product'
import { useProduct } from '@/hooks/useProduct'

interface ProductCardProps {
  productId: string
  className?: string
}

export function ProductCard({ productId, className = '' }: ProductCardProps) {
  const { product, loading, error } = useProduct(productId)

  if (loading) {
    return (
      <div className={`animate-pulse bg-gray-200 rounded-lg h-64 ${className}`}>
        <div className="h-48 bg-gray-300 rounded-t-lg"></div>
        <div className="p-4 space-y-2">
          <div className="h-4 bg-gray-300 rounded w-3/4"></div>
          <div className="h-4 bg-gray-300 rounded w-1/2"></div>
        </div>
      </div>
    )
  }

  if (error || !product) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <p className="text-red-600">Failed to load product</p>
        {error && <p className="text-sm text-red-500">{error}</p>}
      </div>
    )
  }

  return (
    <div className={`bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow ${className}`}>
      {/* Product Image */}
      <div className="relative h-48">
        <Image
          src={product.media.primaryImage}
          alt={product.name}
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
        
        {/* Badges */}
        <div className="absolute top-2 left-2 space-y-1">
          {product.details.isLimitedEdition && (
            <span className="bg-red-500 text-white text-xs px-2 py-1 rounded">
              Limited Edition
            </span>
          )}
          {product.pricing.isOnSale && (
            <span className="bg-green-500 text-white text-xs px-2 py-1 rounded">
              Sale
            </span>
          )}
        </div>

        {/* Wishlist Button */}
        <button className="absolute top-2 right-2 p-2 bg-white rounded-full shadow-md hover:shadow-lg">
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
          </svg>
        </button>
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-900 mb-1 line-clamp-2">
          {product.name}
        </h3>
        
        <p className="text-sm text-gray-600 mb-2">
          {product.brand.name}
        </p>

        {/* Pricing */}
        <div className="flex items-center space-x-2 mb-3">
          <span className="text-lg font-bold text-gray-900">
            ${product.pricing.currentPrice}
          </span>
          {product.pricing.originalPrice > product.pricing.currentPrice && (
            <>
              <span className="text-sm text-gray-500 line-through">
                ${product.pricing.originalPrice}
              </span>
              <span className="text-sm text-green-600 font-medium">
                {product.pricing.discountPercent}% off
              </span>
            </>
          )}
        </div>

        {/* Rating */}
        <div className="flex items-center space-x-1 mb-3">
          <div className="flex text-yellow-400">
            {[...Array(5)].map((_, i) => (
              <svg
                key={i}
                className={`w-4 h-4 ${i < Math.floor(product.metadata.rating) ? 'fill-current' : 'text-gray-300'}`}
                viewBox="0 0 20 20"
              >
                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
              </svg>
            ))}
          </div>
          <span className="text-sm text-gray-600">
            ({product.metadata.reviewCount})
          </span>
        </div>

        {/* Stock Status */}
        <div className="flex items-center justify-between">
          <span className={`text-sm font-medium ${
            product.inventory.inStock ? 'text-green-600' : 'text-red-600'
          }`}>
            {product.inventory.inStock ? 'In Stock' : 'Out of Stock'}
          </span>
          
          <button 
            className="bg-blue-600 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 disabled:bg-gray-400"
            disabled={!product.inventory.inStock}
          >
            Add to Cart
          </button>
        </div>
      </div>
    </div>
  )
}
```

### **Search Component**

```typescript
// components/ProductSearch.tsx
import React, { useState, useEffect } from 'react'
import { useProductSearch } from '@/hooks/useProductSearch'
import { ProductCard } from './ProductCard'
import { TWLProductFilters } from '@/lib/enterprise/models/Product'

export function ProductSearch() {
  const [query, setQuery] = useState('')
  const [filters, setFilters] = useState<TWLProductFilters>({})
  const [page, setPage] = useState(1)
  const { results, loading, error, search } = useProductSearch()

  // Debounced search
  useEffect(() => {
    const timer = setTimeout(() => {
      if (query.trim()) {
        search(query, filters, page)
      }
    }, 300)

    return () => clearTimeout(timer)
  }, [query, filters, page, search])

  const handleFilterChange = (key: keyof TWLProductFilters, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
    setPage(1) // Reset to first page
  }

  return (
    <div className="space-y-6">
      {/* Search Input */}
      <div className="relative">
        <input
          type="text"
          placeholder="Search products..."
          value={query}
          onChange={(e) => setQuery(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
        />
        {loading && (
          <div className="absolute right-3 top-2.5">
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-600"></div>
          </div>
        )}
      </div>

      {/* Filters */}
      <div className="flex flex-wrap gap-4">
        <select
          value={filters.sortBy || ''}
          onChange={(e) => handleFilterChange('sortBy', e.target.value || undefined)}
          className="px-3 py-2 border border-gray-300 rounded-md"
        >
          <option value="">Sort by</option>
          <option value="name">Name</option>
          <option value="price">Price</option>
          <option value="rating">Rating</option>
          <option value="popularity">Popularity</option>
        </select>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={filters.inStockOnly || false}
            onChange={(e) => handleFilterChange('inStockOnly', e.target.checked || undefined)}
          />
          <span>In Stock Only</span>
        </label>

        <label className="flex items-center space-x-2">
          <input
            type="checkbox"
            checked={filters.isLimitedEdition || false}
            onChange={(e) => handleFilterChange('isLimitedEdition', e.target.checked || undefined)}
          />
          <span>Limited Edition</span>
        </label>
      </div>

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-600">Search failed: {error}</p>
        </div>
      )}

      {/* Results */}
      {results && (
        <div>
          <div className="flex justify-between items-center mb-4">
            <p className="text-gray-600">
              {results.total} products found
              {results.searchTime && (
                <span className="text-sm"> in {results.searchTime}ms</span>
              )}
            </p>
          </div>

          {/* Product Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {results.products.map((product) => (
              <ProductCard key={product.id} productId={product.id} />
            ))}
          </div>

          {/* Pagination */}
          {results.hasMore && (
            <div className="flex justify-center mt-8">
              <button
                onClick={() => setPage(prev => prev + 1)}
                disabled={loading}
                className="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400"
              >
                {loading ? 'Loading...' : 'Load More'}
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
```

## 🌐 API Integration

### **TypeScript API Client**

```typescript
// lib/api/enterprise-client.ts
import { TWLProduct, TWLProductSearchResult, TWLProductFilters } from '@/lib/enterprise/models/Product'

export class EnterpriseAPIClient {
  private baseURL: string
  private apiKey?: string

  constructor(baseURL: string = '/api/enterprise', apiKey?: string) {
    this.baseURL = baseURL
    this.apiKey = apiKey
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${this.baseURL}${endpoint}`
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers
    }

    if (this.apiKey) {
      headers['Authorization'] = `Bearer ${this.apiKey}`
    }

    const response = await fetch(url, {
      ...options,
      headers
    })

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}))
      throw new Error(errorData.error?.message || `HTTP ${response.status}: ${response.statusText}`)
    }

    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.error?.message || 'API request failed')
    }

    return data.data
  }

  // Product operations
  async getProduct(productId: string): Promise<TWLProduct> {
    return this.request<TWLProduct>(`/products/${productId}`)
  }

  async searchProducts(
    query?: string,
    filters?: TWLProductFilters,
    page: number = 1,
    pageSize: number = 20
  ): Promise<TWLProductSearchResult> {
    const params = new URLSearchParams({
      page: page.toString(),
      pageSize: pageSize.toString()
    })

    if (query) params.set('q', query)
    
    if (filters) {
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined) {
          params.set(key, Array.isArray(value) ? value.join(',') : String(value))
        }
      })
    }

    return this.request<TWLProductSearchResult>(`/products?${params}`)
  }

  async getProductsBatch(productIds: string[]): Promise<{ products: TWLProduct[]; requested: number; found: number }> {
    return this.request(`/products/batch`, {
      method: 'POST',
      body: JSON.stringify({ productIds })
    })
  }

  // System operations
  async getSystemStatus(): Promise<any> {
    return this.request('/system/status')
  }

  async triggerScan(): Promise<{ message: string }> {
    return this.request('/system/scan', { method: 'POST' })
  }

  // Metadata operations
  async getCategories(): Promise<Array<{ id: string; name: string; productCount: number }>> {
    return this.request('/products/categories')
  }

  async getBrands(): Promise<Array<{ id: string; name: string; isLuxury: boolean; productCount: number }>> {
    return this.request('/products/brands')
  }
}

// Default client instance
export const enterpriseAPI = new EnterpriseAPIClient()
```

## 🧪 Testing Guidelines

### **Unit Tests**

```typescript
// __tests__/enterprise/ProductLoader.test.ts
import { ProductLoader } from '@/lib/enterprise/core/ProductLoader'

describe('ProductLoader', () => {
  let loader: ProductLoader

  beforeEach(() => {
    loader = new ProductLoader({
      productsBasePath: 'test/fixtures/products',
      enableCache: false,
      enableAutoScan: false
    })
  })

  afterEach(async () => {
    await loader.shutdown()
  })

  test('should load product by ID', async () => {
    await loader.initialize()
    
    const result = await loader.loadProduct('test-product-1')
    
    expect(result.success).toBe(true)
    expect(result.data).toBeDefined()
    expect(result.data?.id).toBe('test-product-1')
  })

  test('should return null for non-existent product', async () => {
    await loader.initialize()
    
    const result = await loader.loadProduct('non-existent')
    
    expect(result.success).toBe(false)
    expect(result.data).toBeNull()
  })

  test('should search products with filters', async () => {
    await loader.initialize()
    
    const result = await loader.searchProducts('test', {
      inStockOnly: true
    })
    
    expect(result.success).toBe(true)
    expect(result.data?.products).toBeDefined()
  })
})
```

### **Integration Tests**

```typescript
// __tests__/api/products.test.ts
import { createMocks } from 'node-mocks-http'
import handler from '@/app/api/enterprise/products/[id]/route'

describe('/api/enterprise/products/[id]', () => {
  test('should return product data', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      url: '/api/enterprise/products/test-product-1'
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(200)
    
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(true)
    expect(data.data.id).toBe('test-product-1')
  })

  test('should return 404 for non-existent product', async () => {
    const { req, res } = createMocks({
      method: 'GET',
      url: '/api/enterprise/products/non-existent'
    })

    await handler(req, res)

    expect(res._getStatusCode()).toBe(404)
    
    const data = JSON.parse(res._getData())
    expect(data.success).toBe(false)
    expect(data.error.code).toBe('PRODUCT_NOT_FOUND')
  })
})
```

### **Performance Tests**

```typescript
// __tests__/performance/load-test.ts
import { EnterpriseAPIClient } from '@/lib/api/enterprise-client'

describe('Performance Tests', () => {
  const client = new EnterpriseAPIClient()

  test('should handle concurrent requests', async () => {
    const startTime = Date.now()
    
    // Simulate 100 concurrent requests
    const promises = Array.from({ length: 100 }, (_, i) => 
      client.searchProducts('test', {}, 1, 10)
    )
    
    const results = await Promise.all(promises)
    const duration = Date.now() - startTime
    
    expect(results).toHaveLength(100)
    expect(duration).toBeLessThan(5000) // Should complete in under 5 seconds
    
    // All requests should succeed
    results.forEach(result => {
      expect(result.products).toBeDefined()
    })
  })

  test('should maintain performance under load', async () => {
    const measurements: number[] = []
    
    // Measure response times for 50 requests
    for (let i = 0; i < 50; i++) {
      const start = Date.now()
      await client.getSystemStatus()
      const duration = Date.now() - start
      measurements.push(duration)
    }
    
    const averageTime = measurements.reduce((a, b) => a + b, 0) / measurements.length
    const p95Time = measurements.sort((a, b) => a - b)[Math.floor(measurements.length * 0.95)]
    
    expect(averageTime).toBeLessThan(100) // Average < 100ms
    expect(p95Time).toBeLessThan(200)     // 95th percentile < 200ms
  })
})
```

## 📝 Best Practices

### **1. Error Handling**

```typescript
// Always handle errors gracefully
try {
  const product = await enterpriseAPI.getProduct(productId)
  return product
} catch (error) {
  console.error('Failed to load product:', error)
  
  // Return fallback data or show user-friendly error
  return null
}
```

### **2. Performance Optimization**

```typescript
// Use React.memo for expensive components
export const ProductCard = React.memo(({ productId }: { productId: string }) => {
  // Component implementation
})

// Implement virtual scrolling for large lists
import { FixedSizeList as List } from 'react-window'

export function ProductList({ products }: { products: TWLProduct[] }) {
  const Row = ({ index, style }: { index: number; style: React.CSSProperties }) => (
    <div style={style}>
      <ProductCard productId={products[index].id} />
    </div>
  )

  return (
    <List
      height={600}
      itemCount={products.length}
      itemSize={300}
    >
      {Row}
    </List>
  )
}
```

### **3. Type Safety**

```typescript
// Always use TypeScript interfaces
import { TWLProduct } from '@/lib/enterprise/models/Product'

// Type your API responses
interface APIResponse<T> {
  success: boolean
  data?: T
  error?: {
    code: string
    message: string
  }
}

// Use generic types for reusable components
interface DataTableProps<T> {
  data: T[]
  columns: Array<{
    key: keyof T
    label: string
    render?: (value: T[keyof T], item: T) => React.ReactNode
  }>
}
```

### **4. Caching Strategy**

```typescript
// Implement client-side caching
import { useQuery } from '@tanstack/react-query'

export function useProduct(productId: string) {
  return useQuery({
    queryKey: ['product', productId],
    queryFn: () => enterpriseAPI.getProduct(productId),
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  })
}
```

## 🔄 Development Workflow

### **1. Feature Development**

```bash
# Create feature branch
git checkout -b feature/new-search-filters

# Make changes
# Add tests
npm run test

# Test enterprise system
npm run test:enterprise

# Commit changes
git add .
git commit -m "feat: add advanced search filters"

# Push and create PR
git push origin feature/new-search-filters
```

### **2. Code Review Checklist**

- [ ] TypeScript types are properly defined
- [ ] Error handling is implemented
- [ ] Tests are written and passing
- [ ] Performance impact is considered
- [ ] Documentation is updated
- [ ] Enterprise system integration is tested

### **3. Deployment Process**

```bash
# Build and test
npm run build
npm run test:all

# Deploy to staging
npm run deploy:staging

# Run integration tests
npm run test:integration

# Deploy to production
npm run deploy:production
```

---

**👨‍💻 This developer guide provides everything you need to integrate with and extend the TWL Enterprise Product System.**

**🚀 Ready to build amazing e-commerce experiences with enterprise-grade performance and reliability!**
