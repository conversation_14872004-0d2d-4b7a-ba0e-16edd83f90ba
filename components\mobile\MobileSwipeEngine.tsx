'use client'

/**
 * 🎯 MOBILE SWIPE ENGINE - THE MAESTRO'S MASTERPIECE
 * 
 * Ultra-sophisticated swipe detection and gesture handling system
 * Built for luxury mobile experiences with cutting-edge interactions
 * 
 * Features:
 * - Multi-directional swipe detection (horizontal, vertical, diagonal)
 * - Velocity-based gesture recognition
 * - Momentum scrolling with physics
 * - Haptic feedback integration
 * - Gesture conflict resolution
 * - Performance-optimized with RAF
 */

import { useRef, useCallback, useEffect, useState } from 'react'
import { motion, useMotionValue, useTransform, animate } from 'framer-motion'

interface SwipeConfig {
  threshold?: number
  velocityThreshold?: number
  enableHaptics?: boolean
  enableMomentum?: boolean
  resistanceMultiplier?: number
  snapBackDuration?: number
  directions?: ('left' | 'right' | 'up' | 'down')[]
}

interface SwipeCallbacks {
  onSwipeLeft?: () => void
  onSwipeRight?: () => void
  onSwipeUp?: () => void
  onSwipeDown?: () => void
  onSwipeStart?: (direction: string) => void
  onSwipeEnd?: () => void
  onMomentumEnd?: () => void
}

interface TouchPoint {
  x: number
  y: number
  timestamp: number
}

export default function MobileSwipeEngine({
  children,
  config = {},
  callbacks = {},
  className = '',
  disabled = false
}: {
  children: React.ReactNode
  config?: SwipeConfig
  callbacks?: SwipeCallbacks
  className?: string
  disabled?: boolean
}) {
  // Default configuration with luxury UX parameters
  const defaultConfig: SwipeConfig = {
    threshold: 50,
    velocityThreshold: 0.3,
    enableHaptics: true,
    enableMomentum: true,
    resistanceMultiplier: 0.3,
    snapBackDuration: 0.4,
    directions: ['left', 'right', 'up', 'down']
  }

  const finalConfig = { ...defaultConfig, ...config }

  // Motion values for smooth animations
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const opacity = useTransform([x, y], ([xVal, yVal]: [number, number]) => {
    const distance = Math.sqrt(xVal * xVal + yVal * yVal)
    return Math.max(0.7, 1 - distance / 300)
  })

  // Touch tracking state
  const [isActive, setIsActive] = useState(false)
  const [swipeDirection, setSwipeDirection] = useState<string | null>(null)
  
  // Touch points for velocity calculation
  const touchPoints = useRef<TouchPoint[]>([])
  const startPoint = useRef<TouchPoint | null>(null)
  const containerRef = useRef<HTMLDivElement>(null)

  // Haptic feedback utility
  const triggerHaptic = useCallback((intensity: 'light' | 'medium' | 'heavy' = 'light') => {
    if (!finalConfig.enableHaptics) return
    
    try {
      if ('vibrate' in navigator) {
        const patterns = {
          light: [10],
          medium: [20],
          heavy: [30, 10, 30]
        }
        navigator.vibrate(patterns[intensity])
      }
    } catch (error) {
      // Silently fail if haptics not supported
    }
  }, [finalConfig.enableHaptics])

  // Calculate velocity from touch points
  const calculateVelocity = useCallback((currentPoint: TouchPoint): { vx: number; vy: number } => {
    if (touchPoints.current.length < 2) return { vx: 0, vy: 0 }
    
    const recentPoints = touchPoints.current.slice(-3)
    const timeSpan = currentPoint.timestamp - recentPoints[0].timestamp
    
    if (timeSpan === 0) return { vx: 0, vy: 0 }
    
    const deltaX = currentPoint.x - recentPoints[0].x
    const deltaY = currentPoint.y - recentPoints[0].y
    
    return {
      vx: deltaX / timeSpan,
      vy: deltaY / timeSpan
    }
  }, [])

  // Determine swipe direction with sophisticated logic
  const getSwipeDirection = useCallback((
    deltaX: number, 
    deltaY: number, 
    velocity: { vx: number; vy: number }
  ): string | null => {
    const absX = Math.abs(deltaX)
    const absY = Math.abs(deltaY)
    const absVx = Math.abs(velocity.vx)
    const absVy = Math.abs(velocity.vy)
    
    // Minimum movement threshold
    if (absX < finalConfig.threshold! && absY < finalConfig.threshold!) {
      return null
    }
    
    // Velocity-based direction detection for fast swipes
    if (absVx > finalConfig.velocityThreshold! || absVy > finalConfig.velocityThreshold!) {
      if (absVx > absVy) {
        return velocity.vx > 0 ? 'right' : 'left'
      } else {
        return velocity.vy > 0 ? 'down' : 'up'
      }
    }
    
    // Distance-based direction for slower swipes
    if (absX > absY) {
      return deltaX > 0 ? 'right' : 'left'
    } else {
      return deltaY > 0 ? 'down' : 'up'
    }
  }, [finalConfig.threshold, finalConfig.velocityThreshold])

  // Touch start handler
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    if (disabled) return
    
    const touch = e.touches[0]
    const point: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    }
    
    startPoint.current = point
    touchPoints.current = [point]
    setIsActive(true)
    
    // Light haptic feedback on touch start
    triggerHaptic('light')
    
    callbacks.onSwipeStart?.('unknown')
  }, [disabled, triggerHaptic, callbacks])

  // Touch move handler with momentum
  const handleTouchMove = useCallback((e: React.TouchEvent) => {
    if (disabled || !startPoint.current) return
    
    const touch = e.touches[0]
    const currentPoint: TouchPoint = {
      x: touch.clientX,
      y: touch.clientY,
      timestamp: Date.now()
    }
    
    // Add to touch points for velocity calculation
    touchPoints.current.push(currentPoint)
    if (touchPoints.current.length > 5) {
      touchPoints.current.shift()
    }
    
    const deltaX = currentPoint.x - startPoint.current.x
    const deltaY = currentPoint.y - startPoint.current.y
    
    // Apply resistance for natural feel
    const resistedX = deltaX * finalConfig.resistanceMultiplier!
    const resistedY = deltaY * finalConfig.resistanceMultiplier!
    
    // Update motion values
    x.set(resistedX)
    y.set(resistedY)
    
    // Determine direction for visual feedback
    const velocity = calculateVelocity(currentPoint)
    const direction = getSwipeDirection(deltaX, deltaY, velocity)
    
    if (direction !== swipeDirection) {
      setSwipeDirection(direction)
      if (direction) {
        triggerHaptic('light')
      }
    }
    
    // Prevent default scrolling for controlled directions
    if (direction && finalConfig.directions!.includes(direction as any)) {
      e.preventDefault()
    }
  }, [disabled, x, y, swipeDirection, finalConfig, calculateVelocity, getSwipeDirection, triggerHaptic])

  // Touch end handler with momentum and callbacks
  const handleTouchEnd = useCallback((e: React.TouchEvent) => {
    if (disabled || !startPoint.current) return
    
    const endPoint = touchPoints.current[touchPoints.current.length - 1]
    if (!endPoint) return
    
    const deltaX = endPoint.x - startPoint.current.x
    const deltaY = endPoint.y - startPoint.current.y
    const velocity = calculateVelocity(endPoint)
    const direction = getSwipeDirection(deltaX, deltaY, velocity)
    
    // Execute swipe callback if threshold met
    if (direction && finalConfig.directions!.includes(direction as any)) {
      triggerHaptic('medium')
      
      switch (direction) {
        case 'left':
          callbacks.onSwipeLeft?.()
          break
        case 'right':
          callbacks.onSwipeRight?.()
          break
        case 'up':
          callbacks.onSwipeUp?.()
          break
        case 'down':
          callbacks.onSwipeDown?.()
          break
      }
    }
    
    // Momentum animation if enabled
    if (finalConfig.enableMomentum && (Math.abs(velocity.vx) > 0.1 || Math.abs(velocity.vy) > 0.1)) {
      const momentumX = velocity.vx * 100
      const momentumY = velocity.vy * 100
      
      animate(x, momentumX, {
        duration: 0.6,
        ease: 'easeOut',
        onComplete: () => {
          animate(x, 0, { duration: finalConfig.snapBackDuration })
        }
      })
      
      animate(y, momentumY, {
        duration: 0.6,
        ease: 'easeOut',
        onComplete: () => {
          animate(y, 0, { duration: finalConfig.snapBackDuration })
          callbacks.onMomentumEnd?.()
        }
      })
    } else {
      // Snap back to original position
      animate(x, 0, { duration: finalConfig.snapBackDuration })
      animate(y, 0, { duration: finalConfig.snapBackDuration })
    }
    
    // Reset state
    setIsActive(false)
    setSwipeDirection(null)
    startPoint.current = null
    touchPoints.current = []
    
    callbacks.onSwipeEnd?.()
  }, [disabled, x, y, finalConfig, calculateVelocity, getSwipeDirection, triggerHaptic, callbacks])

  // Prevent context menu on long press
  const handleContextMenu = useCallback((e: React.MouseEvent) => {
    if (isActive) {
      e.preventDefault()
    }
  }, [isActive])

  return (
    <motion.div
      ref={containerRef}
      className={`relative ${className}`}
      style={{
        x,
        y,
        opacity,
        userSelect: isActive ? 'none' : 'auto',
        WebkitUserSelect: isActive ? 'none' : 'auto'
      } as any}
      onTouchStart={handleTouchStart}
      onTouchMove={handleTouchMove}
      onTouchEnd={handleTouchEnd}
      onContextMenu={handleContextMenu}
      // Prevent drag on images
      onDragStart={(e) => e.preventDefault()}
    >
      {children}
      
      {/* Visual feedback for active swipe */}
      {isActive && swipeDirection && (
        <motion.div
          initial={{ opacity: 0, scale: 0.8 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.8 }}
          className="absolute inset-0 pointer-events-none flex items-center justify-center"
        >
          <div className="bg-lime-green/20 backdrop-blur-sm rounded-full p-4">
            <div className="text-lime-green text-2xl">
              {swipeDirection === 'left' && '←'}
              {swipeDirection === 'right' && '→'}
              {swipeDirection === 'up' && '↑'}
              {swipeDirection === 'down' && '↓'}
            </div>
          </div>
        </motion.div>
      )}
    </motion.div>
  )
}
