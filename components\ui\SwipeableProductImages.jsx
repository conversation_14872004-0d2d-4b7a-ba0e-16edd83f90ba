'use client'

import { useState, useRef, useEffect } from 'react'
import { motion, AnimatePresence, PanInfo } from 'framer-motion'
import Image from 'next/image'

export default function SwipeableProductImages({
  images = [],
  videos = [],
  productName = '',
  selectedImage = 0,
  onImageChange
}) {
  const [currentIndex, setCurrentIndex] = useState(selectedImage)
  const [isDragging, setIsDragging] = useState(false)
  const containerRef = useRef(null)

  // Sync with parent component
  useEffect(() => {
    setCurrentIndex(selectedImage)
  }, [selectedImage])

  // Handle swipe gestures
  const handleDragEnd = (event, info) => {
    setIsDragging(false)

    const threshold = 30 // Reduced threshold for better mouse sensitivity
    const velocity = info.velocity.x
    const offset = info.offset.x

    // Determine swipe direction with improved sensitivity for mouse
    if (Math.abs(offset) > threshold || Math.abs(velocity) > 300) {
      if (offset > 0 || velocity > 300) {
        // Swipe right - previous image
        goToPrevious()
      } else if (offset < 0 || velocity < -300) {
        // Swipe left - next image
        goToNext()
      }
    }
  }

  // Combine videos FIRST, then images into a single media array
  const allMedia = [...videos, ...images]
  const totalMedia = allMedia.length

  const goToNext = () => {
    if (currentIndex < totalMedia - 1) {
      const newIndex = currentIndex + 1
      setCurrentIndex(newIndex)
      onImageChange?.(newIndex)

      // Haptic feedback
      if (navigator.vibrate) {
        navigator.vibrate(10)
      }
    }
  }

  const goToPrevious = () => {
    if (currentIndex > 0) {
      const newIndex = currentIndex - 1
      setCurrentIndex(newIndex)
      onImageChange?.(newIndex)

      // Haptic feedback
      if (navigator.vibrate) {
        navigator.vibrate(10)
      }
    }
  }

  const goToImage = (index) => {
    setCurrentIndex(index)
    onImageChange?.(index)
  }

  if (!allMedia || allMedia.length === 0) {
    return (
      <div className="relative aspect-square bg-gray-50 dark:bg-gray-800 rounded-xl overflow-hidden mb-3 flex items-center justify-center">
        <div className="text-gray-400">No media available</div>
      </div>
    )
  }

  // Check if current media is a video (videos come first now)
  const isCurrentVideo = currentIndex < videos.length

  return (
    <div className="space-y-3">
      {/* Main Image Container */}
      <div
        ref={containerRef}
        className="relative aspect-square bg-gray-50 dark:bg-gray-800 rounded-xl overflow-hidden"
      >
        {/* Persistent Drag Layer */}
        <motion.div
          className="absolute inset-0 cursor-grab active:cursor-grabbing z-10"
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          dragElastic={0.2}
          onDragStart={() => setIsDragging(true)}
          onDragEnd={handleDragEnd}
          whileDrag={{ scale: 0.95 }}
          dragMomentum={false}
        />

        {/* Content Layer with Animations */}
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            className="absolute inset-0"
            initial={{ opacity: 0, x: 50 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -50 }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          >
            {isCurrentVideo ? (
              <video
                src={typeof allMedia[currentIndex] === 'string' ? allMedia[currentIndex] : allMedia[currentIndex].src}
                className="w-full h-full object-cover"
                controls
                autoPlay
                muted
                loop
              />
            ) : (
              <Image
                src={allMedia[currentIndex]}
                alt={`${productName} - Media ${currentIndex + 1}`}
                fill
                className="object-cover"
                priority={currentIndex === 0}
                sizes="(max-width: 768px) 100vw, 50vw"
              />
            )}
          </motion.div>
        </AnimatePresence>

        {/* Swipe Hint for Mobile */}
        {totalMedia > 1 && (
          <div className="absolute top-3 right-3 bg-black/30 backdrop-blur-sm text-white px-2 py-1 rounded-md md:hidden opacity-80">
            <p className="text-xs">👈👉 Desliza</p>
          </div>
        )}

        {/* Mouse Drag Hint for Desktop */}
        {totalMedia > 1 && !isDragging && (
          <div className="absolute top-3 left-3 bg-black/30 backdrop-blur-sm text-white px-2 py-1 rounded-md hidden md:block opacity-80">
            <p className="text-xs">🖱️ Arrastra</p>
          </div>
        )}

        {/* Navigation Arrows for Desktop */}
        {totalMedia > 1 && (
          <>
            {currentIndex > 0 && (
              <button
                onClick={goToPrevious}
                className="hidden md:flex absolute left-3 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/90 dark:bg-black/90 backdrop-blur-sm rounded-full items-center justify-center shadow-lg hover:scale-110 transition-all duration-200 z-10"
                aria-label="Previous media"
              >
                <svg className="w-5 h-5 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
                </svg>
              </button>
            )}

            {currentIndex < totalMedia - 1 && (
              <button
                onClick={goToNext}
                className="hidden md:flex absolute right-3 top-1/2 transform -translate-y-1/2 w-10 h-10 bg-white/90 dark:bg-black/90 backdrop-blur-sm rounded-full items-center justify-center shadow-lg hover:scale-110 transition-all duration-200 z-10"
                aria-label="Next media"
              >
                <svg className="w-5 h-5 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                </svg>
              </button>
            )}
          </>
        )}

        {/* Media Counter */}
        {totalMedia > 1 && (
          <div className="absolute bottom-3 left-3 bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded-md text-xs">
            {currentIndex + 1} / {totalMedia}
          </div>
        )}
      </div>
    </div>
  )
}
