'use client'

import { createContext, useContext, useState, useEffect } from 'react'
import { usePathname, useRouter } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'

const RouteTransitionContext = createContext()

export function useRouteTransition() {
  const context = useContext(RouteTransitionContext)
  if (!context) {
    throw new Error('useRouteTransition must be used within RouteTransitionProvider')
  }
  return context
}

// Enhanced navigation with transition awareness
export function RouteTransitionProvider({ children }) {
  const pathname = usePathname()
  const router = useRouter()
  const [isTransitioning, setIsTransitioning] = useState(false)
  const [transitionDirection, setTransitionDirection] = useState('forward')
  const [previousPath, setPreviousPath] = useState(null)

  // Navigation history for determining direction
  const [navigationHistory, setNavigationHistory] = useState([])

  useEffect(() => {
    // Update navigation history
    setNavigationHistory(prev => {
      const newHistory = [...prev, pathname].slice(-5) // Keep last 5 routes
      return newHistory
    })

    // Determine transition direction
    if (previousPath) {
      const isBackNavigation = navigationHistory.includes(pathname)
      setTransitionDirection(isBackNavigation ? 'backward' : 'forward')
    }

    setPreviousPath(pathname)
  }, [pathname, previousPath, navigationHistory])

  // Enhanced navigation function with transition control
  const navigateWithTransition = async (href, options = {}) => {
    const { 
      transition = 'auto',
      direction = 'forward',
      delay = 0 
    } = options

    setIsTransitioning(true)
    setTransitionDirection(direction)

    // Optional delay for smoother UX
    if (delay > 0) {
      await new Promise(resolve => setTimeout(resolve, delay))
    }

    router.push(href)

    // Reset transition state after navigation
    setTimeout(() => {
      setIsTransitioning(false)
    }, 100)
  }

  const value = {
    isTransitioning,
    transitionDirection,
    previousPath,
    navigationHistory,
    navigateWithTransition,
    setIsTransitioning
  }

  return (
    <RouteTransitionContext.Provider value={value}>
      {children}
    </RouteTransitionContext.Provider>
  )
}

// Enhanced Link component with transition support
export function TransitionLink({
  href,
  children,
  transition = 'auto',
  direction = 'forward',
  delay = 0,
  className = '',
  onClick,
  ...props
}) {
  const { navigateWithTransition } = useRouteTransition()

  const handleClick = (e) => {
    e.preventDefault()

    // Call any additional onClick handler passed as prop
    if (onClick) {
      onClick(e)
    }

    // Only navigate if the event wasn't prevented by the onClick handler
    if (!e.defaultPrevented) {
      navigateWithTransition(href, { transition, direction, delay })
    }
  }

  return (
    <a
      href={href}
      onClick={handleClick}
      className={className}
      role="link"
      tabIndex={0}
      {...props}
    >
      {children}
    </a>
  )
}

// Page transition wrapper with enhanced animations
export function PageTransitionWrapper({ children }) {
  const pathname = usePathname()
  const { transitionDirection, isTransitioning } = useRouteTransition()

  // Route-specific transition configurations
  const getTransitionConfig = (path, direction) => {
    const configs = {
      '/': {
        forward: {
          initial: { opacity: 0, scale: 0.98, filter: 'blur(4px)' },
          animate: {
            opacity: 1,
            scale: 1,
            filter: 'blur(0px)',
            transition: { duration: 0.6, ease: [0.25, 0.46, 0.45, 0.94] }
          },
          exit: {
            opacity: 0,
            scale: 1.02,
            filter: 'blur(4px)',
            transition: { duration: 0.3, ease: [0.55, 0.06, 0.68, 0.19] }
          }
        },
        backward: {
          initial: { opacity: 0, x: '-20%' },
          animate: { opacity: 1, x: 0, transition: { duration: 0.5 } },
          exit: { opacity: 0, x: '20%', transition: { duration: 0.3 } }
        }
      },
      '/shop': {
        forward: {
          initial: { x: '100%', opacity: 0 },
          animate: {
            x: 0,
            opacity: 1,
            transition: { duration: 0.5, ease: [0.25, 0.46, 0.45, 0.94] }
          },
          exit: {
            x: '-100%',
            opacity: 0,
            transition: { duration: 0.3, ease: [0.55, 0.06, 0.68, 0.19] }
          }
        },
        backward: {
          initial: { x: '-100%', opacity: 0 },
          animate: { x: 0, opacity: 1, transition: { duration: 0.4 } },
          exit: { x: '100%', opacity: 0, transition: { duration: 0.3 } }
        }
      },
      '/forms-demo': {
        forward: {
          initial: { 
            opacity: 0, 
            scale: 0.9, 
            rotateX: 15,
            transformPerspective: 1000
          },
          animate: { 
            opacity: 1, 
            scale: 1, 
            rotateX: 0,
            transition: { duration: 0.8, ease: [0.25, 0.46, 0.45, 0.94] }
          },
          exit: { 
            opacity: 0, 
            scale: 1.1, 
            rotateX: -15,
            transition: { duration: 0.5, ease: [0.55, 0.06, 0.68, 0.19] }
          }
        },
        backward: {
          initial: { opacity: 0, rotateX: -15 },
          animate: { opacity: 1, rotateX: 0, transition: { duration: 0.6 } },
          exit: { opacity: 0, rotateX: 15, transition: { duration: 0.4 } }
        }
      }
    }

    // Default transition for unspecified routes - OPTIMIZED
    const defaultConfig = {
      forward: {
        initial: { opacity: 0, y: 20 },
        animate: {
          opacity: 1,
          y: 0,
          transition: { duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }
        },
        exit: {
          opacity: 0,
          y: -20,
          transition: { duration: 0.2, ease: [0.55, 0.06, 0.68, 0.19] }
        }
      },
      backward: {
        initial: { opacity: 0, y: -20 },
        animate: { opacity: 1, y: 0, transition: { duration: 0.4 } },
        exit: { opacity: 0, y: 20, transition: { duration: 0.2 } }
      }
    }

    // Check for product detail pages - OPTIMIZED FOR FAST NAVIGATION
    if (path.startsWith('/product/')) {
      return {
        forward: {
          initial: { opacity: 0, y: 20 },
          animate: {
            opacity: 1,
            y: 0,
            transition: { duration: 0.4, ease: [0.25, 0.46, 0.45, 0.94] }
          },
          exit: {
            opacity: 0,
            y: -10,
            transition: { duration: 0.2, ease: [0.55, 0.06, 0.68, 0.19] }
          }
        },
        backward: {
          initial: { opacity: 0, y: -20 },
          animate: { opacity: 1, y: 0, transition: { duration: 0.4 } },
          exit: { opacity: 0, y: 10, transition: { duration: 0.2 } }
        }
      }
    }

    return configs[path] || defaultConfig
  }

  const transitionConfig = getTransitionConfig(pathname, transitionDirection)
  const currentVariant = transitionConfig[transitionDirection] || transitionConfig.forward

  return (
    <AnimatePresence mode="wait" initial={false}>
      <motion.div
        key={pathname}
        initial="initial"
        animate="animate"
        exit="exit"
        variants={currentVariant}
        className="min-h-screen"
        style={{ transformStyle: 'preserve-3d' }}
      >
        {/* Loading overlay during transitions */}
        <AnimatePresence>
          {isTransitioning && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              className="fixed inset-0 z-40 pointer-events-none"
            >
              <div className="absolute inset-0 bg-gradient-to-br from-light-cloud-gray/20 via-transparent to-warm-camel/10 dark:from-deep-pine/20 dark:to-rich-gold/10" />
              
              {/* Subtle particle effect */}
              <div className="absolute inset-0 overflow-hidden">
                {[...Array(6)].map((_, i) => (
                  <motion.div
                    key={i}
                    className="absolute w-1 h-1 bg-rich-gold/40 rounded-full"
                    style={{
                      left: `${20 + i * 15}%`,
                      top: `${30 + Math.sin(i) * 20}%`,
                    }}
                    animate={{
                      scale: [0, 1, 0],
                      opacity: [0, 0.8, 0],
                      x: [0, (Math.random() - 0.5) * 50],
                      y: [0, (Math.random() - 0.5) * 50],
                    }}
                    transition={{
                      duration: 1.5,
                      repeat: Infinity,
                      delay: i * 0.2,
                      ease: "easeInOut"
                    }}
                  />
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {children}
      </motion.div>
    </AnimatePresence>
  )
}
