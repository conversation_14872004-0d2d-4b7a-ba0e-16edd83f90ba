/**
 * TWL Enterprise Product Loader
 * Main interface for loading products with caching, scanning, and performance optimization
 * 
 * Features:
 * - Unified product loading interface
 * - Multi-layer caching integration
 * - Automatic scanning and indexing
 * - Performance monitoring
 * - Error handling and recovery
 * - Batch operations
 */

import { TWLProduct, TWLProductSearchResult, TWLProductFilters, TWLBulkOperationResult } from '../models/Product'
import { ProductScanner } from './ProductScanner'
import { ProductCache } from './ProductCache'
import { Logger } from '../utils/Logger'

/**
 * Loader Configuration
 */
export interface LoaderConfig {
  // Scanner Configuration
  productsBasePath: string
  enableAutoScan: boolean
  scanInterval: number        // Auto-scan interval in seconds
  
  // Cache Configuration
  enableCache: boolean
  cacheConfig: any           // ProductCache config
  
  // Performance
  enableMetrics: boolean
  enableProfiling: boolean
  maxConcurrentLoads: number
  
  // Error Handling
  enableRetry: boolean
  maxRetries: number
  retryDelay: number
  
  // Features
  enableSearch: boolean
  enableFiltering: boolean
  enableBulkOperations: boolean
}

/**
 * Load Result
 */
export interface LoadResult<T> {
  success: boolean
  data?: T
  error?: string
  duration: number
  fromCache: boolean
  cacheLayer?: 'memory' | 'file' | 'redis'
  metadata?: {
    scanned: boolean
    version: number
    checksum: string
  }
}

/**
 * System Status
 */
export interface SystemStatus {
  isReady: boolean
  lastScan: Date | null
  productsLoaded: number
  cacheStatus: {
    enabled: boolean
    hitRate: number
    memoryUsage: number
    fileUsage: number
  }
  performance: {
    averageLoadTime: number
    totalRequests: number
    errorRate: number
  }
  errors: string[]
}

/**
 * Enterprise Product Loader
 */
export class ProductLoader {
  private config: LoaderConfig
  private logger: Logger
  private scanner: ProductScanner
  private cache: ProductCache
  private productIndex: Map<string, TWLProduct>
  private isInitialized: boolean
  private scanTimer?: NodeJS.Timeout
  private loadMetrics: Map<string, number>

  constructor(config: Partial<LoaderConfig> = {}) {
    this.config = {
      // Scanner defaults
      productsBasePath: process.env.TWL_PRODUCTS_PATH || 'public/products',
      enableAutoScan: true,
      scanInterval: 3600, // 1 hour
      
      // Cache defaults
      enableCache: true,
      cacheConfig: {},
      
      // Performance defaults
      enableMetrics: true,
      enableProfiling: false,
      maxConcurrentLoads: 10,
      
      // Error handling defaults
      enableRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      
      // Feature defaults
      enableSearch: true,
      enableFiltering: true,
      enableBulkOperations: true,
      
      ...config
    }

    this.logger = new Logger('ProductLoader')
    this.scanner = new ProductScanner({
      productsBasePath: this.config.productsBasePath,
      enableMetrics: this.config.enableMetrics
    })
    this.cache = new ProductCache(this.config.cacheConfig)
    this.productIndex = new Map()
    this.isInitialized = false
    this.loadMetrics = new Map()

    this.logger.info('🚀 Enterprise Product Loader initialized', {
      basePath: this.config.productsBasePath,
      cacheEnabled: this.config.enableCache,
      autoScanEnabled: this.config.enableAutoScan
    })
  }

  /**
   * Initialize the product loader
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('🔄 Initializing product loader...')

      // Perform initial scan
      await this.performFullScan()

      // Start auto-scan if enabled
      if (this.config.enableAutoScan) {
        this.startAutoScan()
      }

      this.isInitialized = true
      this.logger.info('✅ Product loader initialized successfully')

    } catch (error) {
      this.logger.error('❌ Failed to initialize product loader:', error)
      throw error
    }
  }

  /**
   * Load a single product by ID
   */
  async loadProduct(productId: string): Promise<LoadResult<TWLProduct>> {
    const startTime = Date.now()
    
    try {
      this.logger.debug(`Loading product: ${productId}`)

      // Check cache first
      if (this.config.enableCache) {
        const cachedProduct = await this.cache.get(productId)
        if (cachedProduct) {
          return {
            success: true,
            data: cachedProduct,
            duration: Date.now() - startTime,
            fromCache: true,
            cacheLayer: 'memory' // Would need to track which layer
          }
        }
      }

      // Check in-memory index
      const indexedProduct = this.productIndex.get(productId)
      if (indexedProduct) {
        // Cache the result
        if (this.config.enableCache) {
          await this.cache.set(productId, indexedProduct)
        }

        return {
          success: true,
          data: indexedProduct,
          duration: Date.now() - startTime,
          fromCache: false
        }
      }

      // Product not found
      return {
        success: false,
        error: `Product not found: ${productId}`,
        duration: Date.now() - startTime,
        fromCache: false
      }

    } catch (error) {
      this.logger.error(`Failed to load product ${productId}:`, error)
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
        fromCache: false
      }
    }
  }

  /**
   * Load multiple products by IDs
   */
  async loadProducts(productIds: string[]): Promise<LoadResult<TWLProduct[]>> {
    const startTime = Date.now()
    
    try {
      this.logger.debug(`Loading ${productIds.length} products`)

      const products: TWLProduct[] = []
      const notFound: string[] = []

      // Load each product
      for (const productId of productIds) {
        const result = await this.loadProduct(productId)
        if (result.success && result.data) {
          products.push(result.data)
        } else {
          notFound.push(productId)
        }
      }

      return {
        success: notFound.length === 0,
        data: products,
        duration: Date.now() - startTime,
        fromCache: false,
        error: notFound.length > 0 ? `Products not found: ${notFound.join(', ')}` : undefined
      }

    } catch (error) {
      this.logger.error('Failed to load products:', error)
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
        fromCache: false
      }
    }
  }

  /**
   * Get all products
   */
  async getAllProducts(): Promise<LoadResult<TWLProduct[]>> {
    const startTime = Date.now()
    
    try {
      const products = Array.from(this.productIndex.values())
      
      return {
        success: true,
        data: products,
        duration: Date.now() - startTime,
        fromCache: false
      }

    } catch (error) {
      this.logger.error('Failed to get all products:', error)
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
        fromCache: false
      }
    }
  }

  /**
   * Search products with filters
   */
  async searchProducts(
    query?: string,
    filters?: TWLProductFilters,
    page: number = 1,
    pageSize: number = 20
  ): Promise<LoadResult<TWLProductSearchResult>> {
    const startTime = Date.now()
    
    try {
      if (!this.config.enableSearch) {
        throw new Error('Search is disabled')
      }

      this.logger.debug(`Searching products: query="${query}", page=${page}, pageSize=${pageSize}`)

      let products = Array.from(this.productIndex.values())

      // Apply text search
      if (query) {
        const searchTerm = query.toLowerCase()
        products = products.filter(product => 
          product.name.toLowerCase().includes(searchTerm) ||
          product.brand.name.toLowerCase().includes(searchTerm) ||
          product.details.description.toLowerCase().includes(searchTerm) ||
          product.details.tags.some(tag => tag.toLowerCase().includes(searchTerm))
        )
      }

      // Apply filters
      if (filters) {
        products = this.applyFilters(products, filters)
      }

      // Apply sorting
      if (filters?.sortBy) {
        products = this.applySorting(products, filters.sortBy, filters.sortOrder || 'asc')
      }

      // Calculate pagination
      const total = products.length
      const startIndex = (page - 1) * pageSize
      const endIndex = startIndex + pageSize
      const paginatedProducts = products.slice(startIndex, endIndex)

      const result: TWLProductSearchResult = {
        products: paginatedProducts,
        total,
        page,
        pageSize,
        hasMore: endIndex < total,
        facets: this.generateFacets(products),
        searchTime: Date.now() - startTime
      }

      return {
        success: true,
        data: result,
        duration: Date.now() - startTime,
        fromCache: false
      }

    } catch (error) {
      this.logger.error('Failed to search products:', error)
      return {
        success: false,
        error: error.message,
        duration: Date.now() - startTime,
        fromCache: false
      }
    }
  }

  /**
   * Perform full product scan
   */
  async performFullScan(): Promise<void> {
    try {
      this.logger.info('🔍 Starting full product scan...')

      const { products, result } = await this.scanner.scanAllProducts()

      // Update product index
      this.productIndex.clear()
      for (const product of products) {
        this.productIndex.set(product.id, product)
      }

      // Clear cache to ensure fresh data
      if (this.config.enableCache) {
        await this.cache.clear()
      }

      this.logger.info('✅ Full product scan completed', {
        productsFound: result.productsFound,
        imagesFound: result.imagesFound,
        videosFound: result.videosFound,
        duration: `${result.duration}ms`,
        errors: result.errors.length
      })

    } catch (error) {
      this.logger.error('❌ Full product scan failed:', error)
      throw error
    }
  }

  /**
   * Get system status
   */
  getSystemStatus(): SystemStatus {
    const cacheMetrics = this.config.enableCache ? this.cache.getMetrics() : null

    return {
      isReady: this.isInitialized,
      lastScan: new Date(), // Would track actual last scan time
      productsLoaded: this.productIndex.size,
      cacheStatus: {
        enabled: this.config.enableCache,
        hitRate: cacheMetrics?.overallHitRate || 0,
        memoryUsage: cacheMetrics?.memoryUsage || 0,
        fileUsage: cacheMetrics?.fileUsage || 0
      },
      performance: {
        averageLoadTime: cacheMetrics?.averageResponseTime || 0,
        totalRequests: cacheMetrics?.totalRequests || 0,
        errorRate: 0 // Would calculate from metrics
      },
      errors: []
    }
  }

  /**
   * Shutdown the loader
   */
  async shutdown(): Promise<void> {
    if (this.scanTimer) {
      clearInterval(this.scanTimer)
    }

    if (this.config.enableCache) {
      await this.cache.shutdown()
    }

    this.logger.info('Product loader shutdown')
  }

  // Private helper methods
  private startAutoScan(): void {
    this.scanTimer = setInterval(() => {
      this.performFullScan().catch(error => {
        this.logger.error('Auto-scan failed:', error)
      })
    }, this.config.scanInterval * 1000)

    this.logger.info(`Auto-scan started (interval: ${this.config.scanInterval}s)`)
  }

  private applyFilters(products: TWLProduct[], filters: TWLProductFilters): TWLProduct[] {
    let filtered = products

    if (filters.brands?.length) {
      filtered = filtered.filter(p => filters.brands!.includes(p.brand.id))
    }

    if (filters.categories?.length) {
      filtered = filtered.filter(p => filters.categories!.includes(p.category.id))
    }

    if (filters.priceMin !== undefined) {
      filtered = filtered.filter(p => p.pricing.currentPrice >= filters.priceMin!)
    }

    if (filters.priceMax !== undefined) {
      filtered = filtered.filter(p => p.pricing.currentPrice <= filters.priceMax!)
    }

    if (filters.inStockOnly) {
      filtered = filtered.filter(p => p.inventory.inStock)
    }

    if (filters.isLimitedEdition !== undefined) {
      filtered = filtered.filter(p => p.details.isLimitedEdition === filters.isLimitedEdition)
    }

    if (filters.rating !== undefined) {
      filtered = filtered.filter(p => p.metadata.rating >= filters.rating!)
    }

    return filtered
  }

  private applySorting(products: TWLProduct[], sortBy: string, sortOrder: 'asc' | 'desc'): TWLProduct[] {
    const sorted = [...products]

    sorted.sort((a, b) => {
      let comparison = 0

      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name)
          break
        case 'price':
          comparison = a.pricing.currentPrice - b.pricing.currentPrice
          break
        case 'rating':
          comparison = a.metadata.rating - b.metadata.rating
          break
        case 'popularity':
          comparison = a.metadata.viewCount - b.metadata.viewCount
          break
        case 'newest':
          comparison = a.system.createdAt.getTime() - b.system.createdAt.getTime()
          break
        default:
          comparison = 0
      }

      return sortOrder === 'desc' ? -comparison : comparison
    })

    return sorted
  }

  private generateFacets(products: TWLProduct[]): any {
    // Generate search facets from products
    const brands = new Map<string, number>()
    const categories = new Map<string, number>()
    const priceRanges = new Map<string, number>()

    for (const product of products) {
      // Brand facets
      const brandCount = brands.get(product.brand.name) || 0
      brands.set(product.brand.name, brandCount + 1)

      // Category facets
      const categoryCount = categories.get(product.category.name) || 0
      categories.set(product.category.name, categoryCount + 1)

      // Price range facets
      const price = product.pricing.currentPrice
      let priceRange = ''
      if (price < 100) priceRange = '0-100'
      else if (price < 200) priceRange = '100-200'
      else if (price < 300) priceRange = '200-300'
      else priceRange = '300+'

      const priceCount = priceRanges.get(priceRange) || 0
      priceRanges.set(priceRange, priceCount + 1)
    }

    return {
      brands: Array.from(brands.entries()).map(([name, count]) => ({ name, count })),
      categories: Array.from(categories.entries()).map(([name, count]) => ({ name, count })),
      priceRanges: Array.from(priceRanges.entries()).map(([range, count]) => ({ range, count })),
      sizes: [], // Would implement size facets
      colors: [], // Would implement color facets
      inStock: {
        available: products.filter(p => p.inventory.inStock).length,
        outOfStock: products.filter(p => !p.inventory.inStock).length
      }
    }
  }
}

export default ProductLoader
