#!/usr/bin/env node

const fs = require('fs')
const path = require('path')

// Get page name from command line arguments
const pageName = process.argv[2]

if (!pageName) {
  console.error('❌ Please provide a page name')
  console.log('Usage: npm run generate:page page-name')
  process.exit(1)
}

// Validate page name (kebab-case)
if (!/^[a-z][a-z0-9-]*$/.test(pageName)) {
  console.error('❌ Page name must be in kebab-case (e.g., my-page)')
  process.exit(1)
}

// Convert kebab-case to PascalCase for component name
const componentName = pageName
  .split('-')
  .map(word => word.charAt(0).toUpperCase() + word.slice(1))
  .join('')

// Page template
const pageTemplate = `'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

export default function ${componentName}Page() {
  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            ${componentName}
          </h1>
          <p className="text-warm-camel text-lg max-w-3xl mx-auto">
            Welcome to the ${componentName} page. Add your content here.
          </p>
        </motion.div>

        {/* Main Content */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-16"
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                Main Content
              </h2>
              <p className="text-warm-camel mb-6">
                This is the main content area for the ${componentName} page. 
                Customize this section with your specific content.
              </p>
              
              <div className="flex flex-col sm:flex-row gap-4">
                <AnimatedButton
                  variant="primary"
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                    </svg>
                  }
                  iconPosition="right"
                >
                  Primary Action
                </AnimatedButton>
                
                <TransitionLink href="/">
                  <AnimatedButton
                    variant="secondary"
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                    }
                  >
                    Back to Home
                  </AnimatedButton>
                </TransitionLink>
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Additional Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8"
        >
          {[1, 2, 3].map((item, index) => (
            <motion.div
              key={item}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 + index * 0.1 }}
            >
              <Card variant="default" className="group hover:shadow-lg transition-all duration-300">
                <CardContent className="p-6">
                  <div className="text-4xl mb-4">🎯</div>
                  <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    Feature {item}
                  </h3>
                  <p className="text-warm-camel text-sm">
                    Description for feature {item}. Customize this content based on your needs.
                  </p>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      </div>
    </div>
  )
}
`

// Test template
const testTemplate = `import { render, screen } from '@testing-library/react'
import ${componentName}Page from './page'

// Mock framer-motion
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }) => <div {...props}>{children}</div>,
  },
}))

// Mock components
jest.mock('@/components/ui/Card', () => ({
  Card: ({ children, ...props }) => <div {...props}>{children}</div>,
  CardContent: ({ children, ...props }) => <div {...props}>{children}</div>,
}))

jest.mock('@/components/ui/AnimatedButton', () => {
  return function AnimatedButton({ children, ...props }) {
    return <button {...props}>{children}</button>
  }
})

jest.mock('@/components/transitions/RouteTransitionProvider', () => ({
  TransitionLink: ({ children, href, ...props }) => <a href={href} {...props}>{children}</a>,
}))

describe('${componentName}Page', () => {
  it('renders without crashing', () => {
    render(<${componentName}Page />)
    expect(screen.getByText('${componentName}')).toBeInTheDocument()
  })

  it('displays the page title', () => {
    render(<${componentName}Page />)
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent('${componentName}')
  })

  it('displays main content', () => {
    render(<${componentName}Page />)
    expect(screen.getByText('Main Content')).toBeInTheDocument()
  })

  it('displays feature cards', () => {
    render(<${componentName}Page />)
    expect(screen.getByText('Feature 1')).toBeInTheDocument()
    expect(screen.getByText('Feature 2')).toBeInTheDocument()
    expect(screen.getByText('Feature 3')).toBeInTheDocument()
  })

  it('has a back to home link', () => {
    render(<${componentName}Page />)
    const homeLink = screen.getByText('Back to Home').closest('a')
    expect(homeLink).toHaveAttribute('href', '/')
  })
})
`

// Create page directory and files
const pagePath = path.join('app', pageName)
const pageFilePath = path.join(pagePath, 'page.jsx')
const testFilePath = path.join(pagePath, 'page.test.jsx')

try {
  // Check if page already exists
  if (fs.existsSync(pagePath)) {
    console.error(`❌ Page ${pageName} already exists at ${pagePath}`)
    process.exit(1)
  }

  // Create page directory
  fs.mkdirSync(pagePath, { recursive: true })
  console.log(`📁 Created directory: ${pagePath}`)

  // Write page file
  fs.writeFileSync(pageFilePath, pageTemplate)
  console.log(`✅ Created page: ${pageFilePath}`)

  // Write test file
  fs.writeFileSync(testFilePath, testTemplate)
  console.log(`✅ Created test: ${testFilePath}`)

  console.log(`\n🎉 Page ${pageName} created successfully!`)
  console.log(`\n📝 Next steps:`)
  console.log(`1. Visit your page: http://localhost:3000/${pageName}`)
  console.log(`2. Add navigation link in Header.jsx if needed`)
  console.log(`3. Run tests: npm test ${pageName}`)
  console.log(`4. Customize the content in ${pageFilePath}`)

} catch (error) {
  console.error(`❌ Error creating page: ${error.message}`)
  process.exit(1)
}
