import { notFound } from 'next/navigation'
import SingleProductPage from '@/app/components/features/SingleProductPage'
import { sampleMultiVariantProduct, sampleSingleVariantProduct } from '@/lib/multi-variant-product-data'

// Mock product database - in real app this would come from your database
const products = {
  'nike-air-jordan-1-high-colaboraciones': sampleMultiVariantProduct,
  'nike-air-jordan-1-high-standard': sampleSingleVariantProduct
}

export async function generateStaticParams() {
  // In a real app, you'd fetch all product slugs from your database
  return Object.keys(products).map((slug) => ({
    slug: slug,
  }))
}

export async function generateMetadata({ params }) {
  const product = products[params.slug]
  
  if (!product) {
    return {
      title: 'Producto no encontrado - The White Laces',
    }
  }

  const currentVariant = product.variants[0]
  const description = currentVariant.description || product.description
  
  return {
    title: `${product.name} - The White Laces`,
    description: description,
    keywords: product.keywords.join(', '),
    openGraph: {
      title: product.name,
      description: description,
      images: [
        {
          url: currentVariant.images[0],
          width: 800,
          height: 800,
          alt: product.name,
        },
      ],
      type: 'product',
    },
    twitter: {
      card: 'summary_large_image',
      title: product.name,
      description: description,
      images: [currentVariant.images[0]],
    },
    other: {
      'product:price:amount': product.price,
      'product:price:currency': product.currency,
      'product:availability': product.availability,
      'product:brand': product.brand,
      'product:category': product.styleDisplay,
    }
  }
}

export default function ProductPage({ params }) {
  const product = products[params.slug]

  if (!product) {
    notFound()
  }

  // Add structured data for SEO
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": product.name,
    "description": product.description,
    "brand": {
      "@type": "Brand",
      "name": product.brand
    },
    "category": product.styleDisplay,
    "sku": product.baseSku,
    "image": product.images,
    "offers": {
      "@type": "Offer",
      "price": product.price,
      "priceCurrency": product.currency,
      "availability": `https://schema.org/${product.availability === 'in-stock' ? 'InStock' : 'OutOfStock'}`,
      "seller": {
        "@type": "Organization",
        "name": "The White Laces"
      }
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": product.rating,
      "reviewCount": product.reviews
    },
    "additionalProperty": [
      {
        "@type": "PropertyValue",
        "name": "Gender",
        "value": product.genderDisplay
      },
      {
        "@type": "PropertyValue",
        "name": "Model",
        "value": product.modelFamilyDisplay
      },
      {
        "@type": "PropertyValue",
        "name": "Type",
        "value": product.subType
      }
    ]
  }

  // Add variant-specific structured data for multi-variant products
  if (product.isMultiVariant) {
    structuredData.hasVariant = product.variants.map(variant => ({
      "@type": "ProductModel",
      "name": `${product.name} - ${variant.variantName}`,
      "sku": variant.fullSku,
      "image": variant.images,
      "offers": {
        "@type": "Offer",
        "price": variant.price || product.price,
        "priceCurrency": product.currency,
        "availability": `https://schema.org/${product.availability === 'in-stock' ? 'InStock' : 'OutOfStock'}`
      },
      "additionalProperty": [
        {
          "@type": "PropertyValue",
          "name": "Variant",
          "value": variant.variantName
        },
        {
          "@type": "PropertyValue",
          "name": "Colors",
          "value": variant.colors.join(', ')
        }
      ]
    }))
  }

  return (
    <>
      {/* Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      
      {/* Product Page Component */}
      <SingleProductPage product={product} />
    </>
  )
}

// Custom not found component (removed duplicate export)
