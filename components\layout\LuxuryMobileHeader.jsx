'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { useAuth } from '@/contexts/AuthContext'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
// import { useTheme } from '@/contexts/ThemeContext' // TODO: Implement theme context

export default function LuxuryMobileHeader() {
  const router = useRouter()
  const { user, isAuthenticated } = useAuth()
  const { getItemsCount } = useCart()
  const { getTotalItemsCount } = useWishlist()
  // const { theme, toggleTheme } = useTheme() // TODO: Implement theme context
  
  const [isScrolled, setIsScrolled] = useState(false)
  const [scrollDirection, setScrollDirection] = useState('up')
  const [lastScrollY, setLastScrollY] = useState(0)
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const [searchQuery, setSearchQuery] = useState('')

  // Luxury scroll behavior - hide/show header based on scroll direction
  useEffect(() => {
    const handleScroll = () => {
      const currentScrollY = window.scrollY
      const direction = currentScrollY > lastScrollY ? 'down' : 'up'
      
      setIsScrolled(currentScrollY > 20)
      setScrollDirection(direction)
      setLastScrollY(currentScrollY)
    }

    window.addEventListener('scroll', handleScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleScroll)
  }, [lastScrollY])

  // Haptic feedback for luxury interactions
  const hapticFeedback = (type = 'light') => {
    if (navigator.vibrate) {
      const patterns = {
        light: [5],
        medium: [10],
        heavy: [15]
      }
      navigator.vibrate(patterns[type])
    }
  }

  const handleSearch = (e) => {
    e.preventDefault()
    if (searchQuery.trim()) {
      hapticFeedback('medium')
      router.push(`/search?q=${encodeURIComponent(searchQuery)}`)
      setIsSearchOpen(false)
      setSearchQuery('')
    }
  }

  return (
    <>
      {/* Luxury Mobile Header - Always Sticky */}
      <motion.header
        className={`fixed top-0 left-0 right-0 z-50 lg:hidden transition-all duration-500 ease-out ${
          isScrolled 
            ? 'bg-white/95 dark:bg-black/95 backdrop-blur-xl border-b border-black/10 dark:border-white/10 shadow-lg' 
            : 'bg-transparent'
        }`}
        initial={{ y: 0 }}
        animate={{ 
          y: scrollDirection === 'down' && isScrolled && !isSearchOpen ? -80 : 0 
        }}
        transition={{ duration: 0.3, ease: 'easeInOut' }}
      >
        <div className="px-4 py-3">
          <div className="flex items-center justify-between">
            {/* Logo - Luxury Minimal */}
            <motion.button
              onClick={() => {
                hapticFeedback('light')
                router.push('/')
              }}
              className="flex items-center"
              whileTap={{ scale: 0.95 }}
            >
              <div className="w-10 h-10 bg-gradient-to-br from-lime-green to-lime-green/80 rounded-2xl flex items-center justify-center shadow-lg">
                <span className="text-black font-bold text-xl font-godber">W</span>
              </div>
            </motion.button>

            {/* Right Actions - Fixed padding to prevent cropping */}
            <div className="flex items-center space-x-2 pr-2">
              {/* Search Toggle */}
              <motion.button
                onClick={() => {
                  hapticFeedback('light')
                  setIsSearchOpen(!isSearchOpen)
                }}
                className="p-3 rounded-2xl bg-black/5 dark:bg-white/5 hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-5 h-5 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </motion.button>

              {/* Wishlist */}
              <motion.button
                onClick={() => {
                  hapticFeedback('light')
                  router.push('/wishlist')
                }}
                className="relative p-3 rounded-2xl bg-black/5 dark:bg-white/5 hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-5 h-5 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                </svg>
                {getTotalItemsCount() > 0 && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-lime-green rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-black">{getTotalItemsCount()}</span>
                  </div>
                )}
              </motion.button>

              {/* Cart */}
              <motion.button
                onClick={() => {
                  hapticFeedback('medium')
                  router.push('/cart')
                }}
                className="relative p-3 rounded-2xl bg-black/5 dark:bg-white/5 hover:bg-black/10 dark:hover:bg-white/10 transition-colors"
                whileTap={{ scale: 0.9 }}
              >
                <svg className="w-5 h-5 text-black dark:text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                </svg>
                {getItemsCount() > 0 && (
                  <div className="absolute -top-1 -right-1 w-5 h-5 bg-lime-green rounded-full flex items-center justify-center">
                    <span className="text-xs font-bold text-black">{getItemsCount()}</span>
                  </div>
                )}
              </motion.button>
            </div>
          </div>
        </div>

        {/* Luxury Search Bar */}
        <AnimatePresence>
          {isSearchOpen && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              transition={{ duration: 0.3, ease: 'easeInOut' }}
              className="px-4 pb-4"
            >
              <form onSubmit={handleSearch} className="relative">
                <input
                  type="text"
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  placeholder="Buscar productos de lujo..."
                  className="w-full px-6 py-4 pl-14 pr-16 bg-black/5 dark:bg-white/5 rounded-3xl border border-black/10 dark:border-white/10 text-black dark:text-white placeholder-black/50 dark:placeholder-white/50 focus:outline-none focus:ring-2 focus:ring-lime-green/50 focus:border-lime-green/50 transition-all"
                  autoFocus
                />
                <div className="absolute left-5 top-1/2 transform -translate-y-1/2">
                  <svg className="w-5 h-5 text-black/50 dark:text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                </div>
                {searchQuery && (
                  <motion.button
                    type="button"
                    onClick={() => setSearchQuery('')}
                    className="absolute right-5 top-1/2 transform -translate-y-1/2 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10"
                    whileTap={{ scale: 0.9 }}
                  >
                    <svg className="w-4 h-4 text-black/50 dark:text-white/50" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </motion.button>
                )}
              </form>
            </motion.div>
          )}
        </AnimatePresence>
      </motion.header>

      {/* Spacer for fixed header */}
      <div className="h-16 lg:hidden" />
    </>
  )
}
