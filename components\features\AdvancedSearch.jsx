'use client'

import { useState, useEffect, useCallback } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
// Removed heroicons dependency - using inline SVGs instead
import Button from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import SimpleProductCard from '@/components/ui/SimpleProductCard'
import AnimatedLoader from '@/components/ui/AnimatedLoader'

export default function AdvancedSearch() {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Search state
  const [query, setQuery] = useState(searchParams.get('q') || '')
  const [products, setProducts] = useState([])
  const [filters, setFilters] = useState({
    brands: searchParams.get('brands')?.split(',').filter(Boolean) || [],
    categories: searchParams.get('categories')?.split(',').filter(Boolean) || [],
    gender: searchParams.get('gender') || 'all',
    priceMin: searchParams.get('priceMin') || '',
    priceMax: searchParams.get('priceMax') || '',
    sortBy: searchParams.get('sortBy') || 'relevance'
  })
  
  // UI state
  const [isLoading, setIsLoading] = useState(false)
  const [showFilters, setShowFilters] = useState(false)
  const [availableFilters, setAvailableFilters] = useState({
    brands: [],
    categories: [],
    priceRange: { min: 0, max: 50000, step: 500 },
    genders: [],
    sortOptions: []
  })
  const [pagination, setPagination] = useState({
    total: 0,
    limit: 24,
    offset: 0,
    hasMore: false
  })

  // Debounced search function
  const performSearch = useCallback(async (searchQuery, searchFilters, offset = 0) => {
    setIsLoading(true)
    
    try {
      // Build search URL
      const params = new URLSearchParams()
      
      if (searchQuery.trim()) {
        params.set('q', searchQuery.trim())
      }
      
      Object.entries(searchFilters).forEach(([key, value]) => {
        if (Array.isArray(value) && value.length > 0) {
          params.set(key, value.join(','))
        } else if (value && value !== 'all' && value !== '') {
          params.set(key, value)
        }
      })
      
      params.set('limit', pagination.limit.toString())
      params.set('offset', offset.toString())
      
      const response = await fetch(`/api/search?${params.toString()}`)
      const data = await response.json()
      
      if (response.ok) {
        if (offset === 0) {
          setProducts(data.products)
        } else {
          setProducts(prev => [...prev, ...data.products])
        }
        
        setPagination(data.pagination)
        setAvailableFilters(data.filters.available)
        
        // Update URL
        const newParams = new URLSearchParams()
        if (searchQuery.trim()) newParams.set('q', searchQuery.trim())
        Object.entries(searchFilters).forEach(([key, value]) => {
          if (Array.isArray(value) && value.length > 0) {
            newParams.set(key, value.join(','))
          } else if (value && value !== 'all' && value !== '') {
            newParams.set(key, value)
          }
        })
        
        router.push(`/search?${newParams.toString()}`, { scroll: false })
      } else {
        console.error('Search error:', data.error)
      }
    } catch (error) {
      console.error('Search failed:', error)
    } finally {
      setIsLoading(false)
    }
  }, [router, pagination.limit])

  // Handle search input
  const handleSearch = (e) => {
    e.preventDefault()
    performSearch(query, filters, 0)
  }

  // Handle filter changes
  const handleFilterChange = (filterType, value) => {
    const newFilters = { ...filters }
    
    if (filterType === 'brands' || filterType === 'categories') {
      const currentValues = newFilters[filterType]
      if (currentValues.includes(value)) {
        newFilters[filterType] = currentValues.filter(v => v !== value)
      } else {
        newFilters[filterType] = [...currentValues, value]
      }
    } else {
      newFilters[filterType] = value
    }
    
    setFilters(newFilters)
    performSearch(query, newFilters, 0)
  }

  // Clear filters
  const clearFilters = () => {
    const clearedFilters = {
      brands: [],
      categories: [],
      gender: 'all',
      priceMin: '',
      priceMax: '',
      sortBy: 'relevance'
    }
    setFilters(clearedFilters)
    performSearch(query, clearedFilters, 0)
  }

  // Load more products
  const loadMore = () => {
    if (pagination.hasMore && !isLoading) {
      performSearch(query, filters, pagination.offset + pagination.limit)
    }
  }

  // Initial search on mount
  useEffect(() => {
    performSearch(query, filters, 0)
  }, []) // Only run on mount

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine">
      {/* Search Header */}
      <div className="bg-white dark:bg-fog-black border-b border-soft-steel-gray dark:border-mist-gray">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          {/* Search Bar */}
          <form onSubmit={handleSearch} className="flex gap-4 mb-4">
            <div className="flex-1 relative">
              <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-warm-camel" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
              <input
                type="text"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
                placeholder="Buscar productos, marcas, categorías..."
                className="w-full pl-10 pr-4 py-3 border border-soft-steel-gray dark:border-mist-gray rounded-lg bg-white dark:bg-fog-black text-forest-emerald dark:text-light-cloud-gray focus:ring-2 focus:ring-rich-gold focus:border-transparent"
              />
            </div>
            <Button type="submit" className="px-6">
              Buscar
            </Button>
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowFilters(!showFilters)}
              className="px-4"
            >
              <svg className="h-5 w-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
              </svg>
            </Button>
          </form>

          {/* Active Filters */}
          {(filters.brands.length > 0 || filters.categories.length > 0 || filters.gender !== 'all') && (
            <div className="flex flex-wrap gap-2 mb-4">
              <span className="text-sm text-warm-camel">Filtros activos:</span>
              
              {filters.brands.map(brand => (
                <span
                  key={brand}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-rich-gold text-forest-emerald text-sm rounded-full"
                >
                  {brand}
                  <button
                    onClick={() => handleFilterChange('brands', brand)}
                    className="hover:text-deep-pine"
                  >
                    <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}
              
              {filters.categories.map(category => (
                <span
                  key={category}
                  className="inline-flex items-center gap-1 px-3 py-1 bg-rich-gold text-forest-emerald text-sm rounded-full"
                >
                  {category}
                  <button
                    onClick={() => handleFilterChange('categories', category)}
                    className="hover:text-deep-pine"
                  >
                    <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              ))}

              {filters.gender !== 'all' && (
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-rich-gold text-forest-emerald text-sm rounded-full">
                  {filters.gender}
                  <button
                    onClick={() => handleFilterChange('gender', 'all')}
                    className="hover:text-deep-pine"
                  >
                    <svg className="h-3 w-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                    </svg>
                  </button>
                </span>
              )}
              
              <button
                onClick={clearFilters}
                className="text-sm text-warm-camel hover:text-forest-emerald underline"
              >
                Limpiar filtros
              </button>
            </div>
          )}
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex gap-8">
          {/* Filters Sidebar */}
          {showFilters && (
            <div className="w-80 flex-shrink-0">
              <Card>
                <CardContent className="p-6">
                  <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Filtros
                  </h3>

                  {/* Sort Options */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-warm-camel mb-2">
                      Ordenar por
                    </label>
                    <select
                      value={filters.sortBy}
                      onChange={(e) => handleFilterChange('sortBy', e.target.value)}
                      className="w-full p-2 border border-soft-steel-gray dark:border-mist-gray rounded-lg bg-white dark:bg-fog-black text-forest-emerald dark:text-light-cloud-gray"
                    >
                      {availableFilters.sortOptions.map(option => (
                        <option key={option.value} value={option.value}>
                          {option.label}
                        </option>
                      ))}
                    </select>
                  </div>

                  {/* Gender Filter */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-warm-camel mb-2">
                      Género
                    </label>
                    <div className="space-y-2">
                      {availableFilters.genders.map(gender => (
                        <label key={gender.value} className="flex items-center">
                          <input
                            type="radio"
                            name="gender"
                            value={gender.value}
                            checked={filters.gender === gender.value}
                            onChange={(e) => handleFilterChange('gender', e.target.value)}
                            className="mr-2"
                          />
                          <span className="text-sm text-forest-emerald dark:text-light-cloud-gray">
                            {gender.label}
                          </span>
                        </label>
                      ))}
                    </div>
                  </div>

                  {/* Price Range */}
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-warm-camel mb-2">
                      Rango de Precio (MXN)
                    </label>
                    <div className="flex gap-2">
                      <input
                        type="number"
                        placeholder="Mín"
                        value={filters.priceMin}
                        onChange={(e) => handleFilterChange('priceMin', e.target.value)}
                        className="w-full p-2 border border-soft-steel-gray dark:border-mist-gray rounded-lg bg-white dark:bg-fog-black text-forest-emerald dark:text-light-cloud-gray"
                      />
                      <input
                        type="number"
                        placeholder="Máx"
                        value={filters.priceMax}
                        onChange={(e) => handleFilterChange('priceMax', e.target.value)}
                        className="w-full p-2 border border-soft-steel-gray dark:border-mist-gray rounded-lg bg-white dark:bg-fog-black text-forest-emerald dark:text-light-cloud-gray"
                      />
                    </div>
                  </div>

                  {/* Brands */}
                  {availableFilters.brands.length > 0 && (
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-warm-camel mb-2">
                        Marcas
                      </label>
                      <div className="space-y-2 max-h-40 overflow-y-auto">
                        {availableFilters.brands.map(brand => (
                          <label key={brand.slug} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.brands.includes(brand.slug)}
                              onChange={() => handleFilterChange('brands', brand.slug)}
                              className="mr-2"
                            />
                            <span className="text-sm text-forest-emerald dark:text-light-cloud-gray">
                              {brand.name}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Categories */}
                  {availableFilters.categories.length > 0 && (
                    <div className="mb-6">
                      <label className="block text-sm font-medium text-warm-camel mb-2">
                        Categorías
                      </label>
                      <div className="space-y-2">
                        {availableFilters.categories.map(category => (
                          <label key={category.slug} className="flex items-center">
                            <input
                              type="checkbox"
                              checked={filters.categories.includes(category.slug)}
                              onChange={() => handleFilterChange('categories', category.slug)}
                              className="mr-2"
                            />
                            <span className="text-sm text-forest-emerald dark:text-light-cloud-gray">
                              {category.name}
                            </span>
                          </label>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          )}

          {/* Results */}
          <div className="flex-1">
            {/* Results Header */}
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-2xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray">
                {query ? `Resultados para "${query}"` : 'Todos los productos'}
              </h2>
              <p className="text-warm-camel">
                {products.length} productos encontrados
              </p>
            </div>

            {/* Loading State */}
            {isLoading && products.length === 0 && (
              <div className="flex justify-center py-12">
                <AnimatedLoader />
              </div>
            )}

            {/* Products Grid */}
            {products.length > 0 && (
              <>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
                  {products.map(product => (
                    <SimpleProductCard key={product.id} product={product} index={0} />
                  ))}
                </div>

                {/* Load More */}
                {pagination.hasMore && (
                  <div className="text-center">
                    <Button
                      onClick={loadMore}
                      disabled={isLoading}
                      variant="outline"
                      className="px-8"
                    >
                      {isLoading ? 'Cargando...' : 'Cargar más productos'}
                    </Button>
                  </div>
                )}
              </>
            )}

            {/* No Results */}
            {!isLoading && products.length === 0 && (
              <div className="text-center py-12">
                <p className="text-xl text-warm-camel mb-4">
                  No se encontraron productos
                </p>
                <p className="text-forest-emerald dark:text-light-cloud-gray mb-6">
                  Intenta ajustar tus filtros o buscar con términos diferentes
                </p>
                <Button onClick={clearFilters} variant="outline">
                  Limpiar filtros
                </Button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
