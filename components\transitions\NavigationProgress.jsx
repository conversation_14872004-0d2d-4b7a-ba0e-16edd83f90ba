'use client'

import { useState, useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouteTransition } from './RouteTransitionProvider'

export default function NavigationProgress() {
  const pathname = usePathname()
  const { isTransitioning } = useRouteTransition()
  const [progress, setProgress] = useState(0)
  const [isVisible, setIsVisible] = useState(false)

  useEffect(() => {
    if (isTransitioning) {
      setIsVisible(true)
      setProgress(0)

      // Simulate loading progress
      const progressInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 90) {
            clearInterval(progressInterval)
            return 90
          }
          return prev + Math.random() * 15
        })
      }, 100)

      // Complete progress when transition ends
      const completeTimer = setTimeout(() => {
        setProgress(100)
        setTimeout(() => {
          setIsVisible(false)
          setProgress(0)
        }, 300)
      }, 800)

      return () => {
        clearInterval(progressInterval)
        clearTimeout(completeTimer)
      }
    }
  }, [isTransitioning])

  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="fixed top-0 left-0 right-0 z-50 h-1"
        >
          {/* Background track */}
          <div className="absolute inset-0 bg-neutral-200 dark:bg-neutral-700" />

          {/* Progress bar */}
          <motion.div
            className="absolute left-0 top-0 h-full bg-primary"
            style={{ width: `${progress}%` }}
            initial={{ width: 0 }}
            animate={{ width: `${progress}%` }}
            transition={{ duration: 0.3, ease: "easeOut" }}
          />
          
          {/* Shimmer effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-transparent via-white/30 to-transparent"
            animate={{
              x: ['-100%', '100%']
            }}
            transition={{
              duration: 1.5,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
          
          {/* Glow effect */}
          <motion.div
            className="absolute inset-0 bg-gradient-to-r from-rich-gold/50 via-warm-camel/50 to-forest-emerald/50 blur-sm"
            style={{ width: `${progress}%` }}
            animate={{
              opacity: [0.5, 1, 0.5]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Breadcrumb navigation with transitions
export function AnimatedBreadcrumb({ items = [] }) {
  return (
    <nav className="flex items-center space-x-2 text-sm">
      <AnimatePresence mode="popLayout">
        {items.map((item, index) => (
          <motion.div
            key={item.href || index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ delay: index * 0.1 }}
            className="flex items-center"
          >
            {index > 0 && (
              <motion.span
                initial={{ opacity: 0, scale: 0 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1 + 0.05 }}
                className="mx-2 text-warm-camel"
              >
                /
              </motion.span>
            )}
            
            {item.href ? (
              <motion.a
                href={item.href}
                className="text-warm-camel hover:text-rich-gold transition-colors duration-200"
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {item.label}
              </motion.a>
            ) : (
              <span className="text-forest-emerald dark:text-light-cloud-gray font-medium">
                {item.label}
              </span>
            )}
          </motion.div>
        ))}
      </AnimatePresence>
    </nav>
  )
}

// Route change indicator
export function RouteChangeIndicator() {
  const pathname = usePathname()
  const [isChanging, setIsChanging] = useState(false)

  useEffect(() => {
    setIsChanging(true)
    const timer = setTimeout(() => setIsChanging(false), 1000)
    return () => clearTimeout(timer)
  }, [pathname])

  return (
    <AnimatePresence>
      {isChanging && (
        <motion.div
          initial={{ opacity: 0, scale: 0 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0 }}
          className="fixed bottom-6 right-6 z-50"
        >
          <div className="glass-card rounded-full p-3 shadow-lg">
            <motion.div
              className="w-6 h-6 border-2 border-rich-gold border-t-transparent rounded-full"
              animate={{ rotate: 360 }}
              transition={{
                duration: 1,
                repeat: Infinity,
                ease: "linear"
              }}
            />
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  )
}

// Page transition loading skeleton
export function TransitionSkeleton({ type = 'page' }) {
  const skeletonVariants = {
    page: (
      <div className="min-h-screen p-8 space-y-6">
        {/* Header skeleton */}
        <div className="space-y-3">
          <motion.div
            className="h-8 bg-warm-camel/20 rounded-lg w-1/3"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          />
          <motion.div
            className="h-4 bg-warm-camel/20 rounded w-2/3"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity, delay: 0.2 }}
          />
        </div>
        
        {/* Content skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              className="space-y-3"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: i * 0.1 }}
            >
              <div className="h-48 bg-warm-camel/20 rounded-lg" />
              <div className="h-4 bg-warm-camel/20 rounded w-3/4" />
              <div className="h-4 bg-warm-camel/20 rounded w-1/2" />
            </motion.div>
          ))}
        </div>
      </div>
    ),
    
    product: (
      <div className="min-h-screen p-8">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image skeleton */}
          <motion.div
            className="aspect-square bg-warm-camel/20 rounded-lg"
            animate={{ opacity: [0.5, 1, 0.5] }}
            transition={{ duration: 1.5, repeat: Infinity }}
          />
          
          {/* Details skeleton */}
          <div className="space-y-6">
            <div className="space-y-3">
              <div className="h-8 bg-warm-camel/20 rounded w-3/4" />
              <div className="h-6 bg-warm-camel/20 rounded w-1/2" />
              <div className="h-4 bg-warm-camel/20 rounded w-full" />
              <div className="h-4 bg-warm-camel/20 rounded w-2/3" />
            </div>
            
            <div className="space-y-3">
              <div className="h-12 bg-warm-camel/20 rounded" />
              <div className="h-12 bg-warm-camel/20 rounded" />
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="animate-pulse"
    >
      {skeletonVariants[type]}
    </motion.div>
  )
}
