-- 🔐 TWL ROW LEVEL SECURITY POLICIES
-- 🎯 Secure data access with proper user permissions

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE products ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_variants ENABLE ROW LEVEL SECURITY;
ALTER TABLE suppliers ENABLE ROW LEVEL SECURITY;
ALTER TABLE supplier_products ENABLE ROW LEVEL SECURITY;
ALTER TABLE carts ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlists ENABLE ROW LEVEL SECURITY;
ALTER TABLE wishlist_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;
ALTER TABLE ugc_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE search_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE product_views ENABLE ROW LEVEL SECURITY;
ALTER TABLE collections ENABLE ROW LEVEL SECURITY;
ALTER TABLE collection_products ENABLE ROW LEVEL SECURITY;

-- 👤 USERS POLICIES
-- Users can only access their own profile
CREATE POLICY "Users can view own profile" ON users
    FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Users can update own profile" ON users
    FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can insert own profile" ON users
    FOR INSERT WITH CHECK (auth.uid() = id);

-- 🏷️ BRANDS POLICIES
-- Brands are publicly readable
CREATE POLICY "Brands are publicly readable" ON brands
    FOR SELECT USING (true);

-- Only admins can modify brands
CREATE POLICY "Only admins can modify brands" ON brands
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 📂 CATEGORIES POLICIES
-- Categories are publicly readable
CREATE POLICY "Categories are publicly readable" ON categories
    FOR SELECT USING (active = true);

-- Only admins can modify categories
CREATE POLICY "Only admins can modify categories" ON categories
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 👟 PRODUCTS POLICIES
-- Products are publicly readable when active and in stock
CREATE POLICY "Products are publicly readable" ON products
    FOR SELECT USING (status = 'active');

-- Only admins can modify products
CREATE POLICY "Only admins can modify products" ON products
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 🎨 PRODUCT VARIANTS POLICIES
-- Product variants are publicly readable when active
CREATE POLICY "Product variants are publicly readable" ON product_variants
    FOR SELECT USING (active = true);

-- Only admins can modify product variants
CREATE POLICY "Only admins can modify product variants" ON product_variants
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 🏭 SUPPLIERS POLICIES
-- Only admins can access suppliers
CREATE POLICY "Only admins can access suppliers" ON suppliers
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 🔗 SUPPLIER PRODUCTS POLICIES
-- Only admins can access supplier products
CREATE POLICY "Only admins can access supplier products" ON supplier_products
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 🛒 CARTS POLICIES
-- Users can only access their own cart
CREATE POLICY "Users can access own cart" ON carts
    FOR ALL USING (auth.uid() = user_id);

-- 💝 WISHLISTS POLICIES
-- Users can access their own wishlists
CREATE POLICY "Users can access own wishlists" ON wishlists
    FOR ALL USING (auth.uid() = user_id);

-- Users can view public wishlists
CREATE POLICY "Public wishlists are readable" ON wishlists
    FOR SELECT USING (is_public = true);

-- 📝 WISHLIST ITEMS POLICIES
-- Users can access items in their own wishlists
CREATE POLICY "Users can access own wishlist items" ON wishlist_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM wishlists 
            WHERE wishlists.id = wishlist_items.wishlist_id 
            AND wishlists.user_id = auth.uid()
        )
    );

-- Users can view items in public wishlists
CREATE POLICY "Public wishlist items are readable" ON wishlist_items
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM wishlists 
            WHERE wishlists.id = wishlist_items.wishlist_id 
            AND wishlists.is_public = true
        )
    );

-- 📦 ORDERS POLICIES
-- Users can only access their own orders
CREATE POLICY "Users can access own orders" ON orders
    FOR ALL USING (auth.uid() = user_id);

-- Admins can access all orders
CREATE POLICY "Admins can access all orders" ON orders
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 📋 ORDER ITEMS POLICIES
-- Users can access items in their own orders
CREATE POLICY "Users can access own order items" ON order_items
    FOR ALL USING (
        EXISTS (
            SELECT 1 FROM orders 
            WHERE orders.id = order_items.order_id 
            AND orders.user_id = auth.uid()
        )
    );

-- Admins can access all order items
CREATE POLICY "Admins can access all order items" ON order_items
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- ⭐ REVIEWS POLICIES
-- Reviews are publicly readable
CREATE POLICY "Reviews are publicly readable" ON reviews
    FOR SELECT USING (true);

-- Users can create reviews for their own purchases
CREATE POLICY "Users can create own reviews" ON reviews
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can update their own reviews
CREATE POLICY "Users can update own reviews" ON reviews
    FOR UPDATE USING (auth.uid() = user_id);

-- Users can delete their own reviews
CREATE POLICY "Users can delete own reviews" ON reviews
    FOR DELETE USING (auth.uid() = user_id);

-- 📸 UGC POSTS POLICIES
-- Approved UGC posts are publicly readable
CREATE POLICY "Approved UGC posts are publicly readable" ON ugc_posts
    FOR SELECT USING (status = 'approved');

-- Users can create their own UGC posts
CREATE POLICY "Users can create own UGC posts" ON ugc_posts
    FOR INSERT WITH CHECK (auth.uid() = user_id);

-- Users can view and update their own UGC posts
CREATE POLICY "Users can access own UGC posts" ON ugc_posts
    FOR ALL USING (auth.uid() = user_id);

-- Admins can access all UGC posts for moderation
CREATE POLICY "Admins can moderate UGC posts" ON ugc_posts
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 🔍 SEARCH LOGS POLICIES
-- Users can only access their own search logs
CREATE POLICY "Users can access own search logs" ON search_logs
    FOR ALL USING (auth.uid() = user_id OR user_id IS NULL);

-- Admins can access all search logs for analytics
CREATE POLICY "Admins can access all search logs" ON search_logs
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 👁️ PRODUCT VIEWS POLICIES
-- Users can access their own product views
CREATE POLICY "Users can access own product views" ON product_views
    FOR ALL USING (auth.uid() = user_id OR user_id IS NULL);

-- Admins can access all product views for analytics
CREATE POLICY "Admins can access all product views" ON product_views
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 📚 COLLECTIONS POLICIES
-- Active collections are publicly readable
CREATE POLICY "Active collections are publicly readable" ON collections
    FOR SELECT USING (active = true);

-- Only admins can modify collections
CREATE POLICY "Only admins can modify collections" ON collections
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 🔗 COLLECTION PRODUCTS POLICIES
-- Collection products are publicly readable
CREATE POLICY "Collection products are publicly readable" ON collection_products
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM collections 
            WHERE collections.id = collection_products.collection_id 
            AND collections.active = true
        )
    );

-- Only admins can modify collection products
CREATE POLICY "Only admins can modify collection products" ON collection_products
    FOR ALL USING (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );

-- 🔧 HELPER FUNCTIONS FOR POLICIES

-- Function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin()
RETURNS BOOLEAN AS $$
BEGIN
    RETURN (
        auth.jwt() ->> 'role' = 'admin' OR
        auth.jwt() ->> 'user_metadata' ->> 'role' = 'admin'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user owns a wishlist
CREATE OR REPLACE FUNCTION owns_wishlist(wishlist_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM wishlists 
        WHERE id = wishlist_id 
        AND user_id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user owns an order
CREATE OR REPLACE FUNCTION owns_order(order_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
    RETURN EXISTS (
        SELECT 1 FROM orders 
        WHERE id = order_id 
        AND user_id = auth.uid()
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
