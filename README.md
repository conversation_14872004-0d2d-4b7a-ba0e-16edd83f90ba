# 👟 The White Laces (TWL) - E-commerce Platform

**Luxury Streetwear Meets Gen Z Culture | Mexico-First Strategy | Glassmorphic UI | Mobile-First | AI-Powered UX**

## 🎯 Overview

The White Laces is a luxury streetwear-focused e-commerce platform selling high-end shoes at discount prices. All products are brand new, no second-hand items. The site is mobile-first, features a glassmorphic design system, and is ready for Mexico market launch with plans to expand to Brazil, LATAM, and USA.

## 🛠️ Tech Stack

- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS with custom glassmorphism design system
- **Animations**: Framer Motion + Tailwind animations
- **State Management**: Zustand
- **Internationalization**: next-i18next (es-MX, en-US, pt-BR)
- **Authentication**: Firebase Auth
- **Payments**: Stripe + Mercado Pago
- **Hosting**: Vercel
- **Performance**: SWR for data fetching, Image optimization

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- npm, yarn, or pnpm
- Git

### Installation

1. Clone the repository:
```bash
git clone https://github.com/your-org/twl-ecommerce.git
cd twl-ecommerce
```

2. Install dependencies:
```bash
npm install
# or
yarn install
# or
pnpm install
```

3. Set up environment variables:
```bash
cp .env.example .env.local
```

4. Run the development server:
```bash
npm run dev
# or
yarn dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser.

## 📁 Project Structure

```
/twl-ecommerce
├── /app                    # Next.js App Router pages
├── /components
│   ├── /ui                 # Atomic components (Button, Card, etc.)
│   ├── /features           # Feature-based components
│   ├── /layout             # Layout components (Header, Footer)
│   └── /theme              # Theme provider and utilities
├── /lib                    # Utility functions and configurations
├── /public
│   ├── /locales           # Translation files
│   └── /images            # Static assets
├── /docs                  # Project documentation
├── next.config.js         # Next.js configuration
├── tailwind.config.js     # Tailwind CSS configuration
└── package.json
```

## 🎨 Design System

### Color Palette
- **Fog Black**: `#14161A` - Main background (dark mode)
- **Mist Gray**: `#1E2127` - Cards, overlays
- **Arctic White**: `#FAFAFA` - Light mode background
- **Neon Pulse**: `#FF1C53` - Primary CTA buttons, limited tags
- **Cyber Blue**: `#00F9FF` - Hover states, links
- **Gold Dust**: `#FFD166` - VIP badges, early access

### Typography
- **Headings**: Playfair Display
- **Body**: Inter
- **Monospace**: Fira Code

### Components
All components follow the glassmorphism design pattern with:
- Backdrop blur effects
- Subtle transparency
- Soft shadows
- Smooth animations

## 🌐 Internationalization

The platform supports multiple languages and regions:

- **Primary**: Mexican Spanish (es-MX)
- **Secondary**: English (en-US)
- **Future**: Brazilian Portuguese (pt-BR)

Currency support:
- MXN (Mexican Peso)
- USD (US Dollar)
- BRL (Brazilian Real)

## 🧩 Key Features

### Core E-commerce
- ✅ Product catalog with filtering
- ✅ Shopping cart and wishlist
- ✅ User authentication and profiles
- ✅ Multi-language support
- ✅ Responsive design

### Planned Features
- 🔄 Voice search integration
- 🔄 Visual search (image upload)
- 🔄 AI-powered recommendations
- 🔄 UGC wall and social sharing
- 🔄 Creator tools and referral system
- 🔄 PWA support

## 📱 Mobile-First Approach

The entire platform is designed mobile-first with:
- Touch-friendly interactions
- Optimized loading times
- Responsive glassmorphic components
- Bottom navigation for mobile
- Swipe gestures support

## 🚀 Deployment

### Vercel (Recommended)

1. Connect your GitHub repository to Vercel
2. Configure environment variables
3. Deploy automatically on push to main branch

### Manual Deployment

```bash
npm run build
npm run start
```

## 🧪 Development Scripts

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run format       # Format code with Prettier
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 License

This project is proprietary and confidential. All rights reserved.

## 🔗 Links

- [Design System Documentation](./docs/UXUI%20Design%20System.md)
- [Component Library Spec](./docs/Component%20library%20spec%20sheet.md)
- [Development Roadmap](./docs/Agile%20roadmap.md)
- [Performance Guide](./docs/Performance%20Optimization%20Guide.md)

## 📞 Support

For support and questions, please contact the development team or create an issue in the repository.

---

**Built with ❤️ for the luxury streetwear community**
