/**
 * CYTTE SUPPLIER DATABASE SCHEMA
 * Complete indexing system for 5480+ products
 * Based on: C:\2.MY_APP\TWL\V2\--materials\shoes\2. CYTTE
 */

// CYTTE Product Schema with comprehensive indexing
export const CYTTE_PRODUCT_SCHEMA = {
  // Core Product Information
  id: 'string',                    // Unique product identifier
  sku: 'string',                   // CYTTE SKU code (e.g., "JKD474-EJR", "33172898443003")
  name: 'string',                  // Product display name
  description: 'string',           // Product description
  
  // CYTTE Hierarchy (5-level structure)
  mainCategory: 'string',          // 1. SNEAKERS | 2. SANDALS | 3. FORMAL | 4. CASUAL | 5. KIDS
  brand: 'string',                 // 1. NIKE Limited Edition | 4. GUCCI | 5. DIOR | etc.
  collection: 'string',            // 2. AIR JORDAN | 1. MIXTE | ACE | etc.
  model: 'string',                 // 2. JORDAN 1 HIGH | Screener | B23 | etc.
  variant: 'string',               // Black & Black | Bodega | Union LA | etc.
  
  // Gender Classification
  gender: 'string',                // MIXTE | WOMEN | MEN
  genderDisplay: 'string',         // Unisex | Women | Men (for UI)
  
  // Brand Information
  brandId: 'string',               // nike | gucci | dior | balenciaga | etc.
  brandType: 'string',             // luxury | streetwear | athletic | designer
  
  // Product Classification
  type: 'string',                  // sneaker | sandal | formal | casual | kids
  style: 'string',                 // high-top | low-top | slide | loafer | boot
  
  // Collaboration & Special Editions
  isCollaboration: 'boolean',      // true for collabs (Off-White, Sacai, etc.)
  collaborator: 'string',          // Off-White | Sacai | Travis Scott | etc.
  isLimited: 'boolean',            // Limited edition status
  isExclusive: 'boolean',          // Exclusive release
  isVip: 'boolean',                // VIP access required
  
  // Pricing
  price: 'number',                 // Current price in MXN
  originalPrice: 'number',         // Original retail price
  currency: 'string',              // MXN | USD | EUR
  
  // Images & Media
  images: 'array',                 // Array of image URLs
  videos: 'array',                 // Array of video URLs
  
  // Product Details
  colors: 'array',                 // Available colorways
  sizes: 'array',                  // Available sizes
  materials: 'array',              // Leather, Canvas, Suede, etc.
  
  // Inventory
  stock: 'number',                 // Available quantity
  availability: 'string',          // in-stock | low-stock | out-of-stock | pre-order
  
  // Metadata
  releaseDate: 'string',           // Release date
  season: 'string',                // SS25 | FW24 | etc.
  year: 'number',                  // Release year
  
  // SEO & Search
  tags: 'array',                   // Search tags
  keywords: 'array',               // SEO keywords
  searchTerms: 'array',            // Additional search terms
  
  // Ratings & Reviews
  rating: 'number',                // Average rating (1-5)
  reviews: 'number',               // Number of reviews
  
  // File System Path
  imagePath: 'string',             // Path to product images folder
  
  // Created/Updated
  createdAt: 'string',
  updatedAt: 'string'
}

// Search Index Configuration
export const SEARCH_INDEXES = {
  // Primary Indexes
  primary: ['id', 'sku'],
  
  // Category Indexes
  category: [
    'mainCategory',
    'brand', 
    'collection',
    'model',
    'variant'
  ],
  
  // Gender & Type Indexes
  classification: [
    'gender',
    'type',
    'style',
    'brandType'
  ],
  
  // Collaboration Indexes
  collaboration: [
    'isCollaboration',
    'collaborator',
    'isLimited',
    'isExclusive',
    'isVip'
  ],
  
  // Search Indexes
  search: [
    'name',
    'description',
    'tags',
    'keywords',
    'searchTerms',
    'colors',
    'materials'
  ],
  
  // Price & Availability Indexes
  commerce: [
    'price',
    'availability',
    'stock',
    'releaseDate'
  ]
}

// CYTTE Category Mapping
export const CYTTE_CATEGORIES = {
  mainCategories: {
    'sneakers': '1. SNEAKERS',
    'sandals': '2. SANDALS', 
    'formal': '3. FORMAL',
    'casual': '4. CASUAL',
    'kids': '5. KIDS'
  },
  
  brands: {
    // Sneakers Brands
    'nike-limited': '1. NIKE Limited Edition',
    'adidas-limited': '2. ADIDAS Limited Edition',
    'hermes': '3. HERMES',
    'gucci': '4. GUCCI',
    'dior': '5. DIOR',
    'lv': '6. LV',
    'balenciaga': '7. BALENCIAGA',
    'chanel': '8. CHANEL',
    'louboutin': '9. LOUBOUTIN',
    'off-white': '10. OFF WHITE',
    'givenchy': '11. GIVENCHY',
    'maison-margiela': '12. Maison MARGIELA',
    'valentino': '13. VALENTINO',
    'prada': '14. PRADA',
    'miu-miu': '15. MIU MIU',
    'bottega-veneta': '16. BOTTEGA VENETA',
    'burberry': '17. BURBERRY',
    'golden-goose': '18. GOLDEN GOOSE',
    'gama-normal': '19. GAMA NORMAL',
    'common-project': 'Common Project',
    
    // Sandals Additional
    'nike-collabs': '1. NIKE Collabs',
    'ugg': '9. UGG',
    'crocs': '13. CROCS',
    'birkenstock': '15. BIRKENSTOCK'
  },
  
  collections: {
    // Nike Collections
    'air-force': '1. AIR FORCE',
    'air-jordan': '2. AIR JORDAN',
    'cortez': '3. CORTEZ',
    'dunk-low': '4. DUNK LOW',
    'air-max-1': '5. AIR MAX 1',
    'air-max-97': '6. AIR MAX 97',
    'fog': '7. FOG',
    'off-white': '8. OFF WHITE',
    'jacquemus': '9. JACQUEMUS',
    'blazer': '10. BLAZER',
    'vapor-waffle': '11. VAPOR WAFFLE',
    'lebron-james': '12. LEBRON JAMES'
  },
  
  models: {
    // Jordan Models
    'jordan-1-low': '1. JORDAN 1 LOW',
    'jordan-1-high': '2. JORDAN 1 HIGH'
  },
  
  genders: {
    'mixte': 'MIXTE',
    'women': 'WOMEN', 
    'men': 'MEN'
  }
}

// Search Query Builder
export const buildSearchQuery = (filters) => {
  const query = {
    bool: {
      must: [],
      filter: [],
      should: [],
      must_not: []
    }
  }
  
  // Text search
  if (filters.q) {
    query.bool.should.push(
      { match: { name: { query: filters.q, boost: 3 } } },
      { match: { description: { query: filters.q, boost: 2 } } },
      { match: { tags: { query: filters.q, boost: 2 } } },
      { match: { keywords: filters.q } },
      { match: { collaborator: { query: filters.q, boost: 2 } } },
      { match: { variant: filters.q } }
    )
    query.bool.minimum_should_match = 1
  }
  
  // Category filters
  if (filters.mainCategory) query.bool.filter.push({ term: { mainCategory: filters.mainCategory } })
  if (filters.brand) query.bool.filter.push({ term: { brand: filters.brand } })
  if (filters.collection) query.bool.filter.push({ term: { collection: filters.collection } })
  if (filters.gender) query.bool.filter.push({ term: { gender: filters.gender } })
  if (filters.type) query.bool.filter.push({ term: { type: filters.type } })
  
  // Special filters
  if (filters.isLimited) query.bool.filter.push({ term: { isLimited: true } })
  if (filters.isCollaboration) query.bool.filter.push({ term: { isCollaboration: true } })
  if (filters.isVip) query.bool.filter.push({ term: { isVip: true } })
  
  // Price range
  if (filters.minPrice || filters.maxPrice) {
    const priceRange = {}
    if (filters.minPrice) priceRange.gte = filters.minPrice
    if (filters.maxPrice) priceRange.lte = filters.maxPrice
    query.bool.filter.push({ range: { price: priceRange } })
  }
  
  // Availability
  if (filters.inStock) {
    query.bool.filter.push({ range: { stock: { gt: 0 } } })
  }
  
  return query
}

// Export for use in other modules
export default {
  CYTTE_PRODUCT_SCHEMA,
  SEARCH_INDEXES,
  CYTTE_CATEGORIES,
  buildSearchQuery
}
