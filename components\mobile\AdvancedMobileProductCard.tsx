'use client'

/**
 * 🎯 ADVANCED MOBILE PRODUCT CARD - CUTTING-EDGE INTERACTIONS
 * 
 * Features:
 * - Multi-directional swipe gestures for actions
 * - Advanced haptic feedback patterns
 * - Momentum-based image cycling
 * - Quick action reveals with swipe
 * - 3D touch simulation with pressure
 * - Micro-interactions and animations
 * - Performance-optimized rendering
 */

import { useState, useEffect, useCallback, useRef } from 'react'
import { motion, AnimatePresence, useMotionValue, useTransform, useSpring } from 'framer-motion'
import { useRouter } from 'next/navigation'
import { Heart, ShoppingBag, Eye, Star, Share2 } from 'lucide-react'
import Image from 'next/image'
import MobileSwipeEngine from './MobileSwipeEngine'

interface AdvancedMobileProductCardProps {
  product: any
  index?: number
  enableSwipeActions?: boolean
  enableQuickActions?: boolean
  enableImageCycling?: boolean
  priority?: boolean
}

export default function AdvancedMobileProductCard({
  product,
  index = 0,
  enableSwipeActions = true,
  enableQuickActions = true,
  enableImageCycling = true,
  priority = false
}: AdvancedMobileProductCardProps) {
  const router = useRouter()

  // State management
  const [isActive, setIsActive] = useState(false)
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [showQuickActions, setShowQuickActions] = useState(false)
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  const [swipeDirection, setSwipeDirection] = useState<string | null>(null)
  const [pressureLevel, setPressureLevel] = useState(0)

  // Motion values for advanced animations
  const x = useMotionValue(0)
  const y = useMotionValue(0)
  const scale = useSpring(1, { stiffness: 300, damping: 30 })
  const rotateX = useTransform(y, [-100, 100], [5, -5])
  const rotateY = useTransform(x, [-100, 100], [-5, 5])

  // Refs
  const cardRef = useRef<HTMLDivElement>(null)
  const touchStartTime = useRef<number>(0)
  const longPressTimer = useRef<NodeJS.Timeout>()

  // Product data
  const isWishlisted = false // Simplified for demo
  const hasDiscount = product.originalPrice && product.originalPrice > product.price
  const images = product.images || []
  const hasMultipleImages = images.length > 1

  // Haptic feedback patterns
  const hapticPatterns = {
    light: [10],
    medium: [20],
    heavy: [30, 10, 30],
    success: [10, 50, 10],
    error: [100, 50, 100],
    selection: [5, 10, 5]
  }

  const triggerHaptic = useCallback((pattern: keyof typeof hapticPatterns) => {
    try {
      if ('vibrate' in navigator) {
        navigator.vibrate(hapticPatterns[pattern])
      }
    } catch (error) {
      // Silently fail
    }
  }, [])

  // Image cycling with momentum
  const cycleImage = useCallback((direction: 'next' | 'prev') => {
    if (!hasMultipleImages) return

    setCurrentImageIndex(prev => {
      if (direction === 'next') {
        return (prev + 1) % images.length
      } else {
        return prev === 0 ? images.length - 1 : prev - 1
      }
    })
    
    triggerHaptic('selection')
  }, [hasMultipleImages, images.length, triggerHaptic])

  // Quick actions
  const handleAddToCart = useCallback(async (e?: React.MouseEvent) => {
    e?.stopPropagation()
    setIsAddingToCart(true)
    triggerHaptic('medium')

    try {
      await new Promise(resolve => setTimeout(resolve, 600))
      // Simplified for demo
      console.log('Added to cart:', product.id)
      triggerHaptic('success')
    } catch (error) {
      console.error('Error adding to cart:', error)
      triggerHaptic('error')
    } finally {
      setIsAddingToCart(false)
    }
  }, [product, triggerHaptic])

  const handleWishlistToggle = useCallback((e?: React.MouseEvent) => {
    e?.stopPropagation()

    // Simplified for demo
    console.log('Wishlist toggle:', product.id)
    triggerHaptic('medium')
  }, [product.id, triggerHaptic])

  const handleShare = useCallback(async (e?: React.MouseEvent) => {
    e?.stopPropagation()
    
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: `Check out this amazing ${product.name}`,
          url: window.location.origin + `/product/${product.id}`
        })
        triggerHaptic('success')
      } catch (error) {
        triggerHaptic('error')
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.origin + `/product/${product.id}`)
      triggerHaptic('medium')
    }
  }, [product, triggerHaptic])

  // Navigation
  const handleProductClick = useCallback(() => {
    triggerHaptic('medium')
    router.push(`/product/${product.id}`)
  }, [product.id, router, triggerHaptic])

  // Swipe gesture handlers
  const handleSwipeLeft = useCallback(() => {
    if (enableImageCycling && hasMultipleImages) {
      cycleImage('next')
    } else if (enableSwipeActions) {
      handleAddToCart()
    }
  }, [enableImageCycling, hasMultipleImages, enableSwipeActions, cycleImage, handleAddToCart])

  const handleSwipeRight = useCallback(() => {
    if (enableImageCycling && hasMultipleImages) {
      cycleImage('prev')
    } else if (enableSwipeActions) {
      handleWishlistToggle()
    }
  }, [enableImageCycling, hasMultipleImages, enableSwipeActions, cycleImage, handleWishlistToggle])

  const handleSwipeUp = useCallback(() => {
    if (enableQuickActions) {
      setShowQuickActions(true)
      triggerHaptic('medium')
    }
  }, [enableQuickActions, triggerHaptic])

  const handleSwipeDown = useCallback(() => {
    if (showQuickActions) {
      setShowQuickActions(false)
      triggerHaptic('light')
    }
  }, [showQuickActions, triggerHaptic])

  // Touch pressure simulation
  const handleTouchStart = useCallback((e: React.TouchEvent) => {
    touchStartTime.current = Date.now()
    setIsActive(true)
    scale.set(0.98)
    
    // Simulate pressure with touch force if available
    const touch = e.touches[0]
    if ('force' in touch && typeof touch.force === 'number') {
      setPressureLevel(touch.force)
    }
    
    triggerHaptic('light')
    
    // Long press detection
    longPressTimer.current = setTimeout(() => {
      if (enableQuickActions) {
        setShowQuickActions(true)
        triggerHaptic('heavy')
      }
    }, 500)
  }, [scale, triggerHaptic, enableQuickActions])

  const handleTouchEnd = useCallback(() => {
    const touchDuration = Date.now() - touchStartTime.current
    
    setIsActive(false)
    scale.set(1)
    setPressureLevel(0)
    
    if (longPressTimer.current) {
      clearTimeout(longPressTimer.current)
    }
    
    // Quick tap detection
    if (touchDuration < 200 && !showQuickActions) {
      handleProductClick()
    }
  }, [scale, showQuickActions, handleProductClick])

  // Cleanup
  useEffect(() => {
    return () => {
      if (longPressTimer.current) {
        clearTimeout(longPressTimer.current)
      }
    }
  }, [])

  return (
    <MobileSwipeEngine
      config={{
        threshold: 60,
        velocityThreshold: 0.4,
        enableHaptics: true,
        enableMomentum: true,
        directions: ['left', 'right', 'up', 'down']
      }}
      callbacks={{
        onSwipeLeft: handleSwipeLeft,
        onSwipeRight: handleSwipeRight,
        onSwipeUp: handleSwipeUp,
        onSwipeDown: handleSwipeDown
      }}
      className="w-full"
    >
      <motion.div
        ref={cardRef}
        className="relative bg-white dark:bg-gray-900 rounded-2xl overflow-hidden shadow-lg border border-gray-100 dark:border-gray-800"
        style={{ 
          scale,
          rotateX,
          rotateY,
          transformPerspective: 1000
        }}
        onTouchStart={handleTouchStart}
        onTouchEnd={handleTouchEnd}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ 
          delay: index * 0.1,
          duration: 0.4,
          ease: 'easeOut'
        }}
      >
        {/* Image Container */}
        <div className="relative aspect-square overflow-hidden bg-gray-50 dark:bg-gray-800">
          <AnimatePresence mode="wait">
            <motion.div
              key={currentImageIndex}
              initial={{ opacity: 0, scale: 1.1 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.9 }}
              transition={{ duration: 0.3 }}
              className="absolute inset-0"
            >
              <Image
                src={images[currentImageIndex] || '/placeholder-product.jpg'}
                alt={product.name}
                fill
                className="object-cover"
                priority={priority}
                sizes="(max-width: 768px) 50vw, (max-width: 1200px) 33vw, 25vw"
              />
            </motion.div>
          </AnimatePresence>

          {/* Image indicators */}
          {hasMultipleImages && (
            <div className="absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1">
              {images.map((_, idx) => (
                <div
                  key={idx}
                  className={`w-1.5 h-1.5 rounded-full transition-all duration-200 ${
                    idx === currentImageIndex 
                      ? 'bg-lime-green shadow-lg' 
                      : 'bg-white/50 backdrop-blur-sm'
                  }`}
                />
              ))}
            </div>
          )}

          {/* Discount badge */}
          {hasDiscount && (
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              className="absolute top-2 left-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full"
            >
              -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
            </motion.div>
          )}

          {/* Wishlist button */}
          <motion.button
            onClick={handleWishlistToggle}
            className={`absolute top-2 right-2 p-2 rounded-full backdrop-blur-sm transition-all duration-200 ${
              isWishlisted 
                ? 'bg-lime-green text-white' 
                : 'bg-white/80 text-gray-600 hover:bg-white'
            }`}
            whileTap={{ scale: 0.9 }}
          >
            <Heart 
              className={`w-4 h-4 ${isWishlisted ? 'fill-current' : ''}`} 
            />
          </motion.button>

          {/* Pressure indicator */}
          {pressureLevel > 0 && (
            <motion.div
              className="absolute inset-0 bg-lime-green/20 pointer-events-none"
              style={{ opacity: pressureLevel }}
            />
          )}
        </div>

        {/* Product Info */}
        <div className="p-4">
          <h3 className="font-semibold text-gray-900 dark:text-white text-sm mb-1 line-clamp-2">
            {product.name}
          </h3>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <span className="font-bold text-lime-green">
                ${product.price}
              </span>
              {hasDiscount && (
                <span className="text-xs text-gray-500 line-through">
                  ${product.originalPrice}
                </span>
              )}
            </div>
            
            <motion.button
              onClick={handleAddToCart}
              disabled={isAddingToCart}
              className="bg-lime-green text-black p-2 rounded-full hover:bg-lime-green/90 transition-colors disabled:opacity-50"
              whileTap={{ scale: 0.9 }}
            >
              {isAddingToCart ? (
                <motion.div
                  animate={{ rotate: 360 }}
                  transition={{ duration: 1, repeat: Infinity, ease: 'linear' }}
                  className="w-4 h-4 border-2 border-black border-t-transparent rounded-full"
                />
              ) : (
                <ShoppingBag className="w-4 h-4" />
              )}
            </motion.button>
          </div>
        </div>

        {/* Quick Actions Overlay */}
        <AnimatePresence>
          {showQuickActions && (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
              className="absolute inset-0 bg-black/80 backdrop-blur-sm flex items-center justify-center"
            >
              <div className="flex space-x-4">
                <motion.button
                  onClick={handleWishlistToggle}
                  className="bg-white/20 p-3 rounded-full backdrop-blur-sm"
                  whileTap={{ scale: 0.9 }}
                >
                  <Heart className={`w-6 h-6 text-white ${isWishlisted ? 'fill-current' : ''}`} />
                </motion.button>
                
                <motion.button
                  onClick={handleAddToCart}
                  className="bg-lime-green p-3 rounded-full"
                  whileTap={{ scale: 0.9 }}
                >
                  <ShoppingBag className="w-6 h-6 text-black" />
                </motion.button>
                
                <motion.button
                  onClick={handleShare}
                  className="bg-white/20 p-3 rounded-full backdrop-blur-sm"
                  whileTap={{ scale: 0.9 }}
                >
                  <Share2 className="w-6 h-6 text-white" />
                </motion.button>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Swipe hints */}
        {enableSwipeActions && (
          <div className="absolute bottom-0 left-0 right-0 p-2 bg-gradient-to-t from-black/20 to-transparent pointer-events-none">
            <div className="flex justify-between text-xs text-white/60">
              <span>← {hasMultipleImages ? 'Imagen' : 'Favorito'}</span>
              <span>{hasMultipleImages ? 'Imagen' : 'Carrito'} →</span>
            </div>
          </div>
        )}
      </motion.div>
    </MobileSwipeEngine>
  )
}
