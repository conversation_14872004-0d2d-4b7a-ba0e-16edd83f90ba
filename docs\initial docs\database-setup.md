# TWL Database Setup Guide

## 🚀 Quick Start

This guide will help you set up the Supabase database for The White Laces e-commerce platform.

## 📋 Prerequisites

1. **Supabase Account**: Create a free account at [supabase.com](https://supabase.com)
2. **Node.js**: Version 18+ installed
3. **Git**: For cloning and version control

## 🔧 Step 1: Create Supabase Project

1. **Login to Supabase Dashboard**
   - Go to [app.supabase.com](https://app.supabase.com)
   - Sign in with your account

2. **Create New Project**
   - Click "New Project"
   - Choose your organization
   - Project name: `twl-ecommerce`
   - Database password: Generate a strong password (save it!)
   - Region: Choose closest to your target market (Mexico/US)
   - Click "Create new project"

3. **Wait for Setup**
   - Project creation takes 2-3 minutes
   - You'll see a progress indicator

## 🔑 Step 2: Get API Keys

1. **Navigate to Settings**
   - In your project dashboard, go to Settings → API

2. **Copy Required Keys**
   - **Project URL**: `https://your-project.supabase.co`
   - **Anon Public Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`
   - **Service Role Key**: `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` (Keep this secret!)

## 📝 Step 3: Configure Environment Variables

1. **Create Environment File**
   ```bash
   cp .env.example .env.local
   ```

2. **Update .env.local**
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here
   SUPABASE_SERVICE_ROLE_KEY=your-service-role-key-here
   ```

3. **Verify Configuration**
   ```bash
   # Test connection
   node -e "
   require('dotenv').config({ path: '.env.local' });
   console.log('URL:', process.env.NEXT_PUBLIC_SUPABASE_URL);
   console.log('Anon Key:', process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'Set' : 'Missing');
   console.log('Service Key:', process.env.SUPABASE_SERVICE_ROLE_KEY ? 'Set' : 'Missing');
   "
   ```

## 🗄️ Step 4: Run Database Migrations

1. **Check Migration Status**
   ```bash
   npm run db:status
   ```

2. **Run Initial Migrations**
   ```bash
   npm run db:migrate
   ```

3. **Seed Database with Initial Data**
   ```bash
   npm run db:seed
   ```

4. **Or Run Everything at Once**
   ```bash
   npm run db:reset
   ```

## ✅ Step 5: Verify Setup

1. **Check Tables in Supabase Dashboard**
   - Go to Table Editor in your Supabase dashboard
   - You should see tables: `users`, `brands`, `categories`, `products`, etc.

2. **Test API Endpoints**
   ```bash
   # Start development server
   npm run dev

   # Test brands endpoint
   curl http://localhost:3001/api/brands

   # Test products endpoint
   curl http://localhost:3001/api/products
   ```

3. **Check Row Level Security**
   - In Supabase dashboard, go to Authentication → Policies
   - You should see RLS policies for all tables

## 🔐 Step 6: Configure Authentication (Optional)

1. **Enable Email Authentication**
   - Go to Authentication → Settings
   - Enable "Enable email confirmations"
   - Set site URL: `http://localhost:3001` (development)

2. **Configure Email Templates**
   - Customize confirmation and reset email templates
   - Add your branding and styling

## 📊 Step 7: Set Up Analytics (Optional)

1. **Enable Database Statistics**
   - Go to Settings → Database
   - Enable "Track database statistics"

2. **Set Up Monitoring**
   - Configure alerts for high CPU/memory usage
   - Set up backup schedules

## 🚨 Troubleshooting

### Common Issues

**1. "Missing Supabase environment variables"**
```bash
# Check if .env.local exists and has correct values
cat .env.local | grep SUPABASE
```

**2. "Permission denied" errors**
```bash
# Verify you're using the service role key for migrations
echo $SUPABASE_SERVICE_ROLE_KEY
```

**3. "Table already exists" errors**
```bash
# Check migration status
npm run db:status

# If needed, manually mark migrations as executed in Supabase
```

**4. "Connection timeout" errors**
```bash
# Check your internet connection and Supabase status
curl -I https://your-project.supabase.co
```

### Reset Database (Nuclear Option)

⚠️ **Warning**: This will delete all data!

```bash
# In Supabase dashboard:
# 1. Go to Settings → Database
# 2. Scroll to "Reset database password"
# 3. Click "Reset database" (this recreates the database)
# 4. Run migrations again: npm run db:reset
```

## 📚 Next Steps

After successful database setup:

1. **Test Product Integration**
   - Run the product indexing script: `node scripts/index-cytte-products.js`
   - Verify products appear in database

2. **Set Up Authentication**
   - Configure Firebase Auth integration
   - Test user registration and login

3. **Configure Payments**
   - Set up Stripe and Mercado Pago
   - Test payment processing

4. **Deploy to Production**
   - Create production Supabase project
   - Update environment variables
   - Run migrations on production

## 🔗 Useful Links

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase Dashboard](https://app.supabase.com)
- [TWL Database Schema](./database-schema.md)
- [TWL Backend Architecture](./backend-architecture.md)

## 📞 Support

If you encounter issues:

1. Check the [troubleshooting section](#troubleshooting) above
2. Review Supabase logs in the dashboard
3. Check the [TWL development roadmap](./development-roadmap.md)
4. Create an issue in the project repository

---

*Last Updated: 2025-06-15*
*Database Version: 002 (RLS Policies)*
