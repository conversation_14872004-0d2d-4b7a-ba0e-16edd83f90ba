'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useWishlist } from '@/contexts/WishlistContext'
import { useCart } from '@/contexts/CartContext'
import { getAllRealProducts } from '@/lib/real-products-loader'
import { Heart, ShoppingCart, Trash2, Share2 } from 'lucide-react'
import { Card, CardContent } from '@/components/ui/Card'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import Link from 'next/link'
import Image from 'next/image'

export default function WishlistPage() {
  console.log('💝 WISHLIST PAGE: Production component rendering!')

  const { wishlists, removeFromWishlist, clearWishlist, getTotalItemsCount } = useWishlist()
  const { addItem } = useCart()
  const [selectedListId, setSelectedListId] = useState('default')
  const [allProducts, setAllProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  // Get current wishlist and its items
  const currentWishlist = wishlists?.find(list => list.id === selectedListId) || wishlists?.[0]
  const currentList = currentWishlist?.items || []
  const totalItems = getTotalItemsCount ? getTotalItemsCount() : 0

  // Load all products on mount
  useEffect(() => {
    const loadProducts = async () => {
      try {
        console.log('💝 WISHLIST: Loading real products...')
        const realProducts = getAllRealProducts()
        console.log('💝 WISHLIST: Loaded', realProducts.length, 'products')
        setAllProducts(realProducts)
      } catch (error) {
        console.error('💝 WISHLIST ERROR: Failed to load products:', error)
      } finally {
        setIsLoading(false)
      }
    }
    loadProducts()
  }, [])

  // Get real product details from loaded products
  const getProductDetails = (productId) => {
    const product = allProducts.find(p => p.id === productId || p.id === parseInt(productId))

    if (product) {
      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        price: product.price,
        originalPrice: product.originalPrice,
        image: product.images?.[0] || '/products/placeholder.webp',
        category: product.category,
        inStock: product.stock > 0,
        stock: product.stock,
        sizes: product.variants?.[0]?.sizes || [],
        colors: product.variants?.[0]?.color || {},
        rating: product.rating,
        reviews: product.reviews
      }
    }

    // Fallback for unknown products
    console.warn('💝 WISHLIST: Product not found:', productId)
    return {
      id: productId,
      name: `Producto ${productId}`,
      brand: 'TWL',
      price: 2500,
      originalPrice: 3200,
      image: '/products/placeholder.webp',
      category: 'Sneakers',
      inStock: true,
      stock: 10,
      sizes: ['36', '37', '38', '39', '40', '41', '42'],
      colors: { name: 'Disponible', hex: '#000000' },
      rating: 4.0,
      reviews: 0
    }
  }

  const handleRemoveItem = (productId) => {
    console.log('💝 WISHLIST: Removing item:', productId)
    const product = getProductDetails(productId)
    removeFromWishlist(productId, selectedListId)
    console.log(`💝 ${product.name} eliminado de tu lista de deseos`)
  }

  const handleClearWishlist = () => {
    console.log('💝 WISHLIST: Clearing entire wishlist')
    if (confirm('¿Estás seguro de que quieres eliminar todos los productos de tu lista de deseos?')) {
      clearWishlist()
      console.log('💝 Lista de deseos limpiada')
    }
  }

  const handleAddToCart = (product) => {
    console.log('💝 WISHLIST: Adding to cart:', product.name)

    if (!product.inStock) {
      console.log('💝 Este producto está agotado')
      return
    }

    // Add to cart with default size and quantity
    const defaultSize = product.sizes?.[0] || '40'
    addItem({
      id: product.id,
      name: product.name,
      brand: product.brand,
      price: product.price,
      image: product.image,
      size: defaultSize,
      quantity: 1
    })

    console.log(`💝 ${product.name} agregado al carrito`)
  }

  const handleShare = async (product) => {
    console.log('💝 WISHLIST: Sharing product:', product.name)

    try {
      if (navigator.share) {
        await navigator.share({
          title: product.name,
          text: `Mira este ${product.brand}: ${product.name}`,
          url: window.location.origin + `/products/${product.id}`
        })
        console.log('💝 Producto compartido')
      } else {
        // Fallback: copy to clipboard
        const shareUrl = window.location.origin + `/products/${product.id}`
        await navigator.clipboard.writeText(shareUrl)
        console.log('💝 Enlace copiado al portapapeles')
      }
    } catch (error) {
      console.error('💝 WISHLIST: Error sharing:', error)
      console.log('💝 Error al compartir producto')
    }
  }

  // Show loading state while contexts are initializing
  if (isLoading) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-fog-black">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-lime-green-dark mx-auto mb-4"></div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-pure-white mb-2">
              Cargando Lista de Deseos...
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              Preparando tus productos favoritos
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Empty state for empty wishlist
  if (currentList.length === 0) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-fog-black">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-16">
            <Heart className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h1 className="text-2xl font-bold text-gray-900 dark:text-pure-white mb-2">
              Tu Lista de Deseos está Vacía
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Descubre productos increíbles y guárdalos para más tarde
            </p>
            <Link href="/shop">
              <Button className="bg-lime-green-dark hover:bg-lime-green text-pure-black font-medium">
                Explorar Productos
              </Button>
            </Link>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-pure-white dark:bg-fog-black">
      <div className="container mx-auto px-4 py-8">
        {/* Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-gray-900 dark:text-pure-white mb-2">
              Mi Lista de Deseos
            </h1>
            <p className="text-gray-600 dark:text-gray-400">
              {totalItems} {totalItems === 1 ? 'producto guardado' : 'productos guardados'}
            </p>
          </div>

          {currentList.length > 0 && (
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={handleClearWishlist}
                className="text-red-600 border-red-600 hover:bg-red-50 dark:hover:bg-red-900/20"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Limpiar Lista
              </Button>
            </div>
          )}
        </div>

        {/* Wishlist Items */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <AnimatePresence>
            {currentList.map((productId) => {
              const product = getProductDetails(productId)

              return (
                <motion.div
                  key={productId}
                  layout
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0.9 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card className="group hover:shadow-lg transition-all duration-300 bg-white dark:bg-mist-gray border-gray-200 dark:border-gray-700">
                    <CardContent className="p-4">
                      {/* Product Image */}
                      <div className="relative aspect-square mb-4 overflow-hidden rounded-lg bg-gray-100">
                        <Image
                          src={product.image}
                          alt={product.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-300"
                        />

                        {/* Quick Actions */}
                        <div className="absolute top-2 right-2 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-opacity">
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleRemoveItem(productId)}
                            className="h-8 w-8 bg-white/90 hover:bg-white text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="ghost"
                            onClick={() => handleShare(product)}
                            className="h-8 w-8 bg-white/90 hover:bg-white text-gray-600 hover:text-gray-700"
                          >
                            <Share2 className="w-4 h-4" />
                          </Button>
                        </div>

                        {/* Stock Badge */}
                        {!product.inStock && (
                          <Badge className="absolute bottom-2 left-2 bg-red-600 text-white">
                            Agotado
                          </Badge>
                        )}
                      </div>

                      {/* Product Info */}
                      <div className="space-y-2">
                        <div className="flex items-center justify-between">
                          <Badge variant="secondary" className="text-xs">
                            {product.brand}
                          </Badge>
                          <Badge variant="outline" className="text-xs">
                            {product.category}
                          </Badge>
                        </div>

                        <h3 className="font-semibold text-gray-900 dark:text-pure-white line-clamp-2">
                          {product.name}
                        </h3>

                        <div className="flex items-center gap-2">
                          <span className="text-lg font-bold text-lime-green-dark">
                            ${product.price.toLocaleString()}
                          </span>
                          {product.originalPrice && product.originalPrice > product.price && (
                            <span className="text-sm text-gray-500 line-through">
                              ${product.originalPrice.toLocaleString()}
                            </span>
                          )}
                        </div>
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2 mt-4">
                        <Button
                          onClick={() => handleAddToCart(product)}
                          disabled={!product.inStock}
                          className="flex-1 bg-lime-green-dark hover:bg-lime-green text-pure-black font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                        >
                          <ShoppingCart className="w-4 h-4 mr-2" />
                          {product.inStock ? 'Agregar al Carrito' : 'Agotado'}
                        </Button>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              )
            })}
          </AnimatePresence>
        </div>

        {/* Continue Shopping */}
        {currentList.length > 0 && (
          <div className="text-center mt-12">
            <Link href="/shop">
              <Button variant="outline" className="border-lime-green-dark text-lime-green-dark hover:bg-lime-green-dark hover:text-pure-black">
                Continuar Comprando
              </Button>
            </Link>
          </div>
        )}
      </div>
    </div>
  )
}
