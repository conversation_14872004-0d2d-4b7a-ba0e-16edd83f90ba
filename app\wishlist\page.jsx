'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useWishlist } from '@/contexts/WishlistContext'
import { useCart } from '@/contexts/CartContext'
import { getAllRealProducts } from '@/lib/real-products-loader'
import { Heart, ShoppingCart, Trash2, Share2, ArrowLeft, Filter, Grid3X3, List, Search, Star, Eye } from 'lucide-react'
import Button from '@/components/ui/Button'
import Badge from '@/components/ui/Badge'
import Link from 'next/link'
import Image from 'next/image'

export default function WishlistPage() {
  console.log('💝 WISHLIST PAGE: Enterprise-grade component rendering!')

  const { wishlists, removeFromWishlist, clearWishlist, getTotalItemsCount, isInWishlist } = useWishlist()
  const { addItem } = useCart()
  const [selectedListId, setSelectedListId] = useState('default')
  const [allProducts, setAllProducts] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [viewMode, setViewMode] = useState('grid') // 'grid' or 'list'
  const [searchQuery, setSearchQuery] = useState('')
  const [sortBy, setSortBy] = useState('newest') // 'newest', 'price-low', 'price-high', 'name'
  const [showFilters, setShowFilters] = useState(false)

  // Get current wishlist and its items
  const currentWishlist = wishlists?.find(list => list.id === selectedListId) || wishlists?.[0]
  const currentList = currentWishlist?.items || []
  const totalItems = getTotalItemsCount ? getTotalItemsCount() : 0

  // Load all products on mount with performance optimization
  useEffect(() => {
    const loadProducts = async () => {
      try {
        console.log('💝 WISHLIST: Loading real products...')

        // Use requestIdleCallback for better performance
        if (typeof window !== 'undefined' && window.requestIdleCallback) {
          window.requestIdleCallback(() => {
            const realProducts = getAllRealProducts()
            console.log('💝 WISHLIST: Loaded', realProducts.length, 'products')
            setAllProducts(realProducts)
            setIsLoading(false)
          })
        } else {
          // Fallback for browsers without requestIdleCallback
          setTimeout(() => {
            const realProducts = getAllRealProducts()
            console.log('💝 WISHLIST: Loaded', realProducts.length, 'products')
            setAllProducts(realProducts)
            setIsLoading(false)
          }, 0)
        }
      } catch (error) {
        console.error('💝 WISHLIST ERROR: Failed to load products:', error)
        setIsLoading(false)
      }
    }
    loadProducts()
  }, [])

  // Get real product details from loaded products
  const getProductDetails = (productId) => {
    const product = allProducts.find(p => p.id === productId || p.id === parseInt(productId))

    if (product) {
      return {
        id: product.id,
        name: product.name,
        brand: product.brand,
        price: product.price,
        originalPrice: product.originalPrice,
        image: product.images?.[0] || '/products/placeholder.webp',
        category: product.category,
        inStock: product.stock > 0,
        stock: product.stock,
        sizes: product.variants?.[0]?.sizes || [],
        colors: product.variants?.[0]?.color || {},
        rating: product.rating,
        reviews: product.reviews,
        tags: product.tags || [],
        isLimited: product.isLimited || false,
        isNew: product.isNew || false,
        isFeatured: product.isFeatured || false,
        sku: product.sku || '',
        releaseDate: product.releaseDate || ''
      }
    }

    // Fallback for unknown products
    console.warn('💝 WISHLIST: Product not found:', productId)
    return {
      id: productId,
      name: `Producto ${productId}`,
      brand: 'TWL',
      price: 2500,
      originalPrice: 3200,
      image: '/products/placeholder.webp',
      category: 'Sneakers',
      inStock: true,
      stock: 10,
      sizes: ['36', '37', '38', '39', '40', '41', '42'],
      colors: { name: 'Disponible', hex: '#000000' },
      rating: 4.0,
      reviews: 0,
      tags: [],
      isLimited: false,
      isNew: false,
      isFeatured: false,
      sku: '',
      releaseDate: ''
    }
  }

  // Filter and sort products
  const getFilteredAndSortedProducts = () => {
    let products = currentList.map(productId => getProductDetails(productId))

    // Filter by search query
    if (searchQuery) {
      products = products.filter(product =>
        product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.brand.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.category.toLowerCase().includes(searchQuery.toLowerCase()) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Sort products
    switch (sortBy) {
      case 'price-low':
        products.sort((a, b) => a.price - b.price)
        break
      case 'price-high':
        products.sort((a, b) => b.price - a.price)
        break
      case 'name':
        products.sort((a, b) => a.name.localeCompare(b.name))
        break
      case 'newest':
      default:
        // Keep original order (newest first)
        break
    }

    return products
  }

  const filteredProducts = getFilteredAndSortedProducts()

  const handleRemoveItem = (productId) => {
    console.log('💝 WISHLIST: Removing item:', productId)
    const product = getProductDetails(productId)
    removeFromWishlist(productId, selectedListId)
    console.log(`💝 ${product.name} eliminado de tu lista de deseos`)
  }

  const handleClearWishlist = () => {
    console.log('💝 WISHLIST: Clearing entire wishlist')
    if (confirm('¿Estás seguro de que quieres eliminar todos los productos de tu lista de deseos?')) {
      clearWishlist()
      console.log('💝 Lista de deseos limpiada')
    }
  }

  const handleAddToCart = (product) => {
    console.log('💝 WISHLIST: Adding to cart:', product.name)

    if (!product.inStock) {
      console.log('💝 Este producto está agotado')
      return
    }

    // Add to cart with default size and quantity
    const defaultSize = product.sizes?.[0] || '40'
    addItem(product.id, defaultSize, 1)
    console.log(`💝 ${product.name} agregado al carrito`)
  }

  const handleShare = async (product) => {
    console.log('💝 WISHLIST: Sharing product:', product.name)

    try {
      if (navigator.share) {
        await navigator.share({
          title: product.name,
          text: `Mira este ${product.brand}: ${product.name}`,
          url: window.location.origin + `/products/${product.id}`
        })
        console.log('💝 Producto compartido')
      } else {
        // Fallback: copy to clipboard
        const shareUrl = window.location.origin + `/products/${product.id}`
        await navigator.clipboard.writeText(shareUrl)
        console.log('💝 Enlace copiado al portapapeles')
      }
    } catch (error) {
      console.error('💝 WISHLIST: Error sharing:', error)
      console.log('💝 Error al compartir producto')
    }
  }

  // Show loading state while contexts are initializing
  if (isLoading) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-dark-gray">
        <div className="container mx-auto px-4 py-8">
          {/* Header with back button */}
          <div className="flex items-center gap-4 mb-8">
            <Link href="/shop" className="flex items-center gap-2 text-text-gray hover:text-lime-green-dark transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span className="font-poppins">Volver a la Tienda</span>
            </Link>
          </div>

          <div className="text-center py-16">
            <motion.div
              animate={{ rotate: 360 }}
              transition={{ duration: 2, repeat: Infinity, ease: "linear" }}
              className="w-16 h-16 border-4 border-lime-green-dark border-t-transparent rounded-full mx-auto mb-6"
            />
            <h1 className="text-3xl font-godber text-pure-black dark:text-pure-white mb-3">
              Cargando Lista de Deseos...
            </h1>
            <p className="text-text-gray font-poppins">
              Preparando tus productos favoritos
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Empty state for empty wishlist
  if (currentList.length === 0) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-dark-gray">
        <div className="container mx-auto px-4 py-8">
          {/* Header with back button */}
          <div className="flex items-center gap-4 mb-8">
            <Link href="/shop" className="flex items-center gap-2 text-text-gray hover:text-lime-green-dark transition-colors">
              <ArrowLeft className="w-5 h-5" />
              <span className="font-poppins">Volver a la Tienda</span>
            </Link>
          </div>

          <div className="text-center py-16">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ duration: 0.5, type: "spring" }}
              className="mb-8"
            >
              <div className="relative">
                <Heart className="w-24 h-24 text-gray-300 dark:text-gray-600 mx-auto" />
                <motion.div
                  animate={{ scale: [1, 1.2, 1] }}
                  transition={{ duration: 2, repeat: Infinity }}
                  className="absolute inset-0 w-24 h-24 mx-auto"
                >
                  <Heart className="w-24 h-24 text-lime-green-dark/20" />
                </motion.div>
              </div>
            </motion.div>

            <h1 className="text-4xl font-godber text-pure-black dark:text-pure-white mb-4">
              Tu Lista de Deseos está Vacía
            </h1>
            <p className="text-text-gray font-poppins text-lg mb-8 max-w-md mx-auto">
              Descubre productos increíbles y guárdalos para más tarde. ¡Empieza a crear tu colección de favoritos!
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link href="/shop">
                <Button className="bg-lime-green-dark hover:bg-lime-green text-pure-black font-poppins font-medium px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105">
                  <Search className="w-5 h-5 mr-2" />
                  Explorar Productos
                </Button>
              </Link>
              <Link href="/">
                <Button variant="outline" className="border-lime-green-dark text-lime-green-dark hover:bg-lime-green-dark hover:text-pure-black font-poppins font-medium px-8 py-3 rounded-xl transition-all duration-300">
                  <Heart className="w-5 h-5 mr-2" />
                  Ver Destacados
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-pure-white dark:bg-dark-gray">
      <div className="container mx-auto px-4 py-8">
        {/* Header with Navigation */}
        <div className="flex items-center gap-4 mb-8">
          <Link href="/shop" className="flex items-center gap-2 text-text-gray hover:text-lime-green-dark transition-colors">
            <ArrowLeft className="w-5 h-5" />
            <span className="font-poppins">Volver a la Tienda</span>
          </Link>
        </div>

        {/* Main Header */}
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between mb-8 gap-4">
          <div>
            <h1 className="text-4xl font-godber text-pure-black dark:text-pure-white mb-2">
              Mi Lista de Deseos
            </h1>
            <p className="text-text-gray font-poppins">
              {totalItems} {totalItems === 1 ? 'producto guardado' : 'productos guardados'}
            </p>
          </div>

          {currentList.length > 0 && (
            <div className="flex items-center gap-3">
              <Button
                variant="outline"
                onClick={handleClearWishlist}
                className="text-red-600 border-red-600 hover:bg-red-50 dark:hover:bg-red-900/20 font-poppins"
              >
                <Trash2 className="w-4 h-4 mr-2" />
                Limpiar Lista
              </Button>
            </div>
          )}
        </div>

        {/* Search and Filters */}
        {currentList.length > 0 && (
          <div className="mb-8 space-y-4">
            {/* Search Bar */}
            <div className="relative max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-gray w-5 h-5" />
              <input
                type="text"
                placeholder="Buscar en tu lista..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-border-gray rounded-xl focus:ring-2 focus:ring-lime-green-dark focus:border-transparent font-poppins bg-pure-white dark:bg-dark-gray dark:border-gray-600 dark:text-pure-white"
              />
            </div>

            {/* Filters and View Controls */}
            <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
              <div className="flex items-center gap-4">
                {/* Sort Dropdown */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="px-4 py-2 border border-border-gray rounded-lg focus:ring-2 focus:ring-lime-green-dark focus:border-transparent font-poppins bg-pure-white dark:bg-dark-gray dark:border-gray-600 dark:text-pure-white"
                >
                  <option value="newest">Más recientes</option>
                  <option value="price-low">Precio: menor a mayor</option>
                  <option value="price-high">Precio: mayor a menor</option>
                  <option value="name">Nombre A-Z</option>
                </select>
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center gap-2 bg-light-gray dark:bg-gray-700 rounded-lg p-1">
                <button
                  onClick={() => setViewMode('grid')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'grid'
                      ? 'bg-lime-green-dark text-pure-black'
                      : 'text-text-gray hover:text-lime-green-dark'
                  }`}
                >
                  <Grid3X3 className="w-4 h-4" />
                </button>
                <button
                  onClick={() => setViewMode('list')}
                  className={`p-2 rounded-md transition-colors ${
                    viewMode === 'list'
                      ? 'bg-lime-green-dark text-pure-black'
                      : 'text-text-gray hover:text-lime-green-dark'
                  }`}
                >
                  <List className="w-4 h-4" />
                </button>
              </div>
            </div>

            {/* Results Count */}
            {searchQuery && (
              <p className="text-text-gray font-poppins text-sm">
                {filteredProducts.length} {filteredProducts.length === 1 ? 'resultado encontrado' : 'resultados encontrados'} para "{searchQuery}"
              </p>
            )}
          </div>
        )}

        {/* Wishlist Items */}
        <div className={`${
          viewMode === 'grid'
            ? 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6'
            : 'space-y-4'
        }`}>
          <AnimatePresence>
            {filteredProducts.map((product, index) => (
              <motion.div
                key={product.id}
                layout
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, scale: 0.9 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                {viewMode === 'grid' ? (
                  // Grid View Card
                  <div className="group bg-pure-white dark:bg-gray-800 rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 border border-border-gray dark:border-gray-700 overflow-hidden">
                    {/* Product Image */}
                    <div className="relative aspect-square overflow-hidden bg-light-gray dark:bg-gray-700">
                      <Link href={`/product/${product.id}`}>
                        <Image
                          src={product.image}
                          alt={product.name}
                          fill
                          className="object-cover group-hover:scale-105 transition-transform duration-500"
                        />
                      </Link>

                      {/* Badges */}
                      <div className="absolute top-3 left-3 flex flex-col gap-2">
                        {product.isNew && (
                          <Badge className="bg-lime-green-dark text-pure-black font-poppins text-xs">
                            NUEVO
                          </Badge>
                        )}
                        {product.isLimited && (
                          <Badge className="bg-red-600 text-white font-poppins text-xs">
                            LIMITADO
                          </Badge>
                        )}
                        {!product.inStock && (
                          <Badge className="bg-gray-600 text-white font-poppins text-xs">
                            AGOTADO
                          </Badge>
                        )}
                      </div>

                      {/* Quick Actions */}
                      <div className="absolute top-3 right-3 flex flex-col gap-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => handleRemoveItem(product.id)}
                          className="w-10 h-10 bg-white/90 backdrop-blur-sm hover:bg-white text-red-600 hover:text-red-700 rounded-full flex items-center justify-center shadow-lg transition-colors"
                        >
                          <Trash2 className="w-4 h-4" />
                        </motion.button>
                        <motion.button
                          whileHover={{ scale: 1.1 }}
                          whileTap={{ scale: 0.9 }}
                          onClick={() => handleShare(product)}
                          className="w-10 h-10 bg-white/90 backdrop-blur-sm hover:bg-white text-text-gray hover:text-lime-green-dark rounded-full flex items-center justify-center shadow-lg transition-colors"
                        >
                          <Share2 className="w-4 h-4" />
                        </motion.button>
                        <Link href={`/product/${product.id}`}>
                          <motion.button
                            whileHover={{ scale: 1.1 }}
                            whileTap={{ scale: 0.9 }}
                            className="w-10 h-10 bg-white/90 backdrop-blur-sm hover:bg-white text-text-gray hover:text-lime-green-dark rounded-full flex items-center justify-center shadow-lg transition-colors"
                          >
                            <Eye className="w-4 h-4" />
                          </motion.button>
                        </Link>
                      </div>

                      {/* Discount Badge */}
                      {product.originalPrice && product.originalPrice > product.price && (
                        <div className="absolute bottom-3 left-3">
                          <Badge className="bg-red-600 text-white font-poppins text-xs">
                            -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
                          </Badge>
                        </div>
                      )}
                    </div>

                    {/* Product Info */}
                    <div className="p-5">
                      <div className="flex items-center justify-between mb-3">
                        <Badge variant="secondary" className="text-xs font-poppins bg-light-gray text-text-gray">
                          {product.brand}
                        </Badge>
                        <Badge variant="outline" className="text-xs font-poppins">
                          {product.category}
                        </Badge>
                      </div>

                      <Link href={`/product/${product.id}`}>
                        <h3 className="font-poppins font-semibold text-pure-black dark:text-pure-white line-clamp-2 mb-3 hover:text-lime-green-dark transition-colors">
                          {product.name}
                        </h3>
                      </Link>

                      {/* Rating */}
                      {product.rating && (
                        <div className="flex items-center gap-2 mb-3">
                          <div className="flex items-center">
                            {[...Array(5)].map((_, i) => (
                              <Star
                                key={i}
                                className={`w-4 h-4 ${
                                  i < Math.floor(product.rating)
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="text-sm text-text-gray font-poppins">
                            ({product.reviews || 0})
                          </span>
                        </div>
                      )}

                      {/* Price */}
                      <div className="flex items-center gap-2 mb-4">
                        <span className="text-xl font-bold text-lime-green-dark font-poppins">
                          ${product.price.toLocaleString()} MXN
                        </span>
                        {product.originalPrice && product.originalPrice > product.price && (
                          <span className="text-sm text-text-gray line-through font-poppins">
                            ${product.originalPrice.toLocaleString()}
                          </span>
                        )}
                      </div>

                      {/* Actions */}
                      <div className="flex gap-2">
                        <Button
                          onClick={() => handleAddToCart(product)}
                          disabled={!product.inStock}
                          className="flex-1 bg-lime-green-dark hover:bg-lime-green text-pure-black font-poppins font-medium py-3 rounded-xl disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:scale-105"
                        >
                          <ShoppingCart className="w-4 h-4 mr-2" />
                          {product.inStock ? 'Agregar al Carrito' : 'Agotado'}
                        </Button>
                      </div>
                    </div>
                  </div>
                ) : (
                  // List View Card
                  <div className="group bg-pure-white dark:bg-gray-800 rounded-xl shadow-sm hover:shadow-lg transition-all duration-300 border border-border-gray dark:border-gray-700 overflow-hidden">
                    <div className="flex gap-4 p-4">
                      {/* Product Image */}
                      <div className="relative w-24 h-24 flex-shrink-0 overflow-hidden rounded-lg bg-light-gray dark:bg-gray-700">
                        <Link href={`/product/${product.id}`}>
                          <Image
                            src={product.image}
                            alt={product.name}
                            fill
                            className="object-cover group-hover:scale-105 transition-transform duration-300"
                          />
                        </Link>
                      </div>

                      {/* Product Info */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1 min-w-0">
                            <div className="flex items-center gap-2 mb-2">
                              <Badge variant="secondary" className="text-xs font-poppins bg-light-gray text-text-gray">
                                {product.brand}
                              </Badge>
                              {product.isNew && (
                                <Badge className="bg-lime-green-dark text-pure-black font-poppins text-xs">
                                  NUEVO
                                </Badge>
                              )}
                            </div>

                            <Link href={`/product/${product.id}`}>
                              <h3 className="font-poppins font-semibold text-pure-black dark:text-pure-white line-clamp-1 mb-2 hover:text-lime-green-dark transition-colors">
                                {product.name}
                              </h3>
                            </Link>

                            <div className="flex items-center gap-4 mb-2">
                              <div className="flex items-center gap-2">
                                <span className="text-lg font-bold text-lime-green-dark font-poppins">
                                  ${product.price.toLocaleString()} MXN
                                </span>
                                {product.originalPrice && product.originalPrice > product.price && (
                                  <span className="text-sm text-text-gray line-through font-poppins">
                                    ${product.originalPrice.toLocaleString()}
                                  </span>
                                )}
                              </div>

                              {product.rating && (
                                <div className="flex items-center gap-1">
                                  <Star className="w-4 h-4 text-yellow-400 fill-current" />
                                  <span className="text-sm text-text-gray font-poppins">
                                    {product.rating}
                                  </span>
                                </div>
                              )}
                            </div>

                            <div className="flex items-center gap-2">
                              <Button
                                onClick={() => handleAddToCart(product)}
                                disabled={!product.inStock}
                                size="sm"
                                className="bg-lime-green-dark hover:bg-lime-green text-pure-black font-poppins font-medium disabled:opacity-50 disabled:cursor-not-allowed"
                              >
                                <ShoppingCart className="w-4 h-4 mr-1" />
                                {product.inStock ? 'Agregar' : 'Agotado'}
                              </Button>
                            </div>
                          </div>

                          {/* Actions */}
                          <div className="flex items-center gap-2 ml-4">
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleShare(product)}
                              className="w-8 h-8 text-text-gray hover:text-lime-green-dark rounded-full flex items-center justify-center transition-colors"
                            >
                              <Share2 className="w-4 h-4" />
                            </motion.button>
                            <motion.button
                              whileHover={{ scale: 1.1 }}
                              whileTap={{ scale: 0.9 }}
                              onClick={() => handleRemoveItem(product.id)}
                              className="w-8 h-8 text-text-gray hover:text-red-600 rounded-full flex items-center justify-center transition-colors"
                            >
                              <Trash2 className="w-4 h-4" />
                            </motion.button>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                )}
              </motion.div>
            ))}
          </AnimatePresence>
        </div>

        {/* No Results Message */}
        {searchQuery && filteredProducts.length === 0 && (
          <div className="text-center py-16">
            <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-xl font-godber text-pure-black dark:text-pure-white mb-2">
              No se encontraron productos
            </h3>
            <p className="text-text-gray font-poppins mb-4">
              Intenta con otros términos de búsqueda
            </p>
            <Button
              onClick={() => setSearchQuery('')}
              variant="outline"
              className="border-lime-green-dark text-lime-green-dark hover:bg-lime-green-dark hover:text-pure-black font-poppins"
            >
              Limpiar búsqueda
            </Button>
          </div>
        )}

        {/* Statistics and Continue Shopping */}
        {currentList.length > 0 && (
          <div className="mt-16 space-y-8">
            {/* Statistics */}
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="bg-light-gray dark:bg-gray-800 rounded-xl p-6 text-center">
                <div className="text-2xl font-bold text-lime-green-dark font-poppins mb-2">
                  {filteredProducts.length}
                </div>
                <div className="text-text-gray font-poppins text-sm">
                  Productos en tu lista
                </div>
              </div>
              <div className="bg-light-gray dark:bg-gray-800 rounded-xl p-6 text-center">
                <div className="text-2xl font-bold text-lime-green-dark font-poppins mb-2">
                  ${Math.round(filteredProducts.reduce((sum, p) => sum + p.price, 0)).toLocaleString()}
                </div>
                <div className="text-text-gray font-poppins text-sm">
                  Valor total estimado
                </div>
              </div>
              <div className="bg-light-gray dark:bg-gray-800 rounded-xl p-6 text-center">
                <div className="text-2xl font-bold text-lime-green-dark font-poppins mb-2">
                  ${Math.round(filteredProducts.reduce((sum, p) => sum + (p.originalPrice || p.price) - p.price, 0)).toLocaleString()}
                </div>
                <div className="text-text-gray font-poppins text-sm">
                  Ahorro potencial
                </div>
              </div>
            </div>

            {/* Continue Shopping */}
            <div className="text-center">
              <div className="max-w-md mx-auto mb-6">
                <h3 className="text-xl font-godber text-pure-black dark:text-pure-white mb-2">
                  ¿Listo para comprar?
                </h3>
                <p className="text-text-gray font-poppins">
                  Agrega todos tus productos favoritos al carrito o sigue explorando nuestra colección
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button
                  onClick={() => {
                    filteredProducts.forEach(product => {
                      if (product.inStock) {
                        handleAddToCart(product)
                      }
                    })
                  }}
                  className="bg-lime-green-dark hover:bg-lime-green text-pure-black font-poppins font-medium px-8 py-3 rounded-xl transition-all duration-300 hover:scale-105"
                >
                  <ShoppingCart className="w-5 h-5 mr-2" />
                  Agregar Todo al Carrito
                </Button>
                <Link href="/shop">
                  <Button variant="outline" className="border-lime-green-dark text-lime-green-dark hover:bg-lime-green-dark hover:text-pure-black font-poppins font-medium px-8 py-3 rounded-xl transition-all duration-300">
                    <Search className="w-5 h-5 mr-2" />
                    Continuar Comprando
                  </Button>
                </Link>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
