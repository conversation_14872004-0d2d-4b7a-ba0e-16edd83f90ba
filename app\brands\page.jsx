'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

const brands = [
  {
    id: 1,
    name: 'Nike',
    description: 'Innovación y rendimiento deportivo',
    logo: '✓',
    products: 156,
    featured: true,
    color: 'from-orange-500 to-red-600'
  },
  {
    id: 2,
    name: 'Adidas',
    description: 'Las tres rayas icónicas',
    logo: '⚡',
    products: 134,
    featured: true,
    color: 'from-blue-500 to-purple-600'
  },
  {
    id: 3,
    name: '<PERSON><PERSON>',
    description: 'Lujo italiano atemporal',
    logo: '👑',
    products: 89,
    featured: true,
    color: 'from-yellow-500 to-orange-600'
  },
  {
    id: 4,
    name: 'Balenciaga',
    description: 'Vanguardia y diseño disruptivo',
    logo: '🔥',
    products: 67,
    featured: false,
    color: 'from-gray-500 to-gray-700'
  },
  {
    id: 5,
    name: 'Off-White',
    description: 'Streetwear de lujo',
    logo: '⚪',
    products: 45,
    featured: false,
    color: 'from-black to-gray-800'
  },
  {
    id: 6,
    name: '<PERSON>',
    description: 'Legado del básquetbol',
    logo: '🏀',
    products: 78,
    featured: true,
    color: 'from-red-500 to-black'
  }
]

export default function BrandsPage() {
  const featuredBrands = brands.filter(brand => brand.featured)
  const otherBrands = brands.filter(brand => !brand.featured)

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Nuestras Marcas
          </h1>
          <p className="text-warm-camel text-lg max-w-3xl mx-auto">
            Descubre las marcas más exclusivas y codiciadas del mundo del calzado. 
            Desde iconos deportivos hasta lujo italiano.
          </p>
        </motion.div>

        {/* Featured Brands */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="mb-16"
        >
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8">
            Marcas Destacadas
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {featuredBrands.map((brand, index) => (
              <motion.div
                key={brand.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.3 + index * 0.1 }}
              >
                <Card variant="glass" className="group hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-8">
                    <div className="text-center">
                      <div className={`w-20 h-20 mx-auto mb-6 rounded-full bg-gradient-to-r ${brand.color} flex items-center justify-center text-3xl`}>
                        {brand.logo}
                      </div>
                      
                      <h3 className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-3">
                        {brand.name}
                      </h3>
                      
                      <p className="text-warm-camel mb-4">
                        {brand.description}
                      </p>
                      
                      <div className="flex items-center justify-center gap-4 mb-6">
                        <div className="text-center">
                          <div className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                            {brand.products}
                          </div>
                          <div className="text-sm text-warm-camel">Productos</div>
                        </div>
                      </div>
                      
                      <TransitionLink href={`/shop?brand=${brand.name.toLowerCase()}`}>
                        <AnimatedButton
                          variant="primary"
                          className="w-full"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                          }
                          iconPosition="right"
                        >
                          Explorar {brand.name}
                        </AnimatedButton>
                      </TransitionLink>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Other Brands */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
        >
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-8">
            Más Marcas
          </h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {otherBrands.map((brand, index) => (
              <motion.div
                key={brand.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.7 + index * 0.1 }}
              >
                <Card variant="default" className="group hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-4">
                      <div className={`w-12 h-12 rounded-lg bg-gradient-to-r ${brand.color} flex items-center justify-center text-xl`}>
                        {brand.logo}
                      </div>
                      
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-1">
                          {brand.name}
                        </h3>
                        <p className="text-warm-camel text-sm mb-2">
                          {brand.description}
                        </p>
                        <p className="text-xs text-warm-camel">
                          {brand.products} productos
                        </p>
                      </div>
                      
                      <TransitionLink href={`/shop?brand=${brand.name.toLowerCase()}`}>
                        <AnimatedButton
                          variant="ghost"
                          size="sm"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                            </svg>
                          }
                        >
                          Ver
                        </AnimatedButton>
                      </TransitionLink>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1 }}
          className="mt-16 text-center"
        >
          <Card variant="glass">
            <CardContent className="p-12">
              <h2 className="text-3xl font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
                ¿No encuentras tu marca favorita?
              </h2>
              <p className="text-warm-camel mb-8 max-w-2xl mx-auto">
                Estamos constantemente agregando nuevas marcas a nuestra colección. 
                Contáctanos si hay alguna marca específica que te gustaría ver en TWL.
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <TransitionLink href="/shop">
                  <AnimatedButton
                    variant="primary"
                    size="lg"
                    icon={
                      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                    }
                  >
                    Explorar Tienda
                  </AnimatedButton>
                </TransitionLink>
                
                <AnimatedButton
                  variant="secondary"
                  size="lg"
                  icon={
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                  }
                >
                  Contactar
                </AnimatedButton>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  )
}
