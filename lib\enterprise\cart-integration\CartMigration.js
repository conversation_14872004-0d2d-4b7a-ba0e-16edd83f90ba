/**
 * TWL Cart Migration Utility
 * Seamlessly migrates existing cart to enhanced enterprise cart
 */

import productAdapter from './ProductAdapter'

class CartMigration {
  constructor() {
    this.migrationVersion = '1.0.0'
    this.backupKey = 'twl-cart-backup'
    this.legacyKey = 'twl-cart'
    this.enhancedKey = 'twl-enhanced-cart'
  }

  /**
   * Check if migration is needed
   */
  needsMigration() {
    try {
      const legacyCart = localStorage.getItem(this.legacyKey)
      const enhancedCart = localStorage.getItem(this.enhancedKey)
      
      // Migration needed if legacy cart exists but enhanced cart doesn't
      return legacyCart && !enhancedCart
    } catch (error) {
      console.error('Error checking migration status:', error)
      return false
    }
  }

  /**
   * Migrate legacy cart to enhanced cart format
   */
  async migrateCart() {
    console.log('🔄 Starting cart migration to enterprise system...')
    
    try {
      // 1. Load legacy cart
      const legacyCartData = localStorage.getItem(this.legacyKey)
      if (!legacyCartData) {
        console.log('✅ No legacy cart found, migration not needed')
        return { success: true, migrated: false }
      }

      const legacyCart = JSON.parse(legacyCartData)
      console.log(`📦 Found legacy cart with ${legacyCart.items?.length || 0} items`)

      // 2. Create backup
      this.createBackup(legacyCart)

      // 3. Transform cart items
      const enhancedItems = await this.transformCartItems(legacyCart.items || [])

      // 4. Create enhanced cart structure
      const enhancedCart = {
        items: enhancedItems.validItems,
        discount: legacyCart.discount || null,
        validationResults: {},
        recommendations: [],
        isValidating: false,
        lastValidated: null,
        createdAt: legacyCart.createdAt || new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        migrationVersion: this.migrationVersion,
        migratedAt: new Date().toISOString(),
        migratedFrom: 'legacy'
      }

      // 5. Save enhanced cart
      localStorage.setItem(this.enhancedKey, JSON.stringify(enhancedCart))

      // 6. Log migration results
      const migrationResults = {
        success: true,
        migrated: true,
        totalItems: legacyCart.items?.length || 0,
        migratedItems: enhancedItems.validItems.length,
        failedItems: enhancedItems.failedItems.length,
        enhancedItems: enhancedItems.enhancedItems,
        failedItems: enhancedItems.failedItems
      }

      console.log('✅ Cart migration completed successfully:', migrationResults)
      
      // 7. Show user notification if there were issues
      if (enhancedItems.failedItems.length > 0) {
        this.notifyMigrationIssues(enhancedItems.failedItems)
      }

      return migrationResults

    } catch (error) {
      console.error('❌ Cart migration failed:', error)
      this.restoreFromBackup()
      return { success: false, error: error.message }
    }
  }

  /**
   * Transform legacy cart items to enhanced format
   */
  async transformCartItems(legacyItems) {
    const validItems = []
    const failedItems = []
    let enhancedItems = 0

    console.log(`🔄 Transforming ${legacyItems.length} cart items...`)

    for (const legacyItem of legacyItems) {
      try {
        // Try to get enhanced product data
        const enhancedProduct = await productAdapter.getProductForCart(legacyItem.productId)
        
        if (enhancedProduct) {
          // Create enhanced cart item
          const enhancedItem = {
            id: legacyItem.id || `${legacyItem.productId}-${legacyItem.size}`,
            productId: legacyItem.productId,
            name: enhancedProduct.name || legacyItem.name,
            brand: enhancedProduct.brand || legacyItem.brand,
            image: enhancedProduct.image || legacyItem.image,
            size: legacyItem.size,
            quantity: legacyItem.quantity,
            price: enhancedProduct.price || legacyItem.price,
            
            // Enhanced fields from enterprise system
            originalPrice: enhancedProduct.originalPrice,
            discountPercent: enhancedProduct.discountPercent,
            isLimitedEdition: enhancedProduct.isLimitedEdition,
            stockLevel: enhancedProduct.stockLevel,
            inStock: enhancedProduct.inStock,
            rating: enhancedProduct.rating,
            reviewCount: enhancedProduct.reviewCount,
            category: enhancedProduct.category,
            brandInfo: enhancedProduct.brandInfo,
            
            // Migration metadata
            addedAt: legacyItem.addedAt || new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            _source: enhancedProduct._source || 'migrated',
            _migrated: true,
            _originalData: legacyItem
          }

          validItems.push(enhancedItem)
          
          if (enhancedProduct._source === 'enterprise') {
            enhancedItems++
          }

          console.log(`✅ Enhanced item: ${enhancedItem.name}`)
        } else {
          // Keep legacy item structure if enhancement fails
          const fallbackItem = {
            ...legacyItem,
            id: legacyItem.id || `${legacyItem.productId}-${legacyItem.size}`,
            updatedAt: new Date().toISOString(),
            _source: 'legacy',
            _migrated: true,
            _enhancementFailed: true
          }

          validItems.push(fallbackItem)
          console.log(`⚠️ Kept legacy format: ${fallbackItem.name}`)
        }

      } catch (error) {
        console.error(`❌ Failed to transform item ${legacyItem.productId}:`, error)
        
        failedItems.push({
          ...legacyItem,
          error: error.message,
          failedAt: new Date().toISOString()
        })
      }
    }

    return {
      validItems,
      failedItems,
      enhancedItems
    }
  }

  /**
   * Create backup of legacy cart
   */
  createBackup(legacyCart) {
    try {
      const backup = {
        cart: legacyCart,
        backedUpAt: new Date().toISOString(),
        version: this.migrationVersion
      }
      
      localStorage.setItem(this.backupKey, JSON.stringify(backup))
      console.log('💾 Legacy cart backup created')
    } catch (error) {
      console.error('❌ Failed to create cart backup:', error)
    }
  }

  /**
   * Restore from backup if migration fails
   */
  restoreFromBackup() {
    try {
      const backup = localStorage.getItem(this.backupKey)
      if (backup) {
        const backupData = JSON.parse(backup)
        localStorage.setItem(this.legacyKey, JSON.stringify(backupData.cart))
        console.log('🔄 Restored cart from backup')
      }
    } catch (error) {
      console.error('❌ Failed to restore from backup:', error)
    }
  }

  /**
   * Notify user about migration issues
   */
  notifyMigrationIssues(failedItems) {
    console.warn(`⚠️ ${failedItems.length} items could not be migrated:`, failedItems)
    
    // You can implement user notification here
    // For example, show a toast notification or modal
    if (typeof window !== 'undefined' && window.showToast) {
      window.showToast({
        type: 'warning',
        title: 'Cart Migration Notice',
        message: `${failedItems.length} items in your cart could not be enhanced. They will remain in legacy format.`,
        duration: 8000
      })
    }
  }

  /**
   * Clean up migration artifacts
   */
  cleanupMigration() {
    try {
      // Remove backup after successful migration
      localStorage.removeItem(this.backupKey)
      console.log('🧹 Migration cleanup completed')
    } catch (error) {
      console.error('❌ Failed to cleanup migration:', error)
    }
  }

  /**
   * Get migration status and statistics
   */
  getMigrationStatus() {
    try {
      const enhancedCart = localStorage.getItem(this.enhancedKey)
      const legacyCart = localStorage.getItem(this.legacyKey)
      const backup = localStorage.getItem(this.backupKey)

      if (enhancedCart) {
        const cart = JSON.parse(enhancedCart)
        return {
          isMigrated: true,
          migrationVersion: cart.migrationVersion,
          migratedAt: cart.migratedAt,
          totalItems: cart.items?.length || 0,
          enhancedItems: cart.items?.filter(item => item._source === 'enterprise').length || 0,
          legacyItems: cart.items?.filter(item => item._source === 'legacy').length || 0,
          hasBackup: !!backup
        }
      }

      return {
        isMigrated: false,
        hasLegacyCart: !!legacyCart,
        needsMigration: this.needsMigration(),
        hasBackup: !!backup
      }
    } catch (error) {
      console.error('Error getting migration status:', error)
      return { error: error.message }
    }
  }

  /**
   * Force re-migration (for testing or updates)
   */
  async forceMigration() {
    console.log('🔄 Forcing cart re-migration...')
    
    // Remove enhanced cart to trigger migration
    localStorage.removeItem(this.enhancedKey)
    
    // Run migration
    return await this.migrateCart()
  }
}

// Create singleton instance
const cartMigration = new CartMigration()

// Auto-migrate on first import if needed
if (typeof window !== 'undefined') {
  setTimeout(() => {
    if (cartMigration.needsMigration()) {
      cartMigration.migrateCart().then(result => {
        if (result.success && result.migrated) {
          console.log('🎉 Automatic cart migration completed successfully')
          
          // Clean up after successful migration
          setTimeout(() => {
            cartMigration.cleanupMigration()
          }, 5000) // Wait 5 seconds before cleanup
        }
      }).catch(error => {
        console.error('❌ Automatic cart migration failed:', error)
      })
    }
  }, 1000) // Wait 1 second after page load
}

export default cartMigration
export { CartMigration }
