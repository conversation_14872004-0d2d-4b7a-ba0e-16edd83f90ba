#!/usr/bin/env node

/**
 * TRANSPORT COST UPDATER
 * 
 * Updates all existing products with China-Mexico transport costs ($35 USD)
 * Recalculates pricing strategies with accurate total costs
 */

const fs = require('fs').promises;
const path = require('path');

class TransportCostUpdater {
  constructor() {
    this.catalogPath = path.join(process.cwd(), 'database', 'product-catalog.json');
    this.transportCost = 35; // China to Mexico transport cost in USD
    this.updatedCount = 0;
    this.totalProducts = 0;
  }

  async updateTransportCosts() {
    console.log('🚚 UPDATING TRANSPORT COSTS FOR ALL PRODUCTS');
    console.log('============================================');
    console.log(`💰 Adding $${this.transportCost} USD transport cost (China → Mexico)`);
    console.log('');

    try {
      // Load catalog
      const catalog = await this.loadCatalog();
      this.totalProducts = Object.keys(catalog.products).length;
      
      console.log(`📦 Found ${this.totalProducts} products to update`);
      console.log('');

      // Update each product
      for (const [productId, product] of Object.entries(catalog.products)) {
        this.updateProductPricing(product);
      }

      // Update metadata
      catalog.metadata.lastUpdated = new Date().toISOString();
      catalog.metadata.transportCostUpdated = true;
      catalog.metadata.transportCostAmount = this.transportCost;
      catalog.metadata.transportCostCurrency = 'USD';

      // Save updated catalog
      await this.saveCatalog(catalog);
      
      // Generate report
      this.generateReport();
      
      return {
        totalProducts: this.totalProducts,
        updatedProducts: this.updatedCount,
        transportCost: this.transportCost
      };
      
    } catch (error) {
      console.error('❌ Transport cost update failed:', error);
      throw error;
    }
  }

  updateProductPricing(product) {
    // Check if product has supplier info
    if (!product.supplierInfo || !product.supplierInfo.costUSD) {
      return; // Skip products without supplier cost data
    }

    const supplierCost = product.supplierInfo.costUSD;
    const totalCost = supplierCost + this.transportCost;

    // Update or create pricing strategy
    product.pricingStrategy = {
      supplierCost: supplierCost,
      transportCost: this.transportCost,
      totalCost: totalCost,
      suggestedRetail: Math.round(totalCost * 2.5), // 150% markup on total cost
      premiumRetail: Math.round(totalCost * 3.0), // 200% markup on total cost
      luxuryRetail: Math.round(totalCost * 4.0), // 300% markup on total cost
      currency: 'USD',
      markupStrategy: 'luxury_positioning_with_transport',
      profitMargin: {
        suggested: Math.round(((Math.round(totalCost * 2.5) - totalCost) / totalCost) * 100),
        premium: Math.round(((Math.round(totalCost * 3.0) - totalCost) / totalCost) * 100),
        luxury: Math.round(((Math.round(totalCost * 4.0) - totalCost) / totalCost) * 100)
      },
      breakdown: {
        supplierCost: supplierCost,
        transportCost: this.transportCost,
        totalCost: totalCost,
        suggestedProfit: Math.round(totalCost * 2.5) - totalCost,
        premiumProfit: Math.round(totalCost * 3.0) - totalCost,
        luxuryProfit: Math.round(totalCost * 4.0) - totalCost
      },
      lastCalculated: new Date().toISOString()
    };

    this.updatedCount++;

    // Log progress every 50 products
    if (this.updatedCount % 50 === 0) {
      console.log(`📦 Updated ${this.updatedCount}/${this.totalProducts} products...`);
    }
  }

  async loadCatalog() {
    try {
      const catalogData = await fs.readFile(this.catalogPath, 'utf8');
      return JSON.parse(catalogData);
    } catch (error) {
      console.error('❌ Could not load catalog:', error);
      throw error;
    }
  }

  async saveCatalog(catalog) {
    await fs.writeFile(this.catalogPath, JSON.stringify(catalog, null, 2));
    console.log('💾 Updated catalog saved successfully');
  }

  generateReport() {
    console.log('');
    console.log('📊 TRANSPORT COST UPDATE REPORT');
    console.log('===============================');
    console.log(`📦 Total products: ${this.totalProducts}`);
    console.log(`✅ Products updated: ${this.updatedCount}`);
    console.log(`🚚 Transport cost: $${this.transportCost} USD`);
    console.log(`⚠️  Products skipped: ${this.totalProducts - this.updatedCount} (no supplier cost data)`);
    
    // Sample pricing examples
    console.log('');
    console.log('💰 UPDATED PRICING EXAMPLES:');
    console.log('');
    
    const examples = [
      { supplier: 20, name: 'Budget Sneakers' },
      { supplier: 35, name: 'Mid-Range Shoes' },
      { supplier: 50, name: 'Luxury Footwear' }
    ];
    
    examples.forEach(example => {
      const total = example.supplier + this.transportCost;
      const suggested = Math.round(total * 2.5);
      const premium = Math.round(total * 3.0);
      const luxury = Math.round(total * 4.0);
      
      console.log(`   ${example.name}:`);
      console.log(`     💰 Supplier: $${example.supplier} + Transport: $${this.transportCost} = Total: $${total}`);
      console.log(`     🏷️  Suggested: $${suggested} (${Math.round(((suggested - total) / total) * 100)}% profit)`);
      console.log(`     🏷️  Premium: $${premium} (${Math.round(((premium - total) / total) * 100)}% profit)`);
      console.log(`     🏷️  Luxury: $${luxury} (${Math.round(((luxury - total) / total) * 100)}% profit)`);
      console.log('');
    });
    
    console.log('🎉 TRANSPORT COST UPDATE COMPLETE!');
    console.log('All pricing strategies now include China-Mexico transport costs!');
  }
}

// Main execution
async function main() {
  const updater = new TransportCostUpdater();
  
  try {
    const results = await updater.updateTransportCosts();
    
    console.log('\n🎉 TRANSPORT COST UPDATE SUCCESSFUL!');
    console.log('All products now have accurate pricing with transport costs included!');
    
    return results;
    
  } catch (error) {
    console.error('❌ Transport cost update failed:', error);
    process.exit(1);
  }
}

// Run the updater
if (require.main === module) {
  main();
}

module.exports = TransportCostUpdater;
