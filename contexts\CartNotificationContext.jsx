'use client'

import { createContext, useContext, useState } from 'react'
import CartSuccessModal from '@/components/ui/CartSuccessModal'

const CartNotificationContext = createContext()

export function CartNotificationProvider({ children }) {
  const [notification, setNotification] = useState({
    isOpen: false,
    product: null,
    size: null,
    quantity: 1
  })

  const showCartSuccess = (product, size = 'M', quantity = 1) => {
    setNotification({
      isOpen: true,
      product,
      size,
      quantity
    })
  }

  const hideCartSuccess = () => {
    setNotification({
      isOpen: false,
      product: null,
      size: null,
      quantity: 1
    })
  }

  return (
    <CartNotificationContext.Provider value={{ showCartSuccess, hideCartSuccess }}>
      {children}
      <CartSuccessModal
        isOpen={notification.isOpen}
        onClose={hideCartSuccess}
        product={notification.product}
        size={notification.size}
        quantity={notification.quantity}
      />
    </CartNotificationContext.Provider>
  )
}

export function useCartNotification() {
  const context = useContext(CartNotificationContext)
  if (!context) {
    throw new Error('useCartNotification must be used within a CartNotificationProvider')
  }
  return context
}
