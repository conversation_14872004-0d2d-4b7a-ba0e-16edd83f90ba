Below is your Localization Implementation Guide for The White Laces (TWL) — a luxury streetwear e-commerce platform , built with Next.js , Tailwind CSS , and hosted on Vercel , with a Mexico-first strategy .

This guide ensures that your website supports:

✅ Mexican Spanish (es-MX) as default
✅ English (en-US) as secondary
✅ Brazilian Portuguese (pt-BR) for future LATAM expansion
🌍 The White Laces – Localization Implementation Guide
Next.js | i18n | Glassmorphism UI | Mexico-First Strategy
👟 Luxury Footwear Meets Gen Z Culture | E-commerce Platform 

🎯 Goal
To implement multi-language support across the entire TWL platform, ensuring:

🇲🇽 Mexican users see content in Spanish (es-MX) by default
🌐 Users from other regions can switch to English (en-US) or Portuguese (pt-BR)
📱 All components are responsive and culturally relevant
💸 Prices shown in MXN, COP, CLP, USD based on region
🧠 Seamless integration with AI features (voice search, visual search)


🛠️ Tech Stack Overview

Layer,                              Tool
Framework,                          Next.js App Router
i18n Library,                       next-i18next
Translation Files,                  JSON files in /locales
Hosting,                            Vercel
CDN,                                Vercel Edge Network
Performance,                        Lighthouse score >90

/twl-ecommerce
├── /app
│   └── page.jsx
├── /components
│   └── Button.jsx
├── /public
│   └── images
├── /locales
│   ├── es-MX
│   │   └── common.json
│   ├── en-US
│   │   └── common.json
│   └── pt-BR
│       └── common.json
├── next.config.js
├── next-i18next.config.js
├── package.json
└── README.md


🧩 1. Install Localization Library
🔹 Step 1: Install next-i18next

npm install next-i18next react-i18next i18next


🧭 2. Configure next-i18next
🔹 Step 2: Create next-i18next.config.js in root:

module.exports = {
  i18n: {
    defaultLocale: 'es-MX',
    locales: ['es-MX', 'en-US', 'pt-BR'],
  },
  localePath: typeof window === 'undefined' ? require('path').resolve('./public/locales') : '/locales',
};


🧱 3. Set Up Locale Files

🔹 Step 3: Add translation files under /locales/{lang}/common.json
Example: /locales/es-MX/common.json

{
  "welcome": "Bienvenido a The White Laces",
  "add_to_cart": "Añadir al carrito",
  "limited_edition": "Edición Limitada",
  "wishlist": "Lista de deseos",
  "search_placeholder": "Buscar zapatos...",
  "language_switcher": "Cambiar idioma"
}


Example: /locales/en-US/common.json

{
  "welcome": "Welcome to The White Laces",
  "add_to_cart": "Add to Cart",
  "limited_edition": "Limited Edition",
  "wishlist": "Wishlist",
  "search_placeholder": "Search shoes...",
  "language_switcher": "Change language"
}


Example: /locales/pt-BR/common.json

{
  "welcome": "Bem-vindo ao The White Laces",
  "add_to_cart": "Adicionar ao carrinho",
  "limited_edition": "Edição Limitada",
  "wishlist": "Lista de desejos",
  "search_placeholder": "Buscar calçados...",
  "language_switcher": "Mudar idioma"
}


🧠 4. Update next.config.js
Ensure it includes localization settings:

const { i18n } = require('./next-i18next.config');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  swcMinify: true,
  trailingSlash: true,
  i18n,
};

module.exports = nextConfig;


📝 5. Use Translations in Pages
🔹 In any page component:

import { useTranslation } from 'next-i18next';
import { serverSideTranslations } from 'next-i18next/serverSideTranslations';

export default function HomePage() {
  const { t } = useTranslation('common');

  return (
    <main>
      <h1 className="text-2xl font-bold text-white">{t('welcome')}</h1>
      <button className="bg-neon-pulse text-white px-4 py-2 rounded-lg">
        {t('add_to_cart')}
      </button>
    </main>
  );
}

export async function getStaticProps({ locale }) {
  return {
    props: {
      ...(await serverSideTranslations(locale, ['common'])),
    },
  };
}


🌍 6. Language Switcher Component
🔹 Create /components/ui/LanguageSwitcher.jsx

import Link from 'next/link';
import { useRouter } from 'next/router';

export default function LanguageSwitcher() {
  const { locales, asPath } = useRouter();

  return (
    <div className="flex space-x-2">
      {locales.map((locale) => (
        <Link key={locale} href={asPath} locale={locale} legacyBehavior>
          <a className="px-3 py-1 border border-frosted-overlay rounded-full text-sm hover:bg-mist-gray transition">
            {getLocaleLabel(locale)}
          </a>
        </Link>
      ))}
    </div>
  );
}

function getLocaleLabel(locale) {
  switch (locale) {
    case 'es-MX': return '🇲🇽 MX';
    case 'en-US': return '🇬🇧 EN';
    case 'pt-BR': return '🇧🇷 BR';
    default: return locale;
  }
}


💵 7. Currency & Region Logic
🔹 Create /utils/currency.js

export function formatPrice(amount, locale = 'es-MX') {
  const currencyMap = {
    'es-MX': 'MXN',
    'en-US': 'USD',
    'pt-BR': 'BRL',
  };

  const formatter = new Intl.NumberFormat(locale, {
    style: 'currency',
    currency: currencyMap[locale] || 'USD',
  });

  return formatter.format(amount);
}


🔹 Use in ProductCard:

<p className="text-cyber-blue mt-1">
  {formatPrice(180, currentLocale)}
</p>


📦 8. SEO Meta Tags per Language
Use dynamic meta tags for localized SEO:

import Head from 'next/head';
import { useTranslation } from 'next-i18next';

export default function SeoMeta({ title, description }) {
  const { t } = useTranslation('seo');

  return (
    <Head>
      <title>{title || t('default_title')}</title>
      <meta name="description" content={description || t('default_description')} />
      <meta property="og:title" content={title} />
      <meta property="og:description" content={description} />
    </Head>
  );
}

🧪 9. Accessibility & UX Considerations
Ensure all translated strings are:

✅ Screen reader-friendly
✅ Context-aware (e.g., “tenis” vs “calzado”)
✅ Short and meaningful
✅ Culturally appropriate (avoid slang not understood in LATAM)


📊 10. Localization QA Checklist

Task,Status
✅ All pages support es-MX", "en-US", "pt-BR,✔
✅ Language switcher visible in header/footer,✔
✅ Price formatting correct per locale,✔
✅ Images have alt text in local language,✔
✅ No hardcoded strings in JSX,✔
✅ SEO meta tags updated per language,✔
✅ Localized URLs: /es-MX/shop", "/en/shop,✔
✅ Fallback to es-MX if no locale detected,✔
✅ Translation file sync script ready,✔
✅ Test translations locally before deploying,✔


🧰 11. Tools for Localization

Tool,Purpose
Crowdin / Lokalise,Translation management
i18next-parser,Auto-extract translation keys
Figma + Text Export,Design-to-code handoff
Notion Board,Track translation progress
Chrome DevTools,Force language change
Lighthouse,Check accessibility of translated text


📋 12. Sample Localization Tasks

Task,Description
✅ Translate Homepage,"Welcome message, call-to-action buttons"
✅ Translate Shop Page,"Filter labels, product descriptions"
✅ Translate Account Dashboard,"Profile fields, preferences"
✅ Translate UGC Wall,Labels like “Share Your Look”
✅ Translate Magazine Section,"Editorial titles, captions"
✅ Translate Checkout Flow,"Form labels, error messages"
✅ Translate Help Center,"FAQ answers, returns policy"


🧩 13. Integration with AI Features
Make sure AI-powered features like:

Voice Search
Visual Search
Recommendation Engine
Support localized queries and responses.

Example: If user says "Busca zapatillas rojas" → trigger red sneakers filter


🚀 14. Deployment & Preview Support

Vercel Configuration:
Add this to vercel.json:

{
  "regions": ["mexico-city"],
  "rewrites": [
    {
      "source": "/es-MX/:path*",
      "destination": "/:path*?__NEXT_LOCALE=es-MX"
    },
    {
      "source": "/en-US/:path*",
      "destination": "/:path*?__NEXT_LOCALE=en-US"
    },
    {
      "source": "/pt-BR/:path*",
      "destination": "/:path*?__NEXT_LOCALE=pt-BR"
    }
  ]
}


🧪 15. Testing Localizations
🔹 Run Locally:

npm run dev

Visit:

http://localhost:3000/es-MX
http://localhost:3000/en-US
http://localhost:3000/pt-BR


🔹 Unit Tests:

// __tests__/LanguageSwitcher.test.jsx
import { render, screen } from '@testing-library/react';
import LanguageSwitcher from '@/components/ui/LanguageSwitcher';

describe('LanguageSwitcher', () => {
  it('renders all supported languages', () => {
    render(<LanguageSwitcher />);
    expect(screen.getByText('🇲🇽 MX')).toBeInTheDocument();
    expect(screen.getByText('🇬🇧 EN')).toBeInTheDocument();
    expect(screen.getByText('🇧🇷 BR')).toBeInTheDocument();
  });
});

