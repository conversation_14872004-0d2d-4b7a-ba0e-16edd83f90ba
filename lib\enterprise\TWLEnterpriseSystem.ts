/**
 * TWL Enterprise Product System
 * Main entry point for the enterprise-grade product management system
 * 
 * Features:
 * - Unified system interface
 * - Automatic initialization
 * - Performance monitoring
 * - Health checks
 * - Graceful shutdown
 * - Configuration management
 */

import { ProductLoader } from './core/ProductLoader'
import { ProductAPI } from './api/ProductAPI'
import { Logger } from './utils/Logger'
import { TWLProduct } from './models/Product'

/**
 * System Configuration
 */
export interface SystemConfig {
  // Core Settings
  productsBasePath: string
  enableAutoScan: boolean
  scanInterval: number
  
  // Cache Settings
  enableCache: boolean
  cacheMemorySize: number
  cacheFileEnabled: boolean
  cacheRedisEnabled: boolean
  
  // Performance Settings
  enableMetrics: boolean
  enableProfiling: boolean
  maxConcurrentLoads: number
  
  // API Settings
  enableAPI: boolean
  enableRateLimit: boolean
  rateLimitRequests: number
  rateLimitWindow: number
  
  // Monitoring Settings
  enableHealthChecks: boolean
  healthCheckInterval: number
  enableAlerts: boolean
  
  // Environment
  environment: 'development' | 'staging' | 'production'
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}

/**
 * System Health Status
 */
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'unhealthy'
  uptime: number
  version: string
  environment: string
  components: {
    loader: 'healthy' | 'unhealthy'
    cache: 'healthy' | 'unhealthy'
    api: 'healthy' | 'unhealthy'
    scanner: 'healthy' | 'unhealthy'
  }
  metrics: {
    productsLoaded: number
    cacheHitRate: number
    averageResponseTime: number
    errorRate: number
    memoryUsage: number
    cpuUsage: number
  }
  lastChecked: Date
}

/**
 * TWL Enterprise System
 */
export class TWLEnterpriseSystem {
  private config: SystemConfig
  private logger: Logger
  private loader: ProductLoader
  private api: ProductAPI
  private isInitialized: boolean
  private startTime: Date
  private healthCheckTimer?: NodeJS.Timeout

  constructor(config: Partial<SystemConfig> = {}) {
    this.config = {
      // Core defaults
      productsBasePath: process.env.TWL_PRODUCTS_PATH || 'public/products',
      enableAutoScan: process.env.NODE_ENV !== 'development',
      scanInterval: 3600, // 1 hour
      
      // Cache defaults
      enableCache: true,
      cacheMemorySize: 100, // 100MB
      cacheFileEnabled: true,
      cacheRedisEnabled: false,
      
      // Performance defaults
      enableMetrics: true,
      enableProfiling: process.env.NODE_ENV === 'development',
      maxConcurrentLoads: 10,
      
      // API defaults
      enableAPI: true,
      enableRateLimit: process.env.NODE_ENV === 'production',
      rateLimitRequests: 100,
      rateLimitWindow: 60, // 1 minute
      
      // Monitoring defaults
      enableHealthChecks: true,
      healthCheckInterval: 30, // 30 seconds
      enableAlerts: process.env.NODE_ENV === 'production',
      
      // Environment defaults
      environment: (process.env.NODE_ENV as any) || 'development',
      logLevel: process.env.NODE_ENV === 'production' ? 'info' : 'debug',
      
      ...config
    }

    this.logger = new Logger('TWLEnterpriseSystem')
    this.isInitialized = false
    this.startTime = new Date()

    // Initialize components
    this.loader = new ProductLoader({
      productsBasePath: this.config.productsBasePath,
      enableAutoScan: this.config.enableAutoScan,
      scanInterval: this.config.scanInterval,
      enableCache: this.config.enableCache,
      cacheConfig: {
        memoryMaxSize: this.config.cacheMemorySize,
        fileEnabled: this.config.cacheFileEnabled,
        redisEnabled: this.config.cacheRedisEnabled
      },
      enableMetrics: this.config.enableMetrics,
      maxConcurrentLoads: this.config.maxConcurrentLoads
    })

    this.api = new ProductAPI(this.loader)

    this.logger.info('🚀 TWL Enterprise System created', {
      environment: this.config.environment,
      productsPath: this.config.productsBasePath,
      cacheEnabled: this.config.enableCache,
      autoScanEnabled: this.config.enableAutoScan
    })
  }

  /**
   * Initialize the entire system
   */
  async initialize(): Promise<void> {
    try {
      this.logger.info('🔄 Initializing TWL Enterprise System...')

      // Initialize loader (includes scanner and cache)
      await this.loader.initialize()

      // Initialize API
      if (this.config.enableAPI) {
        await this.api.initialize()
      }

      // Start health checks
      if (this.config.enableHealthChecks) {
        this.startHealthChecks()
      }

      this.isInitialized = true
      this.logger.info('✅ TWL Enterprise System initialized successfully', {
        uptime: this.getUptime(),
        environment: this.config.environment
      })

    } catch (error) {
      this.logger.error('❌ Failed to initialize TWL Enterprise System:', error)
      throw error
    }
  }

  /**
   * Get a product by ID
   */
  async getProduct(productId: string): Promise<TWLProduct | null> {
    if (!this.isInitialized) {
      throw new Error('System not initialized')
    }

    const result = await this.loader.loadProduct(productId)
    return result.success ? result.data || null : null
  }

  /**
   * Search products
   */
  async searchProducts(query?: string, filters?: any, page: number = 1, pageSize: number = 20) {
    if (!this.isInitialized) {
      throw new Error('System not initialized')
    }

    const result = await this.loader.searchProducts(query, filters, page, pageSize)
    return result.success ? result.data : null
  }

  /**
   * Get all products
   */
  async getAllProducts(): Promise<TWLProduct[]> {
    if (!this.isInitialized) {
      throw new Error('System not initialized')
    }

    const result = await this.loader.getAllProducts()
    return result.success ? result.data || [] : []
  }

  /**
   * Trigger manual scan
   */
  async triggerScan(): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('System not initialized')
    }

    await this.loader.performFullScan()
  }

  /**
   * Get system health status
   */
  getHealth(): SystemHealth {
    const loaderStatus = this.loader.getSystemStatus()
    
    return {
      status: this.isInitialized ? 'healthy' : 'unhealthy',
      uptime: this.getUptime(),
      version: '1.0.0',
      environment: this.config.environment,
      components: {
        loader: loaderStatus.isReady ? 'healthy' : 'unhealthy',
        cache: loaderStatus.cacheStatus.enabled ? 'healthy' : 'unhealthy',
        api: this.config.enableAPI ? 'healthy' : 'unhealthy',
        scanner: loaderStatus.isReady ? 'healthy' : 'unhealthy'
      },
      metrics: {
        productsLoaded: loaderStatus.productsLoaded,
        cacheHitRate: loaderStatus.cacheStatus.hitRate,
        averageResponseTime: loaderStatus.performance.averageLoadTime,
        errorRate: loaderStatus.performance.errorRate,
        memoryUsage: loaderStatus.cacheStatus.memoryUsage,
        cpuUsage: 0 // Would implement CPU monitoring
      },
      lastChecked: new Date()
    }
  }

  /**
   * Get system metrics
   */
  getMetrics() {
    const health = this.getHealth()
    const loaderStatus = this.loader.getSystemStatus()

    return {
      system: {
        uptime: health.uptime,
        status: health.status,
        environment: this.config.environment
      },
      products: {
        total: loaderStatus.productsLoaded,
        lastScan: loaderStatus.lastScan
      },
      cache: {
        enabled: loaderStatus.cacheStatus.enabled,
        hitRate: loaderStatus.cacheStatus.hitRate,
        memoryUsage: loaderStatus.cacheStatus.memoryUsage,
        fileUsage: loaderStatus.cacheStatus.fileUsage
      },
      performance: {
        averageResponseTime: loaderStatus.performance.averageLoadTime,
        totalRequests: loaderStatus.performance.totalRequests,
        errorRate: loaderStatus.performance.errorRate
      }
    }
  }

  /**
   * Get API instance for Next.js route handlers
   */
  getAPI(): ProductAPI {
    return this.api
  }

  /**
   * Graceful shutdown
   */
  async shutdown(): Promise<void> {
    try {
      this.logger.info('🔄 Shutting down TWL Enterprise System...')

      // Stop health checks
      if (this.healthCheckTimer) {
        clearInterval(this.healthCheckTimer)
      }

      // Shutdown loader
      await this.loader.shutdown()

      this.isInitialized = false
      this.logger.info('✅ TWL Enterprise System shutdown complete')

    } catch (error) {
      this.logger.error('❌ Error during shutdown:', error)
      throw error
    }
  }

  /**
   * Check if system is ready
   */
  isReady(): boolean {
    return this.isInitialized
  }

  /**
   * Get system configuration
   */
  getConfig(): SystemConfig {
    return { ...this.config }
  }

  // Private methods
  private getUptime(): number {
    return Date.now() - this.startTime.getTime()
  }

  private startHealthChecks(): void {
    this.healthCheckTimer = setInterval(() => {
      const health = this.getHealth()
      
      if (health.status !== 'healthy') {
        this.logger.warn('System health check failed', health)
        
        if (this.config.enableAlerts) {
          // Would send alerts here
        }
      }
      
    }, this.config.healthCheckInterval * 1000)

    this.logger.info(`Health checks started (interval: ${this.config.healthCheckInterval}s)`)
  }
}

// Singleton instance for the application
let systemInstance: TWLEnterpriseSystem | null = null

/**
 * Get or create the singleton system instance
 */
export function getTWLSystem(config?: Partial<SystemConfig>): TWLEnterpriseSystem {
  if (!systemInstance) {
    systemInstance = new TWLEnterpriseSystem(config)
  }
  return systemInstance
}

/**
 * Initialize the system (call this in your app startup)
 */
export async function initializeTWLSystem(config?: Partial<SystemConfig>): Promise<TWLEnterpriseSystem> {
  const system = getTWLSystem(config)
  
  if (!system.isReady()) {
    await system.initialize()
  }
  
  return system
}

/**
 * Shutdown the system (call this in your app cleanup)
 */
export async function shutdownTWLSystem(): Promise<void> {
  if (systemInstance) {
    await systemInstance.shutdown()
    systemInstance = null
  }
}

export default TWLEnterpriseSystem
