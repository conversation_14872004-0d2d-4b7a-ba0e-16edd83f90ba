<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TWL Cart Functionality Test</title>
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background: #14161A;
            color: #FAFAFA;
            padding: 20px;
            line-height: 1.6;
        }
        .test-container {
            max-width: 800px;
            margin: 0 auto;
            background: rgba(255,255,255,0.08);
            backdrop-filter: blur(12px);
            border: 1px solid rgba(255,255,255,0.2);
            border-radius: 16px;
            padding: 30px;
        }
        .test-button {
            background: #BFFF00;
            color: #000;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-weight: 600;
            transition: all 0.2s ease;
        }
        .test-button:hover {
            background: #9FDF00;
            transform: translateY(-2px);
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-family: 'Fira Code', monospace;
            font-size: 14px;
            background: rgba(191, 255, 0, 0.2);
            border: 1px solid #BFFF00;
        }
        .instructions {
            background: rgba(255, 209, 102, 0.2);
            border: 1px solid #FFD166;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🛒 TWL Cart Functionality Test</h1>
        <p>Test cart functionality with real product data and Mexican peso pricing</p>
        
        <div class="instructions">
            <h3>📋 Test Instructions</h3>
            <p>1. Click the buttons below to test cart functionality</p>
            <p>2. Check the browser console for detailed logs</p>
            <p>3. Verify that real product images and Mexican peso pricing are working</p>
            <p>4. Open the cart page to see the results: <a href="http://localhost:3001/cart" target="_blank" style="color: #BFFF00;">Cart Page</a></p>
        </div>

        <div class="test-section">
            <h3>🧪 Cart Tests</h3>
            <button class="test-button" onclick="testAddRealProduct()">Add Real Product to Cart</button>
            <button class="test-button" onclick="testCartPersistence()">Test Cart Persistence</button>
            <button class="test-button" onclick="testMexicanPricing()">Test Mexican Peso Pricing</button>
            <button class="test-button" onclick="clearCart()">Clear Cart</button>
            <div id="test-results" class="test-result" style="display: none;"></div>
        </div>
    </div>

    <script>
        function logResult(message) {
            const resultsDiv = document.getElementById('test-results');
            resultsDiv.style.display = 'block';
            resultsDiv.innerHTML += `<div>${new Date().toLocaleTimeString()}: ${message}</div>`;
            console.log(message);
        }

        function testAddRealProduct() {
            logResult('🧪 Testing Add Real Product to Cart...');
            
            // Simulate adding the Nike x Gucci Air Force product
            const testProduct = {
                id: 'sneakers-nike-mixte-air-force-bd7700-222',
                name: 'NIKE Limited Edition AIR FORCE',
                brand: 'Nike x Gucci',
                images: [
                    '/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- GUCCI/o_1hfi0lgi514331ru41hu4km31qsp47.webp',
                    '/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/1. GUCCI/BD7700-222 -- GUCCI/o_1hfi0lgi61ad617f41o9k1peh1uq548.webp'
                ],
                price: 3570, // Mexican pesos
                originalPrice: 4760, // Mexican pesos
                currency: 'MXN'
            };

            // Create cart item
            const cartItem = {
                id: `${testProduct.id}-9`,
                productId: testProduct.id,
                name: testProduct.name,
                brand: testProduct.brand,
                image: testProduct.images[0],
                size: '9',
                quantity: 1,
                price: testProduct.price,
                originalPrice: testProduct.originalPrice,
                addedAt: new Date().toISOString()
            };

            // Get existing cart or create new one
            let cart = JSON.parse(localStorage.getItem('twl-cart') || '{"items": [], "total": 0, "updatedAt": ""}');
            
            // Add item to cart
            cart.items.push(cartItem);
            cart.total = cart.items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            cart.updatedAt = new Date().toISOString();

            // Save to localStorage
            localStorage.setItem('twl-cart', JSON.stringify(cart));

            logResult(`✅ Added product to cart: ${testProduct.name}`);
            logResult(`💰 Price: $${testProduct.price.toLocaleString('es-MX')} MXN`);
            logResult(`🖼️ Image: ${testProduct.images[0]}`);
            logResult(`📦 Cart total items: ${cart.items.length}`);
            logResult(`💵 Cart total: $${cart.total.toLocaleString('es-MX')} MXN`);
        }

        function testCartPersistence() {
            logResult('🧪 Testing Cart Persistence...');
            
            const cartData = localStorage.getItem('twl-cart');
            if (cartData) {
                const cart = JSON.parse(cartData);
                logResult(`✅ Cart found in localStorage`);
                logResult(`📦 Items: ${cart.items.length}`);
                logResult(`💵 Total: $${cart.total?.toLocaleString('es-MX') || 0} MXN`);
                
                cart.items.forEach((item, index) => {
                    logResult(`   ${index + 1}. ${item.name} - Size ${item.size} - $${item.price?.toLocaleString('es-MX')} MXN`);
                    logResult(`      Image: ${item.image}`);
                });
            } else {
                logResult('❌ No cart data found in localStorage');
            }
        }

        function testMexicanPricing() {
            logResult('🧪 Testing Mexican Peso Pricing...');

            // Use ACTUAL supplier costs from real products
            const testPrices = [
                {
                    product: 'BD7700-222 (Nike x Gucci)',
                    supplierUSD: 35,
                    expectedMXN: 3570,
                    description: 'Real product from Description.txt: 💰250 -- 35$'
                },
                {
                    product: 'Generic Product 1',
                    supplierUSD: 20,
                    expectedMXN: 2805,
                    description: 'Lower-tier product calculation'
                },
                {
                    product: 'Generic Product 2',
                    supplierUSD: 50,
                    expectedMXN: 4335,
                    description: 'Higher-tier product calculation'
                }
            ];

            testPrices.forEach(test => {
                const usdToMxnRate = 17;
                const transportCost = 35;
                const totalCostUSD = test.supplierUSD + transportCost;
                const totalCostMXN = totalCostUSD * usdToMxnRate;
                const premiumRetailMXN = Math.round(totalCostMXN * 3.0);

                logResult(`🏷️ ${test.product}:`);
                logResult(`   📋 ${test.description}`);
                logResult(`   💰 Supplier: $${test.supplierUSD} USD + $${transportCost} transport = $${totalCostUSD} USD`);
                logResult(`   💱 Convert: $${totalCostUSD} USD × ${usdToMxnRate} = $${totalCostMXN.toLocaleString('es-MX')} MXN`);
                logResult(`   🏪 Retail: $${totalCostMXN.toLocaleString('es-MX')} MXN × 3.0 = $${premiumRetailMXN.toLocaleString('es-MX')} MXN`);

                if (Math.abs(premiumRetailMXN - test.expectedMXN) < 100) {
                    logResult(`   ✅ Pricing calculation CORRECT (Expected: $${test.expectedMXN.toLocaleString('es-MX')} MXN)`);
                } else {
                    logResult(`   ❌ Expected $${test.expectedMXN.toLocaleString('es-MX')} MXN, got $${premiumRetailMXN.toLocaleString('es-MX')} MXN`);
                }
                logResult('---');
            });
        }

        function clearCart() {
            logResult('🧪 Clearing Cart...');
            localStorage.removeItem('twl-cart');
            logResult('✅ Cart cleared from localStorage');
        }

        // Initialize
        logResult('🚀 Cart Functionality Test initialized');
        logResult('📍 Ready to test cart with real product data and Mexican peso pricing');
    </script>
</body>
</html>
