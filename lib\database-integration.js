// Database Integration for TWL - Ensures data consistency and validation
import { loadRealProduct } from './real-products-loader.js'
import { getAllProductIds, productExists } from './product-catalog.js'
import { validateProductId } from './product-id-generator.js'

/**
 * Database integration layer that ensures data consistency
 * and provides validation for all product operations
 */

// Product data validation schema
const PRODUCT_SCHEMA = {
  required: ['id', 'name', 'brand', 'price', 'images'],
  optional: ['description', 'sizes', 'features', 'materials', 'videos', 'models'],
  types: {
    id: 'string',
    name: 'string',
    brand: 'string',
    price: 'number',
    images: 'array',
    videos: 'array',
    models: 'array',
    sizes: 'array',
    features: 'array',
    materials: 'array'
  }
}

/**
 * Validate product data against schema
 * @param {object} product - Product data to validate
 * @returns {object} Validation result with errors if any
 */
export const validateProductData = (product) => {
  const errors = []
  const warnings = []

  if (!product) {
    return { valid: false, errors: ['Product data is null or undefined'] }
  }

  // Check required fields
  PRODUCT_SCHEMA.required.forEach(field => {
    if (!product[field]) {
      errors.push(`Missing required field: ${field}`)
    }
  })

  // Check data types
  Object.keys(PRODUCT_SCHEMA.types).forEach(field => {
    if (product[field] !== undefined) {
      const expectedType = PRODUCT_SCHEMA.types[field]
      const actualType = Array.isArray(product[field]) ? 'array' : typeof product[field]
      
      if (actualType !== expectedType) {
        errors.push(`Field ${field} should be ${expectedType}, got ${actualType}`)
      }
    }
  })

  // Check array fields are not empty
  ['images', 'sizes'].forEach(field => {
    if (product[field] && Array.isArray(product[field]) && product[field].length === 0) {
      warnings.push(`Field ${field} is empty array`)
    }
  })

  // Validate product ID format
  if (product.id && !validateProductId(product.id)) {
    errors.push(`Invalid product ID format: ${product.id}`)
  }

  // Check price is positive
  if (product.price && product.price <= 0) {
    errors.push(`Price must be positive, got: ${product.price}`)
  }

  return {
    valid: errors.length === 0,
    errors,
    warnings
  }
}

/**
 * Enhanced product loader with validation and error handling
 * @param {string} productId - Product ID to load
 * @returns {object} Product data with validation status
 */
export const loadValidatedProduct = async (productId) => {
  console.log('🔍 Loading validated product:', productId)

  try {
    // Validate product ID format
    if (!validateProductId(productId)) {
      return {
        success: false,
        error: 'Invalid product ID format',
        product: null
      }
    }

    // Check if product exists in catalog
    if (!productExists(productId)) {
      console.log('⚠️ Product not found in catalog:', productId)
      // Still try to load from real products loader as fallback
    }

    // Load product data
    const product = await loadRealProduct(productId)

    if (!product) {
      return {
        success: false,
        error: 'Product not found',
        product: null
      }
    }

    // Validate product data
    const validation = validateProductData(product)

    if (!validation.valid) {
      console.error('❌ Product validation failed:', validation.errors)
      return {
        success: false,
        error: 'Product data validation failed',
        validationErrors: validation.errors,
        product: product // Return product anyway for debugging
      }
    }

    if (validation.warnings.length > 0) {
      console.warn('⚠️ Product validation warnings:', validation.warnings)
    }

    console.log('✅ Product loaded and validated successfully')
    return {
      success: true,
      product: product,
      warnings: validation.warnings
    }

  } catch (error) {
    console.error('❌ Error loading product:', error)
    return {
      success: false,
      error: error.message,
      product: null
    }
  }
}

/**
 * Batch product validation for catalog integrity
 * @param {array} productIds - Array of product IDs to validate
 * @returns {object} Validation summary
 */
export const validateProductCatalog = async (productIds = null) => {
  console.log('🔍 Starting catalog validation...')

  const idsToValidate = productIds || getAllProductIds().slice(0, 10) // Limit to 10 for testing
  const results = {
    total: idsToValidate.length,
    valid: 0,
    invalid: 0,
    errors: [],
    warnings: []
  }

  for (const productId of idsToValidate) {
    try {
      const result = await loadValidatedProduct(productId)
      
      if (result.success) {
        results.valid++
        if (result.warnings && result.warnings.length > 0) {
          results.warnings.push({
            productId,
            warnings: result.warnings
          })
        }
      } else {
        results.invalid++
        results.errors.push({
          productId,
          error: result.error,
          validationErrors: result.validationErrors
        })
      }
    } catch (error) {
      results.invalid++
      results.errors.push({
        productId,
        error: error.message
      })
    }
  }

  console.log(`✅ Catalog validation complete: ${results.valid}/${results.total} valid`)
  return results
}

/**
 * Product data enrichment - adds computed fields and metadata
 * @param {object} product - Base product data
 * @returns {object} Enriched product data
 */
export const enrichProductData = (product) => {
  if (!product) return product

  const enriched = { ...product }

  // Add computed fields
  enriched.hasDiscount = product.originalPrice && product.originalPrice > product.price
  enriched.discountPercentage = enriched.hasDiscount 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  // Add media counts
  enriched.imageCount = product.images ? product.images.length : 0
  enriched.videoCount = product.videos ? product.videos.length : 0
  enriched.totalMediaCount = enriched.imageCount + enriched.videoCount

  // Add model information
  enriched.hasVariants = product.models && product.models.length > 1
  enriched.variantCount = product.models ? product.models.length : 0

  // Add availability status
  enriched.isAvailable = product.inStock && product.stockCount > 0
  enriched.stockStatus = enriched.isAvailable ? 'in-stock' : 'out-of-stock'

  // Add category information
  enriched.categoryInfo = {
    isLimitedEdition: product.limitedEdition || product.isLimited,
    isCollaboration: product.collaboration && product.collaboration !== '',
    isPremium: product.price > 200 // Consider products over $200 as premium
  }

  // Add SEO-friendly URL
  enriched.url = `/product/${product.id}`
  enriched.canonicalUrl = `https://thewhitelaces.com/product/${product.id}`

  // Add timestamps
  enriched.lastUpdated = new Date().toISOString()

  return enriched
}

/**
 * Search products with enhanced filtering
 * @param {object} filters - Search filters
 * @returns {array} Filtered products
 */
export const searchProducts = async (filters = {}) => {
  console.log('🔍 Searching products with filters:', filters)

  try {
    // For now, get a sample of products
    const sampleIds = getAllProductIds().slice(0, 20)
    const products = []

    for (const productId of sampleIds) {
      const result = await loadValidatedProduct(productId)
      if (result.success) {
        const enrichedProduct = enrichProductData(result.product)
        products.push(enrichedProduct)
      }
    }

    // Apply filters
    let filteredProducts = products

    if (filters.category) {
      filteredProducts = filteredProducts.filter(p => 
        p.category === filters.category.toLowerCase()
      )
    }

    if (filters.brand) {
      filteredProducts = filteredProducts.filter(p => 
        p.brand.toLowerCase().includes(filters.brand.toLowerCase())
      )
    }

    if (filters.priceMin) {
      filteredProducts = filteredProducts.filter(p => p.price >= filters.priceMin)
    }

    if (filters.priceMax) {
      filteredProducts = filteredProducts.filter(p => p.price <= filters.priceMax)
    }

    if (filters.inStock) {
      filteredProducts = filteredProducts.filter(p => p.isAvailable)
    }

    if (filters.limitedEdition) {
      filteredProducts = filteredProducts.filter(p => p.categoryInfo.isLimitedEdition)
    }

    console.log(`✅ Search complete: ${filteredProducts.length} products found`)
    return filteredProducts

  } catch (error) {
    console.error('❌ Search error:', error)
    return []
  }
}

/**
 * Get product recommendations based on a product
 * @param {string} productId - Base product ID
 * @param {number} limit - Number of recommendations
 * @returns {array} Recommended products
 */
export const getProductRecommendations = async (productId, limit = 4) => {
  console.log('🔍 Getting recommendations for:', productId)

  try {
    const baseProduct = await loadValidatedProduct(productId)
    if (!baseProduct.success) return []

    // Get products from same category/brand
    const filters = {
      category: baseProduct.product.category,
      brand: baseProduct.product.brand
    }

    const similarProducts = await searchProducts(filters)
    
    // Remove the base product and limit results
    const recommendations = similarProducts
      .filter(p => p.id !== productId)
      .slice(0, limit)

    console.log(`✅ Found ${recommendations.length} recommendations`)
    return recommendations

  } catch (error) {
    console.error('❌ Recommendations error:', error)
    return []
  }
}

export default {
  validateProductData,
  loadValidatedProduct,
  validateProductCatalog,
  enrichProductData,
  searchProducts,
  getProductRecommendations
}
