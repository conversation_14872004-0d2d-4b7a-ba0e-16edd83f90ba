import SimpleProductCard from '@/components/ui/SimpleProductCard'
import { ProductService } from '@/lib/services/ProductService'

// Simple Category Mapping
const CATEGORIES = {
  'sneakers': { name: 'Sneakers', id: 'sneakers' },
  'sandals': { name: '<PERSON><PERSON><PERSON>', id: 'sandals' },
  'formal': { name: 'Formal', id: 'formal' },
  'casual': { name: 'Casual', id: 'casual' },
  'kids': { name: '<PERSON><PERSON><PERSON>', id: 'kids' }
}

async function getProducts(categorySlug) {
  console.log('🔥🔥🔥 SERVER-SIDE getProducts called for:', categorySlug)

  try {
    // Use ProductService for clean, transformed data
    const result = await ProductService.getProductsByCategory(categorySlug, {
      pageSize: 20,
      sortBy: 'name',
      sortOrder: 'asc'
    })

    console.log('📦 SERVER-SIDE ProductService result:', {
      productsCount: result.products.length,
      total: result.total,
      hasMore: result.hasMore
    })

    console.log(`✅ SERVER-SIDE Loaded ${result.products.length} products for ${categorySlug}`)
    return result.products

  } catch (error) {
    console.error('❌ SERVER-SIDE Error loading products:', error)
    return []
  }
}

export default async function CategoryPage({ params }) {
  console.log('🚀🚀🚀🚀🚀 CATEGORY PAGE COMPONENT EXECUTING!')
  console.log('🚀🚀🚀 RUNNING ON:', typeof window === 'undefined' ? 'SERVER' : 'CLIENT')

  const categorySlug = params.category
  const categoryInfo = CATEGORIES[categorySlug]

  console.log('🚀🚀🚀 CATEGORY SLUG:', categorySlug)
  console.log('🚀🚀🚀 CATEGORY INFO:', categoryInfo)

  // Load products server-side
  const products = await getProducts(categorySlug)
  console.log('🚀🚀🚀 SERVER-SIDE PRODUCTS LOADED:', products.length, 'products')

  if (!categoryInfo) {
    return (
      <div className="min-h-screen bg-fog-black text-white flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">Categoría no encontrada</h1>
          <p className="text-gray-400">La categoría "{categorySlug}" no existe.</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-fog-black text-white">
      {/* Header */}
      <div className="bg-mist-gray border-b border-frosted-overlay">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <h1 className="text-3xl font-bold text-lime-green">{categoryInfo.name}</h1>
          <p className="text-gray-400 mt-2">
            {products.length} productos encontrados
          </p>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-8">
        {products.length === 0 ? (
          <div className="text-center py-12">
            <p className="text-gray-400 mb-4">No se encontraron productos en esta categoría</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {products.map((product) => (
              <SimpleProductCard key={product.id} product={product} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}

