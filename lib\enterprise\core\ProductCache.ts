/**
 * TWL Enterprise Product Cache System
 * Multi-layer caching with memory, file, and optional Redis support
 * 
 * Features:
 * - LRU Memory Cache (Layer 1)
 * - File-based Cache (Layer 2)
 * - Redis Cache (Layer 3 - Optional)
 * - Cache invalidation strategies
 * - Performance metrics
 * - Automatic cleanup
 */

import fs from 'fs'
import path from 'path'
import crypto from 'crypto'
import { TWLProduct } from '../models/Product'
import { Logger } from '../utils/Logger'
import { FileUtils } from '../utils/FileUtils'

/**
 * Cache Configuration
 */
export interface CacheConfig {
  // Memory Cache (Layer 1)
  memoryMaxSize: number        // Max memory cache size in MB
  memoryTTL: number           // TTL in seconds
  memoryMaxItems: number      // Max items in memory
  
  // File Cache (Layer 2)
  fileEnabled: boolean        // Enable file cache
  fileCacheDir: string       // Cache directory
  fileTTL: number            // File cache TTL in seconds
  fileMaxSize: number        // Max file cache size in MB
  
  // Redis Cache (Layer 3)
  redisEnabled: boolean      // Enable Redis cache
  redisUrl?: string          // Redis connection URL
  redisTTL: number          // Redis TTL in seconds
  
  // General
  enableMetrics: boolean     // Enable performance metrics
  enableCompression: boolean // Enable cache compression
  autoCleanup: boolean      // Enable automatic cleanup
  cleanupInterval: number   // Cleanup interval in seconds
}

/**
 * Cache Entry
 */
interface CacheEntry<T = any> {
  key: string
  data: T
  timestamp: number
  ttl: number
  hits: number
  size: number
  checksum: string
  compressed: boolean
}

/**
 * Cache Metrics
 */
export interface CacheMetrics {
  // Hit/Miss Statistics
  memoryHits: number
  memoryMisses: number
  fileHits: number
  fileMisses: number
  redisHits: number
  redisMisses: number
  
  // Performance
  averageResponseTime: number
  totalRequests: number
  
  // Storage
  memoryUsage: number        // MB
  fileUsage: number         // MB
  redisUsage: number        // MB
  
  // Items
  memoryItems: number
  fileItems: number
  redisItems: number
  
  // Cache Efficiency
  overallHitRate: number    // Percentage
  memoryHitRate: number     // Percentage
  fileHitRate: number       // Percentage
  redisHitRate: number      // Percentage
}

/**
 * LRU Cache Implementation
 */
class LRUCache<T> {
  private cache: Map<string, CacheEntry<T>>
  private maxSize: number
  private maxItems: number
  private defaultTTL: number

  constructor(maxSize: number, maxItems: number, defaultTTL: number) {
    this.cache = new Map()
    this.maxSize = maxSize * 1024 * 1024 // Convert MB to bytes
    this.maxItems = maxItems
    this.defaultTTL = defaultTTL
  }

  get(key: string): T | null {
    const entry = this.cache.get(key)
    
    if (!entry) {
      return null
    }

    // Check TTL
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key)
      return null
    }

    // Update hit count and move to end (most recently used)
    entry.hits++
    this.cache.delete(key)
    this.cache.set(key, entry)

    return entry.data
  }

  set(key: string, data: T, ttl?: number): void {
    const size = this.calculateSize(data)
    const entry: CacheEntry<T> = {
      key,
      data,
      timestamp: Date.now(),
      ttl: ttl || this.defaultTTL,
      hits: 0,
      size,
      checksum: this.calculateChecksum(data),
      compressed: false
    }

    // Remove existing entry if it exists
    if (this.cache.has(key)) {
      this.cache.delete(key)
    }

    // Evict items if necessary
    this.evictIfNecessary(size)

    // Add new entry
    this.cache.set(key, entry)
  }

  delete(key: string): boolean {
    return this.cache.delete(key)
  }

  clear(): void {
    this.cache.clear()
  }

  has(key: string): boolean {
    const entry = this.cache.get(key)
    if (!entry) return false

    // Check TTL
    if (Date.now() - entry.timestamp > entry.ttl * 1000) {
      this.cache.delete(key)
      return false
    }

    return true
  }

  size(): number {
    return this.cache.size
  }

  getMemoryUsage(): number {
    let totalSize = 0
    Array.from(this.cache.values()).forEach(entry => {
      totalSize += entry.size
    })
    return totalSize
  }

  getStats(): { items: number; memoryUsage: number; hitRate: number } {
    let totalHits = 0
    let totalRequests = 0

    Array.from(this.cache.values()).forEach(entry => {
      totalHits += entry.hits
      totalRequests += entry.hits + 1 // +1 for the initial set
    })

    return {
      items: this.cache.size,
      memoryUsage: this.getMemoryUsage() / (1024 * 1024), // Convert to MB
      hitRate: totalRequests > 0 ? (totalHits / totalRequests) * 100 : 0
    }
  }

  private evictIfNecessary(newItemSize: number): void {
    // Evict by item count
    while (this.cache.size >= this.maxItems) {
      const firstKey = this.cache.keys().next().value
      if (firstKey) {
        this.cache.delete(firstKey)
      }
    }

    // Evict by memory size
    while (this.getMemoryUsage() + newItemSize > this.maxSize && this.cache.size > 0) {
      const firstKey = this.cache.keys().next().value
      if (firstKey) {
        this.cache.delete(firstKey)
      }
    }
  }

  private calculateSize(data: any): number {
    return JSON.stringify(data).length * 2 // Rough estimate (UTF-16)
  }

  private calculateChecksum(data: any): string {
    return crypto.createHash('md5').update(JSON.stringify(data)).digest('hex')
  }
}

/**
 * Enterprise Product Cache
 */
export class ProductCache {
  private config: CacheConfig
  private logger: Logger
  private memoryCache: LRUCache<TWLProduct>
  private metrics: CacheMetrics
  private cleanupTimer?: NodeJS.Timeout

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      // Memory Cache Defaults
      memoryMaxSize: 100,        // 100MB
      memoryTTL: 3600,          // 1 hour
      memoryMaxItems: 1000,     // 1000 items
      
      // File Cache Defaults
      fileEnabled: true,
      fileCacheDir: path.join(process.cwd(), '.cache', 'products'),
      fileTTL: 86400,           // 24 hours
      fileMaxSize: 500,         // 500MB
      
      // Redis Cache Defaults
      redisEnabled: false,
      redisTTL: 604800,         // 7 days
      
      // General Defaults
      enableMetrics: true,
      enableCompression: false,
      autoCleanup: true,
      cleanupInterval: 3600,    // 1 hour
      
      ...config
    }

    this.logger = new Logger('ProductCache')
    this.memoryCache = new LRUCache(
      this.config.memoryMaxSize,
      this.config.memoryMaxItems,
      this.config.memoryTTL
    )

    this.metrics = this.initializeMetrics()

    // Initialize file cache directory
    if (this.config.fileEnabled) {
      this.initializeFileCache()
    }

    // Start automatic cleanup
    if (this.config.autoCleanup) {
      this.startAutoCleanup()
    }

    this.logger.info('🚀 Enterprise Product Cache initialized', {
      memoryMaxSize: this.config.memoryMaxSize,
      fileEnabled: this.config.fileEnabled,
      redisEnabled: this.config.redisEnabled
    })
  }

  /**
   * Get product from cache (checks all layers)
   */
  async get(key: string): Promise<TWLProduct | null> {
    const startTime = Date.now()
    this.metrics.totalRequests++

    try {
      // Layer 1: Memory Cache
      const memoryResult = this.memoryCache.get(key)
      if (memoryResult) {
        this.metrics.memoryHits++
        this.updateResponseTime(Date.now() - startTime)
        this.logger.debug(`Cache HIT (Memory): ${key}`)
        return memoryResult
      }
      this.metrics.memoryMisses++

      // Layer 2: File Cache
      if (this.config.fileEnabled) {
        const fileResult = await this.getFromFileCache(key)
        if (fileResult) {
          this.metrics.fileHits++
          // Promote to memory cache
          this.memoryCache.set(key, fileResult)
          this.updateResponseTime(Date.now() - startTime)
          this.logger.debug(`Cache HIT (File): ${key}`)
          return fileResult
        }
        this.metrics.fileMisses++
      }

      // Layer 3: Redis Cache (if enabled)
      if (this.config.redisEnabled) {
        const redisResult = await this.getFromRedisCache(key)
        if (redisResult) {
          this.metrics.redisHits++
          // Promote to memory and file cache
          this.memoryCache.set(key, redisResult)
          if (this.config.fileEnabled) {
            await this.setToFileCache(key, redisResult)
          }
          this.updateResponseTime(Date.now() - startTime)
          this.logger.debug(`Cache HIT (Redis): ${key}`)
          return redisResult
        }
        this.metrics.redisMisses++
      }

      this.updateResponseTime(Date.now() - startTime)
      this.logger.debug(`Cache MISS: ${key}`)
      return null

    } catch (error) {
      this.logger.error(`Cache get error for key ${key}:`, error)
      return null
    }
  }

  /**
   * Set product in cache (all layers)
   */
  async set(key: string, product: TWLProduct, ttl?: number): Promise<void> {
    try {
      // Layer 1: Memory Cache
      this.memoryCache.set(key, product, ttl)

      // Layer 2: File Cache
      if (this.config.fileEnabled) {
        await this.setToFileCache(key, product, ttl)
      }

      // Layer 3: Redis Cache
      if (this.config.redisEnabled) {
        await this.setToRedisCache(key, product, ttl)
      }

      this.logger.debug(`Cache SET: ${key}`)

    } catch (error) {
      this.logger.error(`Cache set error for key ${key}:`, error)
    }
  }

  /**
   * Delete from all cache layers
   */
  async delete(key: string): Promise<void> {
    try {
      // Memory cache
      this.memoryCache.delete(key)

      // File cache
      if (this.config.fileEnabled) {
        await this.deleteFromFileCache(key)
      }

      // Redis cache
      if (this.config.redisEnabled) {
        await this.deleteFromRedisCache(key)
      }

      this.logger.debug(`Cache DELETE: ${key}`)

    } catch (error) {
      this.logger.error(`Cache delete error for key ${key}:`, error)
    }
  }

  /**
   * Clear all caches
   */
  async clear(): Promise<void> {
    try {
      // Memory cache
      this.memoryCache.clear()

      // File cache
      if (this.config.fileEnabled) {
        await this.clearFileCache()
      }

      // Redis cache
      if (this.config.redisEnabled) {
        await this.clearRedisCache()
      }

      this.logger.info('All caches cleared')

    } catch (error) {
      this.logger.error('Cache clear error:', error)
    }
  }

  /**
   * Get cache metrics
   */
  getMetrics(): CacheMetrics {
    const memoryStats = this.memoryCache.getStats()
    
    // Update current metrics
    this.metrics.memoryItems = memoryStats.items
    this.metrics.memoryUsage = memoryStats.memoryUsage
    this.metrics.memoryHitRate = memoryStats.hitRate

    // Calculate overall hit rate
    const totalHits = this.metrics.memoryHits + this.metrics.fileHits + this.metrics.redisHits
    const totalMisses = this.metrics.memoryMisses + this.metrics.fileMisses + this.metrics.redisMisses
    this.metrics.overallHitRate = totalHits + totalMisses > 0 ? (totalHits / (totalHits + totalMisses)) * 100 : 0

    // Calculate individual hit rates
    this.metrics.fileHitRate = this.metrics.fileHits + this.metrics.fileMisses > 0 ? 
      (this.metrics.fileHits / (this.metrics.fileHits + this.metrics.fileMisses)) * 100 : 0
    
    this.metrics.redisHitRate = this.metrics.redisHits + this.metrics.redisMisses > 0 ? 
      (this.metrics.redisHits / (this.metrics.redisHits + this.metrics.redisMisses)) * 100 : 0

    return { ...this.metrics }
  }

  /**
   * Cleanup expired entries
   */
  async cleanup(): Promise<void> {
    try {
      this.logger.info('Starting cache cleanup...')

      // File cache cleanup
      if (this.config.fileEnabled) {
        await this.cleanupFileCache()
      }

      // Redis cleanup is handled automatically by Redis TTL

      this.logger.info('Cache cleanup completed')

    } catch (error) {
      this.logger.error('Cache cleanup error:', error)
    }
  }

  /**
   * Shutdown cache system
   */
  async shutdown(): Promise<void> {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer)
    }

    this.logger.info('Product cache shutdown')
  }

  // Private methods for file cache operations
  private async getFromFileCache(key: string): Promise<TWLProduct | null> {
    try {
      const filePath = this.getFileCachePath(key)
      const data = await FileUtils.readJsonFile<CacheEntry<TWLProduct>>(filePath)
      
      if (!data) return null

      // Check TTL
      if (Date.now() - data.timestamp > data.ttl * 1000) {
        await FileUtils.deleteFile(filePath)
        return null
      }

      return data.data
    } catch {
      return null
    }
  }

  private async setToFileCache(key: string, product: TWLProduct, ttl?: number): Promise<void> {
    try {
      const filePath = this.getFileCachePath(key)
      const entry: CacheEntry<TWLProduct> = {
        key,
        data: product,
        timestamp: Date.now(),
        ttl: ttl || this.config.fileTTL,
        hits: 0,
        size: JSON.stringify(product).length,
        checksum: crypto.createHash('md5').update(JSON.stringify(product)).digest('hex'),
        compressed: false
      }

      await FileUtils.writeJsonFile(filePath, entry)
    } catch (error) {
      this.logger.error(`File cache set error for ${key}:`, error)
    }
  }

  private async deleteFromFileCache(key: string): Promise<void> {
    try {
      const filePath = this.getFileCachePath(key)
      if (await FileUtils.fileExists(filePath)) {
        await FileUtils.deleteFile(filePath)
      }
    } catch (error) {
      this.logger.error(`File cache delete error for ${key}:`, error)
    }
  }

  private async clearFileCache(): Promise<void> {
    try {
      if (await FileUtils.directoryExists(this.config.fileCacheDir)) {
        await FileUtils.deleteDirectory(this.config.fileCacheDir)
        await FileUtils.ensureDirectory(this.config.fileCacheDir)
      }
    } catch (error) {
      this.logger.error('File cache clear error:', error)
    }
  }

  private async cleanupFileCache(): Promise<void> {
    try {
      const files = await FileUtils.getFilesInDirectory(this.config.fileCacheDir, {
        extensions: ['json']
      })

      for (const file of files) {
        const data = await FileUtils.readJsonFile<CacheEntry>(file)
        if (data && Date.now() - data.timestamp > data.ttl * 1000) {
          await FileUtils.deleteFile(file)
        }
      }
    } catch (error) {
      this.logger.error('File cache cleanup error:', error)
    }
  }

  private getFileCachePath(key: string): string {
    const hash = crypto.createHash('md5').update(key).digest('hex')
    return path.join(this.config.fileCacheDir, `${hash}.json`)
  }

  private async initializeFileCache(): Promise<void> {
    await FileUtils.ensureDirectory(this.config.fileCacheDir)
  }

  // Redis cache methods (placeholder - would need Redis client)
  private async getFromRedisCache(key: string): Promise<TWLProduct | null> {
    // Redis implementation would go here
    return null
  }

  private async setToRedisCache(key: string, product: TWLProduct, ttl?: number): Promise<void> {
    // Redis implementation would go here
  }

  private async deleteFromRedisCache(key: string): Promise<void> {
    // Redis implementation would go here
  }

  private async clearRedisCache(): Promise<void> {
    // Redis implementation would go here
  }

  private initializeMetrics(): CacheMetrics {
    return {
      memoryHits: 0,
      memoryMisses: 0,
      fileHits: 0,
      fileMisses: 0,
      redisHits: 0,
      redisMisses: 0,
      averageResponseTime: 0,
      totalRequests: 0,
      memoryUsage: 0,
      fileUsage: 0,
      redisUsage: 0,
      memoryItems: 0,
      fileItems: 0,
      redisItems: 0,
      overallHitRate: 0,
      memoryHitRate: 0,
      fileHitRate: 0,
      redisHitRate: 0
    }
  }

  private updateResponseTime(responseTime: number): void {
    const totalTime = this.metrics.averageResponseTime * (this.metrics.totalRequests - 1) + responseTime
    this.metrics.averageResponseTime = totalTime / this.metrics.totalRequests
  }

  private startAutoCleanup(): void {
    this.cleanupTimer = setInterval(() => {
      this.cleanup()
    }, this.config.cleanupInterval * 1000)
  }
}

export default ProductCache
