🧭 USER JOURNEY MAP: ACCOUNT INTERACTIONS

🎯 Target Audience

Fashion-forward Gen Z & Millennials
Sneaker collectors
Luxury fashion enthusiasts
Social media-savvy users
First-time buyers → returning VIP members


📈 Customer Lifecycle Stages

Stage,                                      Description
1. Awareness / Discovery,                   "Learns about TWL via social media, influencer content, or ads"
2. Consideration / Browsing,                "Explores products, creates wishlist, signs up"
3. Purchase / Checkout,                     "Makes first purchase, sets preferences"
4. Post-Purchase / Engagement,              "Receives order, shares look, joins community"
5. Retention / Loyalty,                     "Returns for new drops, earns points, becomes VIP"


🧭 STAGE-BY-STAGE JOURNEY WITH ACCOUNT INTERACTION

🔹 Stage 1: Awareness / Discovery

📍 Touchpoints:
Instagram Reels
TikTok videos
Influencer collaborations
Magazine editorials

🧑‍💻 User Behavior:
Clicks on a shoppable post → lands on product page
Sees “Add to Wishlist” option but must log in or sign up

💡 Opportunity:
Prompt quick registration with social login or email + OTP
Offer first-time visitor perks :
“Create an account and get early access to our next drop!” 

✅ Account Feature Interaction:
Sign-up flow
Social login (Google, Apple)
Guest browsing with save-for-later option


🔹 Stage 2: Consideration / Browsing

📍 Touchpoints:
Product pages
Search bar
Editorial sections
Community wall

🧑‍💻 User Behavior:
Saves items to wishlist
Views recently browsed items
Sets size preferences per brand
Reads magazine/editorial content

💡 Opportunity:
Let users create multiple wishlists (e.g., "Gift Ideas", "Must-Have Drops")
Enable voice search and visual search for fast discovery

✅ Account Feature Interaction:
Wishlist creation & management
Recently viewed items
Style preference settings
Size saver (per brand)
Push notification alerts for price drops


🔹 Stage 3: Purchase / Checkout

📍 Touchpoints:
Cart
Checkout form
Payment gateway
Confirmation page

🧑‍💻 User Behavior:
Reviews cart
Selects saved address/payment method
Confirms shoe size based on saved preferences

💡 Opportunity:
Use saved data to auto-fill checkout fields
Suggest similar items based on purchase history

✅ Account Feature Interaction:
Saved addresses
Payment methods
Size preferences
Order confirmation page
Email/SMS notifications


🔹 Stage 4: Post-Purchase / Engagement

📍 Touchpoints:
Order tracking
Email/SMS updates
UGC sharing buttons
Social media prompts

🧑‍💻 User Behavior:
Tracks package status
Shares unboxing photo/video using #TWLLook
Likes other user-generated looks
Earns first badge (e.g., “First Drop Master”)

💡 Opportunity:
Encourage UGC sharing with incentives
Show real-time delivery status
Invite to community feed

✅ Account Feature Interaction:
Order tracking
My UGC feed
Badges earned
Referral link generator
Product reviews/rating


🔹 Stage 5: Retention / Loyalty

📍 Touchpoints:
Home page
Personalized recommendations
Loyalty program dashboard
Early access emails

🧑‍💻 User Behavior:
Logs in regularly to check new drops
Redeems loyalty points
Joins early access events
Engages in challenges (#TWLLook of the Week)

💡 Opportunity:
Gamify engagement with badges , levels , and leaderboards
Reward top sharers and loyal customers
Offer VIP-only drops

✅ Account Feature Interaction:
Loyalty points system
Early access passes
Exclusive content access
Creator tools
Push notification preferences
AI-powered recommendations


📊 Emotions & Pain Points Across the Journey

Stage,              Emotion,Pain            Point,UX                                Fix

Discovery,          Curious,                No easy way to save items,              Add guest save + quick sign-up
Browsing,           Interested,             Too many steps to register,             Allow social login + OTP
Checkout,           Focused,                Long forms,                             Auto-fill with saved data
Post-Purchase,      Happy,                  No incentive to share,                  "Badges, rewards, community"
Retention,          Loyal,                  Lack of recognition,                    "Loyalty tiers, exclusive drops"

📌 Key Takeaways
Account features should be progressive : Start simple, then unlock more as users engage.
Gamification increases retention : Badges, levels, and leaderboards drive repeat visits.
Personalization builds loyalty : Use AI to suggest products based on behavior and style.
Community drives virality : Sharing looks and engaging with others keeps users coming back.
Biometric login improves experience : Fast, secure, and premium — especially for returning users.
