#!/usr/bin/env node

/**
 * TWL VIDEO OPTIMIZATION SCRIPT - 2024 EDITION
 * 🎬 Optimizes 573+ videos for web performance with 60-80% size reduction
 * 🚀 Enterprise-grade processing with parallel workers
 * 📱 Mobile-first optimization for fast loading
 */

import fs from 'fs/promises'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'
import os from 'os'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

// 🎯 OPTIMIZATION CONFIGURATION
const CONFIG = {
  // Processing settings
  maxConcurrentJobs: Math.min(os.cpus().length, 4),
  retryAttempts: 3,
  timeoutMs: 180000, // 3 minutes per video
  
  // Quality settings optimized for web
  video: {
    crf: 28,              // Balanced quality/size
    preset: 'fast',       // Faster encoding
    maxWidth: 1280,       // Max width for desktop
    maxHeight: 720,       // Max height for desktop
    mobileMaxWidth: 640,  // Max width for mobile
    mobileMaxHeight: 360, // Max height for mobile
    fps: 24,              // Reduced FPS for smaller files
    audioBitrate: '96k'   // Reduced audio bitrate
  },
  
  // Paths
  paths: {
    source: path.join(process.cwd(), 'public', 'products'),
    backup: path.join(process.cwd(), 'backup-videos'),
    logs: path.join(process.cwd(), 'logs')
  }
}

class TWLVideoOptimizer {
  constructor() {
    this.stats = {
      totalVideos: 0,
      processed: 0,
      failed: 0,
      skipped: 0,
      totalSizeReduction: 0,
      startTime: Date.now()
    }
    
    this.activeJobs = new Map()
    this.jobQueue = []
    this.ffmpegPath = null
    this.logFile = null
  }

  async initialize() {
    console.log('🎬 TWL VIDEO OPTIMIZER - 2024 EDITION')
    console.log('=====================================')
    console.log('🎯 Target: 60-80% size reduction')
    console.log('📱 Mobile-first optimization')
    console.log('')
    
    // Create directories
    await this.createDirectories()
    
    // Initialize logging
    await this.initializeLogging()
    
    // Detect FFmpeg
    await this.detectFFmpeg()
    
    this.log('✅ TWL Video Optimizer initialized successfully')
  }

  async createDirectories() {
    const dirs = [CONFIG.paths.backup, CONFIG.paths.logs]
    
    for (const dir of dirs) {
      try {
        await fs.mkdir(dir, { recursive: true })
        console.log(`✅ Directory ready: ${path.relative(process.cwd(), dir)}`)
      } catch (error) {
        console.log(`📁 Directory exists: ${path.relative(process.cwd(), dir)}`)
      }
    }
  }

  async initializeLogging() {
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    this.logFile = path.join(CONFIG.paths.logs, `twl-video-optimization-${timestamp}.log`)
    
    await this.log('=== TWL VIDEO OPTIMIZATION SESSION STARTED ===')
    await this.log(`Timestamp: ${new Date().toISOString()}`)
    await this.log(`System: ${os.platform()} ${os.arch()}`)
    await this.log(`CPUs: ${os.cpus().length}`)
    await this.log(`Memory: ${Math.round(os.totalmem() / 1024 / 1024 / 1024)}GB`)
    await this.log(`Max concurrent jobs: ${CONFIG.maxConcurrentJobs}`)
  }

  async log(message) {
    const timestamp = new Date().toISOString()
    const logEntry = `[${timestamp}] ${message}\n`
    
    console.log(message)
    
    if (this.logFile) {
      try {
        await fs.appendFile(this.logFile, logEntry)
      } catch (error) {
        console.error('❌ Logging error:', error.message)
      }
    }
  }

  async detectFFmpeg() {
    const possiblePaths = [
      'ffmpeg', // System PATH
      'C:\\ffmpeg\\bin\\ffmpeg.exe',
      'C:\\Program Files\\ffmpeg\\bin\\ffmpeg.exe',
      path.join(os.homedir(), 'AppData', 'Local', 'Microsoft', 'WinGet', 'Packages', 'Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe', 'ffmpeg-7.1.1-full_build', 'bin', 'ffmpeg.exe')
    ]

    for (const testPath of possiblePaths) {
      try {
        const result = execSync(`"${testPath}" -version`, { 
          encoding: 'utf8', 
          stdio: 'pipe',
          timeout: 10000 
        })
        
        if (result.includes('ffmpeg version')) {
          this.ffmpegPath = testPath
          const version = result.split('\n')[0]
          await this.log(`✅ FFmpeg detected: ${version}`)
          return
        }
      } catch (error) {
        continue
      }
    }

    throw new Error('❌ FFmpeg not found. Please install FFmpeg and restart.')
  }

  async scanForVideos() {
    await this.log('🔍 Scanning for videos in TWL products directory...')
    
    const videos = []
    await this.scanDirectory(CONFIG.paths.source, videos)
    
    this.stats.totalVideos = videos.length
    
    await this.log(`📊 Scan complete: ${videos.length} videos found`)
    await this.log(`📦 Total size: ${this.calculateTotalSize(videos)} MB`)
    
    return videos
  }

  async scanDirectory(dirPath, videos) {
    try {
      const items = await fs.readdir(dirPath, { withFileTypes: true })
      
      for (const item of items) {
        const fullPath = path.join(dirPath, item.name)
        
        if (item.isDirectory()) {
          await this.scanDirectory(fullPath, videos)
        } else if (this.isVideoFile(item.name)) {
          const stats = await fs.stat(fullPath)
          
          videos.push({
            id: `video-${videos.length + 1}`,
            sourcePath: fullPath,
            fileName: item.name,
            fileSize: stats.size,
            fileSizeMB: Math.round(stats.size / (1024 * 1024) * 100) / 100,
            relativePath: path.relative(CONFIG.paths.source, fullPath)
          })
        }
      }
    } catch (error) {
      await this.log(`❌ Error scanning ${dirPath}: ${error.message}`)
    }
  }

  isVideoFile(fileName) {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v']
    return videoExtensions.includes(path.extname(fileName).toLowerCase())
  }

  calculateTotalSize(videos) {
    return Math.round(
      videos.reduce((total, video) => total + video.fileSizeMB, 0) * 100
    ) / 100
  }

  async processVideos(videos) {
    await this.log(`🚀 Starting TWL video optimization...`)
    await this.log(`📹 Processing ${videos.length} videos with ${CONFIG.maxConcurrentJobs} concurrent jobs`)
    
    // Add all videos to job queue
    this.jobQueue = [...videos]
    
    // Start worker pool
    const workers = []
    for (let i = 0; i < CONFIG.maxConcurrentJobs; i++) {
      workers.push(this.worker(i))
    }
    
    // Wait for all workers to complete
    await Promise.all(workers)
    
    await this.generateReport()
  }

  async worker(workerId) {
    await this.log(`👷 Worker ${workerId} started`)
    
    while (this.jobQueue.length > 0) {
      const video = this.jobQueue.shift()
      if (!video) break
      
      await this.log(`👷 Worker ${workerId} processing: ${video.fileName}`)
      
      try {
        await this.processVideo(video, workerId)
        this.stats.processed++
      } catch (error) {
        await this.log(`❌ Worker ${workerId} failed on ${video.fileName}: ${error.message}`)
        this.stats.failed++
      }
      
      // Progress update
      const progress = Math.round(((this.stats.processed + this.stats.failed) / this.stats.totalVideos) * 100)
      await this.log(`📊 Progress: ${progress}% (${this.stats.processed} processed, ${this.stats.failed} failed)`)
    }
    
    await this.log(`👷 Worker ${workerId} completed`)
  }

  async processVideo(video, workerId) {
    const originalSize = video.fileSizeMB
    
    // Create backup
    const backupPath = path.join(CONFIG.paths.backup, video.relativePath)
    const backupDir = path.dirname(backupPath)
    await fs.mkdir(backupDir, { recursive: true })
    await fs.copyFile(video.sourcePath, backupPath)
    
    // Create optimized version
    const tempPath = `${video.sourcePath}.optimized.mp4`
    
    await this.convertVideo(video.sourcePath, tempPath, workerId)
    
    // Check if optimization was successful
    const stats = await fs.stat(tempPath)
    const newSizeMB = Math.round(stats.size / (1024 * 1024) * 100) / 100
    const reduction = originalSize - newSizeMB
    const reductionPercent = (reduction / originalSize) * 100
    
    if (reductionPercent > 10) {
      // Replace original with optimized version
      await fs.rename(tempPath, video.sourcePath)
      
      this.stats.totalSizeReduction += reduction
      
      await this.log(`✅ Worker ${workerId}: ${video.fileName} - ${originalSize}MB → ${newSizeMB}MB (${Math.round(reductionPercent)}% reduction)`)
    } else {
      // Remove optimized version if savings are minimal
      await fs.unlink(tempPath)
      await this.log(`⚠️  Worker ${workerId}: ${video.fileName} - Minimal savings, keeping original`)
      this.stats.skipped++
    }
  }

  async convertVideo(inputPath, outputPath, workerId) {
    const config = CONFIG.video
    
    // Properly escape Windows paths
    const escapedInput = `"${inputPath}"`
    const escapedOutput = `"${outputPath}"`
    const escapedFFmpeg = `"${this.ffmpegPath}"`
    
    const command = [
      escapedFFmpeg,
      '-i', escapedInput,
      '-c:v', 'libx264',
      '-crf', config.crf.toString(),
      '-preset', config.preset,
      '-vf', `scale='min(${config.maxWidth},iw)':'min(${config.maxHeight},ih)':force_original_aspect_ratio=decrease`,
      '-r', config.fps.toString(),
      '-c:a', 'aac',
      '-b:a', config.audioBitrate,
      '-movflags', '+faststart',
      '-y', escapedOutput
    ].join(' ')
    
    return new Promise((resolve, reject) => {
      const timeout = setTimeout(() => {
        reject(new Error(`Timeout after ${CONFIG.timeoutMs}ms`))
      }, CONFIG.timeoutMs)
      
      try {
        execSync(command, { 
          stdio: 'pipe',
          timeout: CONFIG.timeoutMs,
          maxBuffer: 1024 * 1024 * 10 // 10MB buffer
        })
        
        clearTimeout(timeout)
        resolve()
      } catch (error) {
        clearTimeout(timeout)
        reject(new Error(`FFmpeg failed: ${error.message}`))
      }
    })
  }

  async generateReport() {
    const duration = Math.round((Date.now() - this.stats.startTime) / 1000)
    
    await this.log('\n🎉 TWL VIDEO OPTIMIZATION COMPLETE!')
    await this.log('=====================================')
    await this.log(`⏱️  Duration: ${Math.floor(duration / 60)}m ${duration % 60}s`)
    await this.log(`📹 Total videos: ${this.stats.totalVideos}`)
    await this.log(`✅ Processed: ${this.stats.processed}`)
    await this.log(`❌ Failed: ${this.stats.failed}`)
    await this.log(`⏭️  Skipped: ${this.stats.skipped}`)
    await this.log(`💾 Size reduction: ${Math.round(this.stats.totalSizeReduction * 100) / 100} MB`)
    
    if (this.stats.totalVideos > 0) {
      const successRate = Math.round((this.stats.processed / this.stats.totalVideos) * 100)
      await this.log(`📊 Success rate: ${successRate}%`)
    }
    
    if (this.stats.processed > 0) {
      const avgProcessingTime = duration / this.stats.processed
      await this.log(`⚡ Avg processing time: ${Math.round(avgProcessingTime * 100) / 100}s per video`)
    }
    
    await this.log(`📄 Full log: ${this.logFile}`)
    await this.log(`💾 Backups: ${CONFIG.paths.backup}`)
    await this.log('\n🚀 Videos optimized for fast web rendering!')
  }
}

// Main execution
async function main() {
  const optimizer = new TWLVideoOptimizer()
  
  try {
    await optimizer.initialize()
    
    const videos = await optimizer.scanForVideos()
    
    if (videos.length === 0) {
      await optimizer.log('ℹ️  No videos found in products directory')
      return
    }
    
    // Ask for confirmation before processing
    console.log('\n⚠️  This will optimize all videos and create backups.')
    console.log('📁 Backups will be stored in: backup-videos/')
    console.log('🎯 Target: 60-80% size reduction')
    console.log('\nPress Ctrl+C to cancel, or wait 10 seconds to continue...')
    
    // Wait 10 seconds for user to cancel
    await new Promise(resolve => setTimeout(resolve, 10000))
    
    await optimizer.processVideos(videos)
    
  } catch (error) {
    console.error('❌ TWL video optimization failed:', error)
    process.exit(1)
  }
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}`) {
  main()
}

export default TWLVideoOptimizer
