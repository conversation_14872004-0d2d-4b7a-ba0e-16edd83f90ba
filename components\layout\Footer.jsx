'use client'

import Link from 'next/link'
import Button from '@/components/ui/Button'
import Input from '@/components/ui/Input'

export default function Footer() {

  const footerLinks = {
    company: [
      { name: 'Acerca de TWL', href: '/about' },
      { name: '<PERSON><PERSON>', href: '/contact' },
      { name: 'Careers', href: '/careers' },
      { name: 'Press', href: '/press' },
    ],
    support: [
      { name: 'Env<PERSON><PERSON>', href: '/shipping' },
      { name: 'Devoluciones', href: '/returns' },
      { name: '<PERSON><PERSON><PERSON> de Tallas', href: '/size-guide' },
      { name: 'FAQ', href: '/faq' },
    ],
    legal: [
      { name: 'Privacidad', href: '/privacy' },
      { name: '<PERSON><PERSON><PERSON><PERSON><PERSON>', href: '/terms' },
      { name: 'Cook<PERSON>', href: '/cookies' },
      { name: 'Accessibility', href: '/accessibility' },
    ],
  }

  const socialLinks = [
    { name: 'Instagram', href: 'https://instagram.com/thewhitelaces', icon: 'instagram' },
    { name: 'TikTok', href: 'https://tiktok.com/@thewhitelaces', icon: 'tiktok' },
    { name: 'Twitter', href: 'https://twitter.com/thewhitelaces', icon: 'twitter' },
    { name: 'YouTube', href: 'https://youtube.com/thewhitelaces', icon: 'youtube' },
  ]

  return (
    <footer className="bg-soft-cloud/50 dark:bg-mist-gray/50 border-t border-white/10">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Main Footer Content */}
        <div className="py-16">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8">
            {/* Brand Section */}
            <div className="lg:col-span-2">
              <div className="flex items-center space-x-2 mb-6">
                <div className="w-8 h-8 bg-gradient-refined rounded-lg flex items-center justify-center">
                  <span className="text-white font-bold text-lg">W</span>
                </div>
                <span className="text-xl font-playfair font-bold text-gradient">
                  The White Laces
                </span>
              </div>
              <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-md">
                Lujo urbano redefinido. Descubre calzado streetwear de lujo con drops exclusivos y colaboraciones limitadas.
              </p>
              
              {/* Newsletter Signup */}
              <div className="space-y-4">
                <h4 className="font-semibold text-gray-900 dark:text-white">
                  Boletín
                </h4>
                <div className="flex gap-2">
                  <Input
                    type="email"
                    placeholder="<EMAIL>"
                    className="flex-1"
                  />
                  <Button variant="primary" size="default">
                    Suscribirse
                  </Button>
                </div>
              </div>
            </div>

            {/* Company Links */}
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                Compañía
              </h4>
              <ul className="space-y-3">
                {footerLinks.company.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-600 dark:text-gray-300 hover:text-dusty-rose dark:hover:text-dusty-rose transition-colors duration-300"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Support Links */}
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                Soporte
              </h4>
              <ul className="space-y-3">
                {footerLinks.support.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-600 dark:text-gray-300 hover:text-dusty-rose dark:hover:text-dusty-rose transition-colors duration-300"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>

            {/* Legal Links */}
            <div>
              <h4 className="font-semibold text-gray-900 dark:text-white mb-4">
                Legal
              </h4>
              <ul className="space-y-3">
                {footerLinks.legal.map((link) => (
                  <li key={link.name}>
                    <Link
                      href={link.href}
                      className="text-gray-600 dark:text-gray-300 hover:text-dusty-rose dark:hover:text-dusty-rose transition-colors duration-300"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          </div>
        </div>

        {/* Bottom Footer */}
        <div className="py-8 border-t border-white/10">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <div className="text-gray-600 dark:text-gray-300 text-sm">
              © 2024 The White Laces. Todos los derechos reservados.
            </div>

            {/* Social Links */}
            <div className="flex items-center space-x-4">
              <span className="text-sm text-gray-600 dark:text-gray-300">
                Síguenos:
              </span>
              {socialLinks.map((social) => (
                <Link
                  key={social.name}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-gray-600 dark:text-gray-300 hover:text-dusty-rose dark:hover:text-dusty-rose transition-colors duration-300"
                >
                  <span className="sr-only">{social.name}</span>
                  <div className="w-5 h-5 bg-current rounded"></div>
                </Link>
              ))}
            </div>

            {/* Language/Currency Selector */}
            <div className="flex items-center space-x-4 text-sm">
              <select className="bg-transparent border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-gray-600 dark:text-gray-300">
                <option value="es-MX">🇲🇽 Español (MX)</option>
                <option value="en-US">🇺🇸 English (US)</option>
                <option value="pt-BR">🇧🇷 Português (BR)</option>
              </select>
              <select className="bg-transparent border border-gray-300 dark:border-gray-600 rounded px-2 py-1 text-gray-600 dark:text-gray-300">
                <option value="MXN">MXN $</option>
                <option value="USD">USD $</option>
                <option value="BRL">BRL R$</option>
              </select>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
