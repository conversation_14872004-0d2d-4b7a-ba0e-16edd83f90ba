import type { Meta, StoryObj } from '@storybook/react'
import ProductVariants from './ProductVariants'

const meta: Meta<typeof ProductVariants> = {
  title: 'Components/ProductVariants',
  component: ProductVariants,
  parameters: {
    layout: 'centered',
  },
  tags: ['autodocs'],
}

export default meta
type Story = StoryObj<typeof meta>

export const Default: Story = {
  args: {},
}

export const Loading: Story = {
  args: {},
}
