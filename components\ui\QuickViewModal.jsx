'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import Image from 'next/image'
// Removed heroicons dependency - using inline SVGs instead
import Button from '@/components/ui/Button'
import { useCart } from '@/contexts/CartContext'
import { useWishlist } from '@/contexts/WishlistContext'
import { useCartNotification } from '@/contexts/CartNotificationContext'

export default function QuickViewModal({ product, isOpen, onClose }) {
  const [selectedSize, setSelectedSize] = useState('')
  const [selectedColor, setSelectedColor] = useState('')
  const [selectedImage, setSelectedImage] = useState(0)
  const [isAddingToCart, setIsAddingToCart] = useState(false)
  
  const { addItem } = useCart()
  const { showCartSuccess } = useCartNotification()
  const { addToWishlist, removeFromWishlist, isInWishlist } = useWishlist()
  
  const isWishlisted = isInWishlist(product?.id)
  const hasDiscount = product?.originalPrice && product?.originalPrice > product?.price
  const discountPercentage = hasDiscount 
    ? Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)
    : 0

  // Initialize selections when product changes
  useEffect(() => {
    if (product) {
      setSelectedSize(product.sizes?.[0] || '')
      setSelectedColor(product.colors?.[0] || '')
      setSelectedImage(0)
    }
  }, [product])

  // Close modal on escape key
  useEffect(() => {
    const handleEscape = (e) => {
      if (e.key === 'Escape') onClose()
    }
    
    if (isOpen) {
      document.addEventListener('keydown', handleEscape)
      document.body.style.overflow = 'hidden'
    }
    
    return () => {
      document.removeEventListener('keydown', handleEscape)
      document.body.style.overflow = 'unset'
    }
  }, [isOpen, onClose])

  const handleAddToCart = async () => {
    if (!selectedSize) {
      alert('Por favor selecciona una talla')
      return
    }

    setIsAddingToCart(true)
    try {
      await new Promise(resolve => setTimeout(resolve, 600))
      addItem(product.id, selectedSize, 1)
      showCartSuccess(product, selectedSize, 1)
      onClose()
    } catch (error) {
      console.error('Error adding to cart:', error)
    } finally {
      setIsAddingToCart(false)
    }
  }

  const handleWishlistToggle = () => {
    if (isWishlisted) {
      removeFromWishlist(product.id)
    } else {
      addToWishlist(product.id)
    }
  }

  if (!product) return null

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center p-4">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black/60 backdrop-blur-sm"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-white dark:bg-neutral-800 rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
          >
            {/* Close Button */}
            <button
              onClick={onClose}
              className="absolute top-4 right-4 z-10 w-10 h-10 bg-white/90 hover:bg-white rounded-full flex items-center justify-center transition-all duration-200 shadow-lg"
            >
              <svg className="w-6 h-6 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>

            <div className="grid grid-cols-1 lg:grid-cols-2 h-full">
              {/* Image Section */}
              <div className="relative bg-gray-100 dark:bg-neutral-700 p-6">
                {/* Main Image */}
                <div className="relative aspect-square mb-4 rounded-xl overflow-hidden">
                  <Image
                    src={product.images?.[selectedImage] || product.image || "/placeholder.jpg"}
                    alt={product.name}
                    fill
                    className="object-cover"
                    sizes="(max-width: 1024px) 100vw, 50vw"
                  />
                  
                  {/* Badges */}
                  <div className="absolute top-4 left-4 flex flex-col gap-2">
                    {product.isNew && (
                      <div className="bg-lime-500 text-black text-xs px-2 py-1 rounded-md font-poppins font-semibold">
                        NUEVO
                      </div>
                    )}
                    {product.isLimited && (
                      <div className="bg-red-500 text-white text-xs px-2 py-1 rounded-md font-poppins font-semibold">
                        LIMITADO
                      </div>
                    )}
                    {hasDiscount && (
                      <div className="bg-orange-500 text-white text-xs px-2 py-1 rounded-md font-poppins font-semibold">
                        -{discountPercentage}%
                      </div>
                    )}
                  </div>
                </div>

                {/* Thumbnail Images */}
                {product.images && product.images.length > 1 && (
                  <div className="flex gap-2 overflow-x-auto">
                    {product.images.map((image, index) => (
                      <button
                        key={index}
                        onClick={() => setSelectedImage(index)}
                        className={`relative w-16 h-16 rounded-lg overflow-hidden flex-shrink-0 border-2 transition-all ${
                          selectedImage === index 
                            ? 'border-lime-500' 
                            : 'border-gray-200 hover:border-gray-300'
                        }`}
                      >
                        <Image
                          src={image}
                          alt={`${product.name} ${index + 1}`}
                          fill
                          className="object-cover"
                          sizes="64px"
                        />
                      </button>
                    ))}
                  </div>
                )}
              </div>

              {/* Product Info Section */}
              <div className="p-6 flex flex-col">
                {/* Header */}
                <div className="mb-6">
                  <p className="text-sm uppercase tracking-wide text-gray-600 dark:text-gray-400 mb-2">
                    {product.brand}
                  </p>
                  <h2 className="text-2xl font-godber font-bold text-black dark:text-white mb-3 leading-tight">
                    {product.name}
                  </h2>
                  
                  {/* Price */}
                  <div className="flex items-center gap-3 mb-4">
                    <span className="text-3xl font-poppins font-bold text-black dark:text-white">
                      ${product.price?.toLocaleString()} MXN
                    </span>
                    {hasDiscount && (
                      <span className="text-lg font-poppins line-through text-gray-500">
                        ${product.originalPrice?.toLocaleString()} MXN
                      </span>
                    )}
                  </div>

                  {/* Description */}
                  <p className="text-gray-600 dark:text-gray-300 text-sm leading-relaxed">
                    {product.description}
                  </p>
                </div>

                {/* Size Selection */}
                {product.sizes && product.sizes.length > 0 && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Talla: {selectedSize && <span className="text-lime-500">{selectedSize}</span>}
                    </label>
                    <div className="grid grid-cols-6 gap-2">
                      {product.sizes.map((size) => (
                        <button
                          key={size}
                          onClick={() => setSelectedSize(size)}
                          className={`py-2 px-3 text-sm font-medium rounded-lg border transition-all ${
                            selectedSize === size
                              ? 'border-lime-500 bg-lime-500 text-black'
                              : 'border-gray-300 hover:border-gray-400 text-gray-700 dark:text-gray-300'
                          }`}
                        >
                          {size}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Color Selection */}
                {product.colors && product.colors.length > 0 && (
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                      Color: {selectedColor && <span className="text-lime-500">{selectedColor}</span>}
                    </label>
                    <div className="flex gap-2">
                      {product.colors.map((color) => (
                        <button
                          key={color}
                          onClick={() => setSelectedColor(color)}
                          className={`py-2 px-4 text-sm font-medium rounded-lg border transition-all ${
                            selectedColor === color
                              ? 'border-lime-500 bg-lime-500 text-black'
                              : 'border-gray-300 hover:border-gray-400 text-gray-700 dark:text-gray-300'
                          }`}
                        >
                          {color}
                        </button>
                      ))}
                    </div>
                  </div>
                )}

                {/* Features */}
                {product.features && product.features.length > 0 && (
                  <div className="mb-6">
                    <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Características:
                    </h3>
                    <ul className="text-sm text-gray-600 dark:text-gray-400 space-y-1">
                      {product.features.map((feature, index) => (
                        <li key={index} className="flex items-center">
                          <span className="w-1.5 h-1.5 bg-lime-500 rounded-full mr-2"></span>
                          {feature}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {/* Actions */}
                <div className="mt-auto space-y-3">
                  <div className="flex gap-3">
                    <Button
                      onClick={handleAddToCart}
                      disabled={isAddingToCart || !selectedSize}
                      className="flex-1 bg-lime-500 hover:bg-black text-black hover:text-white"
                    >
                      {isAddingToCart ? (
                        <>
                          <div className="w-4 h-4 border-2 border-black border-t-transparent rounded-full animate-spin mr-2" />
                          Agregando...
                        </>
                      ) : (
                        <>
                          <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l-1 12H6L5 9z" />
                          </svg>
                          Agregar al Carrito
                        </>
                      )}
                    </Button>

                    <button
                      onClick={handleWishlistToggle}
                      className="w-12 h-12 border border-gray-300 hover:border-lime-500 rounded-lg flex items-center justify-center transition-all duration-200 group"
                    >
                      {isWishlisted ? (
                        <svg className="w-6 h-6 text-lime-500" fill="currentColor" viewBox="0 0 24 24">
                          <path d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      ) : (
                        <svg className="w-6 h-6 text-gray-600 group-hover:text-lime-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      )}
                    </button>
                  </div>

                  {!selectedSize && (
                    <p className="text-sm text-red-500 text-center">
                      Selecciona una talla para continuar
                    </p>
                  )}
                </div>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  )
}
