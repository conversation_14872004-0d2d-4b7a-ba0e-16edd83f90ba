'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import EnhancedProductCard from '@/components/ui/EnhancedProductCard'
import { cn } from '@/lib/utils'

export default function EnhancedMasonryGrid({ 
  products, 
  className = '',
  columns = { sm: 2, md: 3, lg: 4, xl: 5 },
  showQuickActions = true
}) {
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  if (!mounted) {
    return (
      <div className={cn('w-full', className)}>
        <div className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 w-full max-w-full overflow-hidden">
          {Array.from({ length: 8 }).map((_, index) => (
            <div
              key={index}
              className="aspect-[4/5] bg-gray-200 dark:bg-gray-700 rounded-2xl animate-pulse"
            />
          ))}
        </div>
      </div>
    )
  }

  // Define different aspect ratios for masonry effect
  const getCardVariant = (index) => {
    const patterns = ['default', 'default', 'masonry', 'default', 'default']
    return patterns[index % patterns.length]
  }

  const getCardAspectRatio = (index) => {
    const patterns = [
      'aspect-[4/5]',    // Standard portrait
      'aspect-[4/5]',    // Standard portrait
      'aspect-[3/4]',    // Taller portrait (masonry)
      'aspect-[4/5]',    // Standard portrait
      'aspect-[5/4]',    // Landscape
      'aspect-[4/5]',    // Standard portrait
      'aspect-[3/4]',    // Taller portrait
      'aspect-[4/5]'     // Standard portrait
    ]
    return patterns[index % patterns.length]
  }

  return (
    <div className={cn('w-full', className)}>
      <motion.div
        className="grid grid-cols-2 gap-3 sm:gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 w-full max-w-full overflow-hidden"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
      >
        {products.map((product, index) => {
          const variant = getCardVariant(index)
          const aspectRatio = getCardAspectRatio(index)
          
          return (
            <motion.div
              key={product.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.05, duration: 0.4 }}
              className={cn(aspectRatio)}
            >
              <EnhancedProductCard
                product={product}
                index={index}
                variant={variant}
                priority={index < 4}
                showQuickActions={showQuickActions}
                className="h-full"
              />
            </motion.div>
          )
        })}
      </motion.div>
    </div>
  )
}
