'use client'

import { forwardRef } from 'react'
import { cn } from '@/lib/utils'

const Card = forwardRef(({
  className,
  variant = 'default',
  hover = true,
  children,
  ...props
}, ref) => {
  const baseClasses = 'glass-card transition-all duration-300'
  
  const variants = {
    default: 'bg-white/10 dark:bg-white/5',
    elevated: 'bg-white/20 dark:bg-white/10 shadow-xl',
    bordered: 'border-2 border-white/20 dark:border-white/10',
    solid: 'bg-neutral-100 dark:bg-neutral-800',
    glass: 'bg-white/10 dark:bg-white/5 backdrop-blur-md border border-white/20',
    primary: 'border border-primary/50 shadow-primary-glow',
    secondary: 'border border-secondary/50 shadow-secondary-glow',
    success: 'border border-success/50 shadow-success-glow',
  }
  
  const hoverClasses = hover ? 'hover:shadow-xl hover:-translate-y-1 hover:scale-[1.02]' : ''

  return (
    <div
      className={cn(
        baseClasses,
        variants[variant],
        hoverClasses,
        className
      )}
      ref={ref}
      {...props}
    >
      {children}
    </div>
  )
})

Card.displayName = 'Card'

const CardHeader = forwardRef(({
  className,
  children,
  ...props
}, ref) => (
  <div
    ref={ref}
    className={cn('flex flex-col space-y-1.5 p-6 pb-4', className)}
    {...props}
  >
    {children}
  </div>
))

CardHeader.displayName = 'CardHeader'

const CardTitle = forwardRef(({
  className,
  children,
  ...props
}, ref) => (
  <h3
    ref={ref}
    className={cn('text-xl font-playfair font-bold leading-none tracking-tight', className)}
    {...props}
  >
    {children}
  </h3>
))

CardTitle.displayName = 'CardTitle'

const CardDescription = forwardRef(({
  className,
  children,
  ...props
}, ref) => (
  <p
    ref={ref}
    className={cn('text-sm text-gray-600 dark:text-gray-300', className)}
    {...props}
  >
    {children}
  </p>
))

CardDescription.displayName = 'CardDescription'

const CardContent = forwardRef(({
  className,
  children,
  ...props
}, ref) => (
  <div
    ref={ref}
    className={cn('p-6 pt-0', className)}
    {...props}
  >
    {children}
  </div>
))

CardContent.displayName = 'CardContent'

const CardFooter = forwardRef(({
  className,
  children,
  ...props
}, ref) => (
  <div
    ref={ref}
    className={cn('flex items-center p-6 pt-0', className)}
    {...props}
  >
    {children}
  </div>
))

CardFooter.displayName = 'CardFooter'

export {
  Card,
  CardHeader,
  CardTitle,
  CardDescription,
  CardContent,
  CardFooter,
}
