'use client'

import { cn } from '@/lib/utils'

export default function SkeletonLoader({ 
  className,
  variant = 'default',
  ...props 
}) {
  const variants = {
    default: 'h-4 w-full',
    text: 'h-4 w-3/4',
    title: 'h-6 w-1/2',
    avatar: 'h-12 w-12 rounded-full',
    button: 'h-10 w-24 rounded-lg',
    card: 'h-48 w-full rounded-lg',
    product: 'aspect-square w-full rounded-lg'
  }

  return (
    <div
      className={cn(
        'animate-pulse bg-gray-200 rounded',
        variants[variant],
        className
      )}
      {...props}
    />
  )
}

// Product Card Skeleton
export function ProductCardSkeleton() {
  return (
    <div className="space-y-4">
      <SkeletonLoader variant="product" />
      <div className="space-y-2">
        <SkeletonLoader variant="text" />
        <SkeletonLoader variant="text" className="w-1/2" />
        <SkeletonLoader variant="text" className="w-1/3" />
      </div>
    </div>
  )
}

// Section Skeleton
export function SectionSkeleton({ count = 6 }) {
  return (
    <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <ProductCardSkeleton key={i} />
      ))}
    </div>
  )
}
