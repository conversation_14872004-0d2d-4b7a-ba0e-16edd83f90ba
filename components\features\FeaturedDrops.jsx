'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import AnimatedProductCard from '@/components/ui/AnimatedProductCard'
import { formatPrice } from '@/lib/utils'

// Mock data - replace with real data from your API
const mockProducts = [
  {
    id: 1,
    name: 'Nike x Off-White Dunk Low',
    brand: 'Nike',
    price: 4500,
    originalPrice: 5200,
    image: '/products/nike-off-white-dunk.jpg',
    isLimited: true,
    isNew: true,
    sizes: ['7', '8', '9', '10', '11'],
    colors: ['White/Black', 'University Red']
  },
  {
    id: 2,
    name: 'Gucci Ace Sneaker',
    brand: 'Gucci',
    price: 12800,
    image: '/products/gucci-ace.jpg',
    isExclusive: true,
    sizes: ['7', '8', '9', '10'],
    colors: ['White/Green', 'White/Red']
  },
  {
    id: 3,
    name: 'Balenciaga Triple S',
    brand: 'Balenciaga',
    price: 18500,
    image: '/products/balenciaga-triple-s.jpg',
    isVip: true,
    sizes: ['8', '9', '10', '11'],
    colors: ['White', 'Black', 'Grey']
  },
  {
    id: 4,
    name: 'Dior B23 High-Top',
    brand: 'Dior',
    price: 22000,
    image: '/products/dior-b23.jpg',
    isLimited: true,
    isExclusive: true,
    sizes: ['7', '8', '9', '10'],
    colors: ['Oblique', 'White']
  }
]

export default function FeaturedDrops() {
  const [hoveredProduct, setHoveredProduct] = useState(null)

  const handleAddToCart = (product) => {
    // Add to cart logic here
    console.log('Adding to cart:', product)
  }

  const handleAddToWishlist = (product) => {
    // Add to wishlist logic here
    console.log('Adding to wishlist:', product)
  }

  return (
    <motion.div
      className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      transition={{ duration: 0.6 }}
    >
      {mockProducts.map((product, index) => (
        <AnimatedProductCard
          key={product.id}
          product={product}
          index={index}
        />
      ))}
    </motion.div>
  )
}
