'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import Link from 'next/link'
import AnimatedProductCard from '@/components/ui/AnimatedProductCard'
import { formatPrice } from '@/lib/utils'
import { loadRealProduct } from '@/lib/real-products-loader'

// Helper function to generate product IDs from CYTTE folder structure
const generateProductIdFromPath = (category, brand, modelFamily, gender, collaboration, sku) => {
  // Convert CYTTE path components to product ID format
  const categorySlug = category.toLowerCase().replace(/^\d+\.\s*/, '').replace(/\s+/g, '-')
  const brandSlug = brand.toLowerCase().replace(/^\d+\.\s*/, '').replace(/\s+/g, '-').replace('limited-edition', '')
  const genderSlug = gender.toLowerCase().replace(/^\d+\.\s*/, '')
  const modelSlug = modelFamily.toLowerCase().replace(/^\d+\.\s*/, '').replace(/\s+/g, '-')
  const skuSlug = sku.toLowerCase()

  return `${categorySlug}-${brandSlug}-${genderSlug}-${modelSlug}-${skuSlug}`
}

// Function to scan CYTTE structure and generate real product mappings
const generateRealProductMappings = () => {
  // This would ideally scan the actual folder structure
  // For now, we'll manually map known products to their categories

  const mappings = {
    sneakers: [
      // Nike x Gucci Air Force products (from 1. SNEAKERS folder)
      'sneakers-nike-mixte-air-force-bd7700-222',
      'sneakers-nike-mixte-air-force-jgd212-ejd',
      'sneakers-nike-mixte-air-force-jgd212-zzf',
      'sneakers-nike-mixte-air-force-zed212-edr',
      // Nike x Off-White products
      'sneakers-nike-mixte-air-force-ao4606-001',
      // Add 3 more from mock data to reach 8 total
      'sneakers-mock-gucci-ace-001',
      'sneakers-mock-balenciaga-triple-s-002',
      'sneakers-mock-dior-b23-003'
    ],
    sandals: [
      // Products from 2. SANDALS folder
      // TODO: Map actual sandals products when available
      'sandals-gucci-mixte-slide-example-001',
      'sandals-hermes-mixte-oran-example-002'
    ],
    formal: [
      // Products from 3. FORMAL folder
      // TODO: Map actual formal products when available
      'formal-chanel-mixte-oxford-example-001',
      'formal-gucci-mixte-loafer-example-002'
    ],
    casual: [
      // Products from 4. CASUAL folder
      // TODO: Map actual casual products when available
      'casual-ugg-mixte-boot-example-001',
      'casual-lv-mixte-sneaker-example-002'
    ],
    kids: [
      // Products from 5. KIDS folder
      // TODO: Map actual kids products when available
      'kids-ugg-mixte-boot-example-001',
      'kids-golden-goose-mixte-sneaker-example-002'
    ]
  }

  return mappings
}

// Create fallback product for categories that don't have real products yet
const createFallbackProduct = (productId, category) => {
  const categoryInfo = categories.find(c => c.id === category)

  // Get mock products for this category to use as fallback
  const mockProducts = mockProductsByCategory[category] || []
  const mockIndex = Math.floor(Math.random() * mockProducts.length)
  const mockProduct = mockProducts[mockIndex]

  if (mockProduct) {
    // Use mock product data with real structure and proper image paths
    return {
      ...mockProduct,
      id: productId,
      cytteCategory: categoryInfo?.cytteFolder,
      images: [
        `/${mockProduct.image}`, // Use the image path as-is (already includes 'images/products/')
        `/${mockProduct.image}` // Use same image for hover effect
      ],
      isPlaceholder: true
    }
  }

  // Ultimate fallback if no mock data - use placeholder images
  const placeholderImages = [
    '/images/placeholder-shoe-1.jpg',
    '/images/placeholder-shoe-2.jpg'
  ]

  return {
    id: productId,
    name: `${categoryInfo?.name || 'Product'} Premium Collection`,
    brand: 'TWL Collection',
    price: 210,
    originalPrice: 280,
    images: placeholderImages,
    category: category,
    cytteCategory: categoryInfo?.cytteFolder,
    sizes: ['36', '37', '38', '39', '40', '41', '42', '43'],
    inStock: true,
    isPlaceholder: true
  }
}

// Categories matching CYTTE organized structure - removed icons for cleaner look
const categories = [
  { id: 'sneakers', name: 'Sneakers', cytteFolder: '1-sneakers' },
  { id: 'sandals', name: 'Sandalias', cytteFolder: '2-sandals' },
  { id: 'formal', name: 'Formal', cytteFolder: '3-formal' },
  { id: 'casual', name: 'Casual', cytteFolder: '4-casual' },
  { id: 'kids', name: 'Kids', cytteFolder: '5-kids' }
]

// Real product IDs organized by CYTTE category structure
const realProductIdsByCategory = generateRealProductMappings()

// Mock data organized by category - fallback for categories without real products yet
const mockProductsByCategory = {
  sneakers: [
    {
      id: 1,
      name: 'Nike x Off-White Dunk Low',
      brand: 'Nike',
      price: 4500,
      originalPrice: 5200,
      image: '/products/nike-off-white-dunk.jpg',
      isLimited: true,
      isNew: true,
      sizes: ['7', '8', '9', '10', '11'],
      colors: ['White/Black', 'University Red'],
      category: 'sneakers'
    },
    {
      id: 2,
      name: 'Gucci Ace Sneaker',
      brand: 'Gucci',
      price: 12800,
      image: '/products/1. SNEAKERS/4. GUCCI/1. MIXTE/placeholder.webp',
      isExclusive: true,
      sizes: ['7', '8', '9', '10'],
      colors: ['White/Green', 'White/Red'],
      category: 'sneakers'
    },
    {
      id: 3,
      name: 'Gucci Horsebit Sneaker',
      brand: 'Gucci',
      price: 18500,
      image: '/products/1. SNEAKERS/4. GUCCI/1. MIXTE/placeholder.webp',
      isVip: true,
      sizes: ['8', '9', '10', '11'],
      colors: ['White', 'Black', 'Grey'],
      category: 'sneakers'
    },
    {
      id: 4,
      name: 'Dior B23 High-Top',
      brand: 'Dior',
      price: 22000,
      image: '/products/dior-b23.jpg',
      isLimited: true,
      isExclusive: true,
      sizes: ['7', '8', '9', '10'],
      colors: ['Oblique', 'White'],
      category: 'sneakers'
    },
    {
      id: 5,
      name: 'Nike Air Force 1 Limited',
      brand: 'Nike',
      price: 3200,
      image: '/products/1. SNEAKERS/1. NIKE Limited Edition/1. AIR FORCE/1. MIXTE/placeholder.webp',
      isNew: true,
      sizes: ['7', '8', '9', '10', '11'],
      colors: ['Chicago', 'Bred', 'Royal'],
      category: 'sneakers'
    },
    {
      id: 6,
      name: 'Yeezy Boost 350 V2',
      brand: 'Adidas',
      price: 4800,
      image: '/products/yeezy-350.jpg',
      isLimited: true,
      sizes: ['7', '8', '9', '10', '11'],
      colors: ['Zebra', 'Cream', 'Oreo'],
      category: 'sneakers'
    },
    {
      id: 7,
      name: 'Louis Vuitton Trainer',
      brand: 'Louis Vuitton',
      price: 28500,
      image: '/products/lv-trainer.jpg',
      isExclusive: true,
      sizes: ['7', '8', '9', '10'],
      colors: ['Monogram', 'White', 'Black'],
      category: 'sneakers'
    },
    {
      id: 8,
      name: 'Golden Goose Superstar',
      brand: 'Golden Goose',
      price: 8900,
      image: '/products/golden-goose.jpg',
      sizes: ['7', '8', '9', '10'],
      colors: ['White/Silver', 'Black/Gold'],
      category: 'sneakers'
    }
  ],
  casual: [
    {
      id: 9,
      name: 'Gucci Loafer Horsebit',
      brand: 'Gucci',
      price: 15600,
      image: '/products/gucci-loafer.jpg',
      isClassic: true,
      sizes: ['7', '8', '9', '10', '11'],
      colors: ['Black', 'Brown', 'Navy'],
      category: 'casual'
    },
    {
      id: 10,
      name: 'Bottega Veneta Intrecciato',
      brand: 'Bottega Veneta',
      price: 18900,
      image: '/products/bottega-casual.jpg',
      isExclusive: true,
      sizes: ['7', '8', '9', '10'],
      colors: ['Black', 'Brown', 'Cream'],
      category: 'casual'
    }
  ],
  tacones: [
    {
      id: 11,
      name: 'Louboutin So Kate 120',
      brand: 'Christian Louboutin',
      price: 19500,
      image: '/products/louboutin-so-kate.jpg',
      isSignature: true,
      sizes: ['35', '36', '37', '38', '39', '40'],
      colors: ['Black', 'Nude', 'Red'],
      category: 'tacones'
    },
    {
      id: 12,
      name: 'Manolo Blahnik Hangisi',
      brand: 'Manolo Blahnik',
      price: 22800,
      image: '/products/manolo-hangisi.jpg',
      isIconic: true,
      sizes: ['35', '36', '37', '38', '39', '40'],
      colors: ['Black', 'Navy', 'Silver'],
      category: 'tacones'
    }
  ],
  sandalias: [
    {
      id: 13,
      name: 'Hermès Oran Sandal',
      brand: 'Hermès',
      price: 12600,
      image: '/images/placeholder.jpg', // Fallback until sandals are organized
      isClassic: true,
      sizes: ['35', '36', '37', '38', '39', '40'],
      colors: ['Black', 'Gold', 'White'],
      category: 'sandalias'
    },
    {
      id: 14,
      name: 'Chanel Chain Sandal',
      brand: 'Chanel',
      price: 16800,
      image: '/products/chanel-sandal.jpg',
      isLuxury: true,
      sizes: ['35', '36', '37', '38', '39', '40'],
      colors: ['Black', 'Beige', 'Gold'],
      category: 'sandalias'
    }
  ],
  formal: [
    {
      id: 15,
      name: 'Tom Ford Oxford',
      brand: 'Tom Ford',
      price: 24500,
      image: '/images/placeholder.jpg', // Fallback until formal are organized
      isFormal: true,
      sizes: ['7', '8', '9', '10', '11'],
      colors: ['Black', 'Brown', 'Burgundy'],
      category: 'formal'
    },
    {
      id: 16,
      name: 'Berluti Alessandro',
      brand: 'Berluti',
      price: 32000,
      image: '/products/berluti-alessandro.jpg',
      isHandmade: true,
      sizes: ['7', '8', '9', '10', '11'],
      colors: ['Black', 'Brown', 'Cognac'],
      category: 'formal'
    }
  ]
}

export default function FeaturedDrops() {
  const [selectedCategory, setSelectedCategory] = useState('sneakers')
  const [hoveredProduct, setHoveredProduct] = useState(null)
  const [realProducts, setRealProducts] = useState({})
  const [loading, setLoading] = useState(true)

  // Load real products on component mount
  useEffect(() => {
    const loadProducts = async () => {
      setLoading(true)
      const loadedProducts = {}

      console.log('🔄 Loading products for NUESTRA COLECCIÓN categories...')

      // Load products for all categories using real CYTTE structure
      for (const [category, productIds] of Object.entries(realProductIdsByCategory)) {
        loadedProducts[category] = []

        console.log(`📂 Loading ${category} products:`, productIds)

        for (const productId of productIds) {
          try {
            const product = await loadRealProduct(productId)
            if (product) {
              // Add category metadata to product
              product.cytteCategory = categories.find(c => c.id === category)?.cytteFolder
              loadedProducts[category].push(product)
              console.log(`✅ Loaded ${category} product:`, product.name)
            } else {
              console.log(`⚠️ Product ${productId} not found, using fallback`)
              // Use fallback for non-existent products
              const fallbackProduct = createFallbackProduct(productId, category)
              loadedProducts[category].push(fallbackProduct)
            }
          } catch (error) {
            console.error(`❌ Error loading product ${productId}:`, error)
            // Fallback to mock data for this product
            const fallbackProduct = createFallbackProduct(productId, category)
            loadedProducts[category].push(fallbackProduct)
          }
        }

        // Ensure we have at least 8 products for each category by adding mock products
        const mockProducts = mockProductsByCategory[category] || []
        while (loadedProducts[category].length < 8 && mockProducts.length > 0) {
          const mockIndex = loadedProducts[category].length % mockProducts.length
          const mockProduct = {
            ...mockProducts[mockIndex],
            id: `${category}-mock-${loadedProducts[category].length}`,
            cytteCategory: categories.find(c => c.id === category)?.cytteFolder,
            images: [`/${mockProducts[mockIndex].image}`, `/${mockProducts[mockIndex].image}`],
            isPlaceholder: true
          }
          loadedProducts[category].push(mockProduct)
        }

        console.log(`📊 ${category} category loaded: ${loadedProducts[category].length} products`)
      }

      setRealProducts(loadedProducts)
      setLoading(false)
      console.log('✅ All NUESTRA COLECCIÓN products loaded!')
    }

    loadProducts()
  }, [])

  const handleAddToCart = (product) => {
    // Add to cart logic here
    console.log('Adding to cart:', product)
  }

  const handleAddToWishlist = (product) => {
    // Add to wishlist logic here
    console.log('Adding to wishlist:', product)
  }

  const currentProducts = realProducts[selectedCategory] || mockProductsByCategory[selectedCategory] || []

  return (
    <div className="space-y-8">
      {/* Category Filter Buttons */}
      <div className="flex flex-wrap justify-center gap-3 mb-8">
        {categories.map((category) => (
          <motion.button
            key={category.id}
            onClick={() => setSelectedCategory(category.id)}
            className={`px-6 py-3 rounded-full font-poppins font-medium transition-all duration-300 flex items-center justify-center ${
              selectedCategory === category.id
                ? 'bg-lime-green text-pure-black shadow-lg shadow-lime-green/25'
                : 'bg-pure-white text-text-gray border border-border-gray hover:border-lime-green hover:text-lime-green'
            }`}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            <span>{category.name}</span>
          </motion.button>
        ))}
      </div>

      {/* Products Grid - 2 Lines (8 products: 4 per line) */}
      <motion.div
        key={selectedCategory}
        className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        {currentProducts.slice(0, 8).map((product, index) => (
          <AnimatedProductCard
            key={product.id}
            product={product}
            index={index}
          />
        ))}
      </motion.div>

      {/* Ver toda la colección Button - Always visible, centered, dark charcoal with lime green text */}
      <div className="text-center mt-8">
        <Link href="/shop">
          <motion.button
            className="px-8 py-4 bg-gray-800 text-lime-green rounded-full font-poppins font-medium hover:bg-gray-900 hover:text-lime-green transition-all duration-300 shadow-lg"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Ver toda la colección
          </motion.button>
        </Link>
      </div>
    </div>
  )
}
