'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import AnimatedInput from '@/components/ui/AnimatedInput'
import AnimatedSelect from '@/components/ui/AnimatedSelect'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'

const mexicanStates = [
  { value: 'aguascalientes', label: 'Aguascalientes' },
  { value: 'baja-california', label: 'Baja California' },
  { value: 'baja-california-sur', label: 'Baja California Sur' },
  { value: 'campeche', label: 'Campeche' },
  { value: 'chiapas', label: 'Chiapas' },
  { value: 'chihuahua', label: 'Chihuahua' },
  { value: 'cdmx', label: 'Ciudad de México' },
  { value: 'coahuila', label: 'Coahuila' },
  { value: 'colima', label: '<PERSON>ima' },
  { value: 'durango', label: 'Durango' },
  { value: 'guanajuato', label: 'Guanajuato' },
  { value: 'guerrero', label: 'Guerrero' },
  { value: 'hidalgo', label: 'Hidalgo' },
  { value: 'jalisco', label: 'Jalisco' },
  { value: 'mexico', label: 'Estado de México' },
  { value: 'michoacan', label: 'Michoacán' },
  { value: 'morelos', label: 'Morelos' },
  { value: 'nayarit', label: 'Nayarit' },
  { value: 'nuevo-leon', label: 'Nuevo León' },
  { value: 'oaxaca', label: 'Oaxaca' },
  { value: 'puebla', label: 'Puebla' },
  { value: 'queretaro', label: 'Querétaro' },
  { value: 'quintana-roo', label: 'Quintana Roo' },
  { value: 'san-luis-potosi', label: 'San Luis Potosí' },
  { value: 'sinaloa', label: 'Sinaloa' },
  { value: 'sonora', label: 'Sonora' },
  { value: 'tabasco', label: 'Tabasco' },
  { value: 'tamaulipas', label: 'Tamaulipas' },
  { value: 'tlaxcala', label: 'Tlaxcala' },
  { value: 'veracruz', label: 'Veracruz' },
  { value: 'yucatan', label: 'Yucatán' },
  { value: 'zacatecas', label: 'Zacatecas' }
]

export default function AddressBook() {
  const {
    addresses,
    defaultAddressId,
    addAddress,
    updateAddress,
    deleteAddress,
    setDefaultAddress
  } = useUserPreferences()

  const [isAddingNew, setIsAddingNew] = useState(false)
  const [editingId, setEditingId] = useState(null)
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    company: '',
    address1: '',
    address2: '',
    city: '',
    state: '',
    postalCode: '',
    country: 'MX',
    phone: '',
    isDefault: false
  })

  const resetForm = () => {
    setFormData({
      firstName: '',
      lastName: '',
      company: '',
      address1: '',
      address2: '',
      city: '',
      state: '',
      postalCode: '',
      country: 'MX',
      phone: '',
      isDefault: false
    })
  }

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e) => {
    e.preventDefault()
    
    if (editingId) {
      updateAddress(editingId, formData)
      if (formData.isDefault) {
        setDefaultAddress(editingId)
      }
      setEditingId(null)
    } else {
      const newId = addAddress(formData)
      if (formData.isDefault) {
        setDefaultAddress(newId)
      }
      setIsAddingNew(false)
    }
    
    resetForm()
  }

  const handleEdit = (address) => {
    setFormData(address)
    setEditingId(address.id)
    setIsAddingNew(true)
  }

  const handleDelete = (id) => {
    if (confirm('¿Estás seguro de que quieres eliminar esta dirección?')) {
      deleteAddress(id)
    }
  }

  const handleCancel = () => {
    setIsAddingNew(false)
    setEditingId(null)
    resetForm()
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
            Libro de Direcciones
          </h2>
          <p className="text-warm-camel mt-1">
            Gestiona tus direcciones de envío y facturación
          </p>
        </div>
        
        {!isAddingNew && (
          <AnimatedButton
            variant="primary"
            onClick={() => setIsAddingNew(true)}
            icon={
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
              </svg>
            }
          >
            Agregar Dirección
          </AnimatedButton>
        )}
      </div>

      {/* Add/Edit Form */}
      <AnimatePresence>
        {isAddingNew && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Card variant="glass">
              <CardContent className="p-6">
                <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                  {editingId ? 'Editar Dirección' : 'Nueva Dirección'}
                </h3>
                
                <form onSubmit={handleSubmit} className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <AnimatedInput
                      label="Nombre"
                      placeholder="Nombre"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      required
                    />
                    
                    <AnimatedInput
                      label="Apellido"
                      placeholder="Apellido"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      required
                    />
                  </div>
                  
                  <AnimatedInput
                    label="Empresa (Opcional)"
                    placeholder="Nombre de la empresa"
                    value={formData.company}
                    onChange={(e) => handleInputChange('company', e.target.value)}
                  />
                  
                  <AnimatedInput
                    label="Dirección"
                    placeholder="Calle y número"
                    value={formData.address1}
                    onChange={(e) => handleInputChange('address1', e.target.value)}
                    required
                  />
                  
                  <AnimatedInput
                    label="Dirección 2 (Opcional)"
                    placeholder="Apartamento, suite, etc."
                    value={formData.address2}
                    onChange={(e) => handleInputChange('address2', e.target.value)}
                  />
                  
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <AnimatedInput
                      label="Ciudad"
                      placeholder="Ciudad"
                      value={formData.city}
                      onChange={(e) => handleInputChange('city', e.target.value)}
                      required
                    />
                    
                    <AnimatedSelect
                      label="Estado"
                      placeholder="Seleccionar estado"
                      options={mexicanStates}
                      value={formData.state}
                      onChange={(value) => handleInputChange('state', value)}
                      required
                    />
                    
                    <AnimatedInput
                      label="Código Postal"
                      placeholder="12345"
                      value={formData.postalCode}
                      onChange={(e) => handleInputChange('postalCode', e.target.value)}
                      required
                    />
                  </div>
                  
                  <AnimatedInput
                    type="tel"
                    label="Teléfono"
                    placeholder="+52 55 1234 5678"
                    value={formData.phone}
                    onChange={(e) => handleInputChange('phone', e.target.value)}
                    required
                  />
                  
                  <div className="flex items-center gap-3">
                    <input
                      type="checkbox"
                      id="isDefault"
                      checked={formData.isDefault}
                      onChange={(e) => handleInputChange('isDefault', e.target.checked)}
                      className="w-4 h-4 text-rich-gold focus:ring-rich-gold border-warm-camel rounded"
                    />
                    <label htmlFor="isDefault" className="text-forest-emerald dark:text-light-cloud-gray">
                      Establecer como dirección predeterminada
                    </label>
                  </div>
                  
                  <div className="flex gap-3 pt-4">
                    <AnimatedButton
                      type="submit"
                      variant="primary"
                      className="flex-1"
                    >
                      {editingId ? 'Actualizar Dirección' : 'Guardar Dirección'}
                    </AnimatedButton>
                    
                    <AnimatedButton
                      type="button"
                      variant="ghost"
                      onClick={handleCancel}
                      className="flex-1"
                    >
                      Cancelar
                    </AnimatedButton>
                  </div>
                </form>
              </CardContent>
            </Card>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Address List */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <AnimatePresence>
          {addresses.map((address, index) => (
            <motion.div
              key={address.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ delay: index * 0.1 }}
            >
              <Card 
                variant="glass" 
                className={`relative ${
                  address.id === defaultAddressId 
                    ? 'ring-2 ring-rich-gold shadow-lg' 
                    : ''
                }`}
              >
                <CardContent className="p-6">
                  {address.id === defaultAddressId && (
                    <div className="absolute top-3 right-3">
                      <span className="bg-rich-gold text-forest-emerald text-xs font-bold px-2 py-1 rounded-full">
                        Predeterminada
                      </span>
                    </div>
                  )}
                  
                  <div className="space-y-2">
                    <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                      {address.firstName} {address.lastName}
                    </h3>
                    
                    {address.company && (
                      <p className="text-warm-camel text-sm">{address.company}</p>
                    )}
                    
                    <div className="text-warm-camel text-sm space-y-1">
                      <p>{address.address1}</p>
                      {address.address2 && <p>{address.address2}</p>}
                      <p>
                        {address.city}, {mexicanStates.find(s => s.value === address.state)?.label} {address.postalCode}
                      </p>
                      <p>{address.phone}</p>
                    </div>
                  </div>
                  
                  <div className="flex gap-2 mt-4">
                    <AnimatedButton
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEdit(address)}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                        </svg>
                      }
                    >
                      Editar
                    </AnimatedButton>
                    
                    {address.id !== defaultAddressId && (
                      <AnimatedButton
                        variant="ghost"
                        size="sm"
                        onClick={() => setDefaultAddress(address.id)}
                        icon={
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                          </svg>
                        }
                      >
                        Predeterminada
                      </AnimatedButton>
                    )}
                    
                    <AnimatedButton
                      variant="danger"
                      size="sm"
                      onClick={() => handleDelete(address.id)}
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                        </svg>
                      }
                    >
                      Eliminar
                    </AnimatedButton>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      {addresses.length === 0 && !isAddingNew && (
        <Card variant="glass">
          <CardContent className="p-8 text-center">
            <div className="text-6xl mb-4">📍</div>
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
              No tienes direcciones guardadas
            </h3>
            <p className="text-warm-camel mb-6">
              Agrega una dirección para acelerar el proceso de checkout
            </p>
            <AnimatedButton
              variant="primary"
              onClick={() => setIsAddingNew(true)}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              }
            >
              Agregar Primera Dirección
            </AnimatedButton>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
