// 🛍️ TWL PRODUCTS API ENDPOINT
// 🎯 RESTful API for product operations with enterprise-grade features

import { NextResponse } from 'next/server'
import { supabase, supabaseAdmin } from '@/lib/supabase'
import { enterpriseIntegration } from '@/lib/enterpriseIntegrationService'

// 📋 GET /api/products - Get products with filtering, pagination, and search
export async function GET(request) {
  try {
    const { searchParams } = new URL(request.url)
    
    // Extract query parameters
    const page = parseInt(searchParams.get('page')) || 1
    const limit = Math.min(parseInt(searchParams.get('limit')) || 20, 100) // Max 100 items
    const search = searchParams.get('search')
    const brand = searchParams.get('brand')
    const category = searchParams.get('category')
    const gender = searchParams.get('gender')
    const featured = searchParams.get('featured') === 'true'
    const isNew = searchParams.get('new') === 'true'
    const priceMin = parseFloat(searchParams.get('price_min')) || null
    const priceMax = parseFloat(searchParams.get('price_max')) || null
    const sortBy = searchParams.get('sort') || 'created_at'
    const sortOrder = searchParams.get('order') || 'desc'

    // Build query
    let query = supabase
      .from('products')
      .select(`
        *,
        brand:brands!inner(
          id,
          name,
          slug,
          logo_url
        ),
        category:categories!inner(
          id,
          name,
          slug
        ),
        variants:product_variants(
          id,
          size,
          color,
          stock_quantity,
          price_adjustment
        )
      `, { count: 'exact' })
      .eq('status', 'active')

    // Apply filters
    if (search) {
      query = query.textSearch('search_vector', search)
    }

    if (brand) {
      query = query.eq('brands.slug', brand)
    }

    if (category) {
      query = query.eq('categories.slug', category)
    }

    if (gender && gender !== 'all') {
      query = query.eq('gender', gender)
    }

    if (featured) {
      query = query.eq('featured', true)
    }

    if (isNew) {
      query = query.eq('is_new', true)
    }

    if (priceMin !== null) {
      query = query.gte('price', priceMin)
    }

    if (priceMax !== null) {
      query = query.lte('price', priceMax)
    }

    // Apply sorting
    const validSortFields = ['created_at', 'price', 'rating', 'name']
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at'
    const ascending = sortOrder === 'asc'
    
    query = query.order(sortField, { ascending })

    // Apply pagination
    const from = (page - 1) * limit
    const to = from + limit - 1
    query = query.range(from, to)

    // Execute query
    const { data: products, error, count } = await query

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to fetch products', details: error.message },
        { status: 500 }
      )
    }

    // Log search if query provided
    if (search) {
      await supabase
        .from('search_logs')
        .insert({
          query: search,
          search_type: 'text',
          filters: { brand, category, gender, featured, isNew, priceMin, priceMax },
          results_count: count || 0
        })
        .select()
    }

    // Calculate pagination metadata
    const totalPages = Math.ceil((count || 0) / limit)
    const hasNextPage = page < totalPages
    const hasPrevPage = page > 1

    return NextResponse.json({
      success: true,
      data: products,
      pagination: {
        page,
        limit,
        total: count,
        totalPages,
        hasNextPage,
        hasPrevPage
      },
      filters: {
        search,
        brand,
        category,
        gender,
        featured,
        isNew,
        priceMin,
        priceMax,
        sortBy: sortField,
        sortOrder
      }
    })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

// 📝 POST /api/products - Create new product (Admin only)
export async function POST(request) {
  try {
    const body = await request.json()
    
    // Validate required fields
    const requiredFields = ['name', 'sku', 'brand_id', 'category_id', 'price']
    const missingFields = requiredFields.filter(field => !body[field])
    
    if (missingFields.length > 0) {
      return NextResponse.json(
        { error: 'Missing required fields', fields: missingFields },
        { status: 400 }
      )
    }

    // Check if SKU already exists
    const { data: existingProduct } = await supabase
      .from('products')
      .select('id')
      .eq('sku', body.sku)
      .single()

    if (existingProduct) {
      return NextResponse.json(
        { error: 'Product with this SKU already exists' },
        { status: 409 }
      )
    }

    // Generate slug from name
    const slug = body.name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/(^-|-$)/g, '')

    // Create product
    const productData = {
      ...body,
      slug,
      status: 'active',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }

    const { data: product, error } = await supabaseAdmin
      .from('products')
      .insert(productData)
      .select(`
        *,
        brand:brands(name, slug, logo_url),
        category:categories(name, slug)
      `)
      .single()

    if (error) {
      console.error('Database error:', error)
      return NextResponse.json(
        { error: 'Failed to create product', details: error.message },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      data: product,
      message: 'Product created successfully'
    }, { status: 201 })

  } catch (error) {
    console.error('API error:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

// 🔧 Helper function to validate product data
function validateProductData(data) {
  const errors = []

  if (!data.name || data.name.trim().length < 3) {
    errors.push('Product name must be at least 3 characters long')
  }

  if (!data.sku || data.sku.trim().length < 3) {
    errors.push('SKU must be at least 3 characters long')
  }

  if (!data.price || data.price <= 0) {
    errors.push('Price must be greater than 0')
  }

  if (data.original_price && data.original_price < data.price) {
    errors.push('Original price cannot be less than current price')
  }

  if (data.stock_quantity && data.stock_quantity < 0) {
    errors.push('Stock quantity cannot be negative')
  }

  const validGenders = ['men', 'women', 'unisex', 'kids']
  if (data.gender && !validGenders.includes(data.gender)) {
    errors.push(`Gender must be one of: ${validGenders.join(', ')}`)
  }

  const validStatuses = ['active', 'inactive', 'draft', 'archived']
  if (data.status && !validStatuses.includes(data.status)) {
    errors.push(`Status must be one of: ${validStatuses.join(', ')}`)
  }

  return errors
}

// 🔍 Helper function to build search filters
function buildSearchFilters(searchParams) {
  const filters = {}

  // Text search
  const search = searchParams.get('search')
  if (search) {
    filters.search = search.trim()
  }

  // Brand filter
  const brand = searchParams.get('brand')
  if (brand) {
    filters.brand = brand
  }

  // Category filter
  const category = searchParams.get('category')
  if (category) {
    filters.category = category
  }

  // Gender filter
  const gender = searchParams.get('gender')
  if (gender && gender !== 'all') {
    filters.gender = gender
  }

  // Boolean filters
  filters.featured = searchParams.get('featured') === 'true'
  filters.isNew = searchParams.get('new') === 'true'
  filters.inStock = searchParams.get('in_stock') !== 'false' // Default to true

  // Price range
  const priceMin = parseFloat(searchParams.get('price_min'))
  const priceMax = parseFloat(searchParams.get('price_max'))
  
  if (!isNaN(priceMin) && priceMin >= 0) {
    filters.priceMin = priceMin
  }
  
  if (!isNaN(priceMax) && priceMax > 0) {
    filters.priceMax = priceMax
  }

  // Validate price range
  if (filters.priceMin && filters.priceMax && filters.priceMin > filters.priceMax) {
    delete filters.priceMin
    delete filters.priceMax
  }

  return filters
}

// 📊 Helper function to get product analytics
async function getProductAnalytics(productId) {
  try {
    const [viewsResult, wishlistResult, ordersResult] = await Promise.all([
      // Get view count
      supabase
        .from('product_views')
        .select('id', { count: 'exact' })
        .eq('product_id', productId),
      
      // Get wishlist count
      supabase
        .from('wishlist_items')
        .select('id', { count: 'exact' })
        .eq('product_id', productId),
      
      // Get order count
      supabase
        .from('order_items')
        .select('id', { count: 'exact' })
        .eq('product_id', productId)
    ])

    return {
      views: viewsResult.count || 0,
      wishlisted: wishlistResult.count || 0,
      ordered: ordersResult.count || 0
    }
  } catch (error) {
    console.error('Error getting product analytics:', error)
    return {
      views: 0,
      wishlisted: 0,
      ordered: 0
    }
  }
}
