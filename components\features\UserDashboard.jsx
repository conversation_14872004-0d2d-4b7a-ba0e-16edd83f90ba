'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { useAuth } from '@/contexts/AuthContext'
import { useWishlist } from '@/contexts/WishlistContext'
import { useUserPreferences } from '@/contexts/UserPreferencesContext'
import AddressBook from '@/components/account/AddressBook'
import PaymentMethods from '@/components/account/PaymentMethods'
import StylePreferences from '@/components/account/StylePreferences'
import RecentlyViewed from '@/components/account/RecentlyViewed'
import { useCart } from '@/contexts/CartContext'
import Button from '@/components/ui/Button'
import { Card, CardContent } from '@/components/ui/Card'
import Badge from '@/components/ui/Badge'

const dashboardSections = [
  {
    id: 'overview',
    name: 'Resumen',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 00-2-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
      </svg>
    )
  },
  {
    id: 'orders',
    name: 'Pedidos',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
      </svg>
    )
  },
  {
    id: 'wishlist',
    name: 'Lista de Deseos',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
      </svg>
    )
  },
  {
    id: 'profile',
    name: 'Perfil',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
      </svg>
    )
  },
  {
    id: 'addresses',
    name: 'Direcciones',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
      </svg>
    )
  },
  {
    id: 'payment',
    name: 'Métodos de Pago',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
      </svg>
    )
  },
  {
    id: 'preferences',
    name: 'Preferencias de Estilo',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
      </svg>
    )
  },
  {
    id: 'recently-viewed',
    name: 'Vistos Recientemente',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
      </svg>
    )
  }
]

export default function UserDashboard({ isOpen, onClose }) {
  const { user, logout } = useAuth()
  const { summary: wishlistSummary, getAllWishlistItems } = useWishlist()
  const { getOrderHistory } = useCart()
  const [activeSection, setActiveSection] = useState('overview')

  if (!isOpen || !user) return null

  const handleLogout = () => {
    logout()
    onClose()
  }

  const renderOverview = () => {
    const wishlistItems = getAllWishlistItems()
    const orders = getOrderHistory()
    
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
            ¡Hola, {user.firstName}! 👋
          </h3>
          <p className="text-warm-camel">
            Bienvenido de vuelta a tu cuenta de The White Laces
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <Card variant="glass" className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-rich-gold mb-1">
                {orders.length}
              </div>
              <div className="text-sm text-warm-camel">Pedidos</div>
            </CardContent>
          </Card>
          
          <Card variant="glass" className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-rich-gold mb-1">
                {wishlistItems.length}
              </div>
              <div className="text-sm text-warm-camel">Favoritos</div>
            </CardContent>
          </Card>
          
          <Card variant="glass" className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-rich-gold mb-1">
                {wishlistSummary.totalLists}
              </div>
              <div className="text-sm text-warm-camel">Listas</div>
            </CardContent>
          </Card>
          
          <Card variant="glass" className="text-center">
            <CardContent className="p-4">
              <div className="text-2xl font-bold text-rich-gold mb-1">
                VIP
              </div>
              <div className="text-sm text-warm-camel">Nivel</div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <Card variant="glass">
          <CardContent className="p-6">
            <h4 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
              Actividad Reciente
            </h4>
            
            <div className="space-y-3">
              {orders.slice(0, 3).map((order, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-warm-camel/5 rounded-lg">
                  <div>
                    <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                      Pedido #{order.id}
                    </p>
                    <p className="text-sm text-warm-camel">
                      {order.items.length} productos • ${order.total.toLocaleString()} MXN
                    </p>
                  </div>
                  <Badge variant="success" size="sm">
                    {order.status}
                  </Badge>
                </div>
              ))}
              
              {orders.length === 0 && (
                <div className="text-center py-8">
                  <p className="text-warm-camel">No tienes pedidos recientes</p>
                  <Button variant="ghost" size="sm" className="mt-2" onClick={onClose}>
                    Explorar productos
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const renderOrders = () => {
    const orders = getOrderHistory()
    
    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
            Mis Pedidos
          </h3>
          <p className="text-warm-camel">
            Historial completo de tus compras
          </p>
        </div>

        <div className="space-y-4">
          {orders.map((order, index) => (
            <Card key={index} variant="glass">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div>
                    <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                      Pedido #{order.id}
                    </h4>
                    <p className="text-sm text-warm-camel">
                      {new Date(order.date).toLocaleDateString('es-MX')}
                    </p>
                  </div>
                  <Badge variant="success" size="sm">
                    {order.status}
                  </Badge>
                </div>
                
                <div className="space-y-2 mb-4">
                  {order.items.slice(0, 2).map((item, itemIndex) => (
                    <div key={itemIndex} className="flex items-center gap-3">
                      <div className="w-12 h-12 bg-gradient-to-br from-warm-camel/20 to-rich-gold/20 rounded-lg flex items-center justify-center">
                        <span className="text-xs text-forest-emerald">IMG</span>
                      </div>
                      <div className="flex-1">
                        <p className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                          {item.name}
                        </p>
                        <p className="text-sm text-warm-camel">
                          Talla {item.size} • Cantidad: {item.quantity}
                        </p>
                      </div>
                      <p className="font-medium text-rich-gold">
                        ${item.price.toLocaleString()} MXN
                      </p>
                    </div>
                  ))}
                  
                  {order.items.length > 2 && (
                    <p className="text-sm text-warm-camel">
                      +{order.items.length - 2} productos más
                    </p>
                  )}
                </div>
                
                <div className="flex items-center justify-between pt-4 border-t border-warm-camel/20">
                  <p className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                    Total: ${order.total.toLocaleString()} MXN
                  </p>
                  <div className="flex gap-2">
                    <Button variant="ghost" size="sm">
                      Ver detalles
                    </Button>
                    <Button variant="ghost" size="sm">
                      Rastrear
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {orders.length === 0 && (
            <Card variant="glass">
              <CardContent className="p-12 text-center">
                <div className="w-16 h-16 bg-warm-camel/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <svg className="w-8 h-8 text-warm-camel" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                  </svg>
                </div>
                <h4 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                  No tienes pedidos aún
                </h4>
                <p className="text-warm-camel mb-4">
                  Explora nuestra colección y realiza tu primera compra
                </p>
                <Button variant="primary" onClick={onClose}>
                  Explorar productos
                </Button>
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    )
  }

  const renderWishlist = () => {
    const wishlistItems = getAllWishlistItems()
    
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
              Mi Lista de Deseos
            </h3>
            <p className="text-warm-camel">
              {wishlistItems.length} productos guardados
            </p>
          </div>
          <Button variant="ghost" size="sm">
            Crear nueva lista
          </Button>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {wishlistItems.map((item, index) => (
            <Card key={index} variant="glass">
              <CardContent className="p-4">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-warm-camel/20 to-rich-gold/20 rounded-lg flex items-center justify-center">
                    <span className="text-sm text-forest-emerald">IMG</span>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-medium text-forest-emerald dark:text-light-cloud-gray">
                      {item.product.name}
                    </h4>
                    <p className="text-sm text-warm-camel mb-1">
                      {item.product.brand}
                    </p>
                    <p className="font-semibold text-rich-gold">
                      ${item.product.price.toLocaleString()} MXN
                    </p>
                  </div>
                  <div className="flex flex-col gap-2">
                    <Button variant="ghost" size="sm">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01" />
                      </svg>
                    </Button>
                    <Button variant="ghost" size="sm">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                      </svg>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
          
          {wishlistItems.length === 0 && (
            <div className="col-span-full">
              <Card variant="glass">
                <CardContent className="p-12 text-center">
                  <div className="w-16 h-16 bg-warm-camel/10 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg className="w-8 h-8 text-warm-camel" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                    </svg>
                  </div>
                  <h4 className="text-lg font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    Tu lista está vacía
                  </h4>
                  <p className="text-warm-camel mb-4">
                    Guarda tus productos favoritos para encontrarlos fácilmente
                  </p>
                  <Button variant="primary" onClick={onClose}>
                    Explorar productos
                  </Button>
                </CardContent>
              </Card>
            </div>
          )}
        </div>
      </div>
    )
  }

  const renderProfile = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
          Mi Perfil
        </h3>
        <p className="text-warm-camel">
          Gestiona tu información personal
        </p>
      </div>

      <Card variant="glass">
        <CardContent className="p-6">
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-warm-camel mb-1">
                  Nombre
                </label>
                <p className="text-forest-emerald dark:text-light-cloud-gray">
                  {user.firstName}
                </p>
              </div>
              <div>
                <label className="block text-sm font-medium text-warm-camel mb-1">
                  Apellido
                </label>
                <p className="text-forest-emerald dark:text-light-cloud-gray">
                  {user.lastName}
                </p>
              </div>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-warm-camel mb-1">
                Email
              </label>
              <p className="text-forest-emerald dark:text-light-cloud-gray">
                {user.email}
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-warm-camel mb-1">
                Teléfono
              </label>
              <p className="text-forest-emerald dark:text-light-cloud-gray">
                {user.phone || 'No especificado'}
              </p>
            </div>
            
            <div className="pt-4">
              <Button variant="primary">
                Editar perfil
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )

  const renderContent = () => {
    switch (activeSection) {
      case 'overview':
        return renderOverview()
      case 'orders':
        return renderOrders()
      case 'wishlist':
        return renderWishlist()
      case 'profile':
        return renderProfile()
      case 'addresses':
        return <AddressBook />
      case 'payment':
        return <PaymentMethods />
      case 'preferences':
        return <StylePreferences />
      case 'recently-viewed':
        return <RecentlyViewed />
      default:
        return renderOverview()
    }
  }

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center p-4"
      onClick={onClose}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.9, y: 20 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.9, y: 20 }}
        className="glass-card rounded-2xl max-w-6xl w-full max-h-[90vh] overflow-hidden"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex h-full">
          {/* Sidebar */}
          <div className="w-64 border-r border-warm-camel/20 p-6">
            <div className="flex items-center gap-3 mb-8">
              <div className="w-12 h-12 bg-gradient-natural rounded-full flex items-center justify-center">
                <span className="text-lg font-bold text-forest-emerald">
                  {user.firstName?.[0]}{user.lastName?.[0]}
                </span>
              </div>
              <div>
                <p className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                  {user.firstName} {user.lastName}
                </p>
                <p className="text-sm text-warm-camel">Miembro VIP</p>
              </div>
            </div>

            <nav className="space-y-2">
              {dashboardSections.map((section) => (
                <button
                  key={section.id}
                  onClick={() => setActiveSection(section.id)}
                  className={`w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left transition-colors ${
                    activeSection === section.id
                      ? 'bg-rich-gold/20 text-rich-gold'
                      : 'text-warm-camel hover:bg-warm-camel/10'
                  }`}
                >
                  {section.icon}
                  {section.name}
                </button>
              ))}
              
              <button
                onClick={handleLogout}
                className="w-full flex items-center gap-3 px-3 py-2 rounded-lg text-left text-warm-camel hover:bg-warm-camel/10 transition-colors mt-8"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Cerrar sesión
              </button>
            </nav>
          </div>

          {/* Main Content */}
          <div className="flex-1 flex flex-col">
            {/* Header */}
            <div className="flex items-center justify-between p-6 border-b border-warm-camel/20">
              <h2 className="text-2xl font-bold text-forest-emerald dark:text-light-cloud-gray">
                {dashboardSections.find(s => s.id === activeSection)?.name}
              </h2>
              <Button variant="ghost" size="icon" onClick={onClose}>
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </Button>
            </div>

            {/* Content */}
            <div className="flex-1 p-6 overflow-y-auto">
              {renderContent()}
            </div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}
