// 🏢 TWL ADMIN DASHBOARD HOME
// 🎯 Executive overview with key metrics and insights

'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import {
  CurrencyDollarIcon,
  ShoppingBagIcon,
  UsersIcon,
  TruckIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  EyeIcon,
  HeartIcon
} from '@heroicons/react/24/outline'
import { supabase } from '@/lib/supabase'

export default function AdminDashboard() {
  const [metrics, setMetrics] = useState({
    revenue: { current: 0, previous: 0, change: 0 },
    orders: { current: 0, previous: 0, change: 0 },
    customers: { current: 0, previous: 0, change: 0 },
    products: { current: 0, previous: 0, change: 0 }
  })
  const [recentOrders, setRecentOrders] = useState([])
  const [topProducts, setTopProducts] = useState([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      
      // Load metrics in parallel
      const [
        revenueData,
        ordersData,
        customersData,
        productsData,
        recentOrdersData,
        topProductsData
      ] = await Promise.all([
        loadRevenueMetrics(),
        loadOrdersMetrics(),
        loadCustomersMetrics(),
        loadProductsMetrics(),
        loadRecentOrders(),
        loadTopProducts()
      ])

      setMetrics({
        revenue: revenueData,
        orders: ordersData,
        customers: customersData,
        products: productsData
      })
      
      setRecentOrders(recentOrdersData)
      setTopProducts(topProductsData)
      
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const loadRevenueMetrics = async () => {
    // Get current month revenue
    const currentMonth = new Date()
    const firstDayCurrentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
    const firstDayPreviousMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)
    const lastDayPreviousMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 0)

    const { data: currentRevenue } = await supabase
      .from('orders')
      .select('total_amount')
      .gte('created_at', firstDayCurrentMonth.toISOString())
      .eq('payment_status', 'completed')

    const { data: previousRevenue } = await supabase
      .from('orders')
      .select('total_amount')
      .gte('created_at', firstDayPreviousMonth.toISOString())
      .lt('created_at', firstDayCurrentMonth.toISOString())
      .eq('payment_status', 'completed')

    const current = currentRevenue?.reduce((sum, order) => sum + order.total_amount, 0) || 0
    const previous = previousRevenue?.reduce((sum, order) => sum + order.total_amount, 0) || 0
    const change = previous > 0 ? ((current - previous) / previous) * 100 : 0

    return { current, previous, change }
  }

  const loadOrdersMetrics = async () => {
    const today = new Date()
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)

    const { count: currentOrders } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', today.toISOString().split('T')[0])

    const { count: previousOrders } = await supabase
      .from('orders')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', yesterday.toISOString().split('T')[0])
      .lt('created_at', today.toISOString().split('T')[0])

    const change = previousOrders > 0 ? ((currentOrders - previousOrders) / previousOrders) * 100 : 0

    return { current: currentOrders || 0, previous: previousOrders || 0, change }
  }

  const loadCustomersMetrics = async () => {
    const currentMonth = new Date()
    const firstDayCurrentMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
    const firstDayPreviousMonth = new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1, 1)

    const { count: currentCustomers } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', firstDayCurrentMonth.toISOString())

    const { count: previousCustomers } = await supabase
      .from('users')
      .select('*', { count: 'exact', head: true })
      .gte('created_at', firstDayPreviousMonth.toISOString())
      .lt('created_at', firstDayCurrentMonth.toISOString())

    const change = previousCustomers > 0 ? ((currentCustomers - previousCustomers) / previousCustomers) * 100 : 0

    return { current: currentCustomers || 0, previous: previousCustomers || 0, change }
  }

  const loadProductsMetrics = async () => {
    const { count: totalProducts } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')

    const { count: lowStockProducts } = await supabase
      .from('products')
      .select('*', { count: 'exact', head: true })
      .eq('status', 'active')
      .lt('stock_quantity', 10)

    return { 
      current: totalProducts || 0, 
      previous: lowStockProducts || 0, 
      change: 0 // This represents low stock count, not a change
    }
  }

  const loadRecentOrders = async () => {
    const { data } = await supabase
      .from('orders')
      .select(`
        id,
        order_number,
        total_amount,
        status,
        created_at,
        user:users(first_name, last_name, email)
      `)
      .order('created_at', { ascending: false })
      .limit(5)

    return data || []
  }

  const loadTopProducts = async () => {
    const { data } = await supabase
      .from('product_analytics')
      .select('*')
      .order('order_count', { ascending: false })
      .limit(5)

    return data || []
  }

  const formatCurrency = (amount) => {
    return new Intl.NumberFormat('es-MX', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0
    }).format(amount)
  }

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString('es-MX', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  const getStatusColor = (status) => {
    const colors = {
      pending: 'bg-yellow-100 text-yellow-800',
      processing: 'bg-blue-100 text-blue-800',
      shipped: 'bg-green-100 text-green-800',
      delivered: 'bg-green-100 text-green-800',
      cancelled: 'bg-red-100 text-red-800'
    }
    return colors[status] || 'bg-gray-100 text-gray-800'
  }

  if (loading) {
    return (
      <div className="px-4 sm:px-6 lg:px-8">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="h-32 bg-gray-200 rounded-lg"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="px-4 sm:px-6 lg:px-8">
      {/* Header */}
      <div className="mb-8">
        <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
          Dashboard
        </h1>
        <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
          Welcome back! Here's what's happening with your store today.
        </p>
      </div>

      {/* Metrics Cards */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4 mb-8">
        <MetricCard
          title="Revenue"
          value={formatCurrency(metrics.revenue.current)}
          change={metrics.revenue.change}
          icon={CurrencyDollarIcon}
          color="bg-green-500"
        />
        <MetricCard
          title="Orders Today"
          value={metrics.orders.current.toString()}
          change={metrics.orders.change}
          icon={ShoppingBagIcon}
          color="bg-blue-500"
        />
        <MetricCard
          title="New Customers"
          value={metrics.customers.current.toString()}
          change={metrics.customers.change}
          icon={UsersIcon}
          color="bg-purple-500"
        />
        <MetricCard
          title="Products"
          value={metrics.products.current.toString()}
          subtitle={`${metrics.products.previous} low stock`}
          icon={TruckIcon}
          color="bg-orange-500"
        />
      </div>

      {/* Content Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Recent Orders */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Recent Orders
            </h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {recentOrders.map((order) => (
              <div key={order.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {order.order_number}
                    </p>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      {order.user?.first_name} {order.user?.last_name}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(order.total_amount)}
                    </p>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(order.status)}`}>
                      {order.status}
                    </span>
                  </div>
                </div>
                <p className="mt-1 text-xs text-gray-500 dark:text-gray-400">
                  {formatDate(order.created_at)}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Top Products */}
        <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
          <div className="px-6 py-4 border-b border-gray-200 dark:border-gray-700">
            <h3 className="text-lg font-medium text-gray-900 dark:text-white">
              Top Products
            </h3>
          </div>
          <div className="divide-y divide-gray-200 dark:divide-gray-700">
            {topProducts.map((product, index) => (
              <div key={product.id} className="px-6 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <span className="flex-shrink-0 w-6 h-6 bg-lime-green text-black rounded-full flex items-center justify-center text-xs font-bold">
                      {index + 1}
                    </span>
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">
                        {product.name}
                      </p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {product.brand_name}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(product.price)}
                    </p>
                    <div className="flex items-center space-x-2 text-xs text-gray-500 dark:text-gray-400">
                      <span className="flex items-center">
                        <EyeIcon className="w-3 h-3 mr-1" />
                        {product.view_count}
                      </span>
                      <span className="flex items-center">
                        <HeartIcon className="w-3 h-3 mr-1" />
                        {product.wishlist_count}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}

// Metric Card Component
function MetricCard({ title, value, change, subtitle, icon: Icon, color }) {
  const isPositive = change > 0
  const isNegative = change < 0

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className="bg-white dark:bg-gray-800 overflow-hidden shadow rounded-lg"
    >
      <div className="p-5">
        <div className="flex items-center">
          <div className="flex-shrink-0">
            <div className={`w-8 h-8 ${color} rounded-md flex items-center justify-center`}>
              <Icon className="w-5 h-5 text-white" />
            </div>
          </div>
          <div className="ml-5 w-0 flex-1">
            <dl>
              <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                {title}
              </dt>
              <dd className="text-lg font-medium text-gray-900 dark:text-white">
                {value}
              </dd>
            </dl>
          </div>
        </div>
        {(change !== undefined || subtitle) && (
          <div className="mt-3">
            {subtitle ? (
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {subtitle}
              </p>
            ) : (
              <div className="flex items-center">
                {isPositive && (
                  <ArrowUpIcon className="w-4 h-4 text-green-500 mr-1" />
                )}
                {isNegative && (
                  <ArrowDownIcon className="w-4 h-4 text-red-500 mr-1" />
                )}
                <span className={`text-sm font-medium ${
                  isPositive ? 'text-green-600' : isNegative ? 'text-red-600' : 'text-gray-500'
                }`}>
                  {Math.abs(change).toFixed(1)}%
                </span>
                <span className="text-sm text-gray-500 dark:text-gray-400 ml-1">
                  vs last period
                </span>
              </div>
            )}
          </div>
        )}
      </div>
    </motion.div>
  )
}
