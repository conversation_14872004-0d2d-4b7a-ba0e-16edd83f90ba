'use client'

import { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'
import CheckoutFlow from '@/components/features/CheckoutFlow'
import { useCart } from '@/contexts/CartContext'

export default function CheckoutPage() {
  const { items, getTotalPrice, getItemsCount, clearCart } = useCart()
  const [isProcessing, setIsProcessing] = useState(false)
  const [orderComplete, setOrderComplete] = useState(false)
  const [orderNumber, setOrderNumber] = useState(null)

  // Redirect if cart is empty and no order is complete
  useEffect(() => {
    if (getItemsCount() === 0 && !orderComplete) {
      // In a real app, you might want to redirect to cart or shop
      console.log('Cart is empty, consider redirecting')
    }
  }, [getItemsCount, orderComplete])

  const handleOrderComplete = async (orderData) => {
    setIsProcessing(true)

    try {
      // Simulate order processing
      await new Promise(resolve => setTimeout(resolve, 2000))

      // Generate order number
      const newOrderNumber = `TWL-${Date.now().toString().slice(-6)}`
      setOrderNumber(newOrderNumber)
      setOrderComplete(true)

      // Clear cart
      clearCart()

      console.log('Order completed:', { orderNumber: newOrderNumber, ...orderData })
    } catch (error) {
      console.error('Order processing failed:', error)
    } finally {
      setIsProcessing(false)
    }
  }

  // Order confirmation view
  if (orderComplete) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center"
          >
            <Card variant="glass">
              <CardContent className="p-12">
                <motion.div
                  className="text-6xl mb-6"
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 10, -10, 0]
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    repeatDelay: 3
                  }}
                >
                  🎉
                </motion.div>

                <h1 className="text-3xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  ¡Pedido Confirmado!
                </h1>

                <p className="text-warm-camel text-lg mb-6">
                  Tu pedido ha sido procesado exitosamente
                </p>

                <div className="bg-rich-gold/10 border border-rich-gold/30 rounded-lg p-6 mb-8">
                  <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    Número de Pedido
                  </h2>
                  <p className="text-2xl font-bold text-rich-gold">
                    {orderNumber}
                  </p>
                </div>

                <div className="space-y-4 text-warm-camel mb-8">
                  <p>📧 Te hemos enviado un email de confirmación</p>
                  <p>📱 Recibirás notificaciones sobre el estado de tu envío</p>
                  <p>🚚 Tiempo estimado de entrega: 3-5 días hábiles</p>
                </div>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <TransitionLink href="/orders">
                    <AnimatedButton
                      variant="primary"
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                      }
                    >
                      Ver Mis Pedidos
                    </AnimatedButton>
                  </TransitionLink>

                  <TransitionLink href="/shop">
                    <AnimatedButton
                      variant="secondary"
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                      }
                    >
                      Seguir Comprando
                    </AnimatedButton>
                  </TransitionLink>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  // Empty cart view
  if (getItemsCount() === 0) {
    return (
      <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="text-center"
          >
            <Card variant="glass">
              <CardContent className="p-12">
                <div className="text-6xl mb-6">🛒</div>

                <h1 className="text-3xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
                  Tu carrito está vacío
                </h1>

                <p className="text-warm-camel text-lg mb-8">
                  Agrega algunos productos increíbles antes de proceder al checkout
                </p>

                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <TransitionLink href="/shop">
                    <AnimatedButton
                      variant="primary"
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                        </svg>
                      }
                    >
                      Explorar Productos
                    </AnimatedButton>
                  </TransitionLink>

                  <TransitionLink href="/">
                    <AnimatedButton
                      variant="secondary"
                      icon={
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                        </svg>
                      }
                    >
                      Volver al Inicio
                    </AnimatedButton>
                  </TransitionLink>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    )
  }

  // Main checkout view
  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-8"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Checkout
          </h1>
          <p className="text-warm-camel">
            Completa tu pedido de forma segura
          </p>
        </motion.div>

        {/* Progress Indicator */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="mb-8"
        >
          <div className="flex items-center justify-center space-x-4">
            <div className="flex items-center">
              <div className="w-8 h-8 bg-rich-gold rounded-full flex items-center justify-center text-forest-emerald font-bold text-sm">
                1
              </div>
              <span className="ml-2 text-sm font-medium text-forest-emerald dark:text-light-cloud-gray">
                Carrito
              </span>
            </div>

            <div className="w-12 h-0.5 bg-rich-gold"></div>

            <div className="flex items-center">
              <div className="w-8 h-8 bg-rich-gold rounded-full flex items-center justify-center text-forest-emerald font-bold text-sm">
                2
              </div>
              <span className="ml-2 text-sm font-medium text-forest-emerald dark:text-light-cloud-gray">
                Checkout
              </span>
            </div>

            <div className="w-12 h-0.5 bg-warm-camel/30"></div>

            <div className="flex items-center">
              <div className="w-8 h-8 bg-warm-camel/30 rounded-full flex items-center justify-center text-warm-camel font-bold text-sm">
                3
              </div>
              <span className="ml-2 text-sm font-medium text-warm-camel">
                Confirmación
              </span>
            </div>
          </div>
        </motion.div>

        {/* Checkout Content */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">

          {/* Checkout Form */}
          <div className="lg:col-span-2">
            <motion.div
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.2 }}
            >
              <CheckoutFlow
                onOrderComplete={handleOrderComplete}
                isProcessing={isProcessing}
              />
            </motion.div>
          </div>

          {/* Order Summary */}
          <div className="lg:col-span-1">
            <motion.div
              initial={{ opacity: 0, x: 20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: 0.3 }}
              className="sticky top-24"
            >
              <Card variant="glass">
                <CardContent className="p-6">
                  <h2 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-4">
                    Resumen del Pedido
                  </h2>

                  {/* Cart Items */}
                  <div className="space-y-4 mb-6">
                    {items.map((item) => (
                      <div key={`${item.id}-${item.size}-${item.color}`} className="flex gap-3">
                        <div className="w-16 h-16 bg-warm-camel/10 rounded-lg flex items-center justify-center">
                          <span className="text-2xl">👟</span>
                        </div>
                        <div className="flex-1">
                          <h3 className="font-medium text-forest-emerald dark:text-light-cloud-gray text-sm line-clamp-2">
                            {item.name}
                          </h3>
                          <p className="text-warm-camel text-xs">
                            {item.color} • Talla {item.size}
                          </p>
                          <div className="flex justify-between items-center mt-1">
                            <span className="text-warm-camel text-xs">
                              Cantidad: {item.quantity}
                            </span>
                            <span className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm">
                              ${(item.price * item.quantity).toLocaleString()} MXN
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>

                  {/* Totals */}
                  <div className="border-t border-warm-camel/20 pt-4 space-y-2">
                    <div className="flex justify-between text-sm">
                      <span className="text-warm-camel">Subtotal:</span>
                      <span className="text-forest-emerald dark:text-light-cloud-gray">
                        ${getTotalPrice().toLocaleString()} MXN
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-warm-camel">Envío:</span>
                      <span className="text-forest-emerald dark:text-light-cloud-gray">
                        {getTotalPrice() >= 2500 ? 'Gratis' : '$150 MXN'}
                      </span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span className="text-warm-camel">IVA (16%):</span>
                      <span className="text-forest-emerald dark:text-light-cloud-gray">
                        ${Math.round(getTotalPrice() * 0.16).toLocaleString()} MXN
                      </span>
                    </div>
                    <div className="border-t border-warm-camel/20 pt-2">
                      <div className="flex justify-between font-semibold">
                        <span className="text-forest-emerald dark:text-light-cloud-gray">Total:</span>
                        <span className="text-forest-emerald dark:text-light-cloud-gray">
                          ${Math.round(getTotalPrice() * 1.16 + (getTotalPrice() >= 2500 ? 0 : 150)).toLocaleString()} MXN
                        </span>
                      </div>
                    </div>
                  </div>

                  {/* Benefits */}
                  <div className="mt-6 space-y-2">
                    <div className="flex items-center gap-2 text-sm text-warm-camel">
                      <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Envío asegurado</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-warm-camel">
                      <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Garantía de autenticidad</span>
                    </div>
                    <div className="flex items-center gap-2 text-sm text-warm-camel">
                      <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      <span>Devoluciones fáciles</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  )
}
