'use client'

import { useState } from 'react'
import Image from 'next/image'
import Button from '@/components/ui/Button'

export default function Hero() {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <section className="relative min-h-screen bg-white flex items-center justify-center overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-screen py-20">

          {/* Left Content */}
          <div className="space-y-8 text-center lg:text-left">
            {/* Large "laces" Typography */}
            <div className="space-y-4">
              <h1 className="text-6xl sm:text-7xl md:text-8xl lg:text-9xl font-bold text-black leading-none tracking-tight">
                laces
              </h1>
              <p className="text-lg sm:text-xl text-gray-600 max-w-md mx-auto lg:mx-0">
                Descubre la colección más exclusiva de sneakers premium y ediciones limitadas
              </p>
            </div>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button
                size="lg"
                variant="primary"
                className="bg-black text-white hover:bg-gray-800 px-8 py-4 text-lg"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                Explorar Colección
              </Button>
              <Button
                size="lg"
                variant="secondary"
                className="border-2 border-black text-black hover:bg-black hover:text-white px-8 py-4 text-lg"
              >
                Ver Catálogo
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8 max-w-md mx-auto lg:mx-0">
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-black">500+</div>
                <div className="text-sm text-gray-500">Productos</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-black">50+</div>
                <div className="text-sm text-gray-500">Marcas</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-black">24/7</div>
                <div className="text-sm text-gray-500">Soporte</div>
              </div>
            </div>
          </div>

      {/* Interactive Floating Elements */}
      <motion.div
        className="absolute top-20 left-10 w-20 h-20 bg-chrome-metallic/30 rounded-full blur-xl"
        animate={{
          y: [0, -20, 0],
          x: [0, 10, 0],
          scale: [1, 1.1, 1],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut"
        }}
        style={{
          x: mousePosition.x * 30,
          y: mousePosition.y * 20,
        }}
      />

      <motion.div
        className="absolute bottom-32 right-16 w-32 h-32 bg-primary/30 rounded-full blur-xl"
        animate={{
          y: [0, 15, 0],
          x: [0, -15, 0],
          scale: [1, 0.9, 1],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 2
        }}
        style={{
          x: mousePosition.x * -40,
          y: mousePosition.y * -25,
        }}
      />

      <motion.div
        className="absolute top-1/2 left-1/4 w-16 h-16 bg-platinum-silver/30 rounded-full blur-xl"
        animate={{
          y: [0, -10, 0],
          x: [0, 20, 0],
          scale: [1, 1.2, 1],
        }}
        transition={{
          duration: 7,
          repeat: Infinity,
          ease: "easeInOut",
          delay: 4
        }}
        style={{
          x: mousePosition.x * 25,
          y: mousePosition.y * 30,
        }}
      />

      {/* Enhanced Particle System with Natural Elements */}
      {[...Array(15)].map((_, i) => (
        <motion.div
          key={i}
          className={`absolute rounded-full ${
            i % 3 === 0 ? 'w-2 h-2 bg-primary/30' :
            i % 3 === 1 ? 'w-1 h-1 bg-chrome-metallic/40' :
            'w-1.5 h-1.5 bg-platinum-silver/25'
          }`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -120, 0],
            x: [0, Math.random() * 40 - 20, 0],
            opacity: [0, 0.8, 0],
            scale: [0, 1.2, 0],
            rotate: [0, 360, 0],
          }}
          transition={{
            duration: Math.random() * 4 + 3,
            repeat: Infinity,
            delay: Math.random() * 3,
            ease: "easeInOut"
          }}
        />
      ))}

      {/* Floating Geometric Shapes */}
      {[...Array(8)].map((_, i) => (
        <motion.div
          key={`geo-${i}`}
          className={`absolute ${
            i % 4 === 0 ? 'w-3 h-3 bg-primary/20 rotate-45' :
            i % 4 === 1 ? 'w-4 h-4 bg-chrome-metallic/15 rounded-full' :
            i % 4 === 2 ? 'w-2 h-6 bg-platinum-silver/20' :
            'w-5 h-1 bg-primary/25'
          }`}
          style={{
            left: `${Math.random() * 100}%`,
            top: `${Math.random() * 100}%`,
          }}
          animate={{
            y: [0, -80, 0],
            x: [0, Math.random() * 60 - 30, 0],
            opacity: [0, 0.6, 0],
            rotate: [0, 180, 360],
            scale: [0.5, 1, 0.5],
          }}
          transition={{
            duration: Math.random() * 6 + 4,
            repeat: Infinity,
            delay: Math.random() * 4,
            ease: "easeInOut"
          }}
        />
      ))}

      {/* Interactive Content */}
      <motion.div
        className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center"
        style={{
          rotateX,
          rotateY,
          transformStyle: "preserve-3d"
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 1, ease: "easeOut" }}
          className="space-y-8"
          style={{ opacity }}
        >
          {/* Interactive Badge */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            transition={{ duration: 0.8, delay: 0.3, type: "spring", stiffness: 100 }}
            className="inline-flex"
            whileHover={{
              scale: 1.05,
              boxShadow: "0 0 25px rgba(0, 249, 255, 0.4)"
            }}
            whileTap={{ scale: 0.95 }}
          >
            <motion.div
              className="glass-card px-6 py-3 text-sm font-medium text-primary border border-primary/30 cursor-pointer"
              animate={{
                borderColor: isHovered
                  ? ["rgba(191, 255, 0, 0.3)", "rgba(191, 255, 0, 0.8)", "rgba(191, 255, 0, 0.3)"]
                  : "rgba(191, 255, 0, 0.3)"
              }}
              transition={{ duration: 2, repeat: Infinity }}
            >
              <motion.span
                animate={{
                  textShadow: isHovered
                    ? "0 0 10px rgba(191, 255, 0, 0.8)"
                    : "0 0 0px rgba(191, 255, 0, 0)"
                }}
              >
                ✨ Descubre calzado streetwear de lujo en The White Laces
              </motion.span>
            </motion.div>
          </motion.div>

          {/* Animated Main Title */}
          <motion.h1
            initial={{ opacity: 0, y: 50 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.5, type: "spring", stiffness: 80 }}
            className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-montserrat font-bold px-4"
            style={{
              transform: `translateZ(50px)`,
            }}
          >
            <motion.span
              className="text-jet-black block"
              animate={{
                backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"],
              }}
              transition={{
                duration: 5,
                repeat: Infinity,
                ease: "linear"
              }}
              style={{
                backgroundSize: "200% 200%",
              }}
              whileHover={{
                scale: 1.02,
                textShadow: "0 0 30px rgba(191, 255, 0, 0.5)"
              }}
            >
              Lujo Urbano Redefinido
            </motion.span>
          </motion.h1>

          {/* Animated Description */}
          <motion.p
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.7, type: "spring", stiffness: 60 }}
            className="text-base sm:text-lg md:text-xl text-jet-black/90 max-w-3xl mx-auto leading-relaxed px-4 font-inter"
            style={{
              transform: `translateZ(30px)`,
            }}
            whileHover={{
              scale: 1.02,
              color: "#0B0B0B"
            }}
          >
            <motion.span
              animate={{
                opacity: [0.8, 1, 0.8],
              }}
              transition={{
                duration: 3,
                repeat: Infinity,
                ease: "easeInOut"
              }}
            >
              Sneakers premium, ediciones limitadas y drops exclusivos de las mejores marcas. México primero, LATAM listo.
            </motion.span>
          </motion.p>

          {/* Interactive CTA Buttons */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 0.9, type: "spring", stiffness: 70 }}
            className="flex flex-col sm:flex-row gap-4 sm:gap-6 justify-center items-center pt-8 px-4"
            style={{
              transform: `translateZ(40px)`,
            }}
          >
            <motion.div
              whileHover={{
                scale: 1.05,
                rotateY: 5,
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <Button
                size="lg"
                variant="primary"
                className="w-full sm:w-auto min-w-[200px] relative overflow-hidden group"
              >
                <motion.span
                  className="relative z-10"
                  animate={{
                    x: isHovered ? [0, 2, 0] : 0,
                  }}
                  transition={{ duration: 0.3 }}
                >
                  Explorar Colección
                </motion.span>
                <motion.div
                  className="absolute inset-0 bg-secondary opacity-0 group-hover:opacity-20"
                  initial={{ x: "-100%" }}
                  whileHover={{ x: "0%" }}
                  transition={{ duration: 0.3 }}
                />
              </Button>
            </motion.div>

            <motion.div
              whileHover={{
                scale: 1.05,
                rotateY: -5,
              }}
              whileTap={{ scale: 0.95 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
            >
              <Button
                size="lg"
                variant="secondary"
                className="w-full sm:w-auto min-w-[200px] relative overflow-hidden group"
              >
                <motion.span
                  className="relative z-10"
                  animate={{
                    x: isHovered ? [0, -2, 0] : 0,
                  }}
                  transition={{ duration: 0.3 }}
                >
                  Ver Drops Limitados
                </motion.span>
                <motion.div
                  className="absolute inset-0 bg-info opacity-0 group-hover:opacity-20"
                  initial={{ x: "100%" }}
                  whileHover={{ x: "0%" }}
                  transition={{ duration: 0.3 }}
                />
                {/* Ripple effect */}
                <motion.div
                  className="absolute inset-0 rounded-lg"
                  whileHover={{
                    boxShadow: [
                      "0 0 0 0 rgba(193, 168, 136, 0.4)",
                      "0 0 0 10px rgba(193, 168, 136, 0.1)",
                      "0 0 0 20px rgba(193, 168, 136, 0)"
                    ]
                  }}
                  transition={{ duration: 0.6 }}
                />
              </Button>
            </motion.div>
          </motion.div>

          {/* Animated Stats */}
          <motion.div
            initial={{ opacity: 0, y: 40 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 1, delay: 1.1, type: "spring", stiffness: 60 }}
            className="grid grid-cols-1 sm:grid-cols-3 gap-4 sm:gap-8 pt-12 sm:pt-16 max-w-2xl mx-auto px-4"
            style={{
              transform: `translateZ(20px)`,
            }}
          >
            {[
              { value: "500+", label: "Marcas Premium", color: "text-secondary", delay: 0 },
              { value: "50K+", label: "Clientes Satisfechos", color: "text-info", delay: 0.2 },
              { value: "24/7", label: "Soporte Premium", color: "text-success", delay: 0.4 }
            ].map((stat, index) => (
              <motion.div
                key={index}
                className="text-center"
                initial={{ opacity: 0, scale: 0.5 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{
                  duration: 0.6,
                  delay: 1.3 + stat.delay,
                  type: "spring",
                  stiffness: 100
                }}
                whileHover={{
                  scale: 1.15,
                  y: -8,
                  rotateY: 5,
                }}
              >
                <motion.div
                  className={`text-2xl sm:text-3xl font-bold ${stat.color} relative`}
                  animate={{
                    textShadow: [
                      "0 0 0px rgba(255, 215, 0, 0)",
                      "0 0 25px rgba(255, 215, 0, 0.6)",
                      "0 0 0px rgba(255, 215, 0, 0)"
                    ]
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    delay: stat.delay
                  }}
                  whileHover={{
                    scale: 1.1,
                    textShadow: `0 0 30px rgba(255, 215, 0, 0.8)`
                  }}
                >
                  {stat.value}
                  {/* Animated underline */}
                  <motion.div
                    className={`absolute -bottom-1 left-0 h-0.5 bg-${stat.color}`}
                    initial={{ width: "0%" }}
                    whileInView={{ width: "100%" }}
                    transition={{ duration: 1, delay: stat.delay + 0.5 }}
                  />
                </motion.div>
                <motion.div
                  className="text-sm text-gray-400 mt-1"
                  whileHover={{ color: "#ffffff" }}
                >
                  {stat.label}
                </motion.div>
              </motion.div>
            ))}
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Interactive Scroll Indicator */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 1, delay: 2 }}
        className="absolute bottom-8 left-1/2 transform -translate-x-1/2 z-20"
        whileHover={{ scale: 1.1 }}
        style={{ opacity }}
      >
        <motion.div
          className="flex flex-col items-center space-y-2 text-gray-400 cursor-pointer"
          animate={{
            y: [0, 10, 0],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            ease: "easeInOut"
          }}
          whileHover={{ color: "#FFD700" }}
        >
          <motion.span
            className="text-sm"
            animate={{
              opacity: [0.5, 1, 0.5],
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          >
            Scroll
          </motion.span>
          <motion.div
            className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center relative overflow-hidden"
            whileHover={{
              borderColor: "#FFD700",
              boxShadow: "0 0 20px rgba(255, 215, 0, 0.6)"
            }}
          >
            <motion.div
              className="w-1 h-3 bg-gray-400 rounded-full mt-2"
              animate={{
                y: [0, 15, 0],
                opacity: [1, 0, 1],
              }}
              transition={{
                duration: 1.5,
                repeat: Infinity,
                ease: "easeInOut"
              }}
              whileHover={{ backgroundColor: "#FFD700" }}
            />
          </motion.div>
        </motion.div>
      </motion.div>

      {/* Ambient Light Effect */}
      <motion.div
        className="absolute inset-0 pointer-events-none bg-primary-dark opacity-5"
        animate={{
          opacity: [0.02, 0.08, 0.02],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
    </section>
  )
}
