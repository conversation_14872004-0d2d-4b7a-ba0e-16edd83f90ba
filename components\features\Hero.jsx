'use client'

import { useState } from 'react'
import Image from 'next/image'
import Button from '@/components/ui/Button'

export default function Hero() {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <section className="relative min-h-screen bg-white flex items-center justify-center overflow-hidden">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center min-h-screen py-20">
          {/* Left Content */}
          <div className="text-center lg:text-left space-y-8">
            <div className="space-y-6">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-900 leading-tight hover:text-dark-red transition-colors duration-300 cursor-pointer">
                <span className="block"><PERSON><PERSON></span>
                <span className="block text-lime-400 hover:text-dark-red transition-colors duration-300">Redefinido</span>
              </h1>
              <p className="text-xl text-gray-600 max-w-lg mx-auto lg:mx-0">
                Descubre la colección más exclusiva de calzado streetwear de lujo.
                Desde colaboraciones limitadas hasta clásicos atemporales.
              </p>
            </div>

            <div className="flex flex-col sm:flex-row gap-4 justify-center lg:justify-start">
              <Button
                size="lg"
                variant="primary"
                className="min-w-[200px]"
                onMouseEnter={() => setIsHovered(true)}
                onMouseLeave={() => setIsHovered(false)}
              >
                Explorar Colección
              </Button>
              <Button
                size="lg"
                variant="outline"
                className="min-w-[200px]"
              >
                Ver Drops Exclusivos
              </Button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-3 gap-6 pt-8 max-w-md mx-auto lg:mx-0">
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-gray-900">500+</div>
                <div className="text-sm text-gray-500">Productos</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-gray-900">50+</div>
                <div className="text-sm text-gray-500">Marcas</div>
              </div>
              <div className="text-center lg:text-left">
                <div className="text-2xl font-bold text-gray-900">24/7</div>
                <div className="text-sm text-gray-500">Soporte</div>
              </div>
            </div>
          </div>

          {/* Right Content - Hero Image */}
          <div className="relative">
            <div className="relative aspect-square max-w-lg mx-auto">
              <div className="absolute inset-0 bg-gradient-to-br from-lime-400/20 to-transparent rounded-full blur-3xl"></div>
              <div className="relative z-10 aspect-square bg-gray-100 rounded-2xl flex items-center justify-center">
                <span className="text-gray-500 text-lg">Hero Product Image</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  )
}
