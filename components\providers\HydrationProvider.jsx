'use client'

import { useState, useEffect } from 'react'

// Simplified hydration-safe component that uses a more robust approach
const HydrationProvider = ({ children }) => {
  const [hasMounted, setHasMounted] = useState(false)

  useEffect(() => {
    console.log('🟢🟢🟢 HYDRATION PROVIDER USE EFFECT RUNNING!')
    setHasMounted(true)
    console.log('🟢🟢🟢 HYDRATION PROVIDER MOUNTED!')
  }, [])

  console.log('🟢🟢🟢 HYDRATION PROVIDER RENDER - HAS MOUNTED:', hasMounted)

  // Use a more robust check that doesn't rely on useEffect
  // Check if we're in the browser environment
  const isClient = typeof window !== 'undefined'

  if (!isClient || !hasMounted) {
    return (
      <div className="min-h-screen bg-pure-white dark:bg-dark-gray flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-lime-green"></div>
      </div>
    )
  }

  return children
}

export default HydrationProvider
