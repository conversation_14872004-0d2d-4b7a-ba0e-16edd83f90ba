'use client'

import { useState } from 'react'
import { useWishlist } from '../../contexts/WishlistContext'
import { useAuth } from '../../contexts/AuthContext'

// Simple inline wishlist button component
function SimpleWishlistButton({ productId, onAuthRequired }) {
  const { isAuthenticated } = useAuth()
  const { isInWishlist, addToWishlist, removeFromWishlist } = useWishlist()

  const isWishlisted = isInWishlist(productId, 'default')

  const handleClick = (e) => {
    e.preventDefault()
    e.stopPropagation()

    console.log('🔥🔥🔥 SIMPLE WISHLIST BUTTON CLICKED!!! 🔥🔥🔥', { productId, isAuthenticated, isWishlisted })

    // TEMPORARY: Allow wishlist without authentication for testing
    // if (!isAuthenticated) {
    //   onAuthRequired?.()
    //   return
    // }

    if (isWishlisted) {
      console.log('💔 Removing from wishlist:', productId)
      removeFromWishlist(productId, 'default')
    } else {
      console.log('💚 Adding to wishlist:', productId)
      addToWishlist(productId, 'default')
    }
  }

  return (
    <button
      onClick={handleClick}
      className={`p-2 rounded-lg transition-colors ${
        isWishlisted
          ? 'bg-lime-green text-pure-black'
          : 'bg-neutral-200 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400 hover:bg-lime-green/20'
      }`}
    >
      {isWishlisted ? (
        <svg className="w-6 h-6" fill="currentColor" viewBox="0 0 24 24">
          <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/>
        </svg>
      ) : (
        <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
        </svg>
      )}
    </button>
  )
}

export default function WishlistTestPage() {
  console.log('🧪🧪🧪 WISHLIST TEST PAGE LOADING!')

  // Mock product data for testing
  const testProducts = [
    {
      id: 'test-product-1',
      name: 'Test Sneaker 1',
      brand: 'Nike',
      price: 2500,
      image: '/products/test-1.jpg'
    },
    {
      id: 'test-product-2', 
      name: 'Test Sneaker 2',
      brand: 'Adidas',
      price: 3000,
      image: '/products/test-2.jpg'
    },
    {
      id: 'test-product-3',
      name: 'Test Sneaker 3', 
      brand: 'Gucci',
      price: 15000,
      image: '/products/test-3.jpg'
    }
  ]

  const [authModalOpen, setAuthModalOpen] = useState(false)

  const handleAuthRequired = () => {
    console.log('🔐 Auth required for wishlist action')
    setAuthModalOpen(true)
  }

  return (
    <div className="min-h-screen bg-pure-white dark:bg-dark-gray pt-36">
      <div className="max-w-4xl mx-auto px-4 py-8">
        <h1 className="text-4xl font-bold text-pure-black dark:text-pure-white mb-8">
          🧪 Wishlist Functionality Test
        </h1>
        
        <div className="mb-8 p-6 bg-lime-green/10 rounded-lg border border-lime-green/20">
          <h2 className="text-xl font-semibold text-pure-black dark:text-pure-white mb-4">
            Test Instructions:
          </h2>
          <ul className="list-disc list-inside space-y-2 text-pure-black dark:text-pure-white">
            <li>Click the heart icons below to add/remove items from wishlist</li>
            <li>Check browser console for detailed logs</li>
            <li>Wishlist state should persist in localStorage</li>
            <li>Heart should fill with lime green when selected</li>
            <li>Authentication is temporarily disabled for testing</li>
          </ul>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {testProducts.map((product) => (
            <div 
              key={product.id}
              className="bg-pure-white dark:bg-neutral-800 rounded-lg shadow-lg p-6 border border-neutral-200 dark:border-neutral-700"
            >
              {/* Product Image Placeholder */}
              <div className="w-full h-48 bg-neutral-100 dark:bg-neutral-700 rounded-lg mb-4 flex items-center justify-center">
                <span className="text-neutral-500 text-sm">
                  {product.name}
                </span>
              </div>

              {/* Product Info */}
              <div className="space-y-2 mb-4">
                <h3 className="font-semibold text-pure-black dark:text-pure-white">
                  {product.name}
                </h3>
                <p className="text-neutral-600 dark:text-neutral-400">
                  {product.brand}
                </p>
                <p className="text-lg font-bold text-lime-green">
                  ${product.price.toLocaleString()} MXN
                </p>
              </div>

              {/* Wishlist Button Test */}
              <div className="flex items-center justify-between">
                <span className="text-sm text-neutral-600 dark:text-neutral-400">
                  Add to Wishlist:
                </span>
                <SimpleWishlistButton
                  productId={product.id}
                  onAuthRequired={handleAuthRequired}
                />
              </div>
            </div>
          ))}
        </div>

        {/* Debug Info */}
        <div className="mt-12 p-6 bg-neutral-100 dark:bg-neutral-800 rounded-lg">
          <h2 className="text-xl font-semibold text-pure-black dark:text-pure-white mb-4">
            🔍 Debug Information
          </h2>
          <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-2">
            Open browser console to see detailed wishlist logs
          </p>
          <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-2">
            Check localStorage for 'twl-wishlist-guest' key
          </p>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            Expected behavior: Hearts should toggle between outline and filled states
          </p>
        </div>

        {/* Auth Modal Placeholder */}
        {authModalOpen && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
            <div className="bg-pure-white dark:bg-neutral-800 p-6 rounded-lg max-w-md">
              <h3 className="text-lg font-semibold text-pure-black dark:text-pure-white mb-4">
                Authentication Required
              </h3>
              <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                In a real app, this would show the login modal.
              </p>
              <button
                onClick={() => setAuthModalOpen(false)}
                className="bg-lime-green text-pure-black px-4 py-2 rounded-lg font-medium"
              >
                Close
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
