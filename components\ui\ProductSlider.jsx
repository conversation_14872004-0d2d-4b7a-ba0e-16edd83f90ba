'use client'

import { useState, useRef, useEffect } from 'react'
import { motion } from 'framer-motion'
import SimpleProductCard from '@/components/ui/SimpleProductCard'

export default function ProductSlider({
  products,
  title,
  showNavigation = true,
  autoSlide = false,
  autoSlideInterval = 3000,
  maxProducts = null
}) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoSliding, setIsAutoSliding] = useState(autoSlide)
  const [isPaused, setIsPaused] = useState(false)
  const sliderRef = useRef(null)
  const autoSlideRef = useRef(null)

  // Limit products if maxProducts is specified
  const displayProducts = maxProducts ? products.slice(0, maxProducts) : products

  const itemsPerView = {
    mobile: 1,
    tablet: 2,
    desktop: 5 // Show 5 products for better display
  }

  // Auto-slide functionality
  useEffect(() => {
    if (isAutoSliding && !isPaused && displayProducts.length > itemsPerView.desktop) {
      autoSlideRef.current = setInterval(() => {
        setCurrentIndex(prev => {
          const maxIndex = Math.max(0, displayProducts.length - itemsPerView.desktop)
          return prev >= maxIndex ? 0 : prev + 1 // Loop back to start
        })
      }, autoSlideInterval)
    }

    return () => {
      if (autoSlideRef.current) {
        clearInterval(autoSlideRef.current)
      }
    }
  }, [isAutoSliding, isPaused, displayProducts.length, autoSlideInterval, itemsPerView.desktop])

  const nextSlide = () => {
    const maxIndex = Math.max(0, displayProducts.length - itemsPerView.desktop)
    setCurrentIndex(prev => prev >= maxIndex ? 0 : prev + 1) // Loop back to start
  }

  const prevSlide = () => {
    const maxIndex = Math.max(0, displayProducts.length - itemsPerView.desktop)
    setCurrentIndex(prev => prev <= 0 ? maxIndex : prev - 1) // Loop to end
  }

  const canGoNext = displayProducts.length > itemsPerView.desktop
  const canGoPrev = displayProducts.length > itemsPerView.desktop

  // Pause auto-slide on hover
  const handleMouseEnter = () => {
    if (isAutoSliding) setIsPaused(true)
  }

  const handleMouseLeave = () => {
    if (isAutoSliding) setIsPaused(false)
  }

  // Touch handling for mobile
  const [touchStart, setTouchStart] = useState(null)
  const [touchEnd, setTouchEnd] = useState(null)

  const minSwipeDistance = 50

  const onTouchStart = (e) => {
    setTouchEnd(null)
    setTouchStart(e.targetTouches[0].clientX)
  }

  const onTouchMove = (e) => {
    setTouchEnd(e.targetTouches[0].clientX)
  }

  const onTouchEnd = () => {
    if (!touchStart || !touchEnd) return

    const distance = touchStart - touchEnd
    const isLeftSwipe = distance > minSwipeDistance
    const isRightSwipe = distance < -minSwipeDistance

    if (isLeftSwipe) {
      nextSlide()
    } else if (isRightSwipe) {
      prevSlide()
    }
  }

  return (
    <div
      className="relative"
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      {/* Auto-slide indicator */}
      {isAutoSliding && (
        <div className="absolute top-0 right-0 z-20 bg-lime-green text-black text-xs px-2 py-1 rounded-bl-lg font-medium">
          AUTO
        </div>
      )}

      {/* Navigation Arrows - Enhanced for mobile */}
      {showNavigation && displayProducts.length > itemsPerView.desktop && (
        <>
          <button
            onClick={prevSlide}
            className="absolute left-2 sm:left-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white/90 dark:bg-neutral-800/90 backdrop-blur-sm shadow-lg flex items-center justify-center transition-all duration-300 hover:bg-lime-green hover:text-black cursor-pointer touch-manipulation"
            aria-label="Producto anterior"
          >
            <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
          </button>

          <button
            onClick={nextSlide}
            className="absolute right-2 sm:right-0 top-1/2 -translate-y-1/2 z-10 w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-white/90 dark:bg-neutral-800/90 backdrop-blur-sm shadow-lg flex items-center justify-center transition-all duration-300 hover:bg-lime-green hover:text-black cursor-pointer touch-manipulation"
            aria-label="Siguiente producto"
          >
            <svg className="w-5 h-5 sm:w-6 sm:h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
            </svg>
          </button>
        </>
      )}

      {/* Products Container - Enhanced for mobile touch */}
      <div
        className="relative px-4 sm:px-8 lg:px-16 py-8 sm:py-12 overflow-hidden"
        onTouchStart={onTouchStart}
        onTouchMove={onTouchMove}
        onTouchEnd={onTouchEnd}
      >
        <motion.div
          className="flex gap-4 sm:gap-6 lg:gap-8 transition-transform duration-500 ease-out"
          style={{
            transform: `translateX(-${currentIndex * (100 / itemsPerView.desktop)}%)`
          }}
          drag="x"
          dragConstraints={{ left: 0, right: 0 }}
          dragElastic={0.1}
          onDragEnd={(event, info) => {
            const threshold = 50
            if (info.offset.x > threshold) {
              prevSlide()
            } else if (info.offset.x < -threshold) {
              nextSlide()
            }
          }}
        >
          {displayProducts.map((product, index) => (
            <motion.div
              key={product.id}
              className="flex-shrink-0 w-full sm:w-1/2 lg:w-1/5 px-1 sm:px-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: index * 0.1 }}
            >
              <SimpleProductCard
                product={product}
                index={index}
              />
            </motion.div>
          ))}
        </motion.div>
      </div>

      {/* Enhanced Dots Indicator with Progress */}
      {showNavigation && displayProducts.length > itemsPerView.desktop && (
        <div className="flex justify-center mt-6 sm:mt-8 gap-2">
          {Array.from({ length: Math.ceil(displayProducts.length / itemsPerView.desktop) }).map((_, index) => (
            <button
              key={index}
              onClick={() => setCurrentIndex(index)}
              className={`relative w-3 h-3 rounded-full transition-all duration-300 touch-manipulation ${
                Math.floor(currentIndex / itemsPerView.desktop) === index
                  ? 'bg-lime-green scale-125'
                  : 'bg-gray-300 hover:bg-gray-400 dark:bg-neutral-600 dark:hover:bg-neutral-500'
              }`}
              aria-label={`Ir a grupo ${index + 1}`}
            >
              {/* Progress ring for auto-slide */}
              {isAutoSliding && Math.floor(currentIndex / itemsPerView.desktop) === index && (
                <div className="absolute inset-0 rounded-full border-2 border-lime-green animate-spin-slow opacity-50"></div>
              )}
            </button>
          ))}
        </div>
      )}

      {/* Mobile swipe hint */}
      <div className="sm:hidden text-center mt-4">
        <p className="text-xs text-gray-500 dark:text-neutral-400">
          Desliza para ver más productos
        </p>
      </div>
    </div>
  )
}
