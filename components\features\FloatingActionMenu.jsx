'use client'

import { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { useRouter } from 'next/navigation'
import EnhancedVoiceSearch from './EnhancedVoiceSearch'
import VisualSearch from './VisualSearch'
import SmartWishlist from './SmartWishlist'
import SocialShopping from './SocialShopping'

export default function FloatingActionMenu() {
  const [isOpen, setIsOpen] = useState(false)
  const [activeModal, setActiveModal] = useState(null)
  const router = useRouter()

  const actions = [
    {
      id: 'voice',
      name: 'Búsqueda por Voz',
      icon: '🎤',
      color: 'from-blue-500 to-purple-600',
      action: () => setActiveModal('voice')
    },
    {
      id: 'visual',
      name: 'Búsqueda Visual',
      icon: '📸',
      color: 'from-green-500 to-teal-600',
      action: () => setActiveModal('visual')
    },
    {
      id: 'wishlist',
      name: 'Mi Wishlist',
      icon: '💝',
      color: 'from-pink-500 to-rose-600',
      action: () => setActiveModal('wishlist')
    },
    {
      id: 'social',
      name: '<PERSON><PERSON>ni<PERSON>',
      icon: '👥',
      color: 'from-orange-500 to-red-600',
      action: () => setActiveModal('social')
    },
    {
      id: 'ux-demo',
      name: 'UX Demo',
      icon: '✨',
      color: 'from-yellow-500 to-orange-600',
      action: () => router.push('/ux-demo')
    }
  ]

  const handleSearch = (searchTerm) => {
    setActiveModal(null)
    router.push(`/shop?search=${encodeURIComponent(searchTerm)}`)
  }

  const closeModal = () => {
    setActiveModal(null)
  }

  return (
    <>
      {/* Floating Action Button */}
      <motion.div
        className="fixed bottom-6 right-6 z-40"
        initial={{ scale: 0 }}
        animate={{ scale: 1 }}
        transition={{ delay: 1, type: "spring", stiffness: 260, damping: 20 }}
      >
        {/* Action Items */}
        <AnimatePresence>
          {isOpen && (
            <motion.div
              className="absolute bottom-16 right-0 space-y-3"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {actions.map((action, index) => (
                <motion.button
                  key={action.id}
                  onClick={action.action}
                  className={`
                    flex items-center gap-3 px-4 py-3 rounded-full
                    bg-gradient-to-r ${action.color}
                    text-white font-medium shadow-lg
                    hover:shadow-xl transition-all duration-200
                    backdrop-blur-sm border border-white/20
                  `}
                  initial={{ 
                    opacity: 0, 
                    x: 50,
                    scale: 0.8
                  }}
                  animate={{ 
                    opacity: 1, 
                    x: 0,
                    scale: 1
                  }}
                  exit={{ 
                    opacity: 0, 
                    x: 50,
                    scale: 0.8
                  }}
                  transition={{ 
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 300,
                    damping: 25
                  }}
                  whileHover={{ 
                    scale: 1.05,
                    x: -5
                  }}
                  whileTap={{ scale: 0.95 }}
                >
                  <span className="text-xl">{action.icon}</span>
                  <span className="text-sm whitespace-nowrap">{action.name}</span>
                </motion.button>
              ))}
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main FAB */}
        <motion.button
          onClick={() => setIsOpen(!isOpen)}
          className={`
            w-14 h-14 rounded-full
            bg-gradient-to-r from-rich-gold to-warm-camel
            text-forest-emerald shadow-lg hover:shadow-xl
            flex items-center justify-center
            transition-all duration-300
            ${isOpen ? 'rotate-45' : 'rotate-0'}
          `}
          whileHover={{ scale: 1.1 }}
          whileTap={{ scale: 0.9 }}
          animate={{
            rotate: isOpen ? 45 : 0,
            backgroundColor: isOpen ? '#FF6B6B' : '#FFD166'
          }}
        >
          <motion.div
            animate={{
              rotate: isOpen ? -45 : 0
            }}
            transition={{ duration: 0.2 }}
          >
            {isOpen ? (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            ) : (
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            )}
          </motion.div>
        </motion.button>

        {/* Pulse Animation */}
        {!isOpen && (
          <motion.div
            className="absolute inset-0 rounded-full bg-rich-gold opacity-30"
            animate={{
              scale: [1, 1.5, 1],
              opacity: [0.3, 0, 0.3]
            }}
            transition={{
              duration: 2,
              repeat: Infinity,
              ease: "easeInOut"
            }}
          />
        )}
      </motion.div>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            className="fixed inset-0 bg-black/20 backdrop-blur-sm z-30"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsOpen(false)}
          />
        )}
      </AnimatePresence>

      {/* Modals */}
      <AnimatePresence>
        {activeModal === 'voice' && (
          <EnhancedVoiceSearch
            isOpen={true}
            onResults={(results, query) => handleSearch(query)}
            onClose={closeModal}
          />
        )}

        {activeModal === 'visual' && (
          <VisualSearch
            onSearch={handleSearch}
            onClose={closeModal}
          />
        )}

        {activeModal === 'wishlist' && (
          <SmartWishlist
            onClose={closeModal}
          />
        )}

        {activeModal === 'social' && (
          <SocialShopping
            onClose={closeModal}
          />
        )}
      </AnimatePresence>

      {/* Help Tooltip */}
      <AnimatePresence>
        {!isOpen && (
          <motion.div
            className="fixed bottom-6 right-24 z-40"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: 20 }}
            transition={{ delay: 3 }}
          >
            <div className="bg-forest-emerald text-white px-3 py-2 rounded-lg text-sm shadow-lg relative">
              ¡Prueba las funciones avanzadas!
              <div className="absolute right-0 top-1/2 transform translate-x-1 -translate-y-1/2 w-0 h-0 border-l-4 border-l-forest-emerald border-t-4 border-t-transparent border-b-4 border-b-transparent"></div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
