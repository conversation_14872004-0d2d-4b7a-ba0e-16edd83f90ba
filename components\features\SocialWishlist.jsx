'use client'

import { useState, useEffect } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import AnimatedButton from '@/components/ui/AnimatedButton'
import Badge from '@/components/ui/Badge'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'

// Mock wishlist data
const mockWishlists = [
  {
    id: 'wl-1',
    name: 'Mis Favoritos',
    description: 'Los sneakers que más me gustan',
    isPublic: true,
    isDefault: true,
    owner: {
      id: 'user1',
      name: '<PERSON>',
      username: '@maria_sneaks',
      avatar: '👩🏻'
    },
    items: [
      { id: 1, name: 'Nike Air Force 1 Low White', brand: 'Nike', price: 2500, addedAt: '2024-01-15' },
      { id: 4, name: 'Jordan 1 Retro High Chicago', brand: 'Jordan', price: 4200, addedAt: '2024-01-18' },
      { id: 2, name: '<PERSON>ezy Boost 350 V2 Zebra', brand: 'Adidas', price: 8500, addedAt: '2024-01-20' }
    ],
    followers: 45,
    likes: 23,
    createdAt: '2024-01-10',
    updatedAt: '2024-01-20'
  },
  {
    id: 'wl-2',
    name: 'Grails 2024',
    description: 'Los holy grails que quiero conseguir este año',
    isPublic: true,
    isDefault: false,
    owner: {
      id: 'user1',
      name: 'María González',
      username: '@maria_sneaks',
      avatar: '👩🏻'
    },
    items: [
      { id: 3, name: 'Gucci Ace Sneakers White', brand: 'Gucci', price: 15000, addedAt: '2024-01-12' },
      { id: 7, name: 'Off-White x Nike Air Force 1 MCA', brand: 'Off-White', price: 67000, addedAt: '2024-01-14' }
    ],
    followers: 89,
    likes: 67,
    createdAt: '2024-01-12',
    updatedAt: '2024-01-14'
  }
]

const mockPublicWishlists = [
  {
    id: 'pub-wl-1',
    name: 'Street Style Essentials',
    description: 'Los básicos para cualquier outfit urbano',
    isPublic: true,
    owner: {
      id: 'user2',
      name: 'Carlos Mendoza',
      username: '@carlos_style',
      avatar: '👨🏽',
      verified: true
    },
    items: [
      { id: 1, name: 'Nike Air Force 1 Low White', brand: 'Nike', price: 2500 },
      { id: 5, name: 'Converse Chuck Taylor All Star', brand: 'Converse', price: 1800 },
      { id: 6, name: 'Vans Old Skool Black/White', brand: 'Vans', price: 1900 }
    ],
    followers: 234,
    likes: 156,
    isFollowing: false
  },
  {
    id: 'pub-wl-2',
    name: 'Luxury Flex',
    description: 'Para cuando quieres impresionar',
    isPublic: true,
    owner: {
      id: 'user3',
      name: 'Ana Rodríguez',
      username: '@ana_kicks',
      avatar: '👩🏻‍🦱',
      verified: true
    },
    items: [
      { id: 3, name: 'Gucci Ace Sneakers White', brand: 'Gucci', price: 15000 },
      { id: 8, name: 'Balenciaga Triple S White', brand: 'Balenciaga', price: 22000 }
    ],
    followers: 567,
    likes: 389,
    isFollowing: true
  }
]

export default function SocialWishlist({ 
  className = '',
  showMyWishlists = true,
  showPublicWishlists = true,
  maxWishlists = 4 
}) {
  const [myWishlists, setMyWishlists] = useState([])
  const [publicWishlists, setPublicWishlists] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [selectedWishlist, setSelectedWishlist] = useState(null)
  const [isCreatingWishlist, setIsCreatingWishlist] = useState(false)

  useEffect(() => {
    // Simulate loading wishlists
    const loadWishlists = async () => {
      setIsLoading(true)
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      if (showMyWishlists) {
        setMyWishlists(mockWishlists)
      }
      if (showPublicWishlists) {
        setPublicWishlists(mockPublicWishlists.slice(0, maxWishlists))
      }
      
      setIsLoading(false)
    }
    
    loadWishlists()
  }, [showMyWishlists, showPublicWishlists, maxWishlists])

  const handleFollowWishlist = (wishlistId) => {
    setPublicWishlists(wishlists => 
      wishlists.map(wl => 
        wl.id === wishlistId 
          ? { 
              ...wl, 
              isFollowing: !wl.isFollowing,
              followers: wl.isFollowing ? wl.followers - 1 : wl.followers + 1
            }
          : wl
      )
    )
  }

  const handleLikeWishlist = (wishlistId, isMyWishlist = false) => {
    const updateFunction = (wishlists) => 
      wishlists.map(wl => 
        wl.id === wishlistId 
          ? { ...wl, likes: wl.likes + 1 }
          : wl
      )
    
    if (isMyWishlist) {
      setMyWishlists(updateFunction)
    } else {
      setPublicWishlists(updateFunction)
    }
  }

  const handleShareWishlist = (wishlist) => {
    const shareUrl = `${window.location.origin}/wishlist/${wishlist.id}`
    
    if (navigator.share) {
      navigator.share({
        title: `${wishlist.name} - Lista de deseos de ${wishlist.owner.name}`,
        text: wishlist.description,
        url: shareUrl
      })
    } else {
      navigator.clipboard.writeText(shareUrl)
      alert('Link copiado al portapapeles')
    }
  }

  const getTotalValue = (items) => {
    return items.reduce((total, item) => total + item.price, 0)
  }

  if (isLoading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="animate-pulse space-y-4">
          <div className="bg-warm-camel/20 rounded h-8 w-64"></div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {[...Array(4)].map((_, index) => (
              <div key={index} className="bg-warm-camel/20 rounded-lg h-48"></div>
            ))}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className={`space-y-8 ${className}`}>
      
      {/* My Wishlists */}
      {showMyWishlists && myWishlists.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                Mis Listas de Deseos
              </h2>
              <p className="text-warm-camel text-sm">
                Organiza y comparte tus sneakers favoritos
              </p>
            </div>
            
            <AnimatedButton
              variant="primary"
              size="sm"
              onClick={() => setIsCreatingWishlist(true)}
              icon={
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              }
            >
              Nueva Lista
            </AnimatedButton>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {myWishlists.map((wishlist, index) => (
              <motion.div
                key={wishlist.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card variant="glass" className="group hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6">
                    
                    {/* Header */}
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                            {wishlist.name}
                          </h3>
                          {wishlist.isDefault && (
                            <Badge variant="primary" size="sm">Principal</Badge>
                          )}
                          {wishlist.isPublic && (
                            <Badge variant="success" size="sm">Pública</Badge>
                          )}
                        </div>
                        <p className="text-warm-camel text-sm line-clamp-2">
                          {wishlist.description}
                        </p>
                      </div>
                      
                      <button
                        onClick={() => handleShareWishlist(wishlist)}
                        className="text-warm-camel hover:text-rich-gold transition-colors"
                      >
                        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                        </svg>
                      </button>
                    </div>

                    {/* Items Preview */}
                    <div className="space-y-2 mb-4">
                      {wishlist.items.slice(0, 3).map((item) => (
                        <div key={item.id} className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-warm-camel/10 rounded flex items-center justify-center">
                            <span className="text-sm">👟</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-forest-emerald dark:text-light-cloud-gray text-sm font-medium truncate">
                              {item.name}
                            </p>
                            <p className="text-warm-camel text-xs">{item.brand}</p>
                          </div>
                          <span className="text-forest-emerald dark:text-light-cloud-gray text-sm font-semibold">
                            ${item.price.toLocaleString()}
                          </span>
                        </div>
                      ))}
                      
                      {wishlist.items.length > 3 && (
                        <p className="text-warm-camel text-xs text-center">
                          +{wishlist.items.length - 3} productos más
                        </p>
                      )}
                    </div>

                    {/* Stats */}
                    <div className="flex items-center justify-between text-sm mb-4">
                      <div className="flex items-center gap-4">
                        <span className="text-warm-camel">
                          {wishlist.items.length} productos
                        </span>
                        <span className="text-warm-camel">
                          {wishlist.followers} seguidores
                        </span>
                      </div>
                      <span className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                        ${getTotalValue(wishlist.items).toLocaleString()} MXN
                      </span>
                    </div>

                    {/* Actions */}
                    <div className="flex gap-2">
                      <TransitionLink href={`/wishlist/${wishlist.id}`} className="flex-1">
                        <AnimatedButton
                          variant="primary"
                          size="sm"
                          className="w-full"
                          icon={
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                            </svg>
                          }
                        >
                          Ver Lista
                        </AnimatedButton>
                      </TransitionLink>
                      
                      <button
                        onClick={() => handleLikeWishlist(wishlist.id, true)}
                        className="px-3 py-2 text-warm-camel hover:text-red-500 transition-colors"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                        </svg>
                      </button>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}

      {/* Public Wishlists */}
      {showPublicWishlists && publicWishlists.length > 0 && (
        <div>
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray">
                Listas Populares
              </h2>
              <p className="text-warm-camel text-sm">
                Descubre las listas de otros sneakerheads
              </p>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {publicWishlists.map((wishlist, index) => (
              <motion.div
                key={wishlist.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card variant="default" className="group hover:shadow-lg transition-all duration-300">
                  <CardContent className="p-6">
                    
                    {/* Owner */}
                    <div className="flex items-center gap-3 mb-4">
                      <div className="w-10 h-10 bg-gradient-to-br from-rich-gold to-warm-camel rounded-full flex items-center justify-center">
                        {wishlist.owner.avatar}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-1">
                          <h4 className="font-semibold text-forest-emerald dark:text-light-cloud-gray text-sm">
                            {wishlist.owner.name}
                          </h4>
                          {wishlist.owner.verified && (
                            <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                            </svg>
                          )}
                        </div>
                        <p className="text-warm-camel text-xs">{wishlist.owner.username}</p>
                      </div>
                      
                      <AnimatedButton
                        variant={wishlist.isFollowing ? "secondary" : "primary"}
                        size="sm"
                        onClick={() => handleFollowWishlist(wishlist.id)}
                      >
                        {wishlist.isFollowing ? 'Siguiendo' : 'Seguir'}
                      </AnimatedButton>
                    </div>

                    {/* Wishlist Info */}
                    <div className="mb-4">
                      <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray mb-1">
                        {wishlist.name}
                      </h3>
                      <p className="text-warm-camel text-sm line-clamp-2">
                        {wishlist.description}
                      </p>
                    </div>

                    {/* Items Preview */}
                    <div className="space-y-2 mb-4">
                      {wishlist.items.slice(0, 2).map((item) => (
                        <div key={item.id} className="flex items-center gap-3">
                          <div className="w-8 h-8 bg-warm-camel/10 rounded flex items-center justify-center">
                            <span className="text-sm">👟</span>
                          </div>
                          <div className="flex-1 min-w-0">
                            <p className="text-forest-emerald dark:text-light-cloud-gray text-sm font-medium truncate">
                              {item.name}
                            </p>
                            <p className="text-warm-camel text-xs">{item.brand}</p>
                          </div>
                          <span className="text-forest-emerald dark:text-light-cloud-gray text-sm font-semibold">
                            ${item.price.toLocaleString()}
                          </span>
                        </div>
                      ))}
                    </div>

                    {/* Stats & Actions */}
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm text-warm-camel">
                        <span>{wishlist.items.length} productos</span>
                        <span>{wishlist.followers} seguidores</span>
                        <span>{wishlist.likes} likes</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => handleLikeWishlist(wishlist.id)}
                          className="text-warm-camel hover:text-red-500 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                          </svg>
                        </button>
                        
                        <button
                          onClick={() => handleShareWishlist(wishlist)}
                          className="text-warm-camel hover:text-green-500 transition-colors"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                          </svg>
                        </button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}
