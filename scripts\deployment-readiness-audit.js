/**
 * TWL Deployment Readiness Audit
 * Comprehensive testing for production deployment readiness
 */

const fs = require('fs')
const path = require('path')

async function runDeploymentReadinessAudit() {
  console.log('🚀 TWL DEPLOYMENT READINESS AUDIT')
  console.log('=' .repeat(60))

  const auditResults = {
    buildTests: [],
    environmentTests: [],
    performanceTests: [],
    deploymentTests: [],
    errors: [],
    warnings: [],
    readinessScore: 0
  }

  try {
    // Test 1: Build Configuration
    console.log('\n1. 🔧 Testing Build Configuration...')
    await testBuildConfiguration(auditResults)

    // Test 2: Environment Setup
    console.log('\n2. 🌍 Testing Environment Setup...')
    await testEnvironmentSetup(auditResults)

    // Test 3: Performance Readiness
    console.log('\n3. ⚡ Testing Performance Readiness...')
    await testPerformanceReadiness(auditResults)

    // Test 4: Deployment Configuration
    console.log('\n4. 🚀 Testing Deployment Configuration...')
    await testDeploymentConfiguration(auditResults)

    // Generate deployment readiness report
    generateDeploymentReport(auditResults)

  } catch (error) {
    console.error('❌ Deployment audit failed:', error)
    auditResults.errors.push(`Deployment audit error: ${error.message}`)
  }

  return auditResults
}

async function testBuildConfiguration(auditResults) {
  const buildChecks = [
    {
      name: 'Next.js Config',
      test: () => fs.existsSync('next.config.js'),
      critical: true
    },
    {
      name: 'Package.json Scripts',
      test: () => {
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        return pkg.scripts && pkg.scripts.build && pkg.scripts.start
      },
      critical: true
    },
    {
      name: 'TypeScript Config',
      test: () => fs.existsSync('tsconfig.json'),
      critical: false
    },
    {
      name: 'Tailwind Config',
      test: () => fs.existsSync('tailwind.config.js'),
      critical: false
    },
    {
      name: 'ESLint Config',
      test: () => fs.existsSync('.eslintrc.json') || fs.existsSync('eslint.config.js'),
      critical: false
    }
  ]

  for (const check of buildChecks) {
    try {
      const passed = check.test()
      
      const result = {
        name: check.name,
        passed,
        critical: check.critical,
        type: 'build'
      }

      auditResults.buildTests.push(result)
      
      if (passed) {
        console.log(`    ✅ ${check.name}`)
      } else {
        const symbol = check.critical ? '❌' : '⚠️'
        console.log(`    ${symbol} ${check.name} - ${check.critical ? 'CRITICAL' : 'Missing'}`)
        
        if (check.critical) {
          auditResults.errors.push(`Critical build config missing: ${check.name}`)
        } else {
          auditResults.warnings.push(`Build config missing: ${check.name}`)
        }
      }
    } catch (error) {
      auditResults.errors.push(`Build test error for ${check.name}: ${error.message}`)
    }
  }
}

async function testEnvironmentSetup(auditResults) {
  const envChecks = [
    {
      name: 'Environment Example',
      test: () => fs.existsSync('.env.example'),
      critical: false
    },
    {
      name: 'Git Ignore',
      test: () => {
        if (!fs.existsSync('.gitignore')) return false
        const content = fs.readFileSync('.gitignore', 'utf8')
        return content.includes('.env') && content.includes('node_modules') && content.includes('.next')
      },
      critical: true
    },
    {
      name: 'Node Version',
      test: () => {
        if (!fs.existsSync('package.json')) return false
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        return pkg.engines && pkg.engines.node
      },
      critical: false
    },
    {
      name: 'Production Dependencies',
      test: () => {
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        const prodDeps = Object.keys(pkg.dependencies || {})
        return prodDeps.includes('next') && prodDeps.includes('react')
      },
      critical: true
    }
  ]

  for (const check of envChecks) {
    try {
      const passed = check.test()
      
      const result = {
        name: check.name,
        passed,
        critical: check.critical,
        type: 'environment'
      }

      auditResults.environmentTests.push(result)
      
      if (passed) {
        console.log(`    ✅ ${check.name}`)
      } else {
        const symbol = check.critical ? '❌' : '⚠️'
        console.log(`    ${symbol} ${check.name} - ${check.critical ? 'CRITICAL' : 'Missing'}`)
        
        if (check.critical) {
          auditResults.errors.push(`Critical environment config missing: ${check.name}`)
        } else {
          auditResults.warnings.push(`Environment config missing: ${check.name}`)
        }
      }
    } catch (error) {
      auditResults.errors.push(`Environment test error for ${check.name}: ${error.message}`)
    }
  }
}

async function testPerformanceReadiness(auditResults) {
  const performanceChecks = [
    {
      name: 'Image Optimization',
      test: () => {
        // Check if next/image is used
        const files = findJSXFiles(['app', 'components'])
        return files.some(file => {
          try {
            const content = fs.readFileSync(file, 'utf8')
            return content.includes('next/image') || content.includes('Image from')
          } catch {
            return false
          }
        })
      },
      critical: false
    },
    {
      name: 'Bundle Analysis Setup',
      test: () => {
        const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'))
        return pkg.scripts && (pkg.scripts['analyze'] || pkg.scripts['bundle-analyzer'])
      },
      critical: false
    },
    {
      name: 'Static Generation',
      test: () => {
        // Check for generateStaticParams or getStaticProps usage
        const files = findJSXFiles(['app'])
        return files.some(file => {
          try {
            const content = fs.readFileSync(file, 'utf8')
            return content.includes('generateStaticParams') || content.includes('getStaticProps')
          } catch {
            return false
          }
        })
      },
      critical: false
    },
    {
      name: 'Error Boundaries',
      test: () => {
        const files = findJSXFiles(['app', 'components'])
        return files.some(file => {
          try {
            const content = fs.readFileSync(file, 'utf8')
            return content.includes('ErrorBoundary') || content.includes('error.jsx') || content.includes('error.tsx')
          } catch {
            return false
          }
        })
      },
      critical: false
    }
  ]

  for (const check of performanceChecks) {
    try {
      const passed = check.test()
      
      const result = {
        name: check.name,
        passed,
        critical: check.critical,
        type: 'performance'
      }

      auditResults.performanceTests.push(result)
      
      if (passed) {
        console.log(`    ✅ ${check.name}`)
      } else {
        console.log(`    ⚠️ ${check.name} - Recommended`)
        auditResults.warnings.push(`Performance optimization missing: ${check.name}`)
      }
    } catch (error) {
      auditResults.errors.push(`Performance test error for ${check.name}: ${error.message}`)
    }
  }
}

async function testDeploymentConfiguration(auditResults) {
  const deploymentChecks = [
    {
      name: 'Vercel Configuration',
      test: () => fs.existsSync('vercel.json'),
      critical: false
    },
    {
      name: 'Docker Configuration',
      test: () => fs.existsSync('Dockerfile') || fs.existsSync('docker-compose.yml'),
      critical: false
    },
    {
      name: 'CI/CD Configuration',
      test: () => fs.existsSync('.github/workflows') || fs.existsSync('.gitlab-ci.yml'),
      critical: false
    },
    {
      name: 'Health Check Endpoint',
      test: () => {
        return fs.existsSync('app/api/health/route.js') || 
               fs.existsSync('app/api/health/route.ts') ||
               fs.existsSync('pages/api/health.js')
      },
      critical: false
    },
    {
      name: 'Security Headers',
      test: () => {
        if (!fs.existsSync('next.config.js')) return false
        const content = fs.readFileSync('next.config.js', 'utf8')
        return content.includes('headers') && (
          content.includes('X-Frame-Options') || 
          content.includes('Content-Security-Policy')
        )
      },
      critical: false
    }
  ]

  for (const check of deploymentChecks) {
    try {
      const passed = check.test()
      
      const result = {
        name: check.name,
        passed,
        critical: check.critical,
        type: 'deployment'
      }

      auditResults.deploymentTests.push(result)
      
      if (passed) {
        console.log(`    ✅ ${check.name}`)
      } else {
        console.log(`    ⚠️ ${check.name} - Optional`)
        auditResults.warnings.push(`Deployment config missing: ${check.name}`)
      }
    } catch (error) {
      auditResults.errors.push(`Deployment test error for ${check.name}: ${error.message}`)
    }
  }
}

function findJSXFiles(directories) {
  const files = []
  
  function scanDirectory(dir) {
    try {
      if (!fs.existsSync(dir)) return
      
      const items = fs.readdirSync(dir, { withFileTypes: true })
      
      for (const item of items) {
        const fullPath = path.join(dir, item.name)
        
        if (item.isDirectory() && !item.name.startsWith('.') && item.name !== 'node_modules') {
          scanDirectory(fullPath)
        } else if (item.isFile() && /\.(jsx?|tsx?)$/.test(item.name)) {
          files.push(fullPath)
        }
      }
    } catch (error) {
      // Skip directories that can't be read
    }
  }

  directories.forEach(scanDirectory)
  return files
}

function generateDeploymentReport(auditResults) {
  console.log('\n🚀 DEPLOYMENT READINESS REPORT')
  console.log('=' .repeat(60))

  // Calculate readiness score
  const allTests = [
    ...auditResults.buildTests,
    ...auditResults.environmentTests,
    ...auditResults.performanceTests,
    ...auditResults.deploymentTests
  ]

  const criticalTests = allTests.filter(t => t.critical)
  const nonCriticalTests = allTests.filter(t => !t.critical)

  const criticalPassed = criticalTests.filter(t => t.passed).length
  const nonCriticalPassed = nonCriticalTests.filter(t => t.passed).length

  // Critical tests are weighted more heavily
  const criticalWeight = 0.8
  const nonCriticalWeight = 0.2

  let readinessScore = 0
  if (criticalTests.length > 0) {
    readinessScore += (criticalPassed / criticalTests.length) * criticalWeight * 100
  }
  if (nonCriticalTests.length > 0) {
    readinessScore += (nonCriticalPassed / nonCriticalTests.length) * nonCriticalWeight * 100
  }

  auditResults.readinessScore = readinessScore.toFixed(1)

  console.log(`\n📊 Deployment Summary:`)
  console.log(`  Readiness Score: ${auditResults.readinessScore}%`)
  console.log(`  Critical Tests: ${criticalPassed}/${criticalTests.length} passed`)
  console.log(`  Optional Tests: ${nonCriticalPassed}/${nonCriticalTests.length} passed`)
  console.log(`  Build Tests: ${auditResults.buildTests.filter(t => t.passed).length}/${auditResults.buildTests.length}`)
  console.log(`  Environment Tests: ${auditResults.environmentTests.filter(t => t.passed).length}/${auditResults.environmentTests.length}`)
  console.log(`  Performance Tests: ${auditResults.performanceTests.filter(t => t.passed).length}/${auditResults.performanceTests.length}`)
  console.log(`  Deployment Tests: ${auditResults.deploymentTests.filter(t => t.passed).length}/${auditResults.deploymentTests.length}`)
  console.log(`  Errors: ${auditResults.errors.length}`)
  console.log(`  Warnings: ${auditResults.warnings.length}`)

  const grade = auditResults.readinessScore >= 90 ? '🚀 READY FOR PRODUCTION' :
                auditResults.readinessScore >= 75 ? '✅ MOSTLY READY' :
                auditResults.readinessScore >= 60 ? '⚠️ NEEDS WORK' : '❌ NOT READY'

  console.log(`\n🎯 Deployment Grade: ${grade}`)

  if (auditResults.errors.length > 0) {
    console.log(`\n❌ Critical Issues:`)
    auditResults.errors.forEach(error => console.log(`  - ${error}`))
  }

  if (auditResults.readinessScore >= 75) {
    console.log('\n🚀 System is ready for production deployment!')
    console.log('✅ All critical requirements met')
    console.log('✅ Build configuration complete')
    console.log('✅ Environment setup verified')
  } else {
    console.log('\n⚠️ Address critical issues before production deployment')
  }

  // Deployment recommendations
  console.log('\n💡 Deployment Recommendations:')
  if (auditResults.readinessScore < 90) {
    console.log('  - Complete all critical configuration items')
  }
  console.log('  - Set up monitoring and alerting')
  console.log('  - Configure automated backups')
  console.log('  - Implement health checks')
  console.log('  - Set up error tracking (Sentry)')
  console.log('  - Configure CDN for static assets')
  console.log('  - Enable compression and caching')

  return auditResults.readinessScore
}

// Run audit if this file is executed directly
if (require.main === module) {
  runDeploymentReadinessAudit()
    .then(results => {
      const success = results.readinessScore >= 75
      console.log(`\n${success ? '🚀' : '⚠️'} Deployment readiness audit completed`)
      process.exit(success ? 0 : 1)
    })
    .catch(error => {
      console.error('❌ Deployment audit execution failed:', error)
      process.exit(1)
    })
}

module.exports = { runDeploymentReadinessAudit }
