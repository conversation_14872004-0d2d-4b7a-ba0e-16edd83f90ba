'use client'

import { useState } from 'react'
import { cn } from '@/lib/utils'

// Updated categories to match CYTTE structure
const categories = [
  { id: 'sneakers', name: 'SNEAKERS', href: '/categories/sneakers', icon: '👟', cytteFolder: '1. SNEAKERS' },
  { id: 'sandals', name: 'SANDALIAS', href: '/categories/sandals', icon: '🩴', cytteFolder: '2. SANDALS' },
  { id: 'formal', name: 'FORMAL', href: '/categories/formal', icon: '👔', cytteFolder: '3. FORMAL' },
  { id: 'casual', name: 'CASUAL', href: '/categories/casual', icon: '👞', cytteFolder: '4. CASUAL' },
  { id: 'kids', name: 'NIÑOS', href: '/categories/kids', icon: '👶', cytteFolder: '5. KIDS' }
]

export default function CategoryNavigation({
  activeCategory = 'sneakers',
  onCategoryChange,
  className,
  showIcons = false,
  variant = 'default' // 'default', 'compact', 'mobile'
}) {
  const [selectedCategory, setSelectedCategory] = useState(activeCategory)

  const handleCategoryClick = (categoryId) => {
    setSelectedCategory(categoryId)
    if (onCategoryChange) {
      onCategoryChange(categoryId)
    }
  }

  // Compact variant for mobile or tight spaces
  if (variant === 'compact') {
    return (
      <div className={cn('w-full', className)}>
        <div className="flex gap-2 overflow-x-auto scrollbar-hide px-2 py-2">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category.id)}
              className={cn(
                'flex-shrink-0 px-4 py-2 rounded-lg text-xs font-medium transition-all duration-200',
                'min-w-fit border',
                selectedCategory === category.id
                  ? 'bg-lime-green border-lime-green text-black font-semibold'
                  : 'bg-white border-gray-200 text-gray-600 hover:border-lime-green/50 hover:text-black dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300'
              )}
            >
              {showIcons && <span className="mr-1">{category.icon}</span>}
              {category.name}
            </button>
          ))}
        </div>
      </div>
    )
  }

  // Mobile variant
  if (variant === 'mobile') {
    return (
      <div className={cn('w-full', className)}>
        <div className="grid grid-cols-2 sm:grid-cols-3 gap-3 p-4">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category.id)}
              className={cn(
                'flex flex-col items-center gap-2 p-4 rounded-xl transition-all duration-300',
                'border-2 min-h-[80px]',
                selectedCategory === category.id
                  ? 'bg-lime-green border-lime-green text-black shadow-lg'
                  : 'bg-white border-gray-200 text-gray-600 hover:border-lime-green/50 hover:shadow-md dark:bg-gray-800 dark:border-gray-700 dark:text-gray-300'
              )}
            >
              <span className="text-2xl">{category.icon}</span>
              <span className="text-xs font-semibold text-center leading-tight">
                {category.name}
              </span>
            </button>
          ))}
        </div>
      </div>
    )
  }

  // Default variant
  return (
    <div className={cn('w-full', className)}>
      {/* TWL Aesthetic Category Navigation - Clean & Centered */}
      <div className="flex justify-center mb-8">
        {/* Centered horizontal layout with TWL styling - Extra padding for hover effects */}
        <div className="flex items-center gap-3 sm:gap-4 lg:gap-6 overflow-x-auto scrollbar-hide px-4 py-6">
          {categories.map((category) => (
            <button
              key={category.id}
              onClick={() => handleCategoryClick(category.id)}
              className={cn(
                // TWL Button Design - Clean & Professional with Minimalist Shadow
                'flex-shrink-0 px-6 py-3 font-poppins font-medium transition-all duration-300 transform',
                'min-w-[90px] rounded-xl border-2 text-sm sm:text-base',
                'hover:scale-102 hover:shadow-sm',
                // Active state: Lime green with black text (TWL rule) - Minimalist shadow
                selectedCategory === category.id
                  ? 'bg-lime-green border-lime-green text-pure-black font-semibold shadow-sm'
                  : 'bg-pure-white border-text-gray/20 text-text-gray hover:border-lime-green/50 hover:text-pure-black dark:bg-neutral-800 dark:border-neutral-600 dark:text-neutral-300 dark:hover:border-lime-green'
              )}
            >
              <div className="flex items-center gap-2">
                {showIcons && <span>{category.icon}</span>}
                <span className="whitespace-nowrap tracking-wide uppercase text-xs sm:text-sm font-semibold">
                  {category.name}
                </span>
              </div>
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}
