/**
 * TWL Product Service
 * High-level service layer for product operations
 * 
 * Features:
 * - Category-based product loading
 * - Data transformation from Enterprise to UI format
 * - Caching and performance optimization
 * - Error handling and fallbacks
 * - Search and filtering
 */

import { getTWLSystem } from '@/lib/enterprise/TWLEnterpriseSystem'
import { ProductTransformer, SimpleProduct, SimpleProductListResponse } from '@/lib/enterprise/transformers/ProductTransformer'
import { TWLProductFilters } from '@/lib/enterprise/models/Product'

/**
 * Category mapping for URL to Enterprise category
 * Enterprise categories are lowercase with dashes (e.g., "1. SNEAKERS" → "sneakers")
 */
const CATEGORY_MAPPING = {
  'sneakers': 'sneakers',
  'sandals': 'sandals',
  'formal': 'formal',
  'casual': 'casual',
  'kids': 'kids'
} as const

/**
 * Product Service Class
 */
export class ProductService {
  
  /**
   * Get products by category with transformation
   */
  static async getProductsByCategory(
    categorySlug: string, 
    options: {
      page?: number
      pageSize?: number
      sortBy?: string
      sortOrder?: 'asc' | 'desc'
    } = {}
  ): Promise<SimpleProductListResponse> {
    try {
      console.log('🔥🔥🔥 ProductService.getProductsByCategory called for:', categorySlug)
      
      // Get Enterprise system
      const system = getTWLSystem()
      
      // Ensure system is initialized
      if (!system.isReady()) {
        console.log('🔄 Initializing Enterprise system...')
        await system.initialize()
      }
      
      // Map category slug to Enterprise category
      const enterpriseCategory = CATEGORY_MAPPING[categorySlug as keyof typeof CATEGORY_MAPPING]
      if (!enterpriseCategory) {
        throw new Error(`Invalid category: ${categorySlug}`)
      }
      
      console.log('🎯 Mapped category:', categorySlug, '->', enterpriseCategory)
      
      // Prepare filters
      const filters: TWLProductFilters = {
        categories: [enterpriseCategory],
        sortBy: options.sortBy as any || 'name',
        sortOrder: options.sortOrder || 'asc'
      }
      
      // Search products using system method
      const result = await system.searchProducts(
        undefined, // no search query
        filters,
        options.page || 1,
        options.pageSize || 20
      )

      console.log('📦 Enterprise system result:', {
        hasData: !!result,
        hasProducts: !!result?.products,
        productsLength: result?.products?.length || 0,
        resultStructure: result ? Object.keys(result) : 'null'
      })

      if (!result) {
        throw new Error('Failed to load products')
      }

      // Transform to UI format
      const transformedResult = ProductTransformer.transformProductList(result)

      console.log('✅ Transformed result:', {
        productsCount: transformedResult.products.length,
        total: transformedResult.total,
        hasMore: transformedResult.hasMore
      })
      
      return transformedResult
      
    } catch (error) {
      console.error('❌ Error in ProductService.getProductsByCategory:', error)
      
      // Return empty result on error
      return {
        products: [],
        total: 0,
        page: options.page || 1,
        pageSize: options.pageSize || 20,
        hasMore: false
      }
    }
  }
  
  /**
   * Get single product by ID with transformation
   */
  static async getProductById(productId: string): Promise<SimpleProduct | null> {
    try {
      console.log('🔥 ProductService.getProductById called for:', productId)
      
      // Get Enterprise system
      const system = getTWLSystem()
      
      // Ensure system is initialized
      if (!system.isReady()) {
        await system.initialize()
      }
      
      // Load product using system method
      const product = await system.getProduct(productId)

      if (!product) {
        console.log('❌ Product not found:', productId)
        return null
      }

      // Transform to UI format
      const transformedProduct = ProductTransformer.transformProduct(product)
      
      console.log('✅ Product transformed:', transformedProduct.name)
      
      return transformedProduct
      
    } catch (error) {
      console.error('❌ Error in ProductService.getProductById:', error)
      return null
    }
  }
  
  /**
   * Search products with transformation
   */
  static async searchProducts(
    query: string,
    filters: {
      categories?: string[]
      brands?: string[]
      priceMin?: number
      priceMax?: number
      inStockOnly?: boolean
      page?: number
      pageSize?: number
    } = {}
  ): Promise<SimpleProductListResponse> {
    try {
      console.log('🔍 ProductService.searchProducts called:', query, filters)
      
      // Get Enterprise system
      const system = getTWLSystem()
      
      // Ensure system is initialized
      if (!system.isReady()) {
        await system.initialize()
      }
      
      // Map category slugs to Enterprise categories
      const enterpriseCategories = filters.categories?.map(
        cat => CATEGORY_MAPPING[cat as keyof typeof CATEGORY_MAPPING]
      ).filter(Boolean)
      
      // Prepare Enterprise filters
      const enterpriseFilters: TWLProductFilters = {
        categories: enterpriseCategories,
        brands: filters.brands,
        priceMin: filters.priceMin,
        priceMax: filters.priceMax,
        inStockOnly: filters.inStockOnly
      }
      
      // Search products using system method
      const result = await system.searchProducts(
        query,
        enterpriseFilters,
        filters.page || 1,
        filters.pageSize || 20
      )

      if (!result) {
        throw new Error('Search failed')
      }

      // Transform to UI format
      const transformedResult = ProductTransformer.transformProductList(result)
      
      console.log('✅ Search completed:', transformedResult.products.length, 'products found')
      
      return transformedResult
      
    } catch (error) {
      console.error('❌ Error in ProductService.searchProducts:', error)
      
      return {
        products: [],
        total: 0,
        page: filters.page || 1,
        pageSize: filters.pageSize || 20,
        hasMore: false
      }
    }
  }
  
  /**
   * Get available categories
   */
  static getAvailableCategories() {
    return Object.keys(CATEGORY_MAPPING).map(slug => ({
      slug,
      name: slug.charAt(0).toUpperCase() + slug.slice(1),
      enterpriseCategory: CATEGORY_MAPPING[slug as keyof typeof CATEGORY_MAPPING]
    }))
  }
}

export default ProductService
