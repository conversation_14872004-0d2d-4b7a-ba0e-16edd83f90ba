'use client'

import { useState } from 'react'
import { motion } from 'framer-motion'
import { TransitionLink } from '@/components/transitions/RouteTransitionProvider'
import AnimatedButton from '@/components/ui/AnimatedButton'
import { Card, CardContent } from '@/components/ui/Card'
import { AnimatedBreadcrumb } from '@/components/transitions/NavigationProgress'

export default function TransitionsDemo() {
  const [selectedTransition, setSelectedTransition] = useState('breathe')

  const transitionTypes = [
    {
      id: 'breathe',
      name: 'Breathe',
      description: 'Organic breathing animation with blur effects',
      icon: '🌬️',
      demo: '/forms-demo'
    },
    {
      id: 'slideLeft',
      name: 'Slide Left',
      description: 'Smooth horizontal slide transition',
      icon: '➡️',
      demo: '/shop'
    },
    {
      id: 'slideRight',
      name: 'Slide Right',
      description: 'Reverse horizontal slide transition',
      icon: '⬅️',
      demo: '/'
    },
    {
      id: 'morph',
      name: 'Morph',
      description: '3D morphing with perspective transformation',
      icon: '🔄',
      demo: '/forms-demo'
    },
    {
      id: 'liquid',
      name: 'Liquid',
      description: 'Fluid border-radius morphing effect',
      icon: '💧',
      demo: '/shop'
    },
    {
      id: 'fadeScale',
      name: 'Fade Scale',
      description: 'Elegant fade with subtle scaling',
      icon: '✨',
      demo: '/'
    }
  ]

  const breadcrumbItems = [
    { href: '/', label: 'Inicio' },
    { href: '/transitions-demo', label: 'Transiciones' }
  ]

  return (
    <div className="min-h-screen bg-light-cloud-gray dark:bg-deep-pine pt-20">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        
        {/* Breadcrumb */}
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="mb-8"
        >
          <AnimatedBreadcrumb items={breadcrumbItems} />
        </motion.div>

        {/* Header */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="text-center mb-12"
        >
          <h1 className="text-4xl font-playfair font-bold text-forest-emerald dark:text-light-cloud-gray mb-4">
            Transiciones de Página
          </h1>
          <p className="text-warm-camel text-lg max-w-3xl mx-auto">
            Experimenta nuestras sofisticadas transiciones entre páginas que crean una experiencia 
            de navegación fluida y natural. Cada transición está diseñada para complementar el 
            contenido y mejorar la percepción de velocidad.
          </p>
        </motion.div>

        {/* Transition Types Grid */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-12"
        >
          {transitionTypes.map((transition, index) => (
            <motion.div
              key={transition.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 + index * 0.1 }}
            >
              <Card 
                variant="glass" 
                className={`cursor-pointer transition-all duration-300 ${
                  selectedTransition === transition.id 
                    ? 'ring-2 ring-rich-gold shadow-lg' 
                    : 'hover:shadow-md'
                }`}
                onClick={() => setSelectedTransition(transition.id)}
              >
                <CardContent className="p-6 text-center">
                  <motion.div
                    className="text-4xl mb-4"
                    animate={{
                      scale: selectedTransition === transition.id ? [1, 1.2, 1] : 1
                    }}
                    transition={{ duration: 0.5 }}
                  >
                    {transition.icon}
                  </motion.div>
                  
                  <h3 className="text-xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-2">
                    {transition.name}
                  </h3>
                  
                  <p className="text-warm-camel text-sm mb-4">
                    {transition.description}
                  </p>
                  
                  <TransitionLink
                    href={transition.demo}
                    transition={transition.id}
                    direction="forward"
                  >
                    <AnimatedButton
                      variant="primary"
                      size="sm"
                      className="w-full"
                      icon={
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                        </svg>
                      }
                      iconPosition="right"
                    >
                      Probar Transición
                    </AnimatedButton>
                  </TransitionLink>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>

        {/* Features Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12"
        >
          
          {/* Technical Features */}
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                Características Técnicas
              </h2>
              
              <div className="space-y-4">
                {[
                  {
                    icon: "⚡",
                    title: "Optimización GPU",
                    description: "Animaciones aceleradas por hardware para 60fps consistentes"
                  },
                  {
                    icon: "🎯",
                    title: "Transiciones Inteligentes",
                    description: "Selección automática basada en el contexto de navegación"
                  },
                  {
                    icon: "📱",
                    title: "Responsive",
                    description: "Adaptadas automáticamente para dispositivos móviles"
                  },
                  {
                    icon: "♿",
                    title: "Accesibilidad",
                    description: "Respeta las preferencias de movimiento reducido"
                  }
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1 + index * 0.1 }}
                    className="flex items-start gap-4"
                  >
                    <div className="text-2xl">{feature.icon}</div>
                    <div>
                      <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                        {feature.title}
                      </h3>
                      <p className="text-warm-camel text-sm">
                        {feature.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* UX Benefits */}
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6">
                Beneficios de UX
              </h2>
              
              <div className="space-y-4">
                {[
                  {
                    icon: "🧠",
                    title: "Continuidad Mental",
                    description: "Mantiene el contexto del usuario durante la navegación"
                  },
                  {
                    icon: "⏱️",
                    title: "Percepción de Velocidad",
                    description: "Las transiciones hacen que la app se sienta más rápida"
                  },
                  {
                    icon: "✨",
                    title: "Deleite Visual",
                    description: "Micro-momentos de satisfacción que mejoran la experiencia"
                  },
                  {
                    icon: "🎨",
                    title: "Identidad de Marca",
                    description: "Refuerza la personalidad premium de The White Laces"
                  }
                ].map((benefit, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: 20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 1 + index * 0.1 }}
                    className="flex items-start gap-4"
                  >
                    <div className="text-2xl">{benefit.icon}</div>
                    <div>
                      <h3 className="font-semibold text-forest-emerald dark:text-light-cloud-gray">
                        {benefit.title}
                      </h3>
                      <p className="text-warm-camel text-sm">
                        {benefit.description}
                      </p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>

        {/* Navigation Examples */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.2 }}
        >
          <Card variant="glass">
            <CardContent className="p-8">
              <h2 className="text-2xl font-semibold text-forest-emerald dark:text-light-cloud-gray mb-6 text-center">
                Prueba las Transiciones
              </h2>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <TransitionLink href="/" transition="breathe" direction="backward">
                  <AnimatedButton
                    variant="secondary"
                    className="w-full"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6" />
                      </svg>
                    }
                  >
                    Ir al Inicio
                  </AnimatedButton>
                </TransitionLink>
                
                <TransitionLink href="/shop" transition="slideLeft" direction="forward">
                  <AnimatedButton
                    variant="primary"
                    className="w-full"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z" />
                      </svg>
                    }
                  >
                    Ver Tienda
                  </AnimatedButton>
                </TransitionLink>
                
                <TransitionLink href="/forms-demo" transition="morph" direction="forward">
                  <AnimatedButton
                    variant="outline"
                    className="w-full"
                    icon={
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    }
                  >
                    Ver Formularios
                  </AnimatedButton>
                </TransitionLink>
              </div>
              
              <div className="mt-6 text-center">
                <p className="text-warm-camel text-sm">
                  Cada enlace utiliza una transición diferente. ¡Observa cómo cambia la experiencia!
                </p>
              </div>
            </CardContent>
          </Card>
        </motion.div>

      </div>
    </div>
  )
}
