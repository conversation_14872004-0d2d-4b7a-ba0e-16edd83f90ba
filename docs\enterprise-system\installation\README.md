# TWL Enterprise System Installation Guide

**🚀 Complete Installation and Setup Guide for All Environments**

This guide provides step-by-step instructions for installing and configuring the TWL Enterprise Product System across development, staging, and production environments.

## 📋 Table of Contents

- [Prerequisites](#-prerequisites)
- [Quick Start](#-quick-start)
- [Development Setup](#-development-setup)
- [Staging Setup](#-staging-setup)
- [Production Setup](#-production-setup)
- [Configuration](#-configuration)
- [Verification](#-verification)
- [Troubleshooting](#-troubleshooting)

## 🎯 Prerequisites

### **System Requirements**

| Component | Minimum | Recommended |
|-----------|---------|-------------|
| **Node.js** | 18.0+ | 20.0+ |
| **npm** | 8.0+ | 10.0+ |
| **Memory** | 2GB | 8GB+ |
| **Storage** | 5GB | 20GB+ |
| **CPU** | 2 cores | 4+ cores |

### **Software Dependencies**

```bash
# Check Node.js version
node --version  # Should be 18.0+

# Check npm version
npm --version   # Should be 8.0+

# Check available memory
free -h         # Linux/macOS
```

### **Environment Preparation**

```bash
# Create project directory
mkdir twl-enterprise
cd twl-enterprise

# Initialize npm project
npm init -y

# Install TypeScript globally (optional)
npm install -g typescript

# Install development tools
npm install -g @types/node ts-node
```

## 🚀 Quick Start

### **1. Install the Enterprise System**

```bash
# Clone or copy the enterprise system files
# Assuming you have the TWL project structure

# Navigate to your TWL project
cd /path/to/your/twl/project

# Install dependencies (if not already installed)
npm install

# The enterprise system is located at:
# lib/enterprise/
```

### **2. Basic Configuration**

Create a basic configuration file:

```typescript
// lib/enterprise/config/basic-config.ts
export const basicConfig = {
  productsBasePath: 'public/products',
  enableCache: true,
  enableAutoScan: true,
  environment: 'development',
  logLevel: 'info'
}
```

### **3. Initialize the System**

```typescript
// lib/enterprise/init.ts
import { initializeTWLSystem } from './TWLEnterpriseSystem'
import { basicConfig } from './config/basic-config'

async function initializeSystem() {
  try {
    console.log('🚀 Initializing TWL Enterprise System...')
    
    const system = await initializeTWLSystem(basicConfig)
    
    console.log('✅ System initialized successfully!')
    console.log('📊 System Status:', system.getHealth().status)
    
    return system
  } catch (error) {
    console.error('❌ Failed to initialize system:', error)
    throw error
  }
}

// Initialize on module load
initializeSystem().catch(console.error)
```

### **4. Test the Installation**

```bash
# Run the test script
npx ts-node lib/enterprise/test-system.ts
```

Expected output:
```
🧪 Testing TWL Enterprise System...
1. Initializing system...
✅ System Status: healthy
✅ System Ready: true
...
🎉 All tests passed! Enterprise system is working correctly.
```

## 💻 Development Setup

### **1. Development Configuration**

```typescript
// config/development.ts
export const developmentConfig = {
  // Core Settings
  productsBasePath: 'public/products',
  enableAutoScan: false,  // Disable for faster development
  scanInterval: 3600,
  
  // Cache Settings
  enableCache: true,
  cacheMemorySize: 50,    // Smaller cache for development
  cacheFileEnabled: true,
  cacheRedisEnabled: false,
  
  // Performance Settings
  enableMetrics: true,
  enableProfiling: true,  // Enable profiling in development
  maxConcurrentLoads: 5,
  
  // Development Settings
  environment: 'development',
  logLevel: 'debug',      // Verbose logging
  enableHealthChecks: false,  // Disable for development
}
```

### **2. Development Scripts**

Add to your `package.json`:

```json
{
  "scripts": {
    "dev": "next dev",
    "dev:enterprise": "ts-node lib/enterprise/examples/integration-example.ts",
    "test:enterprise": "ts-node lib/enterprise/test-system.ts",
    "scan:products": "ts-node -e \"import('./lib/enterprise/TWLEnterpriseSystem').then(m => m.initializeTWLSystem().then(s => s.triggerScan()))\""
  }
}
```

### **3. Development Workflow**

```bash
# Start development server
npm run dev

# In another terminal, test enterprise system
npm run test:enterprise

# Run integration examples
npm run dev:enterprise

# Manually trigger product scan
npm run scan:products
```

### **4. Hot Reload Setup**

```typescript
// lib/enterprise/dev-server.ts
import { initializeTWLSystem, shutdownTWLSystem } from './TWLEnterpriseSystem'

let system: any = null

async function startDevServer() {
  if (system) {
    await shutdownTWLSystem()
  }
  
  system = await initializeTWLSystem({
    environment: 'development',
    enableAutoScan: false,
    logLevel: 'debug'
  })
  
  console.log('🔥 Enterprise system ready for development')
}

// Handle hot reload
if (module.hot) {
  module.hot.accept(() => {
    startDevServer()
  })
  
  module.hot.dispose(() => {
    if (system) {
      shutdownTWLSystem()
    }
  })
}

startDevServer()
```

## 🎭 Staging Setup

### **1. Staging Configuration**

```typescript
// config/staging.ts
export const stagingConfig = {
  // Core Settings
  productsBasePath: process.env.TWL_PRODUCTS_PATH || 'public/products',
  enableAutoScan: true,
  scanInterval: 1800,     // 30 minutes
  
  // Cache Settings
  enableCache: true,
  cacheMemorySize: 100,
  cacheFileEnabled: true,
  cacheRedisEnabled: false,  // Enable if Redis available
  
  // Performance Settings
  enableMetrics: true,
  enableProfiling: false,
  maxConcurrentLoads: 10,
  
  // Staging Settings
  environment: 'staging',
  logLevel: 'info',
  enableHealthChecks: true,
  healthCheckInterval: 60,
}
```

### **2. Environment Variables**

```bash
# .env.staging
NODE_ENV=staging
TWL_PRODUCTS_PATH=public/products
TWL_CACHE_SIZE=100
TWL_LOG_LEVEL=info
TWL_API_KEY=staging_api_key_here
TWL_RATE_LIMIT=50
```

### **3. Staging Deployment**

```bash
# Build the application
npm run build

# Set environment
export NODE_ENV=staging

# Start the application
npm start

# Or use PM2 for process management
npm install -g pm2
pm2 start ecosystem.config.js --env staging
```

### **4. PM2 Configuration**

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'twl-enterprise-staging',
    script: 'npm',
    args: 'start',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_staging: {
      NODE_ENV: 'staging',
      TWL_PRODUCTS_PATH: 'public/products',
      TWL_CACHE_SIZE: '100',
      TWL_LOG_LEVEL: 'info'
    },
    env_production: {
      NODE_ENV: 'production',
      TWL_PRODUCTS_PATH: 'public/products',
      TWL_CACHE_SIZE: '200',
      TWL_LOG_LEVEL: 'warn'
    }
  }]
}
```

## 🚀 Production Setup

### **1. Production Configuration**

```typescript
// config/production.ts
export const productionConfig = {
  // Core Settings
  productsBasePath: process.env.TWL_PRODUCTS_PATH || 'public/products',
  enableAutoScan: true,
  scanInterval: 3600,     // 1 hour
  
  // Cache Settings
  enableCache: true,
  cacheMemorySize: 200,   // Larger cache for production
  cacheFileEnabled: true,
  cacheRedisEnabled: true,  // Enable Redis in production
  
  // Performance Settings
  enableMetrics: true,
  enableProfiling: false,
  maxConcurrentLoads: 20,
  
  // Production Settings
  environment: 'production',
  logLevel: 'warn',       // Less verbose logging
  enableHealthChecks: true,
  healthCheckInterval: 30,
  enableAlerts: true,
  
  // Security Settings
  enableRateLimit: true,
  rateLimitRequests: 100,
  rateLimitWindow: 60,
}
```

### **2. Production Environment Variables**

```bash
# .env.production
NODE_ENV=production
TWL_PRODUCTS_PATH=/app/public/products
TWL_CACHE_SIZE=200
TWL_LOG_LEVEL=warn
TWL_API_KEY=production_api_key_here
TWL_RATE_LIMIT=100
TWL_REDIS_URL=redis://redis:6379
TWL_ENABLE_ALERTS=true
```

### **3. Docker Production Setup**

```dockerfile
# Dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json ./
RUN npm ci --only=production

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000
ENV HOSTNAME "0.0.0.0"

CMD ["node", "server.js"]
```

### **4. Docker Compose Production**

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - TWL_PRODUCTS_PATH=/app/public/products
      - TWL_REDIS_URL=redis://redis:6379
    volumes:
      - ./public/products:/app/public/products:ro
    depends_on:
      - redis
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - app
    restart: unless-stopped

volumes:
  redis_data:
```

## ⚙️ Configuration

### **Complete Configuration Reference**

```typescript
interface SystemConfig {
  // Core Settings
  productsBasePath: string          // Path to products directory
  enableAutoScan: boolean          // Enable automatic scanning
  scanInterval: number             // Scan interval in seconds
  
  // Cache Settings
  enableCache: boolean             // Enable caching system
  cacheMemorySize: number          // Memory cache size in MB
  cacheFileEnabled: boolean        // Enable file cache
  cacheRedisEnabled: boolean       // Enable Redis cache
  
  // Performance Settings
  enableMetrics: boolean           // Enable performance metrics
  enableProfiling: boolean         // Enable performance profiling
  maxConcurrentLoads: number       // Max concurrent operations
  
  // API Settings
  enableAPI: boolean               // Enable API endpoints
  enableRateLimit: boolean         // Enable rate limiting
  rateLimitRequests: number        // Requests per minute
  rateLimitWindow: number          // Rate limit window in seconds
  
  // Monitoring Settings
  enableHealthChecks: boolean      // Enable health monitoring
  healthCheckInterval: number      // Health check interval in seconds
  enableAlerts: boolean           // Enable alerting
  
  // Environment Settings
  environment: 'development' | 'staging' | 'production'
  logLevel: 'debug' | 'info' | 'warn' | 'error'
}
```

### **Environment-Specific Configurations**

```typescript
// config/index.ts
import { developmentConfig } from './development'
import { stagingConfig } from './staging'
import { productionConfig } from './production'

export function getConfig() {
  const env = process.env.NODE_ENV || 'development'
  
  switch (env) {
    case 'development':
      return developmentConfig
    case 'staging':
      return stagingConfig
    case 'production':
      return productionConfig
    default:
      return developmentConfig
  }
}
```

## ✅ Verification

### **1. System Health Check**

```bash
# Check system status
curl http://localhost:3000/api/enterprise/system/status

# Expected response
{
  "success": true,
  "data": {
    "status": "healthy",
    "components": {
      "loader": "healthy",
      "cache": "healthy",
      "api": "healthy",
      "scanner": "healthy"
    }
  }
}
```

### **2. Product Loading Test**

```bash
# Test product loading
curl http://localhost:3000/api/enterprise/products/test-product

# Test search functionality
curl "http://localhost:3000/api/enterprise/products?q=Nike&page=1&pageSize=5"
```

### **3. Performance Verification**

```typescript
// scripts/performance-test.ts
import { initializeTWLSystem } from '../lib/enterprise/TWLEnterpriseSystem'

async function performanceTest() {
  const system = await initializeTWLSystem()
  
  console.log('🚀 Running performance tests...')
  
  // Test response times
  const start = Date.now()
  await system.searchProducts('Nike', {}, 1, 10)
  const duration = Date.now() - start
  
  console.log(`Search response time: ${duration}ms`)
  
  // Test cache performance
  const metrics = system.getMetrics()
  console.log(`Cache hit rate: ${metrics.cache.hitRate}%`)
  console.log(`Memory usage: ${metrics.cache.memoryUsage}MB`)
  
  // Test system health
  const health = system.getHealth()
  console.log(`System status: ${health.status}`)
}

performanceTest()
```

## 🔧 Troubleshooting

### **Common Issues**

#### **1. System Won't Initialize**

```bash
# Check Node.js version
node --version

# Check products directory exists
ls -la public/products

# Check permissions
ls -la public/

# Run with debug logging
NODE_ENV=development npm run test:enterprise
```

#### **2. Poor Performance**

```typescript
// Check cache configuration
const system = getTWLSystem()
const metrics = system.getMetrics()
console.log('Cache hit rate:', metrics.cache.hitRate)

// Increase cache size if needed
const config = {
  cacheMemorySize: 200,  // Increase from default
  enableCache: true
}
```

#### **3. Memory Issues**

```bash
# Monitor memory usage
node --max-old-space-size=4096 your-app.js

# Check for memory leaks
npm install -g clinic
clinic doctor -- node your-app.js
```

### **Debug Mode**

```typescript
// Enable debug mode
const system = await initializeTWLSystem({
  logLevel: 'debug',
  enableProfiling: true,
  enableMetrics: true
})

// Check detailed logs
const logs = system.getLogger().getLogBuffer()
console.log('System logs:', logs)
```

---

**🚀 Your TWL Enterprise System is now ready for production with enterprise-grade performance, reliability, and monitoring!**

**📊 Next Steps: Configure monitoring, set up alerts, and deploy to your production environment.**
